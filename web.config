<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <handlers>
            <add name="python" path="*.py" verb="*" modules="FastCgiModule" scriptProcessor="X:\web\SPAPI_Python_SDK\venv\Scripts\python.exe" resourceType="File" />
            <add name="PythonHandler" path="*" verb="*" modules="FastCgiModule" scriptProcessor="X:\web\SPAPI_Python_SDK\venv\Scripts\python.exe|X:\web\SPAPI_Python_SDK\venv\Lib\site-packages\wfastcgi.py" resourceType="Unspecified" />
        </handlers>
    </system.webServer>
    <appSettings>
    <add key="WSGI_HANDLER" value="django.core.wsgi.get_wsgi_application()" />
    <add key="PYTHON" value="X:\web\SPAPI_Python_SDK" />
    <add key="DJANGO_SETTINGS_MODULE" value="SPAPI_Python_SDK.settings" />
  </appSettings>
</configuration>
