{"info": {"_postman_id": "73a2e4eb-e98d-4f32-8443-866953365e6c", "name": "Tokens API Sandbox Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "New Request", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "x-amz-access-token", "value": "", "type": "text", "description": "access_token value"}], "body": {"mode": "raw", "raw": "{\n    \"restrictedResources\": [\n    {\n        \"method\": \"GET\",\n        \"path\": \"/orders/v0/orders/{orderId}/address\"\n    }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://sandbox.sellingpartnerapi-na.amazon.com/tokens/2021-03-01/restrictedDataToken", "protocol": "https", "host": ["sandbox", "sellingpartnerapi-na", "amazon", "com"], "path": ["tokens", "2021-03-01", "restrictedDataToken"]}}, "response": []}, {"name": "New Request", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "x-amz-access-token", "value": "", "type": "text", "description": "restrictedDataToken value"}], "url": {"raw": "https://sandbox.sellingpartnerapi-na.amazon.com/orders/v0/orders/TEST_CASE_200/address", "protocol": "https", "host": ["sandbox", "sellingpartnerapi-na", "amazon", "com"], "path": ["orders", "v0", "orders", "TEST_CASE_200", "address"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "lwa_token", "value": "", "disabled": true}]}