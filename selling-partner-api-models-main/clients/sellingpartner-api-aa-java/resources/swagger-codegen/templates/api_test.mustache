{{>licenseInfo}}

package {{package}};

import {{invokerPackage}}.ApiException;
{{#imports}}import {{import}};
{{/imports}}
import org.junit.Test;
import org.junit.Ignore;

{{^fullJavaUtil}}
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
{{/fullJavaUtil}}
import com.amazon.SellingPartnerAPIAA.LWAException;

/**
* API tests for {{classname}}
*/
@Ignore
public class {{classname}}Test {

private final {{classname}} api = new {{classname}}();

{{#operations}}{{#operation}}
/**
* {{summary}}
*
* {{notes}}
*
* @throws ApiException if the Api call fails
* @throws LWAException If calls to fetch LWA access token fails
*/
@Test
public void {{operationId}}Test() throws ApiException, LWAException {
{{#allParams}}
    {{{dataType}}} {{paramName}} = null;
{{/allParams}}
{{#returnType}}{{{returnType}}} response = {{/returnType}}api.{{operationId}}({{#allParams}}{{paramName}}{{#hasMore}}, {{/hasMore}}{{/allParams}});

// TODO: test validations
}
{{/operation}}{{/operations}}
}