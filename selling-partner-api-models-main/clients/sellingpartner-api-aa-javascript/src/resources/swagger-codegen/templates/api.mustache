{{>licenseInfo}}
{{=< >=}}
import {ApiClient} from "../ApiClient.js";
<#imports>import {<import>} from '../<#modelPackage><&modelPackage>/</modelPackage><import>.js';
</imports>

<#emitJSDoc>/**
* <baseName> service.
* @module <#invokerPackage><&invokerPackage>/</invokerPackage><#apiPackage><&apiPackage>/</apiPackage><classname>
* @version <projectVersion>
*/</emitJSDoc>
export class <classname> {

    <#emitJSDoc>/**
    * Constructs a new <&classname>. <#description>
    * <description></description>
    * @alias module:<#invokerPackage><&invokerPackage>/</invokerPackage><#apiPackage><apiPackage>/</apiPackage><classname>
    * @class
    * @param {module:<#invokerPackage><&invokerPackage>/</invokerPackage>ApiClient} [apiClient] Optional API client implementation to use,
    * default to {@link module:<#invokerPackage><&invokerPackage>/</invokerPackage>ApiClient#instance} if unspecified.
    */</emitJSDoc>
    constructor(apiClient) {
        this.apiClient = apiClient || ApiClient.instance;
    }

<#operations><#operation><#emitJSDoc><^usePromises>
    /**
     * Callback function to receive the result of the <operationId> operation.
     * @callback module:<#invokerPackage><invokerPackage>/</invokerPackage><#apiPackage><apiPackage>/</apiPackage><classname>~<operationId>Callback
     * @param {String} error Error message, if any.
     * @param <#vendorExtensions.x-jsdoc-type>{<&vendorExtensions.x-jsdoc-type>} data The data returned by the service call.</vendorExtensions.x-jsdoc-type><^vendorExtensions.x-jsdoc-type>data This operation does not return a value.</vendorExtensions.x-jsdoc-type>
     * @param {String} response The complete HTTP response.
     */</usePromises>

    /**<#summary>
     * <summary></summary><#notes>
     * <notes></notes><#allParams><#required>
     * @param {<&vendorExtensions.x-jsdoc-type>} <paramName> <description></required></allParams><#hasOptionalParams>
     * @param {Object} opts Optional parameters<#allParams><^required>
     * @param {<&vendorExtensions.x-jsdoc-type>} opts.<paramName> <description><#defaultValue> (default to <.>)</defaultValue></required></allParams></hasOptionalParams><^usePromises>
     * @param {module:<#invokerPackage><&invokerPackage>/</invokerPackage><#apiPackage><&apiPackage>/</apiPackage><&classname>~<operationId>Callback} callback The callback function, accepting three arguments: error, data, response<#returnType>
     * data is of type: {@link <&vendorExtensions.x-jsdoc-type>}</returnType></usePromises><#usePromises>
     * @return {Promise} a {@link https://www.promisejs.org/|Promise}<#returnType>, with an object containing data of type {@link <&vendorExtensions.x-jsdoc-type>} and HTTP response</returnType><^returnType>, with an object containing HTTP response</returnType></usePromises>
     */
</emitJSDoc>    <operationId><#usePromises>WithHttpInfo</usePromises>(<vendorExtensions.x-codegen-arg-list><^usePromises><#hasParams>, </hasParams>callback</usePromises>) {<#hasOptionalParams>
      opts = opts || {};</hasOptionalParams>
      let postBody = <#bodyParam><#required><paramName></required><^required>opts['<paramName>']</required></bodyParam><^bodyParam>null</bodyParam>;
<#allParams><#required>
      // verify the required parameter '<paramName>' is set
      if (<paramName> === undefined || <paramName> === null) {
        throw new Error("Missing the required parameter '<paramName>' when calling <operationId>");
      }
</required></allParams>

      let pathParams = {<#pathParams>
        '<baseName>': <#required><paramName></required><^required>opts['<paramName>']</required><#hasMore>,</hasMore></pathParams>
      };
      let queryParams = {<#queryParams>
        '<baseName>': <#collectionFormat>this.apiClient.buildCollectionParam(<#required><paramName></required><^required>opts['<paramName>']</required>, '<collectionFormat>')</collectionFormat><^collectionFormat><#required><paramName></required><^required>opts['<paramName>']</required></collectionFormat><#hasMore>,</hasMore></queryParams>
      };
      let headerParams = {<#headerParams>
        '<baseName>': <#required><paramName></required><^required>opts['<paramName>']</required><#hasMore>,</hasMore></headerParams>
      };
      let formParams = {<#formParams>
        '<baseName>': <#collectionFormat>this.apiClient.buildCollectionParam(<#required><paramName></required><^required>opts['<paramName>']</required>, '<collectionFormat>')</collectionFormat><^collectionFormat><#required><paramName></required><^required>opts['<paramName>']</required></collectionFormat><#hasMore>,</hasMore></formParams>
      };

      let contentTypes = [<#consumes>'<& mediaType>'<#hasMore>, </hasMore></consumes>];
      let accepts = [<#produces>'<& mediaType>'<#hasMore>, </hasMore></produces>];
      let returnType = <#returnType><&returnType></returnType><^returnType>null</returnType>;

      return this.apiClient.callApi(
        '<&path>', '<httpMethod>',
        pathParams, queryParams, headerParams, formParams, postBody,
        contentTypes, accepts, returnType<^usePromises>, callback</usePromises>
      );
    }
<#usePromises>
    <#emitJSDoc>

    /**<#summary>
     * <summary></summary><#notes>
     * <notes></notes><#allParams><#required>
     * @param {<&vendorExtensions.x-jsdoc-type>} <paramName> <description></required></allParams><#hasOptionalParams>
     * @param {Object} opts Optional parameters<#allParams><^required>
     * @param {<&vendorExtensions.x-jsdoc-type>} opts.<paramName> <description><#defaultValue> (default to <.>)</defaultValue></required></allParams></hasOptionalParams><^usePromises>
     * @param {module:<#invokerPackage><&invokerPackage>/</invokerPackage><#apiPackage><&apiPackage>/</apiPackage><&classname>~<operationId>Callback} callback The callback function, accepting three arguments: error, data, response<#returnType>
     * data is of type: {@link <&vendorExtensions.x-jsdoc-type>}</returnType></usePromises><#usePromises>
     * @return {Promise} a {@link https://www.promisejs.org/|Promise}<#returnType>, with data of type {@link <&vendorExtensions.x-jsdoc-type>}</returnType></usePromises>
     */
</emitJSDoc>    <operationId>(<vendorExtensions.x-codegen-arg-list>) {
      return this.<operationId>WithHttpInfo(<vendorExtensions.x-codegen-arg-list>)
        .then(function(response_and_data) {
          return response_and_data.data;
        });
    }
</usePromises>
</operation></operations>

}
<={{ }}=>
