{{#emitJSDoc}}
/**
 * The {{classname}} model module.
 * @module {{#invokerPackage}}{{invokerPackage}}/{{/invokerPackage}}{{#modelPackage}}{{modelPackage}}/{{/modelPackage}}{{classname}}
 * @version {{projectVersion}}
 */
{{/emitJSDoc}}
export class {{classname}} {{#parentModel}}extends {{classname}} {{/parentModel}}{{#parent}}{{^parentModel}}{{#vendorExtensions.x-isArray}}extends Array {{/vendorExtensions.x-isArray}}{{/parentModel}}{{/parent}}{
{{#emitJSDoc}}
  /**
   * Constructs a new <code>{{classname}}</code>.{{#description}}
   * {{description}}{{/description}}
   * @alias module:{{#invokerPackage}}{{invokerPackage}}/{{/invokerPackage}}{{#modelPackage}}{{modelPackage}}/{{/modelPackage}}{{classname}}
   * @class{{#useInheritance}}{{#parent}}
   * @extends {{#parentModel}}module:{{#invokerPackage}}{{invokerPackage}}/{{/invokerPackage}}{{#modelPackage}}{{modelPackage}}/{{/modelPackage}}{{classname}}{{/parentModel}}{{^parentModel}}{{#vendorExtensions.x-isArray}}Array{{/vendorExtensions.x-isArray}}{{#vendorExtensions.x-isMap}}Object{{/vendorExtensions.x-isMap}}{{/parentModel}}{{/parent}}{{#interfaces}}
   * @implements module:{{#invokerPackage}}{{invokerPackage}}/{{/invokerPackage}}{{#modelPackage}}{{modelPackage}}/{{/modelPackage}}{{.}}{{/interfaces}}{{/useInheritance}}{{#vendorExtensions.x-all-required}}
   * @param {{name}} {{=< >=}}{<&vendorExtensions.x-jsdoc-type>}<={{ }}=> {{#description}}{{{description}}}{{/description}}{{/vendorExtensions.x-all-required}}
   */
{{/emitJSDoc}}
  constructor({{#vendorExtensions.x-all-required}}{{name}}{{^-last}}, {{/-last}}{{/vendorExtensions.x-all-required}}) {
{{#parentModel}}
    super({{#vendorExtensions.x-all-required}}{{name}}{{^-last}}, {{/-last}}{{/vendorExtensions.x-all-required}});
{{/parentModel}}
{{#parent}}
  {{^parentModel}}
    {{#vendorExtensions.x-isArray}}
    super();
    {{/vendorExtensions.x-isArray}}
  {{/parentModel}}
{{/parent}}
{{#useInheritance}}
  {{#interfaceModels}}
    {{classname}}.call(this{{#vendorExtensions.x-all-required}}, {{name}}{{/vendorExtensions.x-all-required}});
  {{/interfaceModels}}
{{/useInheritance}}
{{#vars}}
  {{#required}}
    this.{{name}} = {{name}};
  {{/required}}
{{/vars}}
  }

{{#emitJSDoc}}
  /**
   * Constructs a <code>{{classname}}</code> from a plain JavaScript object, optionally creating a new instance.
   * Copies all relevant properties from <code>data</code> to <code>obj</code> if supplied or a new instance if not.
   * @param {Object} data The plain JavaScript object bearing properties of interest.
   * @param {{=< >=}}{module:<#invokerPackage><invokerPackage>/</invokerPackage><#modelPackage><modelPackage>/</modelPackage><classname>}<={{ }}=> obj Optional instance to populate.
   * @return {{=< >=}}{module:<#invokerPackage><invokerPackage>/</invokerPackage><#modelPackage><modelPackage>/</modelPackage><classname>}<={{ }}=> The populated <code>{{classname}}</code> instance.
   */
{{/emitJSDoc}}
  static constructFromObject(data, obj) {
    if (data){{! TODO: support polymorphism: discriminator property on data determines class to instantiate.}} {
      switch(typeof data) {
        case 'string':
          obj = String(data);
          break;
        case 'number':
          obj = Number(data);
          break;
        case 'boolean':
          obj = Boolean(data);
          break;
      }
      obj = obj || new {{classname}}();
{{#parent}}
  {{^parentModel}}
      ApiClient.constructFromObject(data, obj, '{{vendorExtensions.x-itemType}}');
  {{/parentModel}}
{{/parent}}
{{#useInheritance}}
  {{#parentModel}}
      {{classname}}.constructFromObject(data, obj);
  {{/parentModel}}
  {{#interfaces}}
      {{.}}.constructFromObject(data, obj);
  {{/interfaces}}
{{/useInheritance}}
{{#vars}}
      if (data.hasOwnProperty('{{baseName}}'))
        obj.{{name}}{{{defaultValueWithParam}}}
{{/vars}}
    }
    return obj;
  }
{{#emitModelMethods}}

  {{#vars}}
    {{#emitJSDoc}}
  /**{{#description}}
   * Returns {{{description}}}{{/description}}{{#minimum}}
   * minimum: {{minimum}}{{/minimum}}{{#maximum}}
   * maximum: {{maximum}}{{/maximum}}
   * @return {{=< >=}}{<&vendorExtensions.x-jsdoc-type>}<={{ }}=>
   */
    {{/emitJSDoc}}
  {{getter}}() {
    return this.{{name}};
  }

    {{#emitJSDoc}}
  /**{{#description}}
   * Sets {{{description}}}{{/description}}
   * @param {{=< >=}}{<&vendorExtensions.x-jsdoc-type>}<={{ }}=> {{name}}{{#description}} {{{description}}}{{/description}}
   */
    {{/emitJSDoc}}
  {{setter}}({{name}}) {
    this.{{name}} = {{name}};
  }

  {{/vars}}
{{/emitModelMethods}}
}
{{#hasVars}}

  {{#vars}}
    {{#isEnum}}
      {{^isContainer}}
{{>partial_model_inner_enum}}
      {{/isContainer}}
    {{/isEnum}}
    {{#items.isEnum}}
      {{#items}}
        {{^isContainer}}
{{>partial_model_inner_enum}}
        {{/isContainer}}
      {{/items}}
    {{/items.isEnum}}
    {{#emitJSDoc}}
/**{{#description}}
 * {{{description}}}{{/description}}
 * @member {{=< >=}}{<&vendorExtensions.x-jsdoc-type>}<={{ }}=> {{name}}{{#defaultValue}}
 * @default {{{defaultValue}}}{{/defaultValue}}
 */
    {{/emitJSDoc}}
{{classname}}.prototype.{{name}} = {{#defaultValue}}{{{defaultValue}}}{{/defaultValue}}{{^defaultValue}}undefined{{/defaultValue}};

  {{/vars}}
{{/hasVars}}
{{#useInheritance}}
  {{#interfaceModels}}
// Implement {{classname}} interface:
    {{#allVars}}
      {{#emitJSDoc}}
/**{{#description}}
 * {{{description}}}{{/description}}
 * @member {{=< >=}}{<&vendorExtensions.x-jsdoc-type>}<={{ }}=> {{name}}{{#defaultValue}}
 * @default {{{defaultValue}}}{{/defaultValue}}
 */
      {{/emitJSDoc}}
{{classname}}.prototype.{{name}} = {{#defaultValue}}{{{defaultValue}}}{{/defaultValue}}{{^defaultValue}}undefined{{/defaultValue}};

    {{/allVars}}
  {{/interfaceModels}}
{{/useInheritance}}