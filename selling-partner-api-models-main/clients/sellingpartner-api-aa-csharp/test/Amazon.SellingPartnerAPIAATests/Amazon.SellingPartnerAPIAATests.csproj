<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>

    <IsPackable>false</IsPackable>
    <ReleaseVersion>2.0</ReleaseVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.5.0" />
    <PackageReference Include="xunit" Version="2.4.0" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.0" />
    <PackageReference Include="coverlet.collector" Version="1.2.0" />
    <PackageReference Include="Moq" Version="4.14.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Amazon.SellingPartnerAPIAA\Amazon.SellingPartnerAPIAA.csproj" />
  </ItemGroup>
</Project>
