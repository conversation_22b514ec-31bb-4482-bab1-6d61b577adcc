# coding: utf-8

{{>partial_header}}

from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from {{packageName}}.api_client import ApiClient


{{#operations}}
class {{classname}}(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client
{{#operation}}

    def {{operationId}}(self, {{#sortParamsByRequiredFlag}}{{#allParams}}{{#required}}{{paramName}}, {{/required}}{{/allParams}}{{/sortParamsByRequiredFlag}}**kwargs):  # noqa: E501
        """{{#summary}}{{{.}}}{{/summary}}{{^summary}}{{operationId}}{{/summary}}  # noqa: E501

{{#notes}}
        {{{notes}}}  # noqa: E501
{{/notes}}
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
{{#sortParamsByRequiredFlag}}
        >>> thread = api.{{operationId}}({{#allParams}}{{#required}}{{paramName}}, {{/required}}{{/allParams}}async_req=True)
{{/sortParamsByRequiredFlag}}
{{^sortParamsByRequiredFlag}}
        >>> thread = api.{{operationId}}({{#allParams}}{{#required}}{{paramName}}={{paramName}}_value, {{/required}}{{/allParams}}async_req=True)
{{/sortParamsByRequiredFlag}}
        >>> result = thread.get()

        :param async_req bool
{{#allParams}}
        :param {{dataType}} {{paramName}}:{{#description}} {{{description}}}{{/description}}{{#required}} (required){{/required}}{{#optional}}(optional){{/optional}}
{{/allParams}}
        :return: {{#returnType}}{{returnType}}{{/returnType}}{{^returnType}}None{{/returnType}}
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.{{operationId}}_with_http_info({{#sortParamsByRequiredFlag}}{{#allParams}}{{#required}}{{paramName}}, {{/required}}{{/allParams}}{{/sortParamsByRequiredFlag}}**kwargs)  # noqa: E501
        else:
            (data) = self.{{operationId}}_with_http_info({{#sortParamsByRequiredFlag}}{{#allParams}}{{#required}}{{paramName}}, {{/required}}{{/allParams}}{{/sortParamsByRequiredFlag}}**kwargs)  # noqa: E501
            return data

    def {{operationId}}_with_http_info(self, {{#sortParamsByRequiredFlag}}{{#allParams}}{{#required}}{{paramName}}, {{/required}}{{/allParams}}{{/sortParamsByRequiredFlag}}**kwargs):  # noqa: E501
        """{{#summary}}{{{.}}}{{/summary}}{{^summary}}{{operationId}}{{/summary}}  # noqa: E501

{{#notes}}
        {{{notes}}}  # noqa: E501
{{/notes}}
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
{{#sortParamsByRequiredFlag}}
        >>> thread = api.{{operationId}}_with_http_info({{#allParams}}{{#required}}{{paramName}}, {{/required}}{{/allParams}}async_req=True)
{{/sortParamsByRequiredFlag}}
{{^sortParamsByRequiredFlag}}
        >>> thread = api.{{operationId}}_with_http_info({{#allParams}}{{#required}}{{paramName}}={{paramName}}_value, {{/required}}{{/allParams}}async_req=True)
{{/sortParamsByRequiredFlag}}
        >>> result = thread.get()

        :param async_req bool
{{#allParams}}
        :param {{dataType}} {{paramName}}:{{#description}} {{{description}}}{{/description}}{{#required}} (required){{/required}}{{#optional}}(optional{{#defaultValue}}, default to {{{.}}}{{/defaultValue}}){{/optional}}
{{/allParams}}
        :return: {{#returnType}}{{returnType}}{{/returnType}}{{^returnType}}None{{/returnType}}
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = [{{#allParams}}'{{paramName}}'{{#hasMore}}, {{/hasMore}}{{/allParams}}]  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method {{operationId}}" % key
                )
            params[key] = val
        del params['kwargs']
{{#allParams}}
{{#required}}
        # verify the required parameter '{{paramName}}' is set
        if self.api_client.client_side_validation and ('{{paramName}}' not in params or
                                                       params['{{paramName}}'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `{{paramName}}` when calling `{{operationId}}`")  # noqa: E501
{{/required}}
{{/allParams}}

{{#allParams}}
{{#hasValidation}}
    {{#maxLength}}
        if self.api_client.client_side_validation and ('{{paramName}}' in params and
                                                       len(params['{{paramName}}']) > {{maxLength}}):
            raise ValueError("Invalid value for parameter `{{paramName}}` when calling `{{operationId}}`, length must be less than or equal to `{{maxLength}}`")  # noqa: E501
    {{/maxLength}}
    {{#minLength}}
        if self.api_client.client_side_validation and ('{{paramName}}' in params and
                                                       len(params['{{paramName}}']) < {{minLength}}):
            raise ValueError("Invalid value for parameter `{{paramName}}` when calling `{{operationId}}`, length must be greater than or equal to `{{minLength}}`")  # noqa: E501
    {{/minLength}}
    {{#maximum}}
        if self.api_client.client_side_validation and ('{{paramName}}' in params and params['{{paramName}}'] >{{#exclusiveMaximum}}={{/exclusiveMaximum}} {{maximum}}):  # noqa: E501
            raise ValueError("Invalid value for parameter `{{paramName}}` when calling `{{operationId}}`, must be a value less than {{^exclusiveMaximum}}or equal to {{/exclusiveMaximum}}`{{maximum}}`")  # noqa: E501
    {{/maximum}}
    {{#minimum}}
        if self.api_client.client_side_validation and ('{{paramName}}' in params and params['{{paramName}}'] <{{#exclusiveMinimum}}={{/exclusiveMinimum}} {{minimum}}):  # noqa: E501
            raise ValueError("Invalid value for parameter `{{paramName}}` when calling `{{operationId}}`, must be a value greater than {{^exclusiveMinimum}}or equal to {{/exclusiveMinimum}}`{{minimum}}`")  # noqa: E501
    {{/minimum}}
    {{#pattern}}
        if self.api_client.client_side_validation and ('{{paramName}}' in params and not re.search(r'{{{vendorExtensions.x-regex}}}', params['{{paramName}}']{{#vendorExtensions.x-modifiers}}{{#-first}}, flags={{/-first}}re.{{.}}{{^-last}} | {{/-last}}{{/vendorExtensions.x-modifiers}})):  # noqa: E501
            raise ValueError("Invalid value for parameter `{{paramName}}` when calling `{{operationId}}`, must conform to the pattern `{{{pattern}}}`")  # noqa: E501
    {{/pattern}}
    {{#maxItems}}
        if self.api_client.client_side_validation and ('{{paramName}}' in params and
                                            len(params['{{paramName}}']) > {{maxItems}}):
            raise ValueError("Invalid value for parameter `{{paramName}}` when calling `{{operationId}}`, number of items must be less than or equal to `{{maxItems}}`")  # noqa: E501
    {{/maxItems}}
    {{#minItems}}
        if self.api_client.client_side_validation and ('{{paramName}}' in params and
                                            len(params['{{paramName}}']) < {{minItems}}):
            raise ValueError("Invalid value for parameter `{{paramName}}` when calling `{{operationId}}`, number of items must be greater than or equal to `{{minItems}}`")  # noqa: E501
    {{/minItems}}
{{/hasValidation}}
{{#-last}}
{{/-last}}
{{/allParams}}
        collection_formats = {}

        path_params = {}
{{#pathParams}}
        if '{{paramName}}' in params:
            path_params['{{baseName}}'] = params['{{paramName}}']{{#isListContainer}}  # noqa: E501
            collection_formats['{{baseName}}'] = '{{collectionFormat}}'{{/isListContainer}}  # noqa: E501
{{/pathParams}}

        query_params = []
{{#queryParams}}
        if '{{paramName}}' in params:
            query_params.append(('{{baseName}}', params['{{paramName}}'])){{#isListContainer}}  # noqa: E501
            collection_formats['{{baseName}}'] = '{{collectionFormat}}'{{/isListContainer}}  # noqa: E501
{{/queryParams}}

        header_params = {}
{{#headerParams}}
        if '{{paramName}}' in params:
            header_params['{{baseName}}'] = params['{{paramName}}']{{#isListContainer}}  # noqa: E501
            collection_formats['{{baseName}}'] = '{{collectionFormat}}'{{/isListContainer}}  # noqa: E501
{{/headerParams}}

        form_params = []
        local_var_files = {}
{{#formParams}}
        if '{{paramName}}' in params:
            {{#notFile}}form_params.append(('{{baseName}}', params['{{paramName}}'])){{/notFile}}{{#isFile}}local_var_files['{{baseName}}'] = params['{{paramName}}']{{/isFile}}{{#isListContainer}}  # noqa: E501
            collection_formats['{{baseName}}'] = '{{collectionFormat}}'{{/isListContainer}}  # noqa: E501
{{/formParams}}

        body_params = None
{{#bodyParam}}
        if '{{paramName}}' in params:
            body_params = params['{{paramName}}']
{{/bodyParam}}
        {{#hasProduces}}
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            [{{#produces}}'{{{mediaType}}}'{{#hasMore}}, {{/hasMore}}{{/produces}}])  # noqa: E501

        {{/hasProduces}}
        {{#hasConsumes}}
        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            [{{#consumes}}'{{{mediaType}}}'{{#hasMore}}, {{/hasMore}}{{/consumes}}])  # noqa: E501

        {{/hasConsumes}}
        # Authentication setting
        auth_settings = [{{#authMethods}}'{{name}}'{{#hasMore}}, {{/hasMore}}{{/authMethods}}]  # noqa: E501

        return self.api_client.call_api(
            '{{{path}}}', '{{httpMethod}}',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type={{#returnType}}'{{returnType}}'{{/returnType}}{{^returnType}}None{{/returnType}},  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)
{{/operation}}
{{/operations}}