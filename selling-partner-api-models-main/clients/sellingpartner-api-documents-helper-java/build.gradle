plugins {
    id 'idea'
    id 'eclipse'
    id 'java'
}

group = 'com.amazon.sellingpartnerapi'
version = '1.0.0'


repositories {
    jcenter()
    mavenCentral()
}

sourceCompatibility = JavaVersion.VERSION_1_8
targetCompatibility = JavaVersion.VERSION_1_8


/*sourceSets {
    main {
        java {
            srcDirs = ['src/main/java']
        }
        resources {
            srcDirs = ['src/main/resources']
        }
    }

    test {
        java {
            srcDirs = ['src/test/java']
        }
        resources {
            srcDirs = ['src/test/resources']
        }
    }
}*/

task execute(type: JavaExec) {
    main = System.getProperty('mainClass')
    classpath = sourceSets.main.runtimeClasspath
}

test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}

dependencies {
    testImplementation(platform('org.junit:junit-bom:5.7.0'))
    testImplementation('org.junit.jupiter:junit-jupiter')
    testRuntimeOnly("org.junit.platform:junit-platform-launcher:1.7.0")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine:5.7.0")
    testRuntimeOnly("org.junit.vintage:junit-vintage-engine:5.7.0")

    implementation 'com.squareup.okhttp:okhttp:2.7.5'
    implementation 'com.google.guava:guava:28.2-jre'

    // https://mvnrepository.com/artifact/org.threeten/threetenbp
    implementation group: 'org.threeten', name: 'threetenbp', version: '1.3.5'

    // https://mvnrepository.com/artifact/junit/junit
    implementation 'org.junit.jupiter:junit-jupiter-migrationsupport:5.5.1'

    implementation 'org.mockito:mockito-core:3.0.0'
    implementation 'org.mockito:mockito-inline:3.0.0'

    implementation 'org.apache.directory.studio:org.apache.commons.io:2.4'

}
