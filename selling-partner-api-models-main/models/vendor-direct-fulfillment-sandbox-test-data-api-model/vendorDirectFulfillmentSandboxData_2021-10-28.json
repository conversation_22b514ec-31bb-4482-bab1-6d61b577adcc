{"swagger": "2.0", "info": {"description": "The Selling Partner API for Vendor Direct Fulfillment Sandbox Test Data provides programmatic access to vendor direct fulfillment sandbox test data.", "version": "2021-10-28", "title": "Selling Partner API for Vendor Direct Fulfillment Sandbox Test Data", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sandbox.sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/vendor/directFulfillment/sandbox/2021-10-28/orders": {"post": {"tags": ["vendorDFSandbox"], "description": "Submits a request to generate test order data for Vendor Direct Fulfillment API entities.", "operationId": "generateOrderScenarios", "parameters": [{"in": "body", "name": "body", "required": true, "description": "The request payload that contain parameters to generate test order data scenarios.", "schema": {"$ref": "#/definitions/GenerateOrderScenarioRequest"}}], "responses": {"202": {"description": "Success.", "schema": {"$ref": "#/definitions/TransactionReference"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}, "x-amzn-api-sandbox-only": true}}, "/vendor/directFulfillment/sandbox/2021-10-28/transactions/{transactionId}": {"get": {"tags": ["vendorDFSandboxtransactionstatus"], "description": "Retrieves the transaction status identified by the specified `transactionId`, and returns the requested test order data if the transaction is successful.", "operationId": "getOrderScenarios", "responses": {"200": {"description": "Success.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "examples": {"application/json": {"transactionStatus": {"transactionId": "ff35f39e-e69f-499e-903e-6c4f6c32609f-20210827003391", "status": "Success", "testCaseData": {"scenarios": [{"scenarioId": "SCENARIO_1", "orders": [{"orderId": "T11121"}, {"orderId": "T11123"}]}, {"scenarioId": "SCENARIO_2", "orders": [{"orderId": "T22241"}, {"orderId": "T22244"}]}]}}}}, "schema": {"$ref": "#/definitions/TransactionStatus"}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "401": {"description": "The request's authorization header is not formatted correctly or does not contain a valid token.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "404": {"description": "The resource specified does not exist.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "415": {"description": "The request payload is in an unsupported format.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}}, "parameters": [{"name": "transactionId", "in": "path", "description": "The transaction identifier returned in the response for the `generateOrderScenarios` operation.", "required": true, "type": "string"}], "x-amzn-api-sandbox": {"dynamic": {}}, "x-amzn-api-sandbox-only": true}}}, "definitions": {"GenerateOrderScenarioRequest": {"description": "The `generateOrderScenarios` request body.", "type": "object", "properties": {"orders": {"description": "The list of test orders requested as indicated by party identifiers.", "type": "array", "items": {"$ref": "#/definitions/OrderScenarioRequest"}}}}, "OrderScenarioRequest": {"description": "The party identifiers required to generate the test data.", "type": "object", "required": ["sellingParty", "shipFromParty"], "properties": {"sellingParty": {"description": "The identifier for the selling party or vendor.", "$ref": "#/definitions/PartyIdentification"}, "shipFromParty": {"description": "The warehouse code for the vendor.", "$ref": "#/definitions/PartyIdentification"}}}, "PartyIdentification": {"description": "The identification object for the party information. For example, warehouse code or vendor code.", "type": "object", "required": ["partyId"], "properties": {"partyId": {"type": "string", "description": "Assigned identification for the party. For example, warehouse code or vendor code."}}}, "Pagination": {"description": "A generated string used to pass information to your next request. If `NextToken` is returned, pass the value of `NextToken` to the next request. If `NextToken` is not returned, there are no more order items to return.", "type": "object", "properties": {"nextToken": {"type": "string", "description": "A generated token that retrieves the next set of results. This token is specified in the next request."}}}, "TransactionReference": {"description": "A GUID assigned by Amazon to identify this transaction.", "type": "object", "properties": {"transactionId": {"type": "string", "description": "A GUID (Globally Unique Identifier) assigned by Amazon to uniquely identify the transaction."}}}, "TransactionStatus": {"description": "The payload for `getOrderScenarios`.", "type": "object", "properties": {"transactionStatus": {"$ref": "#/definitions/Transaction"}}}, "Transaction": {"description": "The transaction details that include the status. If the transaction is successful, also includes the requested test order data.", "type": "object", "properties": {"transactionId": {"description": "The unique identifier returned in the response for the `generateOrderScenarios` request.", "type": "string"}, "status": {"description": "The current processing status of the transaction.", "type": "string", "enum": ["FAILURE", "PROCESSING", "SUCCESS"], "x-docgen-enum-table-extension": [{"value": "FAILURE", "description": "Transaction has failed."}, {"value": "PROCESSING", "description": "Transaction is in process."}, {"value": "SUCCESS", "description": "Transaction has completed successfully."}]}, "testCaseData": {"description": "Test case data for the transaction. Only available when the transaction status is `SUCCESS`.", "$ref": "#/definitions/TestCaseData"}}, "required": ["transactionId", "status"]}, "TestCaseData": {"description": "The set of test case data returned in response to the test data request.", "type": "object", "properties": {"scenarios": {"description": "Set of use cases that describes the possible test scenarios.", "type": "array", "items": {"$ref": "#/definitions/Scenario"}}}}, "Scenario": {"description": "A scenario test case response returned when the request is successful.", "properties": {"scenarioId": {"description": "An identifier that identifies the type of scenario that user can use for testing.", "type": "string"}, "orders": {"description": "A list of orders that can be used by the caller to test each life cycle or scenario.", "type": "array", "items": {"$ref": "#/definitions/TestOrder"}}}, "required": ["scenarioId", "orders"], "type": "object"}, "TestOrder": {"description": "Error response returned when the request is unsuccessful.", "properties": {"orderId": {"description": "An error code that identifies the type of error that occurred.", "type": "string"}}, "required": ["orderId"], "type": "object"}, "ErrorList": {"description": "A list of error responses returned when a request is unsuccessful.", "type": "object", "properties": {"errors": {"type": "array", "description": "An array of individual error objects that contain error details.", "items": {"$ref": "#/definitions/Error"}}}, "required": ["errors"]}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}}}