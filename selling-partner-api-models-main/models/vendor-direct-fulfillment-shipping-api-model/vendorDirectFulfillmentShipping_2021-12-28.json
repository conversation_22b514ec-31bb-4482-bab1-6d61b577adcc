{"swagger": "2.0", "info": {"description": "The Selling Partner API for Direct Fulfillment Shipping provides programmatic access to a direct fulfillment vendor's shipping data.", "version": "2021-12-28", "title": "Selling Partner API for Direct Fulfillment Shipping", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/vendor/directFulfillment/shipping/2021-12-28/shippingLabels": {"get": {"tags": ["vendorShippingLabels"], "description": "Returns a list of shipping labels created during the time frame that you specify. You define that time frame using the createdAfter and createdBefore parameters. You must use both of these parameters. The date range to search must not be more than 7 days.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getShippingLabels", "parameters": [{"name": "shipFromPartyId", "in": "query", "description": "The vendor warehouseId for order fulfillment. If not specified, the result will contain orders for all warehouses.", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "The limit to the number of records returned.", "required": false, "type": "integer", "maximum": 100, "minimum": 1}, {"name": "createdAfter", "in": "query", "description": "Shipping labels that became available after this date and time will be included in the result. Must be in ISO-8601 date/time format.", "required": true, "type": "string", "format": "date-time"}, {"name": "createdBefore", "in": "query", "description": "Shipping labels that became available before this date and time will be included in the result. Must be in ISO-8601 date/time format.", "required": true, "type": "string", "format": "date-time"}, {"name": "sortOrder", "in": "query", "description": "Sort ASC or DESC by order creation date.", "required": false, "type": "string", "default": "ASC", "enum": ["ASC", "DESC"], "x-docgen-enum-table-extension": [{"value": "ASC", "description": "Sort in ascending order by order creation date."}, {"value": "DESC", "description": "Sort in descending order by order creation date."}]}, {"name": "nextToken", "in": "query", "description": "Used for pagination when there are more ship labels than the specified result size limit. The token value is returned in the previous API call.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/ShippingLabelList"}, "examples": {"pagination": {"nextToken": "MDAwMDAwMDAwMQ=="}, "shippingLabels": [{"purchaseOrderNumber": "2JK3S9VCE", "sellingParty": {"partyId": "999US"}, "shipFromParty": {"partyId": "ABCD"}, "labelFormat": "PNG", "labelData": [{"packageIdentifier": "PKG001", "trackingNumber": "1Z6A34Y60369738804", "shipMethod": "UPS_GR_RES", "shipMethodName": "UPS Ground Residential", "content": "Base 64 encoded string goes here "}]}, {"purchaseOrderNumber": "2JK3S9VD", "sellingParty": {"partyId": "999US"}, "shipFromParty": {"partyId": "ABCD"}, "labelFormat": "PNG", "labelData": [{"packageIdentifier": "PKG002", "trackingNumber": "1Z6A34Y60369738805", "shipMethod": "UPS_GR_RES", "shipMethodName": "UPS Ground Residential", "content": "Base 64 encoded string goes here "}]}]}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "examples": {"application/json": {"errors": [{"code": "InvalidRequest", "message": "The request is invalid."}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}}, "post": {"tags": ["vendorShippingLabels"], "description": "Creates a shipping label for a purchase order and returns a transactionId for reference.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "submitShippingLabelRequest", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/SubmitShippingLabelsRequest"}, "description": "Request body that contains the shipping labels data."}], "responses": {"202": {"description": "Success.", "schema": {"$ref": "#/definitions/TransactionReference"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}}}, "/vendor/directFulfillment/shipping/2021-12-28/shippingLabels/{purchaseOrderNumber}": {"get": {"tags": ["vendorShippingLabels"], "description": "Returns a shipping label for the purchaseOrderNumber that you specify.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getShippingLabel", "parameters": [{"name": "purchaseOrderNumber", "in": "path", "description": "The purchase order number for which you want to return the shipping label. Should be the same `purchaseOrderNumber` as received in the order.", "required": true, "type": "string", "pattern": "^[a-zA-Z0-9]+$"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/ShippingLabel"}, "examples": {"application/json": {"purchaseOrderNumber": "2JK3S9VC", "sellingParty": {"partyId": "999US"}, "shipFromParty": {"partyId": "ABCD"}, "labelFormat": "PNG", "labelData": [{"packageIdentifier": "PKG001", "trackingNumber": "1Z6A34Y60369738804", "shipMethod": "UPS_GR_RES", "shipMethodName": "UPS Ground Residential", "content": "iVBORw0KGgoAAAANSUhEUgAAAyAAAAV4CAYAAABYfbnIAABfDUlEQVR42uzd25LjOJJAwf7/n+592UttTaZEABGBAOhuBrOZrtSFFCXhSCL5zz///POvYRiGYRiGYRhG0fjnXwAAgGwCBAAAECAAAIAAAQAAECAAAIAAAQAAyA2Q/zm81rd///Z3I387epu/jdn7EPU3I/f1yf3/6d9n7uun2/vt+maXN3O7WV2m0XU7s709eRxGH6snf/vktqO2ryfLFrG9P13PAMAlAfJ08rF6XU/+bubfRv/76v2Yvf2ZOBudAM/eh9XbydgeRv776OP5dF2PLMNP/z1imUYuH7l9ra7PmWVg7HV7JeBWozviNkZfY6M/ONi1XgAESECAzH7iPTORHp0gzk7YBEh+gDz9lLxzgERF8UxsjD420dc1s12tbn/eBNYn3tkT+E4BknUbAAJk4QpHJ26f/mZkArTyxrA6UXq6XBEBEnFfs8KgKkBWtpvZx2D2ulY+RY5ez9HXnREzI3EtQHI/LIr6mWrmZDvqejOWIeonxAAC5GGAjExcnkzkn34CmzkRG53YZ0wUoz5Nj1rGbgHyZLuZvf87AiR6W1n571nfvERufyZx8RP32X2BqiKkKkBGl2Hm2yMAAdIgQEYm2LMT2cwA+WnZBEh+gEROjiMez9nJxcwnrVlxMLp/RXWAiJDciXtEgETGwq4AyVpW2y0gQILiI2Ji9zRkZl7MI/bTGJmcrwTI06MgZQbI7H3I/qnXzLqeOarSyuMZvePqyuM0cp0r21B0gKysBwSIAAG4PEBmJ3afJnVRE6+VCfvOAFmd2N36DcjsdhPxbUFVgIxOAGfjPyL+MgNk9gMGE7vn21WnSf7bAgTo84FL1jy44uAYVXP4VgGy+mnop5/WVOwcmzk5/3OZBEh8gERuNyPbUWWAfJswZu0v0jFAMibY1E/yO9+36EPI2wZh/+tW5nMxMhSqA2TXkfraBUjU5DZiQjzzN9kBMvN3tx4FK2O76RIgmT9vmj1fxuiBISKOShWx/Zj85YVvh08gd+6EPvvGDuwJj8SJdPrrR8brc9TPa7cHyOoEeOZIO1GT7uzzNMxsJDsCpOowqJHne8g8rGzE47k7QCLPmbG6zJGH4X0aRCZ9+yf4lW/+GdeT9VMKoOY1q8sHIJH73kW8z2WEU2mAzNRZ1VF7Zg7lGzmBrQiQyEm4AOkdIJnLVBEgGet/dZmIn9xv+B1xeIBELYMTEMIdH6Q8eW5H3hcBsnjnR158n37qFHGbq0cXWn0gM54sK0dBWvnE4NPjOXM7o/dn5NPKkW1h9fFcmWhEbKOrz53ZAFk5v0TUY2Vi1zNATvoGJDocRAjcHSDRl+vyAU7rb0AA8Iad8WnhrgDJWAYRAne+nq1cfmeA7Io3AQJA9RtOq4nA6PVk/ywLeFeAVPyqRoAA8Mr46DoRmLmezJ99AQKkS4AccRQsAO55k37LRCAjQJwrBARIdIDs+OZUgADQIj6+Haa280Qg6nj3UQGS/QYPCJCO8SFAALwxT02YBYgAAQFy50+wCgPHix7Am9+URy/z2xtV9VFdqgNkdOIgPkCAnBQgxT/vyrnzTxZs5rwVMytw9XwUEcs+extRX79FnXdhdZ2Nnhsm6lwi3uhh/jVy5EzhlW9qlQEyc26ekfUPnBEgq9fZ9TC8lbcZFiCjL8y//du3/xbxM4Enn+bNFubIRjUTbrP1++nyO9ZZxFnvv50Je/WxBAHyT+gJTjPPKRL9AcrKcvhABATI6uWqXyeqv3HZ9g3IkwBZmXRHTqZnJvozk++Z259dFyshEbHOBAjcFSBPrrfLfV8NkKj7CNwTILOvDR0CZOUgJMcEyKfLPP3vq7+5i5isf7qvqwUZucxZ8bJ6/TNP1ortEAAQIBnXu+N8Q9Hx8ZoAqZ6QRgTI6uQ7+tP8iB2lKgIk4lsRAQIAVE3mZ/YN63KEvMxvgY8IkNmdd6om0yMbS8cAWYmQrHUmQACA3RPrbrdZHADvDZDoo0xFTaZnftP39PZWJucRR+haPeZ/5PWu7twqQACA3QESNV8TIIUBMnrdmffr6Q7WERPqmQCJmGBHT+Jn1plvQAAAXh2JewKk8tuPjInxk3/P/nlSVP12WmdRj7sAAQAQIP/xd5WTydUdqk8NkOj9UbLXmQABABAgZRP9p6FSuUP1k0l8VExkHNJ2Z4DMng8mYod+AQIAIECGJ4eRk8uMyfTIiWSqJuxR912AAADQNkAid8J+cp2rRxR48nffYmLl6E0jy5N1ZuId62x0eWaW3RmHAQBeECAzsQIAAAgQAAAAAQIAAAgQAAAAAQIAAAgQAABAgAAAABwQIKvno3hy7odvt/f0v2Xc96fLFnFfIpdp5vFcPRfK6H3I2uZWb3/2bwEABEjwiQhn/+7TZPe36xm97sizaf82EZ9ZppUziWedBXxk3T+JgNkAenrfItbD6skdAQC4JEBWJ+yzk/Wnk9CV2Bi5jQ4BEr0OZwMk8/ZnwwUAgIMC5Onfr4ZE5OR19d8jAmR0mVYez9n7OXq7s4/J6LdoAgQA4KUBMvpNQ8VkPWJSGREoT+5fRYDM3M+Z2FyJwtFoEiAAAALk42Vmv034Npk+PUCeLtPsfR+djM9M/p/Gy+w3IKP7aggQAIDLAmR0YhgZID9NlE8PkCfLNHPfZyfuK99+RAdI1DoXIABHT3auux4QIEXfgKz+BCsiQEYPq3tygMxc12p8RAZIdMx6IwFOmWyPftg38t729L169BD1ka+lUdfZ7XpAgCRN/n57sYs+mtXoEayiJ7EZ0TYbDd9uM3NCPvKGJEAA1l6XMj84W/k5beRrqW88QICETE5XXjhnJ+urk92nf/M0wHYGSHZkrj6WAgQgJiRW9slc+TntrdHgPQI2BMhKQMye72Nm0rozQKJ+Vvb0DeLkAJkNMgECCJD895fdASI+QICEBEjEi2bGGcUjJ7HRAZQVIBmT99kAiT7rvAAB3hYgo6+TT75hj37dzN4HUYDAwQHy9Df8MzujPT2s6rd9BqK+zYjcke/JOnzyt9Evet+uf/SxyXjRzngsRnaKrNiBEqBjgDz9m10BMvo+ccL1gACBRm+eAMRP8GePHll1/z797bcPyE67HhAgAMDrAuTJz6l3/FRq5Dqjoqf6ekCAAACvDJCZv+kUIKf+DQgQAOD6APk7KASIAAEBAgCkB8ifB3oZ/XcBIkBAgACAAHn8N6NHZRQgAgQECAAIkOlwiPqblYl35HlAos7vtON6QIAAAMcGyMyhZKMCITtAfrtM1Al/s68HBAgAcGx8RJww9enPiFbuW3SAjNzO6Il/s68HBAgAwEsmQ52uBwQIAIAAESAgQAAAxAcIEACAMyZAba4HBAgAAIAAAQAABAgAAIAAAQAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAAAASJAAAAAAQIAAAgQAAAAAQIAAAgQAAAAAQIAAAgQAABAgAAAAAgQAABAgAAAAAgQAABAgAAAAAIEAABAgAAAAAIEAABAgAAAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAgAABAAAQIAAAgAABAAAQIAAAgAABAAAEiAABAAAECAAAIEAAAAAECAAAIEAAAAAECAAAIEAAAAABAgAAIEAAAAABAgAAIEAAAAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAECAAAAACBAAAECAAAAACBAAAECAAAAAAgQAAOCwAPnvK/5xjP796mW+/d3o3367X6vrJ+r2s+47AAC0CZDZifOpATI7mc8MkKqIAgCArQEyMjleuY7Zy6x8e/Bkkj+7rlZue/Z+iRAAAI4OkNEIOD1AViMkI0BG74sIAQBAgBwUICuT+JUA+Wk9zsaQCAEA4OoAybieEwNk1zqIWicAANAqQGavU4DU/5RqZjlFCAAA7QIk4uhQAqQ2QDL2dwEAgPAA+RQhKz8JijwkrgARIAAAXBQgTwLipgDJmpB3DJC//w4AAFoEyJOQiJ5MdzgPSOa66xIgAADQNkC+hchpAVJ5JnEBAgCAACmKkJMCpGp9CRAAAARIQIScFCAbHpSyEwqKDwAAjguQnTt2C5CaExECAIAAESA/XiYjXIQKAAACRIBMX251nxgAAGgbIKcfBeuEABkJhYjzswAAwPYAmT16VOZlRu5Tp6NfVZ1RPjJsAACgJECeTHxXJswCJOYQu1HXBQAALQIEAABAgAAAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAgAABAAAQIFUr4tczhs+eRX3mNlfv70/X8/S2VtdBxPKNXi77fsw+Hl0eewAAAXJQiIxOdp/+zezfj0y0M25ndcIc9TjMXP/s/Z7dfjo99gAAAuSACFn998hP2qMm2SPXEzUxn50Uz3xjEnU/Zif0nR97AAAB8pIAiZgYR35y/+TfI4NiNUCiYm90Ha4sa9fHHgBAgFwcIFETzOgJ/5OfaXUIkOjYm5nEryxrx8ceAECAXB4gGdcXuYw/7awePcnNDpAnf7vyLULmZXc89gAAAuQlATIzOc6agP52FK+MoIgIkCff1kQGSHS8dHrsAQAEyOUBsjLRzJyErh4GtmuAzP60bOaxO/WxBwAQIC8JkNHJcfYkdOVITxUB8unyI5P91biKCJhujz0AgAC5OEBmJ7cCZCwqIr7BWFmGkx57AAABcnmAzEyQV88ePnpG7s4B8mR/j5G/nXncVo5q1e2xBwAQIC8KkIydp0f//oR9QHYEyLcRGTG7HnsAAAHykgD56d93TEJHj9a0e2I8csSumaN7VZ00UIAAAObaAqQ8QP7+m8hzQYxExG+X7XQiwp/uW/SJIEcj5fTHHgBAgFwWGDP7XlROQkd+vnRygIx+q1Ox7ex+7AEABEizAImYFM7uc/Dk76riKvr+RGxjs7cbGVQrJxbc/dgDAAiQxgGSOcmfmWBGTaYr94eInhRnhc/o/Ys843rlYw8AIEA2B8jM+SKyfjYT9al59KfzK38XPSF+a4BEPPYAAAKkSYCs7Ffw6UhS2T+dGbmtp38bvV6it6vofU9Wz5ny087k3R57AAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAECAAAAACBAAAECAAAAACJBfVsT/G7PXsXL7lcv30/I++btP62rmMtnrq+L+zNzuzDrLut8V67NyOaOuf/frAgAIkIvD49PEY3SSszJBqlq+n+7D6KTpyWRz5rJZk7qn66J6u5pdj7sCZHZ9Vi7n08uM3m726wIACJCXxsfTycbMpD3isqOBM/N3qxO4iAng0wle1eMdfTurk++obSZrff79N5XLOfpBQOT9yX5uA4AAuThAZqKiy0+wVu93VYDMLPuTCW3F4x11Gx0CJHt9Pv2pU2WArGy7Va8LACBABMgRARIxkayYiK8GjwCJ/bYm+zETIADA6wMkevJwaoDsmoivBkjG41O93Lsnr9nrs2q9R63XXcsPAAKE/7eCKiaL0b/nz76ejAnl04lq5oQxKkA6xeTO9SlABAgACJDEyZ8AyQ2Q1Z+KVe+E3uGbqNHrfkOARP3cS4AAgAAJnZyceh6QiuvJmlDOBEjURDDym4vIw7BWB8iusBAgACBAXh8fmeceOClAZk8MlxUgERPBqnM0RJ5QL+v+ZU6sR5a5apI+G7kCBAAESGmMvDlARievWQES8e3Fykknd4XI7hNUZl+HAAEAAcKmSZQAGTtre/S3MhWTxI4BErE+u3/TM3IbAgQABEibCBEg+ZfPPi9D5KffVWGbcZ+q1qcAESAAIEBeECBR19XpPCCj+3d0D5Ddk9eq9dkpQDLWtwABAAEiQAIn/6vLXR0gUYFRFW7dA6Q62LoEyMh24kzoACBApiYkXSYaGZO5qoDIPJ/EzPVUHz74pACpXp8dAiTjYBICBAAESMok/cSjYM0G1q4AeTLJjVqPXQJk5+S1en2eGCDRyy1AAECAPJr8nnoekN+Wb/RwqasBEXWOiB0Tx4jHMXryGjnR7xwgO3a4nwlrAQIAAiRskj5zhKKZw5FGHAY1ejmf3qdP1zP69yP3d2b5Zh/j2cch6vGM3iZ2r8+q5Yye/Fe/LgCAAAG44MMFAECAAAAAAgQAAECAAAAAAgQAAECAAAAAAgQAABAgAgQAABAgAACAAAEAABAgAACAAAEAABAgAACAAOmzYr6OU5cF3vgc7v7acsL1f7qt7Nfd7vf/9vsFIEA2h8e3N4eZ68l48426rozl+fv+dLyN3euo+3MgOgKi1knHDwx2Pdcz1kP09VY/L7p+oNTl9fr26wcESPv4mJ1QdQ+QDpNrAXJ3gEReJipAdqzT2deVyNiqmthmrJ/KSOsapwJEgIAAeXF8/Pa30df/2+W6BEjWOqy4jajHpfNj3uUT2cjL7trud76uRK+rrKCLfJwy7nvm/a+KfQEiQECAvDw+ZieJOyajkS/UAkSAVP8MKvObqF3rL3qbyf770clg5vMjO546xKkAESAgQF4cIBmTIwHy/TorbkOA1ARI9ATkxACJnDDvuP5v22OHAFn5sKhTgETfr+xlOv36AQHScoI1elkBIkAEiACJWoZuAZL52pn9DVHVdXV5TxIggAARIFvf7KK/rhYg90xwKpYjMkB2bve7HvvbAyRyuxIgAgQQIAKkYYB8m5ydNOGOvg0BMj+pn43LiHDstN3vmpwLEAEiQAABclGAnDBxFiACpOtz5Onhj0cuL0DiDt3bJUBmJpoCRIAAAuTqAIleJ7MTsMgX7x1nYBYgNY95p0nR0wiIDpBd2323AMk41G+XCbwAESCAAHlFhOyYnGcFyKdlFSACpGOAnLDd7wqQmQg55bkjQASIAAEB8voAqX6zFiAC5JTnyMgEd2aSLEDmA+RbBJ7wfD/1ud8xQLLOM3Li9QMC5LgIyTgjefYLd9VkTIDsecy7BsjMuq4+THTFJ7iZh+Kd3c5O+cBBgNSfiPAN1w8IkNdEiAARIKe/gQqQmm2r4uzRpwXIqa9XJwaIM6EDAqR5iGS9qWSeO+Db8gkQP8GaXZ6Ic1dEfFtQvd1XhETGhO2UCfwt3+CcsF6zt4+Trh8QIFdEyO6dcKMmRwJEgDzZxyAiYk7Z7mdfRyICaWSfj1N/wiRA/tn6vH779QMCpG2MdAyQHV9dCxABEjlhPmW733X/onZUf9PzPeK6d+wvuPu5LUIAAXJohGRPRiMmOW+ckAiQuGXaESAdtvuI5+jqNrP6M62Oz/eu1y9ABAggQNqESLc30x0v2gJk/0RiV4CMLOeT6zhpu9+xfVfv4H7jc0eACEpAgGx/s78pQHa9aAuQdwZI1HMhe/+P7o9N1RnNu0+UTz1KmQARIIAAaTd5iTyKVuQb9mlvnF0CJHuSuLJ9nhggERPBXdt95np9W4DMfpN204RVgAgQECACRIAIEAEyGBC7ruO2AMl6HnR8vq++pgqQ9efF7M//Trt+QIC0e6G/IUCiJpMCRIDMxMOu69i53e/Ypm8KkB2P/60Bkn2UxtOvHxAgVwVI9ZtKxDJkTMYESM1jLkB6bfenTzZ3Phd3BvpbAiQyaE+/fkCAHBsgOz6hESACpPtzR4DUb8u7J2oZ97/yEMpv+QlW9M/5Tv97QIAc90K/69CXuwKk+ic11bcR9Wlb9WN+wyRpdR112e53T3yrdlzPfvy6BEjXo6FlfTNWeU6rLtcPCJC2L/QRb3rRb7DRb8hR17dzotDlcal4zN8SIKuPXeW2tDs+nk5YI5at4izunV7vdsborte57Pvd6foBAXJkgOz4hE+ACBABcmaA7IybzJ9XCpC+ARL5nLn5+gEBAvCKDzpuvU0AECAAjQIEABAgAACAAAEAAAQIAACAAAEAAAQIAACAAAEAAAQIAAAgQAAAAAQIAAAgQAAAAAQIAAAgQAAAAAECAAAgQAAAAAECAAAgQAAAAAECAAAIEAAAAAECAAAIEAAAAAECAAAIEAAAQIAIEAAAQIAAAAACBAAAQIAAAAACBAAAQIAAAAACBAAAECAAAAACBAAAECAAAAACBAAAECAAAIAAAQAAECAAAIAAAQAAECAAAIAAAQAABAgAAIAAAQAABAgAAIAAAQAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAAAAQIAACBAAAAAAQIAACBAAAAAAQIAACBAAAAAAQIAAAgQAAAAAQIAAAgQAAAAAQIAAAgQAABAgAAAAAgQAABAgAAAAAgQAABAgAAAAAIEAABAgAAAAAIEAABAgAAAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAgAABAAAQIAAAgAABAAAQIAAAgAABAAAEiAABAAAECAAAIEAAAAAECAAAIEAAAAAECAAAIEAAAAABAgAAIEAAAAABAgAAIEAAAAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAECAAAAACBAAAECAAAAACBAAAECAAAAAAgQAAECAAAAAAgQAAECAAAAAAgQAABAgAgQAABAgAACAAAEAABAgAACAAAEAABAgAACAAAEAAAQIAACAAAEAAAQIAACAAAEAAAQIAAAgQAAAAAQIAAAgQAAAAAQIAAAgQAAAAAECAAAgQAAAAAECAAAgQAAAAAECAAAIEAAAAAECAAAIEAAAAAECAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAAACBAAAQIAAAAACBAAAQIAAAAACBAAAECAAAAACBAAAECAAAAACBAAAECAAAIAAAQAAECAAAIAAAQAAECAAAIAAAQAABAgAAIAAAQAABAgAAIAAAQAAegaIYRiGYRiGYRhG0bASDMMwDMMwDMMQIIZhGIZhGIZhCBDDMAzDMAzDMAwBYhiGYRiGYRjGCQECHH0kif8dAABHHAULECAAAAIEECAAgAABBAgAgAABAQIAIEAAAQIAIEBAgAAACBBAgAAAAgQQIAAAAgQEiAABAATIx0nR7IQp43LV97PTMtx+P29YPgECAAiQxYnR7KQp43LV97PTMtx+P29YPgECAAiQiUnRn5OjT/82e50Z96X69qqX4fb7ecPyCRAAQIAIEAEiQAQIt70JLG+Hb1w3HdfRifc5+j3ftvfu9eIxFCACRIAIEAES/mI9Mjq9we1anlvezE/ZDt1n26THsc96OfF9TIAkPBARkyb7gNhHwj4gAkSA+ISu+/bnPucth23vHevnpHXS5f1LgPzwoHz7t5EVnnG56vv59Dpn72fGv2Xcz5H1svPIUxn3c+RyAkSACJBztrvs9ZZ5n0+IJ9ve/evolHVyw3Px2gDJnEDdcLmsT8UrH6NO3xLdsOwCZN+Ld5frrngz8ia4FmAnRuMt8WTbu3s9Za6X3fdThDQKkE77g1RfLnO/gKrHqNN+Mjcs+5sDZHWCcsqk/NRP7t4YH5XX0+0+V99vn+733vZOWjdd3iu8/goQASJABMgLPyXsGiBPb69b6ImPvRPBHfc5+ttZAXLmtnfiutm5XXd+zxEgAsQk3LILkAs+qd35Glg9MRQfeyeCu34WKEBse8Js/wcBAqRow3vT5ewHYdntA9LjxXnHRGjXm43f3u/5aZ3r/mfqQ523BEjHx1CY9bjOi9/f7zkKVsRRjaLu58x6iLq9kdufXdez6zNivUQtX/WyZz62bz9E5U0B0ins/Pb+zonUjut+w2T6tm1PgPS5rwKk8SfHN3wSX/ETj66X6/QYnbQtCRABUhEfb/j9/akTqROeX2+YSN+47QmzXgFy4c9hzz8T+g37IlQ++bpdrtNjdNq2JED2/lZdgPgE+sQJT9fJqyM7vWvdCZC+HwYIEAEiQATIqwOk4oV5x4S80/4fb3jzEyACxLb3z3Hrpds3fAJEgAgQASJABMiVAbJjPY6ugxu3oa6TTQFyf4C8dd2dGGYC5IAA+TYxsg9I3WTSPiA91pl9QO4MkOjf/O4KkJvfAE88WaAAuWP7O3nbEyC1QSlAkjawv//3t3/77TpG/zb6fj69jtnbzrq9iHVdcbnZ66y+vYj7KUByAmHHpPyEAHlyewKk3+11+ZmKAHnftnfyh0rZtyVADgiQ1U+GT/kG4s3nJPFve7fdNwRI5ovzjgDZ8eby9PZufBMUIALEtidAuq8XAVL8xnfDPhhvPiu7f9u/7QqQewJk1+uwADk/QDI/eb35sbLtCZCTAuTC93QBIkD8mwB5X4Ds+llS59e2G3dGf0uAdDqxmkmYALFe7oskASJABIgAESCJk7vsN6/ur20CpNftZUZIt8dYgAgQAXJ/fJQFyLdJkn1A7ANiHxABsuNToh0B0nn/DwFyR4CMHpyk0+MrQO5ffzteXzq/pr3x24+SAKk+klDEEbKijmYVsV5GLvd02TPW341H1sq4TkfB6vNCPXL5itvtEh+jE16TwJrbyzhEtACx7QmQ/t/4XX50y14/DTn50+jT1ssNy95p4u48IOcHSPYb++k/Lb01QLqfjyH6XDV+imLbEyD2d7oyQE7ZT6HLE2/Herlh2avvS8ZjJEByJmECRIDcNglcjZAnZ70/4bESIALk1AC58SAfAkSACBAB8qYXrSMDpPrNJnP/ABGyZxIYESF2TrbtCZD9h/19a3gIEAEiQATIawMkc7+I6u0u8/YESN9J4G3hIUAEyJsD5I3sA9Lgybdzvdyw7J0m7vYBuTdAIt7cBUj/7abirOJdPmn1DYhtT4D4BuTKAPnzgRj9u+yjYGUfSWtkeauPgtXlxadqW5rdziKuM+pxFyCxbyZvCJDqw7+aCNZPoG/8KdatE7Tbtj0BUvO8FCDFG2P25KrTNwmdvhG4cWK689+qHj8BMvbiXbFfxOhluwbISZ+qZkwMOk4CM/cB6fB4CpC+254A2fccFSDFb5KdfqffaT8EvyGse2wztonsT70ESF6ArLzJd4iP7MO/3jgRjD5vStYn3BkTIAFi2xMgc7eVsS0IEAEiQASIABEgRwbISROGbpPAnZ9mRv/UsPOkR4Ds3fYqbs95QM4OSAEiQASIAHltgMy+oVTtF9E1QG792c5JE8EdE/KTHtfbJ2Hdtz0B0n+7ECCJKz57cmUfkPdMSnf+W9XjJ0D6BcjIT2Ju+5TVRDBu/ey4vt2P6xsmYAJEgIiQJgHy54r/6f9XHAVr9mhI2Ufkml2G2fuy47GOvp8VR8GKOJqVo2CdGSAzk6Q3fPvxhkOkrpxhvPKnVxnLLEDese0JkPPe1wSIT7GPvL3qjdq3T7XrVIAIEAFS981A528/VpdZgLxj26t6PegYIKe+rwmQ5JW/49+q72en9XLKY5txXzIuJ0D2v1hXH+7yp2/Mdv38qnqidPM2VTXB6XguEQFy/7YnQESIABEgAkSACJCmATLyhtZh/w9vjH0nj7snSwLEttc1QKo/PBEgAkSACBABIkCuD5Cq9eGN8awJYNdPhwWIbU+ACJCrA+TbhMo+IPYBsQ+IAIl6s6yYKN3y7cfo+rYdnhMgnSb+tq29296Nk2wBIkCmHoCVf4s6StSnv4u4vd1H3co4ulSndTa7TE8v5yhY50bIybe9I0De9Oa4cwIoQGxbN8dH9WNdcX4er7EXBUjHSVmnbzWq18sN3zx12s6cB0SAnBgfo8tsAnjOJFyA2PYEyNp1CRAB0uoM6tXXmbFebtj3ptN25kzoPQLk9NveFSBveoPcNXkSIALkDeuoy09lo07S6PVVgAgQASJAvCG/PkBOnzicvs2dtn4FiG3vxpiqPESx11YBIkAEiADxhtzyMKan//xqZplNAM+YiDsRoW1PgOwPAwFy4ZM0+zrtA2IfEPuAnBMgt8TPrgB52xvljglgt2/yBIj4OO0x33GCRq+pjQIk4whBEUdtyrjOqKNLRRxh6dNtRB15KuPfZrePjHWWsY1HHI1MgJwdIBXL3mmdmwD2n4wLENueABEgVwXI7d8KVC97xn055ZwrN0zAs9aLN+fxELjltrv/Vv+ynSXLl7HT+WwEyLu2vdOXs+LQu9WxdOkHOn2OynLKfhHVy55xX04563zXCVjF4yBAzngBf2OAvOVNc/fy7Z70mHCLjxOX96RIePnPWQWIABEgAkSACJAzlvttk7+Mddztcdv9U0bb3j3LfsthfV9yMA8BIkAEiAARIJ1ve+Wkh7tPunjaG+qp3ww8uW8nHazhxm1LfOStg66HXxfbxQGyMjGyD4h9QHY/ftXPB/uAnP/mLUDOf1Ptft9PnsALkLO3vRu2dc/FFwXInw/S3/975XJPr3P2iFUZR54a+buI5VtZF5X/Vr2ud19/1OMpQObeFE6OHwGyf6Jz8+SsQ6C/bdJmIlq3zXgevjBAVidJN3xCX718p764uJ93r8e3B0iHNzsBctfk76THQ4AIjx3bzknPxZc+rnvemFcud8o+CtXLd/oLivspQKDLhOHmSRq2Petu73PRYylABIiJvQABExnPIWx7IEAEiABxPwUI1D23wbYHFwXIyiTJPiD2AXE/BQhkPW/AtgcXBkjEEY8qjnT19N8y7lvUUbBO/iakal3PvhHsPuKYAAEABMhhE6NTzoXx5pPR3X5OEucBAQBoeCb0iolt17OBdzqD+psfo07LIEAAAAEiQASIABEgAAACRIAIEAEiQACA1wZIt4mRfUDOi5Cu67p6GQQIACBAJiZHf//vT3838m+z9+XUf5tdvqp4WL1cxpHDZq9z9vFyFCwAgI0B8mSSdPv5Ll6yEZV9a1P9DVL37dNzBAAQIF8mSG864/fb4iN7v5XqfWhO2D49PwAAASJAWkZB9Y7XAkSAAAAIEMERGiQCRIAAALQLkG+TJPuA9AiPTvtyZGxLt26fniMAgAD5YXL09//+9Hff/i3jKFEVR+E6ITpWv0VY3Sayj1g1sgyzR92KWk8CBAAQIA0mRm/8hHt3fJx+nozK+7kjFAQIACBA/v33mB2eT/yNf3QoVIXIKY/DaWerFyAAgAARIOXxccLtCRABAgAgQBoHSFV0VN2+ABEgAAD2Afm35z4gO8Mj877YB8Q+IACAACk7ClbUdcwemSniKFgVk7xO8ZEZIaPrOmNberq9ZByJLeoxFSAAgABJnPjunJRVTPQ6xseO+3b7Ebgyf5oIACBAgie8WZervs7M29i1M3m39fDkOqv3P8k+uhgAgAARICXXX3mej8qdqgWIAAEABIgAaTTh3nXCwax1IkAECAAgQNpEyM5JWcdvP3ae9fy0Q8vaBwQA4OUBknHkqcyjDEVe5+okPis8Op7f4sn2Er3Njfzb7m1JgFz94vw4jldeK55cduW+VX7QEfU3UdcZ9Zqbvc6zlzHj8Y16jmQ873Y9r1fuw+j96XrAHATI9KTpLUe+yo6P3ddTtb2cOqG0bhAgAkSACBABggBp8Mb69h3PV184opd15X7tDrXTJpMCBAEiQASIABEgCBAB0ipAKs7DMXsbAkSAIEAEiAARIAIEBMhhR77qMhHv9i2IABEgAkSACBABIkAECAIkddL01iNfdZqEd/8W5IYJpXWDABEgAkSACBAEyKY32NF/e3qdXY9clP0CvStCVqLlyb89fRwqjmY1u407ChYCRIAIEAEiQPAe1/w8IJ0mZZk7fEf+feQLROZ9zTgvR/X5PGa3F+cBQYAIEAEiQAQIAuRlb9w7r7Pi249dh9h9+rcZZyavPqP57HpzJnQEiAARIAJEgOA9ToAcFSBR8ZGxc7kAESAIEAEiQASIAAEB0ihAsn9+lXHG84y/FyACBAEiQASIABEgCJCr37y7XOfuF+ZdbxCj69M+IALEi7MAESACRIAIEARI2hts5L99+rvIyd7sdWb+/Orpi0H2tyAzf/vT/8/YPqK2idltMPO+ePEXIAJEgAgQASJAECAJnw6ffo6Q7In87m81ZsJp10T6lvPNePEXIAJEgAgQASJAECALT8Dbz5LeadLf/b50O9Fit21JgAgQASJABIgAESAIEAEiQASIAEGACBABIkAECAgQASJABAgCRIAIEAEiQAQIrw2Qb5Mm+4C8L0B2TKTtA4IAESACRIAIEAHC5QEycsSjiKNgzV5u9shFHY4mddpO6KvrM3MbfHq5jPsiQBAgAkSACBABggBJ/IS308ab/c2Mw/Du2yY6nT/EeUAQIAJEgAgQAYIA2fRG2mkDrtg3xYkI92wTnc6g7kzoCBABIkAEiADBe5wAaRkgUX+/OhHI+HsBIkAQIAJEgAgQAYIAESBFk9RdR5TqdOQuASJAECACRIAIEAGCACl9M+06garYTyDzxXn0TS/zzX/0b7O3CfuAIEAEiAARIAJEgPCCAJk9ylDE7c3exux1PD2qUfZPoCJfIKp+rjW6vVRvgxlHaXMULASIABEgAkSAIECS30Cr37BPuS+ZLzTZE6DM2Om6DXY7T40XfwEiQASIABEgAgQB8m+vM1t3PMt21iF2dz5+O7+p6XCdu86u7sVfgAgQASJABIgAQYAIkPAAqVyOip3VBYgAQYAIEAEiQAQIAkSAFC975CF2s+975rcfAkSAIEAEiAARIAIEAZLyBlr9ht31vqxM2iPe0DOuN+K+2AfEPiAIEAEiQASIAEGAhL2J/v2/M67/221E3JeZJ/lvl+t6iN3oT/Cjji61cxscOVpXxbYlQASIABEgAkSACBAESLNPok+5XHY8rI6o+DjlW6nVx6/DRBUBIkAEiAARIAKE1wdIp9/3d7xc9gtJRnjMxMcJ++WMXK7Li68XfwEiQASIABEgAgQBIkDKAiQjQqIeAwEiQBAgAkSACBABAgKk6eUi1lFVeESElQARIAgQASJABIgAQYAcN0m6YR+QrCdzRnTM3E/7gAgQBIgAESACRIBAaYBUHwUr477MHvFo5nIV5/qInhB9Wo5vy159BKnZ2xtZBgGCABEgAkSACBDYFCCdNsrqT79XPjXvGCEz8RG9Xiof25Mm/F78BYgAESACRIAIEARIsw2z+vf/EfsNdIqQlfiIXi8Vj+0p27UAESACRIAIEAEiQBAgAiR0op111vOI8Bi9zwJEgCBABIgAESACBARI8wAZefOqio6VF0UBIkAQIAJEgAgQAQL2AVm4XNW+DpmH0824fvuACBAEiAARIAJEgMCWAPlzA91xHRlHPBq5b7NHgooKhV0nKFxZ9uyjYI3cz4zLCRAEiAARIAJEgOA9rug8INWTq06fbu/8NmRHfGQt+03bmQBBgAgQASJABAgC5NA34A6Xm73O1dvrGh0Vy37ydiZAECACRIAIEAGC9zgBcmSAZIVIxeMnQAQIAkSACBABIkAQIALk0ABZDZIdj58AESAIEAEiQASIAEGAHPEm3PVys9d5+xPXPiACBAEiQASIABEg0CZAso9KtfuoRtFHwYpanxWXe7p8UUccy9jOMraXzOXz4i9ABIgAESACRIAgQBImTZ3OI1F9P6vXZ6fH4ZTHdsc24cVfgAgQASJABIgAQYAEv6E8udyb9/votK/Mmx/bXduEF38BIkAEiAARIAIEASJABIgAESAIEAEiQASIAAEBIkAEiABBgAgQASJABIgA4XUBsjJpsg9I3frs9DjYB0SACBABIkAEiAARIAiQ6SfeT/+/+khJUfdl5DZ++7eM29t9ueyjRM1uL7PremQbjFg+AYIAESACRIAIEARI4qfGnb4tOOW+ZExIu32yX/n4nXyuFi/+AkSACBABIkAECAJk4InSaX+JU+5L5Jt15uVOefxOOWO7ABEgAkSACBABIkAQIAJEgAgQAYIAESACRIAIEO9BCBABIkAECAJEgAgQASJABAivDZBvkyT7gNgHZPfjZx8QBIgAESACRIAIEC4JkOwjF3W+L9VHwXq6DFGXO/HIYSOP7dN/W3lcIh9rL/4CRIAIEAEiQAQIrw+Q28/dMHu56ifr7d9sVN+e84AgQASIABEgAkSA0DBAbj979ezlqp+wt+/bUX17zoSOABEgAkSACBABggARIAJEgHjxFyACRIAIEAEiQBAgAkSACBABggARIAJEgAgQKAqQlYmRfUD6Pw72AbEPCAJEgAgQASJAvAfRLkD+3ECrLvf0OmePzDR7hKWK5ctehtmjg2UcBStqe8w+ClbFNuHFX4AIEAEiQASIAEGAHPbGHnG5U56Abz6HRqdvPDpszwgQASJABIgAESAIkCZv6qOXO+VJ+OaziHfa50OAIEAEiAARIAIE73ECRIAIEAGCABEgAkSACBABggARIAJEgAgQASJABIgAESACBAFy2Rt7xOXsA2IfkF1h4MVfgAgQASJABIgAQYAUvTGv/FvG0bmijnhUNWEdXS+7j4LVddlHtjNHwUKACBABIkAECN7jDguQUz6Jv+F+Vk96bz8/ivOAIEAEiAARIAIEDguQU/ZFuOF+Zlyu+jqrl33X4+7FX4AIEAEiQASIAEGAmNgLEAEiQBAgAkSACBABAgJEgAgQAYIAESACRIAIEAGCAFl48kb8m/tpHxD7gCBABIgAESACRIDwsgCpPgJR5tGIVu/L6Kfxket+9ohVUUf8yl722dub3c5W1oUA8eIsQASIABEgAgQBsuGT6E6fUp9yexmPww3LfsO3NgJEgAgQASJABIgAQYAkvsl2+p3+KbeXOdk5edlv2G9FgAgQASJABIgAESAIEAEiQASIAEGACBABIkAEiPcgBIgAESACBAEiQASIABEgAgQBMjGBsg9I3aTTPiBnTv69+AsQASJABIgAESAIkIU32yf/lnEUrIijGs0uw+6jYM1eZ6d1nbHOZq8z6ghdAsSLswARIAJEgAgQBEjDN+Wu/5axDB6HunV20rczXvwFiAARIAJEgAgQBMiGN+RO/5axDB6HunV22v4pXvwFiAARIAJEgAgQBIiJrwARIAIEASJABIgAESAgQASIx0GAIEAEiAARIAJEgCBAFp68Xf8tYxk8DvYBESACRIAIEAEiQAQIAmTiiZd5PdVHWIo4UtLIv+18HKLWS/XRs3avp4j1KUAEiAARIAJEgAgQBEjzSdIp54q4/ZP9U9bL7snlSc8tBIgAESACRIAIENoHSPVGesrZsm/ft+GU9dJxYilAECACRIAIEAGCABEgAkSACBAEiAARIAJEgAgQBIiJtvUiQLz4CxABIkAEiAARIAiQzZMk+zpYLydNLk96biFABIgAESACRIDQOkAyjlw0e4Sl2fuZvewRR9laWX+zR23Kvi9RRwfLWL8ZRwATIAgQASJABIgAQYAkTow6nUOj06f7uyc5Xe/LKdvLjsfdi78AESACRIAIEAGCAPn3nLOId9q/oeMEp8N9OWV72fW4e/EXIAJEgAgQASJAECACRIAIEAGCABEgAkSACBAQIAJEgAgQBIgAESACRIAIEK4MkG8TI/uA2Afkxu3FPiAIEAEiQASIAIGNAfLnBjr6b5/+rvo6Z64/637O3peny777iFERR5Davb1kHMlLgAgQASJABIgAESAIkE1vwjuvs9O5TE759P6U66zeXpwHBAEiQASIABEgCJBD34CrrrPT2dxP2X/hlOus3l6cCR0BIkAEiAARIHiPEyACRIAIEAAAASJABIgAESAAgADZGCE7r9M+IPYBsQ8IAMDBARJxxKOo68y4LxHrJeooShHXWX3UrYjHOetx2LnsAgQAECDBk6ZOn+6/+VuP6uVr+MQ44oSBAgQAECCDE6au+ze8eb+P6uXrHB879yURIACAABEgAkSACBAAAAEiQASIABEgAIAAmZg02QfEPiDdIqTzsgsQAECADE6cRv8t4zorjqp04nqpXr6OEXLKdQoQAECAAGWhJEAAAAECCBAAAAECAgQAQIAAAgQAECCAAAEAECCAAAEABAhQ+yT+6X8DALQOEMMwDMMwDMMwjKJhJRiGYRiGYRiGIUAMwzAMwzAMwxAghmEYhmEYhmEYAsQwDMMwDMMwDAFiGIZhGIZhGIYhQAzDMAzDMAzDECCGYRiGYRiGYQgQwzAMwzAMwzAMAWIYhmEYhmEYhgAxDMMwDMMwDMMQIIZhGIZhGIZhCBDDMAzDMAzDMASIYRiGYRiGYRiGADEMwzAMwzAM48wAAQCA3UzMBQgAAAgQQ4AAACBADAECAAACxBAgAAAIEEOAAACAABEgAAAgQAwBAgCAADEECAAACBBDgAAAIEAMAQIAAAJEgFD+BLrhyf/2xxEAECBGYoDM3kj09UZvoB2eNLc82Xfexs71UbneOzyfsoM2+oUOAAFiCJBWARIxUTlxglS5/ro9Xh0eRwFSFyBiBECAGAKkbYBET7Ce/m3HJ/jM5Xa8oGSvj8jLCJD9ASJGAASIcfA+IFlv7LPXFTmRXp1Ejl6m05M7a7lnt5vsiWTWZP/k51Plz84y140IARAgxksCJHrjy7w/K5OTyEl4hyf2zY9V9jac+U3BjudT5b4vFetHhAAIEEOAtJqg7AiQvy9/Ynx0m0zu3H4j1q0A2Rd5IgRAgBgCpP2kNnoydGJ8dHusovZn2rWOTwyQiPCrfr0RIQACxBAgR05qTwqQzAnXrsnk7oDpEMFdAmT1W4gdrzciBECAGALkuEmtABEg0dfdOUAi15sAAUCACBABcnGAVJ+AsXuA7Dwh5c0BsvLY7Xq9ESEAAsQQIFcESLfJy2lnX785QLIPapAdIFERIkAAECACRIAEB0inCYwAESDRkSBAABAghgDZNKntHiEn3rfT42PlPt4QIDOPw87XGxECIEAMAXJVgOyeyAiQfgGSfWjnigBZXZcCBAABIkAESNKkdveE5sYA6Xr0q7cFyMr6FCAACBABIkCKAqR6UnNTgHQ9+aAAWV9OAQKAABEgrwuQrDNY757cnLp/Stb6EyDxO4oLEAAEiCFAgnb8nVmObhGSdeK4iOWrjg8BknekqtFlFSAACBAB8soA2THJrZ7o3BogGYEoQHIC5MkO7AIEAAEiQK4LkB1BIEBiDjkbue4ESN65OgQIAALEECBNvonoetu7r9dO6HcFyMgyCxAABIgAeX2AdHhCnRQgEY+9ABEgAgQAASJArg2QU55UJ0VIdYCcFCHZP0vrGiBPl73L81t8AAgQQ4BcHyA7Jj03BUjm7QqQ/AD5n+sRIAAIEAEiQIru345QEiACpDJAniy/AAFAgAiQ9pNbAdIvQroFSJcIWbnuTs+nynPi7HjeiA8AAWIIkNRPXgVI3VnFKy4vQOpjXoAAIECMFgGSMZkWIPVP6LcFSNVkv+PPjKq28ZE3kep1Iz4ABIhxaYBETQzeEiC7n9Q3BMju7aVbGEU+PplvJKeuXwAEiLEpQJ5sLFWT8hMCZPfkp8vk/bbJaNV1VT2fbguQDucCAkCAGIUB8vSEZKcGSOVPhyqe3G8KiNV1Ubl/TeXzqepkfzvPHSM+AASIcXCAZGwwmbdXsazdP3mdWV8r6zbjsaq8rupP1Ls/nyKXZ+cbEAACxDg4QCI3muzbOmk5T3uiVwbI6HWf9rh2fj6dHiAACBDjkgCpmGx2CJAOt9/pCd9hm6i+rk6HUd7xfIpcjqo3IAAEiHFxgDzZoN7y5LnpRcCLYZ/14bEBQIAYAgQAAASIIUAAABAghgABAAABIkAAAECAGAIEAAABYggQAAAQIIYAAQBAgBgCBAAABIgAAQAAAWIIEAAABIghQAAAQIAYAgQAAAFiCBAAABAghgABAECAGAIEAAABYggQAAAQIIYAAQBAgBgCBAAABIghQGj6ojPy362v/3siY91aZkCAGNcFyOoNjV5PxMZb8ST49ndPru/pdYyup5V1m/0YdQ+Qp8u48lzYtX2vXL5iO/h2X0fW6+hjkLFMK+us6nk3er2z63X1eZK5PjJeL3c8xiISAWJcGyAjL3grl1mdJGW8gD8NjIjrj/qkcuTvZ5c9K0Cq3kxnJxDRf1dxPSsTzspPzZ+85syE4o74iLov0es7Y1vIeJ5kro+V1/OM94KVZfItFgLEuDZAov4tepIdMUme+WZh9E024m8yJsmry541Eap6I515LKPXbWV8zARs5TYx+ml41DJUbNOrk/Wd29NKOK0GW9b6WNnGsi9b9d4HAsQQIAEvwllvrqtvlE/fiGd+zpAdPRWTtdn1lfkC2GVyHvkNSsbj3TlAusXHrtfGiMuvLNPKhxBZ62PX45SxTAIEAWIIkM0vwKuXzf6kLvJTxh0TsKoA2flG2ilAMn6+1TlARu7n05/JVG7L3QMkc7L+6bVRgNR8KCRAECDGa46ClfVtw+rEuPpT/aiv1EeDYTUwdr9h7f75Vfb20O2gCtnRUH0fI37OtGvbyY6BLgGSvf2cFCDZ8S9AECCGANkQIFVvmNFvVBEBUvVb450vLB0nkZnxEb19Zq23rOXssE9W1wDpOFnfGSBZz9OVHeCzA2T1+kGAGAJkcQK38kKcOVGs2tHzlAB5OnmYPYTlbQHy5FDNldtm1ja0Gu8zP/uJWrfVARJ137IOUVz9k87M5+rK+1TEB1JZP28GAWIIkKDJ284dOlcmQdEBknV4zOwXmpsCJOPbj9XDkEZPVqoOBzt7mN2Z8+RELWNGgEQH7cwydwqQrO1v5VDcGd/YChAEiCFAEt9Aoj51nL1v2b9frzzU5YkBsvsT1BMCJOJvMp+7Wcu4ckK3qnWbHSCR6zt7n5qsAMvc/k4MkO7faoMAMY77BiTy24+n19fhTSwiQH77t8gThGVMPp9Olne8AO6Mj6hPnStO+pn1c6WsE25GrdvqCXjVz8NWviGpPPhHVpSt/Czt6bdMq/et8gMZECDGq36CFb2xVgVIZlDNTCgz1vWOF5XuAbLzp2AjZ7CPXr+ZP1XqMPnvFCCr21nm9lSx7NXrI2P/kJV1tOvACiBAjNfthF4xiak6ClbmkU9m37R2/typ8++XbwmQqglL9M8wZ0PpDQFScbmK7X/3YaBX9r+puOyn1/LOH96AADFeEyARb4wVR/LJOBHhk7/J+JlLZoB0egHcGR9RAVJx33ee0E+A9AqQHdtL9f3dddmdz0UQIMarzgMSESDRk/Df/j7qt/aRARIdX1nnpDg5QDI+na8MkIjHVYCsfUhweoDs/PbjtADJ3j4ECALEECDJb9CRk8SK8ypETVRndxyPmDxkBkinN9wuAbIS6xHLkL1sOwMk8oOQqNe3ivWdscxZ8dEh7gUICBBDgKS+SY6+2UWetXlXeO2aaEYFyO7zgFTE28phmVdvp/IgDh0DJGuiH3127YrtacffZ6+P7HPo7DhBrABBgBivC5CMN6FvL9JZbzYz35ZETPSzfjIV9Y1J9r4yqy9UlRPfjPNORE6CKg6NnT3ZqTypXdW2vvIaUHXAjN2v+5XrY+X1LuOcKVWHWgYBYhwbICuHvI26zOz+EFHnvpi5X7NHMFldjojHNurFJWLCnDEJGXk8q49I8/T6Mp4fu46wU3WemuztPfvkdpnbU1R8rDxXs9dH5YkII7YPAYIAMXwDApsDhPvfiOg5Oai4HJ4zCBBDgADgAwQAAWIIEAAABIghQAAAQIAYAgQAAAFiCBAAAASIIUAAAECAGAIEAAABYggQAAAQIIYAAQBAgBgCBAAAAWJyLkAAAECAGAIEAAABYggQAAAQIIYAAQBAgBgCBAAABIgAAQAAAWIIEAAABIghQAAAQIAYAgQAAAFiCBAAABAgAgQAAASIIUAAABAghgABAAABYggQAAAEiNE4QAzDMAzDMAzDMASIYRiGYRiGYRgCxDAMwzAMwzAMQ4AYhmEYhmEYhiFADMMwDMMwDMMw/jdAAAAAio50JkAAAAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAECAAAAACBAAAECAAAAACBAAAECAAAAAAoRXbAT/MX77myfXs3IfRu/nymVXrn/0PjxZ3qjlyngcV5Yx+7JRj2nGfcjYxla2n8rHonKbjFqf2a+B3V43Ml6rK99rop7jGa+Hu173std/9uv86H1buQ8IEASIABEgAkSACBABIkAEiAARIAgQASJABIgAESACRIAIEAGCAEGACBABIkAEiAARIAJEgAgQAYIAESACRIAIEAEiQASIABEgCBAEiAARIAJEgAgQASJABAgCBAEiQASIABEgAkSACBABIkAECAJEgAgQASJABIgAESACRIAgQBAgAkSACBABIkAEiAARIAJEgCBABIgAESACRIAIEAEiQAQIAgQBIkAEiAARIAJEgAgQAYIAQYAIEAEiQASIABEgAkSACBABggARIAJEgAgQASJABIgAESAIEASIABEgAkSACBABIkAEiAARIAgQASJABIgAESACRIAIEAGCAEGACBABIkAEiAARIAJEgCBAECACRIAIEAEiQASIABEgAsTcU4DYCASIABEgAkSACBABIkAECAIEASJABIgAESACRIAIEAEiQAQIAkSACBABIkAEiAARIAJEgCBAECACRIAIEAEiQASIABEgCBAEiAARIAJEgAgQASJABIgAQYAgQASIABEgAkSACBABIkAQIAgQASJABIgAESACRIAIEAEiQBAgAkSACBABIkAEiAARIAIEAYIAESACRIAIEAEiQASIABEgAgQBIkAEiAARIAJEgAgQASJAECAIEAEiQASIABEgAkSACBAECAJEgAgQASJABIgAESACRIAIEASIABEgAkSACBABIkAEiABBgCBABIgAESACRIAIEAEiQASIAEGACBABIkAEiAARIAJEgAgQBAgCRIAIEAEiQASIABEgAgQBggARIAJEgAgQASJABIgAESACBAEiQASIABEgAkSACBABIkAQIAgQASJABIgAESACRIAIEAEiQBAgAkSACBABIkAEiAARIAIEAYIAESACRIAIEAEiQASIAEGAIEAEiAARIAJEgAgQASJABIgAQYAIEAEiQASIABEgAkSACBAECAJEgAgQASJABIgAESACRIAIEASIABEgAkSACBABIkAEiABBgCBABIgAESACRIAIEAEiQBAgCBABIkAEiAARIAJEgAgQAWLu6YGyEQgQASJABIgAESACRIAIEAQIAkSACBABIkAEiAARIAJEgAgQBIgAESACRIAIEAEiQASIAEGAIEAEiAARIAJEgAgQASJAECAIEAEiQGLfmKJGxwCJmCQLEAEiQASIAEGAIEAEiAARIOEB8uftVU4wKh+fXQGyazs85b5FvdfsWp7M18PO21rE66MAQYAgQATINQHS6Y1WgAgQAbI2ORUgAkSAIEAQIAKkdYCcMgnM3oajJlQCRIB0CxEBIkAECAIEASJAWgTIaZPAjgHy7ZsTASJAOk9WBYgAESACBAEiQARIWYCcOAkUIAJk17cIp09a3xQgt0SIAEGAIEAEyFUBEvmGGHX9JwdI1k7E3SaZpwZI1YRtdRvInLjueoyinrMZl41+3YvefgQIAgQBIkCuCZCqiYQAESCd13/HAIlcXxHvNTcGyOprkwARIAIEASJABEjhRFSACBAB0mcbqDhPjQARIAJEgCBABIgAESACRIC8LECibluACBABggBBgAgQASJABIgAKYsQASJABAgCBAEiQASIABEgAkSACBABggBBgAgQASJA7g6QlXUhQPYFSMXzWoAIEAEiQBAgAkSACBABIkAEiAARIAIEAYIAESACRIAIEAEiQASIABEgAgQBIkAEiAARIMtHRRIgAkSACBABIkAQIAJEgAgQAfLaAMma/AsQASJABIgAQYAIEAEiQASIABEgAuRVAVL5+iBAECAIEAEiQBoHSOQ67BogqxEiQASIABEgAkSAIEAEiAA5LkAyJt4C5J4AyZz8CxABIkAEiABBgAgQASJABIgAESACRIAIEAQIAkSACJB3BUj0OrwtQEYmpwJEgAgQAYIAQYAIEAEiQATIEQGSPfkXIAKkKkC6vD4IEAQIAkSACJCGAZKxDgWIABEgAkSAIEAQIAJEgAgQAfJv/qGJBYgAESACBAGCABEgAiR8MiFA5n7WJUAEiAC5P0A6fUAhQBAgCBABcmSARLwxCRABIkAEiAARIAgQBIgAESAh+wIIEAEiQASIABEgCBAEiAARIKVBIkDGY02ACBABIkAECAIEASJArgyQ7EmnABEgAkSACBABggBBgAgQAVIWICuHaRUgewMka5kFyP4AmX2vESBrAdLtKHkCBAGCABEg2wKkKkI6BUj1pHdl2QWIABEgvQJk9wE9BAgCBAEiQK4IkC5vwgKkV4BEL7cAiQuQyAMgCBABIkDMPQWIjUCACJAtAdLhjbgiQHZMegWIAIkMkKplFyACRIAIEASIABEgZW9MAkSACJCeAVK57AJEgAgQAYIAESACpPSNSYAIkOhlFyBrz+XI7UuACBABggBBgAiQdgGy4805O0B2TXoFiACJeh2I2rYESMy2mPm+KUAQIAgQASJANkaIABEgNwXIbz9xy17nAsR5QAQIAgQBIkAEiAA5IkAyJp9vD5Dd25MAESACBAGCABEgAmTx9+rf1s/pE0YBIkCqtyUBIkAEiABBgAgQAdLqjSl7EiZA7g2Q6nh5S4Bkv9cIEAEiQAQIAkSACJDtAZI5GRMgvQIkYh0KkB7xIUD2Bci32xMgCBAEiAARIIOXzZpUrU5musdH5O/2Bci9AZL1Oi9ABIgAMfcUIDYCASJAjg2Q6ImZALkvQHbsP3LbmdAFiAARIAgQBIgAESCNA6Ryp+mI+9A5QCIm3gJEgAgQAYIAQYAIEAHSNkC+TXwEiAARIALktACJfK8XIAgQBIgAESAJkxYBck+A7DqErwARIAJEgCBAECACRIAIkKTnjgARIAJEgAgQBAgCRIC0DZDsT92jJmsCRIAIEAGSFQgCRICYewoQG4EAESACRIAIEAEiQASIAEGAIEAEyM0BkjXpjZiwvTFARg+DK0AEiAC5O0BGzwsjQBAgCBABIkAEiAARIAJEgAgQBAgCRIAIkLcGSFZQRE/UBYgAESACRIAgQBAgAkSAFAZI1mMhQASIABEgAkSAIEAQIAJEgAgQASJABIgAESAIEASIABEgZwXI6nV2CpDu26QAESACRIAIEAGCABEgAqRlgFROFAVI7TYpQASIABEgAkSAIEAEiABJe+M+PUCyg0KACBABIkAEiAARIAgQASJANgdI5b4Pu4MiI0Ay94WJ2iYFiAARILEBUrGPmABBgCBABMiVAVJ9/gsBIkAEiAARIAIEAYIAESACpEWARD5e1QGSeUJGASJABIgAESDmngLERiBABMi2AKlaD523GQGSf5SzrG3ylAD5baIpQASIAEGAIEAEyGsDJPLNLztAon/O1DVAor41eroubw2QlZ+0ZWzvpwRI5ra6M0Aynm+r67/qPEECBAGCABEgxwXIrslJ53NPnLa+d03kuq+jDtvfrQFySpCfuJwCBAGCABEgAkSACJBD19GbA+RNE3MBIkAQIAgQASJABIgAMYEVIAJEgCBAECACRIAIEAEiQASIABEgAkSAIEAEiAA5KECythkBclaAnDIpFCACRIAIEAGCABEgAiQpQLLfqCu3mextOGObzHocd2+TGQGS8RrY7XUj47W68r2m8ihY2e+J2Y9jh9cHAYIAQYAIkBYBkvlGLEAEiAARIAJEgCBAECACRIAIEAEiQASIABEgAsTc0wNlIxAgAkSACBABIkAEiAARIAgQBIgAESACRIAIEAEiQASIABEgCBABIkAEiAARIAJEgAgQAYIAQYAIEAEiQASIABEgAkSAIEAQIAJEgAgQASJABIgAESACBAGCABEgAkSACBABIkAEiABBgCBABIgAESACRIAIEAEiQASIAEGACBABIkAEiAARIAJEgAgQBAgCRIAIEAEiQASIABEgAkSACBAEiAARIAJEgAgQASJABIgAQYAgQASIABEgAkSACBABIkAQIAgQASJABIgAESACRIAIEAEiQBAgAkSACBABIkAEiAARIAIEAYIAESACRIAIEAEiQASIABEgAgQBIkAEiAARIAJEgAgQASJAECAIEAEiQASIABEgAkSACBAECAJEgAgQASJABIgAESACRIAIEASIABEgAkSACBABIkAEiABBgCBABIgAESACRIAIEAEiQASIAEGACBABIkAEiAARIAJEgAgQBAgCRIAIEAEiQASIABEgAgQBggARIAJEgAgQASJABIgAESACBAEiQASIABEgAkSACBABIkAQIAgQASJABIgAESACRIAIEAEiQBAgAkSACBABIkAEiAARIAIEAYIAESACRIAIEAEiQASIAEGAIEAEiAARIAJEgAgQASJABIgAQYAIEAEiQASIABEgAkSACBAECAJEgAgQASJABIgAESACRIAIEASIABEgAkSACBABIkAEiABBgCBABIgAESACRIAIEAEiQBAgCBABIkAEiAARIAJEgAgQAWLuKUAQIAJEgAgQASJABIgAESAIEASIABEgAkSACBABIkAEiAARIAgQASJABIgAESACRIAIEAGCAEGACBABIkAEiAARIAJEgJjXChAEiAARIAJEgAgQASJABIgAQYAgQASIABEgAkSACBABIkAQIAgQASJABIgAESACRIAIEAEiQBAgAkSACBABIkAEiAARIAIEAYIAESACRIAIEAEiQASIABEgAgQBIkAEiAARIAJEgAgQASJAECAIEAEiQASIABEgAkSACBAECAJEgAgQASJABIgAESACRIAIEASIABEgAkSACBABIkAEiABBgCBABIgAESACRIAIEAEiQASIAEGACBABIkAEiAARIAJEgAgQBAgCRIAIEAEiQASIABEgAgQBggARIAJEgAgQASJABIgAESACBAEiQASIABEgAkSACBABIkAQIAgQASJABIgAESACRIAIEAEiQBAgAkSACBABIkAEiAARIAIEAYIAESACRIAIEAEiQASIAEGAIEAEiAARIAJEgAgQASJABIgAQYAIEAEiQASIABEgAkSACBAECAJEgAgQASJABIgAESACRIAIEASIABEgAkSACBABIkAEiABBgCBABIgAESACRIAIEAEiQBAgCBABIkAEiAARIAJEgAgQAWLuKUBsBAJEgAgQASJABIgAESACBAGCABEgAkSACBABIkAEiAARIAIEASJABIgAESACRIAIEAEiQBAgCBABIkAEiAARIAJEgAgQBAgCRIAIEAEiQASIABEgAkSAIEAQIAJEgAgQASJABIgAESAIEASIABEgAkSACBABIkAEiAARIAgQASJABIgAESACRIAIEAGCAEGACBABIkAEiAARIAJEgAgQAYIAESACRIAIEAEiQASIABEgCBAEiAARIAJEgAgQASJABAgCBAEiQASIABEgAkSACBABIkAECAJEgAgQASJABIgAESACRIAgQBAgAkSACBABIkAEiAARIAJEgCBABIgAESACRIAIEAEiQAQIAgQBIkAEiAARIAJEgAgQAYIAQYAIEAEiQASIABEgAkSACBABggARIAJEgAgQASJABIgAESAIEASIABEgAkSACBABIkAEiAARIAgQASJABIgAESACRIAIEAGCAEGACBABIkAEiAARIAJEgCBAECACRIAIEAEiQASIABEgAkSAIEAEiAARIAJEgAgQASJABAgCBAEiQASIABEgAkSACBABIkAECAJEgAgQASJABIgAESACRIAgQBAgAkSACBABIkAEiAARIAgQAABAgAgQAABAgAAAAAIEAABAgAAAAAIEAABAgAAAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAwDsCxDAMwzAMwzAMo2hYCYZhGIZhGIZhCBDDMAzDMAzDMASIYRiGYRiGYRiGADEMwzAMwzAMQ4AYhmEYhmEYhmEIEMMwDMMwDMMwBIhhGIZhGIZhGALEMAzDMAzDMAxDgBiGYRiGYRiGIUAMwzAMwzAMwzAEiGEYhmEYhmEYAsQwDMMwDMMwDAFiGIZhGIZhGIYhQAzDMAzDMAzDODNAAAAAsgkQAABAgAAAAAIEAABAgAAAAAIEAABAgAAAAAIEAAAQIP9xgV/H08s8+bfR61q97azbe3K5rHX/7boyrhsAAMIC5KfJ67f/vnq51b9/cttPw2Lm3yMn8avL8effZ103AACUBMjsv3UJkKzAqAiQiJiKuG4AABAgD287675XBcjI/dkdUgAACBABUnDfs5f9lG9yAAAQIK8PkNH7GDEhrwyQzHgBAIDyADl5J/S//9uJAfLkiF5Z1w0AAOkBMnrI1pMCZPa/FT1gU/GxunO6+AAAYGuARP1btwD56b/PBEjGOTVmz9Xx5HadBwQAgOMCJGKyf0uArNzfimXPvJ8AACBAJs99sXrfOwRI1nUDAMB1AfL0SFWf/n31tk8NkOywAwAAAVIYICvrYzYosr7RECAAABwXIBk7oe8MkL8jpGqdRAVIZFgCAEB5gHw7BO+3w7j+FhNPrzfi73+77YjJedShiZ9ed1RMOPIVAAAtA4RzHlgAABAgVD2oVgQAAAIEAAAQINYEAAAgQAAAAAECAAAgQAAAAAECAAAgQAAAAAECAAAIEAAAAAECAAAIEAAAAAECAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAAACBAAAQIAAAAACBAAAQIAAAAACBAAAECAAAAACBAAAECAAAAACBAAAECAAAIAAAQAAECAAAIAAAQAAECAAAIAAAQAABIgAAQAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAAAAQIAACBAAAAAAQIAACBAAAAAAQIAAAgQAAAAAQIAAAgQAAAAAQIAAAgQAABAgAAAAAgQAABAgAAAAAgQAABAgAAAAAIEAABAgAAAAAIEAABAgAAAAAIEAAAQIAIEAAAQIAAAgAABAAAQIAAAgAABAAAQIAAAgAABAAAECAAAgAABAAAECAAAgAABAAAECAAAIEAAAAAECAAAIEAAAAAECAAAIEAAAAABAgAAIEAAAAABAgAAIEAAAAABAgAACBAAAAABAgAA/Fd7d6CiOBIEYPj9n3oODhaGZTXVVdWdTuf7IHA7p44xcaxfowoQAAAAAQIAAAgQAABAgLglAAAAAQIAAAgQAAAAAQIAAAgQAAAAAQIAAAgQAABAgAAAAAgQAABAgAAAAAgQAABAgHy9kj9CCdbdt9zvAIAtAuT3UPKvZcZw9PvfxLZLdNutul4z13Vkna7Oe3UZKwfyzLaqnOeO9d11vwUANgqQb0NJ94Bg0MgPc6ujYPXAWBmaI/9/lwG4ElcrbsuT9lsA4IEB0hkhho15w+odz+KvvtzsUL3TbffGANnptgcAHhIgXYOCYaN2m909VN4dIN9O88QA+fPfV6fNXMddAmSn/RYAOCxARo77jlyPkcuJHD4WuazRZ5tHn+XNDFtdg1zn4Uajt8+Ky62s12j4fLstR5/1H903o9s3s6/cvY4ALx7aLC9bBEgxQKLD4NUGGBkqPw1K1esUvT06fmd2u4y+AhC9rqcGSMf7EKLDePQ2HhnORy+vevoZ61jdPgACxCJAXhYgkWdTu4aizLA0+zpVo6s6mEcvNxsmAmT9oV+RT4LrCs+Odem8jwgQAAFiESCXd4Ds4NY1JHZFQ/czxVfh03XYSeWVlVnvfbjzPSCRKO3cj1cFyNW2m30/WBUgnYfOAQgQiwA5JEA+3Smyd5qRYaV6ObMuqzIkV3e+ziH2DQHSGRd3B8jKEF8RINF1B3hzgFhf6/36ALmKkK5DbO78hJ/ZATJrKBcg5wTI3z/rePVAgAAYTJ+wvm95DBAgjUO9ALkvQLJD69sCpOv3rg6Q6jrvGiCR/RZAgJwbEpnznRAtAkSAHBMgVzv0iQFy15vqBUjv34a3vgQP8PS/h9VDzEdOf9J7KQRIcdCtDH4z35i98jqtjJDqIDfj07Hu+hSszsMBdwuQzPplbw8BAmAwnRUfmfcPz/g9tvNLAqTrmPXOT3eadRz9t8vPXo+O4Xj24HpngHR8yeVTAqS6L3Z8EeGKSBcgAGcHSDQuqr/jia8cCZCfn1S1duxQmetSfekusjNkr2v19q3srF3POGc/YawaBZU/Lp3HoXbsJ5Ev3Kx8gWdk21av86x17AxHAAGyd3yMPq6d/NG2AgQAAINpMUBGQ+Xb5VRfRREgAgQAgBcGyNXpR+Kj8/rYzgIEAICf5x+ClTnPjE/SEiACBACAwwbTru/wqL6Xw5vQBQgAAC8NkI7v8uh4RcV2FiAAABw4mM6OkMz5bWcBAgDAwYNp1/d/zDiP7SxAAAA4cDCtfmdHx2I7CxAAAF42mAoPASJAAAAM8JbNFwECAIAAsQgQAQIAIEAsAkSAAAAgQCwCRIAAAAgQiwARIAAAhAJEUJ05qAsQAAAMpgvj49tpIuFiOwsQAAAMpkPx8S1ARi/HdhYgAAC8cDAdfeUiexpfRChAAAB4+WCaiYTqaQWIAAEA4GWDaeUVio7TnxBrAgQAAINpMT6y51/xe21nAQIAwIMG0673ZXSf96nhJkAAADCYLhj+Z5z/CUN95qOHI+u30/oLEAAAATI1QE64nF22c/RjiXfedwQIAIAAmXZ97w6Zb0O7ABEgAAAcEiDd6/uWeVWAAPD1j/fVz6oPAqPH9XZd5u/TVi7vyQNE5svPvp0/883PXUNK975ReTNwx/4WXb/qbb3p0CZABMjQ39sdb0cBApB4YPg0MH16sKgMcJ2X/SmWIqfNXF7Xuu8aIVfrFDlNZtvcuW9kPg61c3+Lrt/o7551vxUgAmR1gET/W4AAPOzBIRofI6Gyasgc+VnmGeuOZ7l33NZX2zAbKR0Db8fpuveNVftbNlAEiAARIAIE4JEPDicGyOyh+YkBEtmGkX9HX1F5U4BE97fR3ydABIgA+Xw4lgABePADxA4BMnLYzuiz9N0B8rTHm5HDyyKHn42evjNAOrbl6CFinfvb6O/qCJDsIXErB3LL+UsmQD6dToAAPDg+qsNTV4D860EmGyDZL7aqDHlPCpC/H7yrh1xFTt8VIJ2vVlTfVJ7Z37pCeHTfHLlvCRCLABEgANMH0sqbXDNBsupZ7pWD39MC5NuD/9X/uytAut7I3nW4XcdhYNmoqMTxjp+CZXlngJzyWCpAAAYe/K+eccoMQivjovIej8whNR2v/uwUIJHYzG7z0VdQIqGUDYrodqu+MbwSDSNh3x3aAsQiPgQIwLIH/k/DwNXPrwaI6NAxOihH1qUaIJXvy3jSNo8GSGU7Zj7mdmRfy2zL0Z/P3N9G9q2O7/kwI4EAAWDhgOTxgTv35R3ecwEIEABAWAMCBAAAQIAAAAACBAAAECAAAAACBAAAECAAAAACBAAAECAAAIAAESAAAIAAAQAABAgAAIAAAQAAnhEgFovFYrFYLBaLxTJ7+T9CdBgAALDsVRA3AQAAIEAAAAABAgAAIEAAAAABAgAAIEAAAAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAECAAAAACBAAAECAAAAACBAAAECAAAAAAgQAAECAAAAAAgQAAECAAAAAAgQAABAgAAAAAgQAABAgAAAAAgQAABAgAACAAAEAABAgAACAAAEAABAgAACAAAEAABAgAACAAAEAAAQIAACAAAEAAAQIAACAAAEAAAQIAAAgQAAAAAQIAAAgQAAAAAQIAAAgQAAAAAECAAAgQAAAAAECAAAgQAAAAAECAAAIEAAAAAECAAAIEAAAAAECAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAAACBAAAQIAAAAACBAAAQIAAAAACBAAAECAAAAACBAAAECAAAAACBAAAECAAAIAAAQAAECAAAIAAAQAAECAAAIAAAQAABAgAAIAAAQAABAgAAIAAAQAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAAAAeImAAAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAAAAQIAACBAAAAAAQIAACBAAAAAAQIAAAgQAAAAAQIAAAgQAAAAAQIAAAgQAABAgAAAAAgQAABAgAAAAAgQAABAgAAAAAIEAABAgAAAAAIEAABAgAAAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAQIAAAgAABAAAQIAAAgAABAAAQIAAAgAABAAAECAAAgAABAAAECAAAgAABAAAECAAAIEAAAAAECAAAIEAAAAAECAAAIEAAAAABAgAAIEAAAAABAgAAIEAAAAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAAABAgAACBAAAECAAAAACBAAAECAAAAACBAAAECAAAAAAgQAAECAAAAAAgQAAECAAAAAAgQAABAgAAAAAgQAABAgAAAAAgQAABAgAACAAAEAABAgAACAAAEAABAgAACAAAEAAAQIAACAAAEAAAQIAACAAAEAAAQIAAAgQNwEAACAAAEAAAQIAACAAAEAAAQIAACAAAEAAAQIAAAgQAAAAAQIAAAgQAAAAAQIAAAgQAAAAAECAAAgQAAAAAECAAAgQAAAAAECAAAIEAAAAAECAAAIEAAAAAECAAAIEAAAQIAAAAAIEAAAQIAAAAAIEAAAYEv/AasPkdDtNTO4AAAAAElFTkSuQmCC"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}}, "post": {"tags": ["vendorShippingLabels"], "description": "Creates shipping labels for a purchase order and returns the labels.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createShippingLabels", "parameters": [{"name": "purchaseOrderNumber", "in": "path", "description": "The purchase order number for which you want to return the shipping labels. It should be the same purchaseOrderNumber as received in the order.", "required": true, "type": "string", "pattern": "^[a-zA-Z0-9]+$"}, {"in": "body", "name": "body", "required": true, "description": "The request payload that contains parameters for creating shipping labels.", "schema": {"$ref": "#/definitions/CreateShippingLabelsRequest"}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/ShippingLabel"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "409": {"description": "The request conflicts with the current state of the resource (shipment).", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}}}, "/vendor/directFulfillment/shipping/2021-12-28/shipmentConfirmations": {"post": {"tags": ["vendorShipping"], "description": "Submits one or more shipment confirmations for vendor orders.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "submitShipmentConfirmations", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/SubmitShipmentConfirmationsRequest"}, "description": "Request body containing the shipment confirmations data."}], "responses": {"202": {"description": "Success.", "schema": {"$ref": "#/definitions/TransactionReference"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}}}, "/vendor/directFulfillment/shipping/2021-12-28/shipmentStatusUpdates": {"post": {"tags": ["vendorShipping"], "description": "This operation is only to be used by Vendor-Own-Carrier (VOC) vendors. Calling this API submits a shipment status update for the package that a vendor has shipped. It will provide the Amazon customer visibility on their order, when the package is outside of Amazon Network visibility.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "submitShipmentStatusUpdates", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/SubmitShipmentStatusUpdatesRequest"}, "description": "Request body that contains the shipment status update data."}], "responses": {"202": {"description": "Success.", "schema": {"$ref": "#/definitions/TransactionReference"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}}}, "/vendor/directFulfillment/shipping/2021-12-28/customerInvoices": {"get": {"tags": ["customerInvoices"], "description": "Returns a list of customer invoices created during a time frame that you specify. You define the time frame using the createdAfter and createdBefore parameters. You must use both of these parameters. The date range to search must be no more than 7 days.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getCustomerInvoices", "parameters": [{"name": "shipFromPartyId", "in": "query", "description": "The vendor warehouseId for order fulfillment. If not specified, the result will contain orders for all warehouses.", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "The limit to the number of records returned", "required": false, "type": "integer", "maximum": 100, "minimum": 1}, {"name": "createdAfter", "in": "query", "description": "Orders that became available after this date and time will be included in the result. Must be in ISO-8601 date/time format.", "required": true, "type": "string", "format": "date-time"}, {"name": "createdBefore", "in": "query", "description": "Orders that became available before this date and time will be included in the result. Must be in ISO-8601 date/time format.", "required": true, "type": "string", "format": "date-time"}, {"name": "sortOrder", "in": "query", "description": "Sort ASC or DESC by order creation date.", "required": false, "type": "string", "enum": ["ASC", "DESC"], "x-docgen-enum-table-extension": [{"value": "ASC", "description": "Sort in ascending order by order creation date."}, {"value": "DESC", "description": "Sort in descending order by order creation date."}]}, {"name": "nextToken", "in": "query", "description": "Used for pagination when there are more orders than the specified result size limit. The token value is returned in the previous API call.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CustomerInvoiceList"}, "examples": {"payload": {"pagination": {"nextToken": "MDAwMDAwMDAwMQ=="}, "customerInvoices": [{"purchaseOrderNumber": "PO98676856", "content": "base 64 content goes here"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "examples": {"application/json": {"errors": [{"code": "InvalidRequest", "message": "The request is invalid."}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}}}, "/vendor/directFulfillment/shipping/2021-12-28/customerInvoices/{purchaseOrderNumber}": {"get": {"tags": ["customerInvoices"], "description": "Returns a customer invoice based on the purchaseOrderNumber that you specify.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getCustomerInvoice", "parameters": [{"name": "purchaseOrderNumber", "in": "path", "description": "Purchase order number of the shipment for which to return the invoice.", "required": true, "type": "string", "pattern": "^[a-zA-Z0-9]+$"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CustomerInvoice"}, "examples": {"application/json": {"purchaseOrderNumber": "PO98676856", "content": "base 64 encoded string"}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}}}, "/vendor/directFulfillment/shipping/2021-12-28/packingSlips": {"get": {"tags": ["vendorShipping"], "description": "Returns a list of packing slips for the purchase orders that match the criteria specified. Date range to search must not be more than 7 days.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getPackingSlips", "parameters": [{"name": "shipFromPartyId", "in": "query", "description": "The vendor warehouseId for order fulfillment. If not specified the result will contain orders for all warehouses.", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "The limit to the number of records returned", "required": false, "type": "integer", "maximum": 100, "minimum": 1}, {"name": "createdAfter", "in": "query", "description": "Packing slips that became available after this date and time will be included in the result. Must be in ISO-8601 date/time format.", "required": true, "type": "string", "format": "date-time"}, {"name": "createdBefore", "in": "query", "description": "Packing slips that became available before this date and time will be included in the result. Must be in ISO-8601 date/time format.", "required": true, "type": "string", "format": "date-time"}, {"name": "sortOrder", "in": "query", "description": "Sort ASC or DESC by packing slip creation date.", "required": false, "type": "string", "default": "ASC", "enum": ["ASC", "DESC"], "x-docgen-enum-table-extension": [{"value": "ASC", "description": "Sort in ascending order by packing slip creation date."}, {"value": "DESC", "description": "Sort in descending order by packing slip creation date."}]}, {"name": "nextToken", "in": "query", "description": "Used for pagination when there are more packing slips than the specified result size limit. The token value is returned in the previous API call.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/PackingSlipList"}, "examples": {"application/json": {"pagination": {"nextToken": "NEBxNEBxNEBxNR=="}, "packingSlips": [{"purchaseOrderNumber": "UvgABdBjQ", "content": "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", "contentType": "application/pdf"}, {"purchaseOrderNumber": "VvgCDdBjR", "content": "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", "contentType": "application/pdf"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "examples": {"application/json": {"errors": [{"code": "InvalidRequest", "message": "The request is invalid."}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}}}, "/vendor/directFulfillment/shipping/2021-12-28/packingSlips/{purchaseOrderNumber}": {"get": {"tags": ["vendorShipping"], "description": "Returns a packing slip based on the purchaseOrderNumber that you specify.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getPackingSlip", "parameters": [{"name": "purchaseOrderNumber", "in": "path", "description": "The purchaseOrderNumber for the packing slip you want.", "required": true, "type": "string", "pattern": "^[a-zA-Z0-9]+$"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/PackingSlip"}, "examples": {"application/json": {"purchaseOrderNumber": "UvgABdBjQ", "content": "JVBERi0xLjQKJeLjz9MKNCAwIG9iaiA8PC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDMyMjQ+PnN0cmVhbQp4nOVcW4/dthF+31+hlwLJgxlyhlegKOD1ro32qUEW6IOTh6BOUhR2AqcB+vc7pEiJkmYlitrd3mwYx+LhZTjXb6g5/HyjBkl/X8UPF2D466ebzzdyUGOLlUrAoLTQ8Qs5/HRz+3Dz1VscFAwPP9ZjlRRgTQjBheHh0/D+i2+/+PP3P/0wfPnd8PCnuSNaIRVIqaTeDlGrzhqE9RiCV2rbefjlx/XkVu70X09unUBjpQTPdP72y7H3/cPN1zU/jMeKH6uvrPDTV5/p76t5IwoEKEMPedT4jRoMGJHWtQMQEUYqeqBOw6uKtXFU+vhU95fDx8WjAI/UJKf//W34y83PRN+7m/ffUfMH+sJYO/zzhp3qm0RX8ALBSYnj6uCFC/EPPUAQ3iX9+OqPn9Rw98tiI8p4oRyOO/zd8A7uhn/89v2vvyUOvUuao4QkuSs/zp2fnCOy5OCl0DorHwiPOkrDpY7jo7UucyLPF/fpaNnh1x+GHxMpLeNoVZOGojk9FoTqHYrCm06KtQgjt86vagX2EuyE6x3qyQA6hSOF6paOUsKYxCl9fiwK27+w7ueVMolZXTQ70U+yJyfWOzYI6Dch2a+SAMV2z2sWYLLdLisCLUz3WJOst4tkK0L/uuGKnwvC9TIaJcXAzqFqdJJdG0a8oJWoR/PvW9leYDUN7me1F91DQ/GVHRvWclTMrg0TosP+lXHkVo+71KbfXWqb3WVH9HYXrFiHkVldRBvJucuvMzb74ecPBc3XGJ7MSANKqWkE4eCv3oIeQgT6scvDh5vfE2bDPwwPfydsAWpu06mNoK/Vc6PJHUPd06ZGQyjTz40uN6ok3tzoc6NGOzeG3CirdV7nNrfpl9F7wagyYWxQYcSZc/OImF8IZDNsJwQRjIx7zly3lJGwbDeiZpHObWHL9TXH9agNuU2lNsquYH8ybqzLYlXAyGoj6b51R/ERavb7Y4vofbXfW6aNG/sm02cq+u7KPqqx98x+N0zmBhYG6GqBt7mtIk7JTJzXuwQrtd2YAmazF5iiMDPezIJUo4DIkR0o1RMrhjKZFjUrgbKZFqXOKwbTphzTDwp9sNtPeaYtMG2N63L92H1w9HG6wdHC6AG7D0avWF5x8715xPcSzFKAa9/7qjjfPY88dfs4gFv8X+aPs445D/1mQSQ1KtJf5WgXO4cwT7MsEwdoEu2jRusp+vpVGLidom8lhTeTpVQu5C43WlfZxT0nm5mlzVLA4NLJEDlork2uHs+yaTXVUkrVly3Sel6ypiPRIqvH5fk+BYAoruj0k9HWZ4xle5oSTn9aIIRR80kd0ySXT2e3vZxoKYz5uxZZPCNJfeb0NscOgMpyijnJCs3mKK0F4MbtUjjSTOyWdcB0JfCvfSyIUFltDsBQA9wcgCkoW7+mZ607Tgl7WneMXvxf5o/TWFcz+kGNLYrxNMuudQCNsNpFe1Ut2pARB4UpU8m4wBV7pCFFxLVrzsF1EwwVpQVGHYjoGcIbOtqHJ47syOJZg+2uv4w4EcmojS3+kkAM5yNjoG7h4L8hJJ3k8MsHUsZRLvme8s+1adxl5Ua9gRjUWDmmDO9RVB5V3eeOru7oy2hgsrzaeaqSNR2OLuv4ajTIyX3OHcuBhassfaK87liWBi4VrYkEVdI97fc5BAXAh3ltKH7fbJKnBgbxguCYMcehwGSCC6ZzEgede2JNZ/ucrYKEKfer12G3DkXd1NHialq8CqOOaYSiCLaaE8aerOMCI2QyoDABPfU6BvOYccWIk6SRHNlsXNX4eAAdx3tKk/N48HJ8Tx7/UfCEQP9exwQgOsWYGdLz7Q6AbPKPLwwRT/rGl8W0ZyPTm1EQ6i76tyjgqN/J3bBCScisSSjPAMFOMv7pAeHjzIV4yhdxmQ5PwGY52FiwsqqyQJoY7fTVnqSt8AklSjOlbNHyYr5mc+pWPl2Cd4WKqhRCmvEMjJbil3HkLyStgoDMgqe3HEizxrUe3cg+3A2bk64lipUlA/H6KAqW2OarIMpB6AyMKfPy+21s3lXoWeCJ5rQtB9sVH7PYZmYygvMUpryOESI8IjjQWWD3KVryAovv0HskBuWVyiLFmPLIKmfMWySAAwes5LJQPn6+VHaUBVExqduEwM4SiWVNj0oEvPA9EpmwjzswIS6x54TEW1XBSNLvTsgbLztjKEvjQSNrlNw68HpHmBV/n98foh9r8P5vhHnL+FeOxOmcWB5YK+8Ads6hWKeCDcdqHJkl8wj1OmVxFr2zp+FMHOCVs9KXPp8vs4eRO0qpu/wLlCQuHGnGWTslRW08Y1zKltdARvmnhHjzqpOXgt5xEBdkwB54JDxM+N88dt4xEV8jDOASXTYlZs8cWMYxHU8sU95h1ycJo/U4gXXdRPtpSXPmnVUTF3rEHqywJLG0t56C8FSyPVslMZ/0VH6ZPycqQGj/0ItfZffgqR7NHqqwYrR5nQrAwdvZ0L7mKtiNljkLjRN9GiCR8zF9akXWEc3JUgN9v36O/WLe9/kGCKPZGJpzubiWhNlQB2/SuPlpLBqHsWh8YftxukTIo5Xn1gmPJh0fGGGdzgXycxH62r5pTh8rhIB6gpsramBp4jhqRhCh0sq9Ri9CVWKUWYxCy815n6Usf6P+Kla8bw5Pl12xvMd3W51x5CQ3xuuWx3McTdlO48nDwZw8oQiZ0DpNzNZiBdbVHiyhJjdu9F0zVQ21EEMQWmM0hfSjDHnAZW6b7XvnyGRZbDZ0KjBEaHzNJB+tmsOSF5t04rRIngzBpC10QuO2rgRNJa1SEYZVrU6J2M5WBTIFEBGRW2jgNG7RGIS5NmcCFsaa/YU4iljS2XWg1LpA2ES4JTua+ZaPfL2QeLBznkcsO5jFWdL5nbOLc0WR7DrFPSwKJTnS2cXZnogM7fzOb/Nwz70LOdwlL1/cmhPa8U2tUkuz5zhyYkucxrZb267OgXXrHR2bJa+xHI+xYD8Why98QuA0qdmAeafAkcQ7GpakgllM6FAvtme7xmOBaqEqQGy2rHbtwDLlIjstWC+WHOw3tlLEOxqWHfdbw9KOUJGfqrvlE7GJt5eyT6XDvn5ddJ68drO6xPmPditizbU5kLXr7P6cqlp9Nq19N8VaG6uHrFO45pJYReA9dHvUuGV22eyRprflOjyrU+Ap2uJcEul4pOFgFfCa4Us7cDsRr5lAxgY3Lgq2a9IzoopjIMpb+lQTsM3syNa8OhAQC40v+oT2GFzmNBVJ7ei23dwueoVmV87TvgOoFqZlfXXQt8kgn98FtEZ7XpO4xnZcsO/3D+PyS6VE+9H20KnwGeIu4D6ek2Xni0XMS9mTC4vDiINowut3c8jm6Wd1uTV/OhHHy3Bz0BHLL1fAVZK7Yxr54SxYueQsLga99vOg5lB2jcPtDqSdw+2RjPUgWH5WV9sbFsQMVSFLblxYUZD8GcR0TKjqX/rtNFqhXcW8e4YoPhY2Yz8sb+1VOMgoWZfMUnQpbLFMfo6Iy2gnz8v/sEQcTFzVSknQ5PHXE5iVytRvhXxRqjoKlOzddRy7c2fc2UZx+bMW9tScm5IdzXVkN/OmELTxbCjCWp0aXgGwq/A0Pvk7DW4VmN60b99nrkTI8uduq05eCgkyBCvDo7/aZ34k/3KncMWqnDtCCf+9Du4EmGFjWLPb48HpKLZcWQGI420Xw6sQBMa14/UoJl5lgzFe5heyON3iZbzI9etplHVCS8wl2wHSO1cT4k/APWVpebSeX+dOb5e1iho2X4JWFTnXBc5A6iVDgh8f60canQqMx89NeXFsTsXF8aaY5TyxZV34PM4zF/xWFSAuVS38D1xxQTMe4MJpaQ4XsmGyhv1QyiIcHsDkqT7U12iRw5p8xteM+1lkx0Pi8jN2bSt3ooqbN5sbJxbcnDe0rY8hkYeq5yif+tq7VBGeb5yLbiQVRqV7YNJ9d7GkwpnFfXfjDXHjZS7eOAXZaOr77qQdomWvL4LZHRqvnhkvoUKD54eDUKlIo3N4DCTYT3x8JQEj32gTp4cXIXcST17C2v7hnrA/XBAcOW10/csrJaweLw/Uscrx9PhYX39lfbIcuMC++GMZhAv0u/lWv57lfbwf9ML4ILS6sD5IUp8LygvRQYZ+9QOCMsH32x6BDacuDI8XyVywHiDI7q+sH2j7F/wmkpsvF3D10I8qFzD3jod020r3/iOExAvqi9F1uwvrR999hf+O+HeF/15cCVwYKPCF/u0TkEap+rdP0NipC+LX8bIe0+98tRH+gvQ0ZfbB9rNf+zHt6d5+oGQV++k30Xmzzre+gW8szv0X9ROZ6QplbmRzdHJlYW0KZW5kb2JqCjcgMCBvYmogPDwvRmlsdGVyL0ZsYXRlRGVjb2RlL0xlbmd0aCAyNTkyL04gMz4+c3RyZWFtCnicnZZ3WFPnHsffc072YCQhbAh7hqVAAJERpoAM2aIQkgABEiAkDPdAVLCiqMhSBCmKWLBahtSJKA6K4t4NUgSUWqziwtFEnqf19vbe29vvH+d8nt/7+73n/Y33eQ4ApIBMrjAXVgFAKJKII/y9GbFx8QzsAIABHmCAPQAcbm62V1hYMJAr0JfNyJU7gX/Rq5sAUryvMRV7gf9PqtxssQQAKEzOs3j8XK6ci+ScmS/JVtgn5UxLzlAwjFKwWH5AOWsoOHWGrT/7zLCngnlCEU/OkXLO5gl5Cu6V84Y8KV/OiCKX4jwBP1/O1+VsnCkVCuT8RhEr5HPkOaBICruEz02Ts52cSeLICLac5wCAI6V+wclfsIRfIFEkxc7KLhQLUtMkDHOuBcPexYXFCODnZ/IlEmYYh5vBEfMY7CxhNkdUCMBMzp9FUdSWIS+yk72LkxPTwcb+i0L918W/KUVvZ+hF+OeeQfT+P2x/5ZfVAABrSl6bLX/YkqsA6FwHgMbdP2zGewBQlvet4/IX+dAV85ImkWS72trm5+fbCPhcG0VBf9f/dPgb+uJ7Nortfi8Pw4efwpFmShiKunGzMrOkYkZuNofLZzD/PMT/OPCvz2EdwU/hi/kieUS0fMoEolR5u0U8gUSQJWIIRP+pif8w7E+amWu5qI0fAS3RBqhcpgHk536AohIBkrBbvgL93rdgfDRQ3LwY/dGZuf8s6N93hcsUj1xB6uc4dkQkgysV582sKa4lQAMCUAY0oAn0gBEwB0zgAJyBG/AEvmAeCAWRIA4sBlyQBoRADPLBMrAaFINSsAXsANWgDjSCZtAKDoNOcAycBufAJXAF3AD3gAyMgKdgErwC0xAEYSEyRIU0IX3IBLKCHCAWNBfyhYKhCCgOSoJSIREkhZZBa6FSqByqhuqhZuhb6Ch0GroADUJ3oCFoHPoVegcjMAmmwbqwKWwLs2AvOAiOhBfBqXAOvAQugjfDlXADfBDugE/Dl+AbsAx+Ck8hACEidMQAYSIshI2EIvFICiJGViAlSAXSgLQi3Ugfcg2RIRPIWxQGRUUxUEyUGyoAFYXionJQK1CbUNWo/agOVC/qGmoINYn6iCajddBWaFd0IDoWnYrORxejK9BN6Hb0WfQN9Aj6FQaDoWPMMM6YAEwcJh2zFLMJswvThjmFGcQMY6awWKwm1grrjg3FcrASbDG2CnsQexJ7FTuCfYMj4vRxDjg/XDxOhFuDq8AdwJ3AXcWN4qbxKngTvCs+FM/DF+LL8I34bvxl/Ah+mqBKMCO4EyIJ6YTVhEpCK+Es4T7hBZFINCS6EMOJAuIqYiXxEPE8cYj4lkQhWZLYpASSlLSZtI90inSH9IJMJpuSPcnxZAl5M7mZfIb8kPxGiapkoxSoxFNaqVSj1KF0VemZMl7ZRNlLebHyEuUK5SPKl5UnVPAqpipsFY7KCpUalaMqt1SmVKmq9qqhqkLVTaoHVC+ojlGwFFOKL4VHKaLspZyhDFMRqhGVTeVS11IbqWepIzQMzYwWSEunldK+oQ3QJtUoarPVotUK1GrUjqvJ6AjdlB5Iz6SX0Q/Tb9Lfqeuqe6nz1Teqt6pfVX+toa3hqcHXKNFo07ih8U6ToemrmaG5VbNT84EWSstSK1wrX2u31lmtCW2atps2V7tE+7D2XR1Yx1InQmepzl6dfp0pXT1df91s3SrdM7oTenQ9T710ve16J/TG9an6c/UF+tv1T+o/YagxvBiZjEpGL2PSQMcgwEBqUG8wYDBtaGYYZbjGsM3wgRHBiGWUYrTdqMdo0ljfOMR4mXGL8V0TvAnLJM1kp0mfyWtTM9MY0/WmnaZjZhpmgWZLzFrM7puTzT3Mc8wbzK9bYCxYFhkWuyyuWMKWjpZpljWWl61gKycrgdUuq0FrtLWLtci6wfoWk8T0YuYxW5hDNnSbYJs1Np02z2yNbeNtt9r22X60c7TLtGu0u2dPsZ9nv8a+2/5XB0sHrkONw/VZ5Fl+s1bO6pr1fLbVbP7s3bNvO1IdQxzXO/Y4fnBydhI7tTqNOxs7JznXOt9i0VhhrE2s8y5oF2+XlS7HXN66OrlKXA+7/uLGdMtwO+A2NsdsDn9O45xhd0N3jnu9u2wuY27S3D1zZR4GHhyPBo9HnkaePM8mz1EvC690r4Nez7ztvMXe7d6v2a7s5exTPoiPv0+Jz4AvxTfKt9r3oZ+hX6pfi9+kv6P/Uv9TAeiAoICtAbcCdQO5gc2Bk/Oc5y2f1xtECloQVB30KNgyWBzcHQKHzAvZFnJ/vsl80fzOUBAaGLot9EGYWVhO2PfhmPCw8JrwxxH2Ecsi+hZQFyQuOLDgVaR3ZFnkvSjzKGlUT7RydEJ0c/TrGJ+Y8hhZrG3s8thLcVpxgriueGx8dHxT/NRC34U7Fo4kOCYUJ9xcZLaoYNGFxVqLMxcfT1RO5CQeSUInxSQdSHrPCeU0cKaSA5Nrkye5bO5O7lOeJ287b5zvzi/nj6a4p5SnjKW6p25LHU/zSKtImxCwBdWC5+kB6XXprzNCM/ZlfMqMyWwT4oRJwqMiiihD1Jull1WQNZhtlV2cLctxzdmRMykOEjflQrmLcrskNPnPVL/UXLpOOpQ3N68m701+dP6RAtUCUUF/oWXhxsLRJX5Lvl6KWspd2rPMYNnqZUPLvZbXr4BWJK/oWWm0smjlyCr/VftXE1ZnrP5hjd2a8jUv18as7S7SLVpVNLzOf11LsVKxuPjWerf1dRtQGwQbBjbO2li18WMJr+RiqV1pRen7TdxNF7+y/6ryq0+bUzYPlDmV7d6C2SLacnOrx9b95arlS8qHt4Vs69jO2F6y/eWOxB0XKmZX1O0k7JTulFUGV3ZVGVdtqXpfnVZ9o8a7pq1Wp3Zj7etdvF1Xd3vubq3TrSute7dHsOd2vX99R4NpQ8VezN68vY8boxv7vmZ93dyk1VTa9GGfaJ9sf8T+3mbn5uYDOgfKWuAWacv4wYSDV77x+aarldla30ZvKz0EDkkPPfk26dubh4MO9xxhHWn9zuS72nZqe0kH1FHYMdmZ1inriusaPDrvaE+3W3f79zbf7ztmcKzmuNrxshOEE0UnPp1ccnLqVPapidOpp4d7EnvunYk9c703vHfgbNDZ8+f8zp3p8+o7ed79/LELrheOXmRd7LzkdKmj37G//QfHH9oHnAY6Ljtf7rricqV7cM7giaseV09f87l27nrg9Us35t8YvBl18/athFuy27zbY3cy7zy/m3d3+t6q++j7JQ9UHlQ81HnY8KPFj20yJ9nxIZ+h/kcLHt0b5g4//Sn3p/cjRY/JjytG9UebxxzGjo37jV95svDJyNPsp9MTxT+r/lz7zPzZd794/tI/GTs58lz8/NOvm15ovtj3cvbLnqmwqYevhK+mX5e80Xyz/y3rbd+7mHej0/nvse8rP1h86P4Y9PH+J+GnT78ByeL04gplbmRzdHJlYW0KZW5kb2JqCjYgMCBvYmpbL0lDQ0Jhc2VkIDcgMCBSXQplbmRvYmoKOSAwIG9iaiA8PC9BbHRlcm5hdGUvRGV2aWNlR3JheS9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDczNy9OIDE+PnN0cmVhbQp4nGNgYJ6Qk5xbzCTAwFBQVFLkHuQYGREZpcB+noGNgZkBDBKTiwscAwJ8QOy8/LxUBlTAyMDw7RqIZGC4rAsyi4E0wJoMtBhIHwBio5TU4mQg/QWI08tLCoDijDFAtkhSNphdAGJnhwQ5A9ktDAxMPCWpFSC9DM75BZVFmekZJQqGlpaWCo4p+UmpCsGVxSWpucUKnnnJ+UUF+UWJJakpQLVQO0CA1yW/RME9MTNPwchAlUR3EwSgcISwEOGDEEOA5NKiMggLrEiAQYHBgMGBIYAhkaGeYQHDUYY3jOKMLoyljCsY7zGJMQUxTWC6wCzMHMm8kPkNiyVLB8stVj3WVtZ7bJZs09i+sYez7+ZQ4uji+MKZyHmBy5FrC7cm9wIeKZ6pvEK8k/iE+abxy/AvFtAR2CHoKnhFKFXoh3CviIrIXtFw0S9ik8SNxK9IVEjKSR6TypeWlj4hUyarLntLrk/eRf6PwlbFQiU9pbfKa1UKVE1Uf6odVO/SCNVU0vygdUB7kk6qrpWeoN4r/SMGCwxrjWKMbU3kTZlNX5pdMN9pscRyglWdda5NnG2gnau9tYOxo46TmrOSi4KrvJuCu7KHuqeul4m3jY+7b7Bfgn9+QH3gxKClwbtCLoa+DGeKkIu0ioqIroiZGbsn7kECW6JuUlhyQ8qa1JvpHBkWmZlZc7Mv5rLn2edXFGwqfFesXZJVuqrsTYV+ZUnVrhrGWq+6qfUPG/WaaprPtsq1FbYf7ZTuKuo+3ava19h/d6LNpNmT/06Nn3Z4hsbM/lnf5yTMPT3ffMHSRSKLW5d8W5a5/N7KkFWn17is3bfecsO2TSabt2w12bZ9h9XO/btd95zdF7b/wcGcQz+PtB8TP77ipPWpc2eSz/46P+mi9qWjVxKv/rs+56bNrbt36u8p3z/xMO+x2JP9zzJfiLw8+Dr/rfy7Cx+aPpl+fvV1wffwnwK/Tv1p/ef4/z8AXyIQegplbmRzdHJlYW0KZW5kb2JqCjggMCBvYmpbL0lDQ0Jhc2VkIDkgMCBSXQplbmRvYmoKMTAgMCBvYmo8PC9TdWJ0eXBlL1R5cGUxL1R5cGUvRm9udC9CYXNlRm9udC9IZWx2ZXRpY2EtQm9sZC9FbmNvZGluZy9XaW5BbnNpRW5jb2Rpbmc+PgplbmRvYmoKMTIgMCBvYmogPDwvRmlsdGVyL0ZsYXRlRGVjb2RlL0xlbmd0aCA0NTc+PnN0cmVhbQp4nF2U3WrjMBCF7/MUumwviq2RbG+hBEqWhVxsd2m6D2BL49SwkYXiXOTtK+tMU6ghP58yMzpHM0q12//ch2lR1d80uwMvapyCT3yeL8mxGvg4hY0m5Se3CJV3d+rjpsrJh+t54dM+jLMyiPKXKJFKVa/5y3lJV3X37OeB75XncV3/kzynKRzV3b/d4bZ6uMT4n08cFlWXNQ6+fFa733186U+sqlLnYe9z0LRcH3L6V8TbNbKiwhoa3Oz5HHvHqQ9H3jzV+dmqp1/52a7Vv/1uG6QNo3vv0y18zM+2kM5U11SDCORBplDzCLKFWslrCnUNqAURqC9kNGgASRVXyPYgj5oSySAGjaiJPF1DmQNBtcF+GqoNPGioph8gqLZQraHaSk2othYkOlsQdJJEQqeV3UVnB4LOxhQi6OyEoLPBDgSdLVQTdLaoSXK62I/kdCUPOknyOkTCX7ZZlMlvjyB0hdAHO4Dgz+KsCf46nBlJH9B3En/iAf4IXTHiD94N/HXoppHpwVkbmR4qYynzZz6n8Wt6YaeG8lZ6gUUNc0YWEaJlujqpi0rr5K83+Hat3CWlfKPKBS5Xab1EU+DbP0Gc45pVXh+aCQt+CmVuZHN0cmVhbQplbmRvYmoKMTUgMCBvYmogPDwvTGVuZ3RoMSA1NDY4L0ZpbHRlci9GbGF0ZURlY29kZS9MZW5ndGggMzcyMj4+c3RyZWFtCnichTcJUFvnmf//PyyZG6ELzKUDicNCCJ2Yy4AxGMJpg22wDTxASLIlSxYyYJtg4iQU24nJktS5trancdbT3U7S3SxNZ4du23XHbXeadGfTTbOz3W27kzRup4nTxE3DbHna7//fAyttZyrp0/v+67uP/yGMEEpDC4hDjT0HKu0Xn4g9jFDGH2B2dHw6pkPi54cAZDLiC0njfwPAvuCZye6K1fuAfhWh9F6/l58g+r/vQijzKKy7/TCxPZMbhfEzMC72h2KzofXkV2H8DRj/OBge5xEea6Q4wDshfjaC9qJrCGU9AWPdST7k/dUHjz8LY6CfdDESnorFr6JehNTVdD0S9UZEcdSHqTzo859SCSj9IADQQHdhWz5ADAB0IlkAToAbACADtw/gMYDvA3wEPOFsEsiS9G2EtuUA2AD8ALB/22cIyYCW7GsIycFO8n4AoCv/OULbOwH+EeAdhJKBfvIEwF8DwFoynEuBuRSgmwJ2SPkNQqlwPnUOAPanFQOAjmmwngbraTCXDuvpLYggO8j9L+RD8JYcIYdCr+D0Cr0dP2UXfowt5MONbLK2MQ2WKEd3sB83wD7kcenV5bjhzuwszKeAnkGyirbT05wjDzs49b33Xr6y8qVfYYRXhTXcIrQL1HoEWeK/JWaSgVKRErQ1mF1Ot8OuUatkJXaX02hQq3DZzOLiDIVAIJC5vHB+efn8wnLk+rVr16k3muF8No4jsLG+RGYEAgqHQuWwe+CBX8gY5Fs6V1qd9hX33r42PC281qfD8wJzIkat8LdMklAGldOhbcAOu1ZtNhrkitbZ7NK2UqU639BUjeOdFhP3BZlWeJbJ+ztSSN5FmWiHxNGKmcxaYFgCbOG8TK3S4OtpDQO7R13TfE/Nyoc2y85ql9NTUb17tm/uixWY28gfz8fbc3t6e7u37IA/ATuoEXjbUwiieNQZ2JhgEHmJQSZ3uJyvJe3r2jNo5J3nH3/s9PgJWdIPqnYlffenzbW5fLbq0uMLV0JeTXX2G7XVijHQEXTDi+RjpKE6Gl0ekRxTU1aAHWqjYqSv79hIXo2mNN9qXF7GTw9lWr3jqcnjskJzQ1AIAY1jQOMl8KcMNFbIXR6wbOY/3H6UGKujhzceEuUvAT8UkjsoBxkl+d0e5g4nNSuVHwYlSjsIIFqo2TRa3NMub39oJFIXfWjuwtVl+4T+Fw63e1elaylLffR42dRYS6jxb1/61g92qHF3Dj4+Xuc6zfymid/D50CedOCkAUbM5ZS8ptrhtl59z1BTfCbDtRu/LVR8kpomymeOf4T/B+ybh8zUb5JpmcuZRHKXKCf40O1xmWnkafCJtKI+c8egbajeVmN27B83RWp8I7/JdWktxkOGinx9f6utvTzdbjUU8UrtgUHh2n6N8pC8tciwFV9EBzwV1G6MicIIccb8qcBXik0e98psxtBkWxfuKTMbhMdw3NPa1SYswtmkuJmoIK7hrFZMISpyiYvq63nzX1+MBp99p6C/0V6pyy23Zm0ncuECnt9Y62nNGudKbCL/nDiP3kEv0DzUKpkTMnFObXlVeXb3dbwrt0Bj+Q7b5wE585jvkFLyVgaW69V6VwMR3SVvrz7RNDJVM38cNwj5C+errCUWv4tM7zQdP+SMPDkaCz7yV4dN5p2lJmrrQogFC9BLRVqETOAfyeUabUI4Y1O901lP4fzSxfn5i0uByKMXIpELj0Zm1159ZW3tlVfXqGw18Qjug/gFX2tZpnkcGZiSutOyb1/L5bqmprqnRt4/d/buyLG7c3N3j1H+dfF75OeMfx5opGIhIqrBHF6IqX40cd8eHhoapvBCz/PHfc8dEP/xkem5uenpc+emT94cPPhS9OTNoYM3qSxQe9GvIe44Vi8U/VfJKoQ/ob2CXGb1LoutiOHOAVKPncbe9Rdv3Xpu8uDq18nqa7f+ZpU0CY7/Vv0UzkFOEhucS6Ha6V16F4bDaqO6RMHhQeGbuGbp8OHz/3TxBP6O0BK5uI5ThE+Zv3aBfRVwLm8z/sUAVoLH5G7JfRAru6Z7Gxob93QMZeOLwsepZRW+ucazfaHRy+Zqt9uRMoTLojdTpkab/bXlYryUgDwaMc8dGORRc/M4SXgZ31snTbHQBrQJDvLot6QYbKtFxQjCDENdovYUU93tkYqhBjMnibJVYpWmCEsuIKk19raGxbPTS3tqHbaHfZMLwt0iQ+0uT62jbaDSYXJUVVqsJN15MNfQU8sHJw7VjucVPOQcDPqEX2jri50uu9VoLfwvo0udZdttc1iov8tYD7mDcmntweDmB0bYrD9atRVzBjmtSyAHHt/fJ+vpPhZtmOo6v7D38WHLUV3hwEhVLXHXTlaT/slIefjI3hP1X7k1++qwSuFPyxR+mTsxFHJ4mJ0yICZNYkwqmfJyYwPobS55fismyccjd8+ee38zKDGLEWrbbWLs6NW9VzH4fuP27Gb9hy4ItSIX7IpMsq3Cr4VqS60rZr4rsTrN7+o6Fo4Ml3cUpM4uzfKHxvc11w5qK5VlnlF7g+NybPpKYYFFKDu7tHOsqH4vn5n+ae4zXR0giw3yowDsBZyUm91LLRItwpveLHGxIk7V+slg9+XTY8P8gemy6o5jPc88Uh0ptUettY0ltbhSP9I+HCmOFnbmF6vyDEfafTPq7Ghm9s7SomIN8DJA3X0Z9IKSaKKhkMhPlsiQKYiLtMbuenfoSH9bZ3OZWWvqbHCdOurrGd+/70mVJr0op9XdckDPa1UahTqzKLfZ1XGkjC+iPoEyT/aBfeVin9NDY/vZW6TuLVI/O7txW7RxK/TuWujdCtpjqRe2ih3VV66E+iyFbOtKQbeZP+Uca7T25qWfc1otbujf5F3hfm7BlTM9c226Aju+kSes53XuP9DJan18HZcA7TTRe6wlATEPLim3fWFFqcnISVE8S/o3vpGv5agsu0Gi98mvIaMyRVm4hNrYsdJutlrNADguYNJdXlxcToHereKf4WlyEe4JYpTUYyN0Ooea1g2R4bR7z0BfV59m9tIlnbmoNF3d2//7oawnLgU/0u2AzI7HUTX+Gn6C3IZOCDdEsJiT3bw3+9W7f9SvFJ/rVysHh7faFdjjg3rWrzgW391gf+kGs1kFpeLAqWnebT561yfOVVob9yyeOjM9GQof4gfJ6uR+R7tKeWj30HFsvT06jvO/PnQUSXmTA3RT2U1UrWeKQvbg7wtvrq9jO1mN3YytxtDm3i+zesosqnQkYyNW9F79v68Iv8T214Tfk1XhJ7hM+GfhSdwuvM5iAixCnmZxk0LvBUa5UelQQrfFH1V9UPml+y/fF0ZeOXztGq30OAUbpDiyQxxliHGklW6OW3GkToyj2bTCAQsLpLKOHWNSHL1J3rAVGFgc7dDeJyc/F0dQZPuAdiHzAbhADT5wbl4d2H1FuqiQb9W7PSv11XCHSHcNVAyZHEO21i481LIjRViER57wMI5XGQsaDYXdrexOAXdX/DpJYV35wd1y8y4oRs/reZ1lZx9ZOtPeUFvV2tzUUtWkyVIsLZy/ouMVbV0ZnW3ZYq9wxj+B95xb1C+JXf1Kqd1eCpDO/gHoXrAxlwN3GcgNfSo2susMvdHg9w8PPvnSCwNHHx3uvXoT+4TnIdyX8SnhCo5u3tHhnQS/AWeTwacuTBMb69U6LN/A14RPcaYf7wz6hf8IbtXlT6EuQ4JpsQNa0MDTwt89xV34w7yY/zQ+pti9UcWiSYxQpZEz5rK+sBP34tTgfHPdV1+8OuYP+I6T1ehY3Wie8DZOFz7Bp4/7RZkgf9C3IX9ALyVHLch1ZBeZTEXZkFdEoRCEz70hcqSHrNG6T9bIJRj7xSdeRL3YtJ2QVDlHSBL8biByrxfpjkhvlKilqauJ6h/fIP8et6IF7oc4F4bXmRBx0APeTkFXLL6VHule6hnJrPsdSubu0h0/srTdYM/nzKp4sfCzbXncW7AvGewgvsPCP/dGHAhuq4X1H23L+5N3Wxe+j+wYtMQrqJxMoxSyH1nIYdSMv4hayQTgBciCH0MZpAMdAyjB30MaYkBmWGslOSgJ3oJzYN4DUIhHUA2Xj+rgfbGffA/1wpwGYBc9B2AGKIM9GaQI1vqBdjuy4W8iA+BpxIda8V4AM9qNL6EU/F1UzXjMwt5mgBsAzyMZ3ceBbPg/Qa585OQKkQx/gHT0HY88DP7/DFUzLV2QZ0loALI+UWe4dbOxDi9uzZ8iVVv2SiObtiMoiWRKOIeKtuaTkGIL34bSiUrCZSiL6CVcjvaQL0v4dpRJGiU8OQFPRTvIexKeloBDXdjCsxLkUSTIk81kgLhIgpxB/4uGJRzT+72EE+C8Q8I51LA1n0T7tIRvgx3FEi6DSGuQcDlaxI0Svh3q1JyEJyfgqciJ70h4WgKeAf7fxLMS5FEkyJPNZGhCIcSjsyiMTgLvvTAaQ14UBbwZ5oJoArCDbGYKBaRdVcgKd1L6TTz94GzF1tk98IygMzAXQD7kRzE4bYdzVdALdagFzgZhTqTaBSMedulQJ8xNAA86FwYsgCYBxmE1tiVDGOZ0MPbDzBRgdEcQuOuAlxedQqdhTDG6FmH8w0yrGYbH4OtldCJM4hCj8kDDSZgLw+xflvHPr1sA52FmQqJA5+lMdEtCH+MYY9y9bF8MMB4wL7NpFJ1gsot6/iUp/EyjCKpBlfCdYV8rrDw4FZLOWMGOVLNK4HMaVvmmEH82fFK3NzTmjeqaw8EJ3UFvdCoAU1VWm80mLrPVCrq6Jxw5Ew34/DGd3Vbl1LXwwRhs7eJ5n64zNmHVdYUnApOBcT5GKYQndTF/YEo3GQh6dVHvqdOBqHdKF4kGwlHdTDQQi3lP6iLeaCgwxRhORsOhP6GYMLbo+JMTsKGL1/FRStAXmIp5o94JXSzKT3hDfPTEFOX5xyT8sVikprJyZmbGOsGWQrBiHQ+HKr2ng7xYW9gn/jTtXX/+8//Gr2mECmVuZHN0cmVhbQplbmRvYmoKMTYgMCBvYmogPDwvRmlsdGVyL0ZsYXRlRGVjb2RlL0xlbmd0aCAyNj4+c3RyZWFtCnicm8XEwF7HgAzUQQRvvzr3v9rXDAAvwQR9CmVuZHN0cmVhbQplbmRvYmoKMTQgMCBvYmo8PC9EZXNjZW50IC0yODkvTWlzc2luZ1dpZHRoIDUwMC9DYXBIZWlnaHQgNjkzL1N0ZW1WIDAvVHlwZS9Gb250RGVzY3JpcHRvci9Gb250RmlsZTIgMTUgMCBSL0ZsYWdzIDMzL0ZvbnROYW1lL0VBQUFBQitBbWF6b25FbWJlci1Cb2xkL0ZvbnRCQm94Wy0yMjAgLTI4OSAxMzA3IDk3OV0vSXRhbGljQW5nbGUgMC9Bc2NlbnQgOTc5L0NJRFNldCAxNiAwIFI+PgplbmRvYmoKMTMgMCBvYmo8PC9EVyAwL1N1YnR5cGUvQ0lERm9udFR5cGUyL0NJRFN5c3RlbUluZm88PC9TdXBwbGVtZW50IDAvUmVnaXN0cnkoQWRvYmUpL09yZGVyaW5nKFVDUyk+Pi9UeXBlL0ZvbnQvQmFzZUZvbnQvRUFBQUFCK0FtYXpvbkVtYmVyLUJvbGQvRm9udERlc2NyaXB0b3IgMTQgMCBSL1dbMFs1MDAgMjYyIDQwMiA2MzAgNTk0IDYwMCA0MDUgNjEyIDU0MSAzODggNTg2IDU4NiA0NTUgNTQ2IDYxMiA1MzYgMjg0IDU4NiA1ODYgMzUxIDc5NiAzMTggNzExIDU4NiA1ODYgNTg2IDU4NiA1ODYgMzUxIDU0MyA1OTYgNTg1IDQ0NSA1OTYgNjE1IDMyNSAyOTQgMzk0IDQ1MiA2MTIgNjMyIDU3OCA2NzIgNjY1IDYxNSA5MTcgNDczIDI4NCA3OTggNDkzIDUxNiA2MzddXS9DSURUb0dJRE1hcC9JZGVudGl0eT4+CmVuZG9iagoxMSAwIG9iajw8L1N1YnR5cGUvVHlwZTAvVHlwZS9Gb250L0Jhc2VGb250L0VBQUFBQitBbWF6b25FbWJlci1Cb2xkL0VuY29kaW5nL0lkZW50aXR5LUgvVG9Vbmljb2RlIDEyIDAgUi9EZXNjZW5kYW50Rm9udHNbMTMgMCBSXT4+CmVuZG9iagoxOCAwIG9iaiA8PC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDQ4Nz4+c3RyZWFtCnicXZTbjpswEEDf8xV+3D6swGMwu9IqUpWqUh56UdN+AGCTRWoAEfKQvy/4TFOpSLkc7PHMsTXODsdPx6FfTPZ9HttTXEzXD2GO1/E2t9E08dwPOysm9O2ilL7bSz3tsjX4dL8u8XIcutE4ZoXbpDONyX6sf67LfDdPH8PYxA8mxG57/20Oce6Hs3n6dTg93p5u0/Q7XuKwmDy9i0NIv9nhSz19rS/RZGmd52NYJ/XL/XkN/zfj532KRhJbamjHEK9T3ca5Hs5x95avz968fV6f/bb6f+OlJ6zp2vd6fkzv1mefyK6U55JDAgXIJSoKqEhUvUAlVEE+kReoSlTqmi+Qxr1CDqohDzWQhVoyaPYAvUKROiPUUSdjNqeWEsLPY2Txq6jT4uc1Dj9PZRa/kuxW/dgzi5+nToufbyH8nI7hV2h2/CrNgJ9oBvycEn5OK8PPsdeCn2N3Bb8KW8GvIIOoH2sKfo5zEPwKjASHinMQHCrNgINnrwUHr7Xg4DVuc+iaHHfBoWCvBQffJHLqUEM4lDg4dWBNpw5U7XAQ9trpGbFLjjMqyO44I0d2h59g69SvTg2jnWH/9smjr4QFRVcqdTbjW6dtN8ajjdvbPK8dnC6M1Lpb0/ZDfNw80zhtUenzB1llIVwKZW5kc3RyZWFtCmVuZG9iagoyMSAwIG9iaiA8PC9MZW5ndGgxIDU5ODAvRmlsdGVyL0ZsYXRlRGVjb2RlL0xlbmd0aCA0MDYxPj5zdHJlYW0KeJyFOAl0U9eV7z7ZlrxblmUBxrLkRTbGyLYW75blVV7kXcbGu5AlS2AjWRYYm8WUECAkBg8hJCmdtjnQTshJSqDQYRIy09KcTtomzZy0p03mlCFtmEI6NEsnJG2Jv+b+xbZIcqaSr/99293fvfeLACEkhhwgImJu787XHR2dPkRIwts4O+rYFVAR/vM6AnX5xieF8X8gwPjErOvHYSdfRfSHhMSvcTvtY1T5vSJCpCW4XuTGCUms6FEc+3Cc6Z4M7J47GN2J41M4vjPhddgJfB/Pkl8h3J207/aRZnKOkMS9OFbtsE86o86pkX7iNwgJO+PzTgeCp0kHIQqWvsrnd/p4cRR9rDzkwU+OAGYElt6LLA/cVoGAY7iMKqF+tBHhWQRcExUgDCIEEFDnMByHuRG+jXCHkPAYBCvCVQTcH5GAgOcjnidEXIeAdMW4T4L7JCiTBPWU/JaQSAMC6hG1BmEC4V1CosMQbAiLrAMQUM4YlCNWgoD7Y5FP7FEElDsWecUlIaDscShbHNosDmnExxBKdKjLdfoBelBMiF6qlorUUrUOFnXMryCPfrCUSK8t7ULraMk70AebcB8pNqrlWsh9x2bD8ygD9dMrREKk7Hm9LlmeFJEhQqQSDJoM262nnzn39B7fTZ+XXrn43LOXqHPpf4KK+QOsxZEj/CfcJ9GEqLPFGbJsvaJYL5bBxPw+z/fPTwam3c9euX4dwj67ePFj5lPCeSkS+f0Fz6DS6mjIEOlTgP0TwR8nto991+2fcEzsdD4HC8w03Ge2wWnGCWeYcPYsJc3Be1RFb5E4soaTVVokiJueLZali6XJep3RoGl2tPTafbu2DtZHP2mpqqo/VktvMberHpvbc8pshJe0zPsFL40MEkH3PNQ9iiQSIpNmLGufrdcVGQ0b4Z8d9527dzufOlFlnj8BMcwn9Mr0Vvt0Z635EKcL+ora8Hw0r4uMU0Ykg5uXL03dvRH4ztmpG38CJfN7cEE78zmEMZeZJ9lzZcEPaTh9lWQiVy0YDSbQ6xRyTUZ6hDwpOQ2UwOtkTObk0GT/uqu5xGPpH3G2VlsKqgc6LY8Epns8Q5bO/BJoSuurLenVZXamGQtz8tekK3tqR/0bOjJMxqzCZOQVhTLuQhkjUEZePogM+l57bSJIryxdpO1LLbxtzcFh+lOUSUpSUSpOJNYW4mQULFsLxUmCMCik2fq468kX5nyOr116+vHveKa2b/f5tk/4oN97tu9H5xeuppRFbpHNh/3wu0cXjh85srDA2Sou+FcYp48QjOMsVM4ozTBWgV6ul2dIWdLFMF5oOj44Et95+rQ6Z0NOjOw4aMwxi8fbmJtZyig+dsRBDXyKsYORquB1iYNlOxX/8vVtJxdct9c1leRmpiizNkrDKWHs8K2l5+sr4izirHyeRnnwU/IKmWN9pkjXGA1CCO1NzcxMRYjKSk3NYoHdy+aVf0Xbifho63OhyVrQVgXBj+EWjSIysh5vkxJNVSxHnVaoibPTI8R61lqLtLOnvavRvWf//qkRl+R6VUP4X6H0zuauNEv20YfnF7Zvzdf8pqVZklhpQn7NmHeMSBfzklrKmlqcnCTHsORQNjwVSF/B8ZDStzQbmprBodlgbXB0R40ODKkdjvpm6NMVbBInSJiTLJbH+OG+vsZiaWtkHuP1Rx4wAUESz+mkMFEh9MTS5u7o9QW6VFmyKrmjFO63KNUJov6wAuYYFx81nC3eQFvwJ6WiEOu1OQpT1epUBLy04bRHnZKiZgH5FQTvwXkaQZJ5v/Nn+GBPBd7z58usu/Z9bVdteWmxrbmls8gsUx45MP/oektiz1DsQE8SJzdypfnoCy7LZWAGy5DevUFVN6jdZlv6Fh/D6BeahPaLRs+Q8BD5sjEvZKTLkyDdf+CAn4WFhYX44/P7jx/fP3+845Vr115hz+cG/xd+gufXsTdTnc0GlyAvF/hiI38rpNm6YqOGpZcMAUlm70ZLj7uvrK6wvGcg020c6b9T12Aomso1pKV31jX1SmuK8tIaZPL2Tua0Se+K7dVs4PwQvE+CmMtiMYI4u6BJOXME1dnGEocMPS+OqSyl00uPK+Qi3nfj+O8x4S5LxcZivRRyfvTaEO1q6Rjm7zFwOQ3zFJdfpSJM5Bg2eFOk9PSPh14d9r9700vVzCJMLf0XvcL0wvnlcxqM6VOouxr9ZNDkQ4ijvpyUoCRtQ4/X2dXe0FYzqspvKzds63M0DXcU6o+uUcarNjiqO1QNa6vXKROViiqdxaZpSNNg5NQG91OJqBB55BC8juFGTbZRCQpjNp8Ei416OZdu5AqOm1guw+QnNwHGicIYBxA+tqW8Pye3rTm/r9zU1djVuLG9ZdvApK5cX8b8UVeqLzk4F2HsUKaK7iWk9lQYevRhs3OSvHat5E8J67srbBNRc1Cl0clvRVSBT6NPuhFWxseNEv/lcHUAvaE2qo1oL0xM8mypCLYxl6Fp1G4fuvNEK/ySKex84g9gZS5z5/QYbzLMmQqSgV7kspAQM8vpEw0mw1mjkNvbuupHHeLMPq19qnRb/ez86WOOmjc1rSrRqXJLnTtr1941KQFnrafyubPXfrYRipIS4+621zQ0sf7BnoBG8b7XA8ooh0WQMyfg18zH1NHVvvR1lAd9SDNRnmiUiGQtp2vkG5rpIKu9ubmdhT2HHtqLUP/wP5w4fPjE4mHbyxe+9/JLFy68zPJrwHi4x+dadfYDAYpPOBs9tNVideRqG5scRfUdTTDFXCjSF8AxLNWAteQTWkH/7f+587Sic/jc+Qtne1oGSvcHpvZUOWVpVy688FJKt3zP4TWH9q4V7vM9GoZ3REpSlu/jSrlEKfAaSrXA3etLsWrbxqrRIoO9tqfO8TuTOa1Kc9SgVJtmOjvn6kogcWl9vRZSFPJr/4JxWIh2Wiv4DeMQMNSE8GZFLWZ5sMYC1nR81csH9g4IBoVgZVFLzcP+HQ/V1xQbZ7baZ5nPnK2WhrbS1keKygzdNeVlZhpT3J+S3lHW7xnbXLlVub7VuNntYj4o7S2vrizZYFS9vaF8jby4s9RUxtXeD7naG42Zh8hCKq14OZB41c8vl1oPV4GtJ12Okx3QL5TZI1z59Z7d0nMOfVCHOoZjvKwTIlPIYDK1XC1ebZvqvO3WTutm05AMJpnbscV5E3PHA64tnsz6aos5qgY2dv08yj+2dTaH84cBaa5DOdey+RFQPFa6EMshWQV2liJcEVjC4Kg9LGcwr3Kk+MD2ffufOJxrS1O3dWS2ZUY8UVVvof6HDqcoC4dM7n3Pnf/BzxLjrTHxzLuKpFuNdaZ6stxjcTWfjXu+5r/579tOnHD9BCvNCDyD8cb2hTrsC6P4vlDBl0lBQXlIX9gdaR1iG8P+6sOWquraY7W/oD811C7M7jlVztCjK30hVyMx7qK42H+w+GLyh2c2atutWHJHPE1W6Dbq9Mws3C+xtDZhiWVjVgN/42oIthBZyyHLxj+m65C2bjV4k+Gp9LacymGjf7S/VtI9v3O4fbCpo3mPqVJpyjpYX5+aVjHdunvBlM9kzhzMsaS19lRrQayQX+zdgrJi5woB+me2r2LrYrHRsHrV2ObKNzz87ZI8fYo268wZeMEc0/VPiQ2SjJzBNqab82ki1wP/Ge9q+ldQUKPiGVIISR+r9MDqeB9Slut+CGkmHF5gukO6AGxruLpUjPEYh5yUq28awsUTyTkXrTxt/23fVbDJXLfX/c1vzFebvz63t7KCXhnrNrQkyXrNfR6o+GCmvAI23vQWl670NTQWc0UU353gewhkZMubHb+Z/QjIvvfwFaDsBr6EfPRRMMj3gHg7slEaAo9ibMVxNDrwHrLvQuh34J21kvWKjDTGtXDo0IKrr7u7D0tn48HHHn0IrjJVtoEBG1934UM8G8m9hcnVXGdrw3eXnwex0+662cX8gqzG1q0vxJb0gdhyjDhWQgsFf6+Wiy0gyuA4vscG2DuhkHH2iwelXpWeGmd+GmKlSQk5L/K9NvJo497piN7IXxr53+5OXbi046Mg/ID5R3AwjUvoe/bdYDfXU0Wx+VUtzgB9JG6mcWbmPZMHyA4gTPf7O69eZRtfCAcbb+taPJfI5U+kb6IPNEeYqsRqee3iw7pKw5Zthc6KAW/FkVkYbDt5ZjhXV9rSk53lspVMPxXo5mklBH3wGsYftjgK0EMC2MaY5xdFBz/fz6+zmeYq3n/Wrka+8KnlmZDB3IYjzDuQawV7WwvzzbYH3/5FtAwWCb420muU7V/d/BMOkw7IklAaHSGi6AaKr/b0ww6iGhB+LSB11a3V7G8GwSX6VlBLDoheh7XYjnMNJtxHWxHszkXcrw0Ia3f/wTMSX3GPRIrusDvezKs7yj2f0iQGC5nb4TEi1uuRaGv+9wn8L3ojiATDDbj+eXjMl363KINPiA4iOFm19BKxwe+IWITvzbSDNNM+YqODRELLSBk9QKJEkcQM8yQOltCHfyHlkEz6qJQUiPaRZtiJsRYkNfAGKaAmEk/r8BlPciEV6TSTcVEj0n6RaBCvRVAi6BEMCBpaRRroCDHTNjzTTApZPvisY9eBQf6sLFYEpIl8EukkghNli0OeKAe9TjpoDI6t3FiJMsfRgySK5QVvkwT4LfqV1bwM18NIL0ofagfAOXasQhssz0/R2hUbxiAfHqdETFMEXETSV+bDQvaEk1iaIeARREYLBFyMel8WcAnap0vAI0NwrMj0MwGPCcHjUKdlPCGElzREnkRuHmMlDGOX/J5sE3Bgq5SAU6S0TsBFpG5lPixkTzjuyBXwCKLBXTwuJofBKuASzKlHBTwyBI9Gf70l4DEheBypWMETQnhJQ+RJ5OarySSx4/uyl+zAyK/H0VbiJH7Eu/A5TnaSCVxnx5u5+WniEfYWEi0p4L6hNFYpbPoChVpc95FZxDw468Y8pyI6PF2Iva8KtbbjvoBAuxVHdtylIlacG0NO7JwXMQ9xIThwNbAiiRfnVDh248w0YuyOCeStQl5OMoUSeDiMXfNx/L2cRjMcHsCvk6Pj4+Se5Kis6unCOS/O/n0Zv3o9D3E7zowJFNh5FWeRZQnHOY4BjruT2xdAzI6Yk7Osn2znZOf1/HtSuDmNfHj38vE7w321uLJ6alI4o0U7sprlIx/OS9WT9jnvDlX95FanX9XlHN85YferNjv90x6cLdQWFBTwO7gNm4QNtV7frN8z7g6odAWFBlWdfSKAu1vt9nGVNTCmVbV6xzwuj8MeYIl4XaqA2zOtcnkmnCq/c2qnx++cVvn8Hq9fNeP3BALOHSqf0z/pmeZ4uvzeyS9RDBnnqew7xnBDq11l97MExz3TAaffOaYK+O1jzkm7f/s0y/OLJNyBgK8sP39mZkY7xi1N4orW4Z3Md6JK7G3hPsHH2d+jv/rzf82+ASEKZW5kc3RyZWFtCmVuZG9iagoyMiAwIG9iaiA8PC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDMwPj5zdHJlYW0KeJybx8DAXscABSwgQhFE8Hvsvv3v73+IKABWPgY0CmVuZHN0cmVhbQplbmRvYmoKMjAgMCBvYmo8PC9EZXNjZW50IC0yODEvTWlzc2luZ1dpZHRoIDUwMC9DYXBIZWlnaHQgNjkzL1N0ZW1WIDAvVHlwZS9Gb250RGVzY3JpcHRvci9Gb250RmlsZTIgMjEgMCBSL0ZsYWdzIDMzL0ZvbnROYW1lL0VBQUFBQStBbWF6b25FbWJlci1SZWd1bGFyL0ZvbnRCQm94Wy0yMDcgLTI4MSAxMjkyIDk3NF0vSXRhbGljQW5nbGUgMC9Bc2NlbnQgOTc0L0NJRFNldCAyMiAwIFI+PgplbmRvYmoKMTkgMCBvYmo8PC9EVyAwL1N1YnR5cGUvQ0lERm9udFR5cGUyL0NJRFN5c3RlbUluZm88PC9TdXBwbGVtZW50IDAvUmVnaXN0cnkoQWRvYmUpL09yZGVyaW5nKFVDUyk+Pi9UeXBlL0ZvbnQvQmFzZUZvbnQvRUFBQUFBK0FtYXpvbkVtYmVyLVJlZ3VsYXIvRm9udERlc2NyaXB0b3IgMjAgMCBSL1dbMFs1MDAgMjYyIDM5MCA2OTAgNDgxIDc2OSA1OTIgNjAwIDYwNCA1NzAgNjQwIDc3NyAzODMgNTA5IDI0OCAyNzggNTI5IDg5MyAzNzMgMjU1IDQ2MSA1NzQgNTgwIDUyNyAyODUgNTg2IDg0MCA0MzIgNTg2IDU4NiA1ODYgNTg2IDU4NiA1NzUgNjA3IDU5MCA1ODYgNzc3IDU4NiA1ODYgNTEwIDU5MiA1ODggNTgwIDM3MyA2MjEgNjEzIDUyNiAyNDggNzA2IDUyNCA1ODggMjQ4IDYwNCA2NDIgNTg2IDQ3MiA0NzZdXS9DSURUb0dJRE1hcC9JZGVudGl0eT4+CmVuZG9iagoxNyAwIG9iajw8L1N1YnR5cGUvVHlwZTAvVHlwZS9Gb250L0Jhc2VGb250L0VBQUFBQStBbWF6b25FbWJlci1SZWd1bGFyL0VuY29kaW5nL0lkZW50aXR5LUgvVG9Vbmljb2RlIDE4IDAgUi9EZXNjZW5kYW50Rm9udHNbMTkgMCBSXT4+CmVuZG9iagoyMyAwIG9iaiA8PC9Db2xvclNwYWNlWy9JQ0NCYXNlZCA3IDAgUl0vTmFtZS9JbTMvU3VidHlwZS9JbWFnZS9IZWlnaHQgMTYwL0ZpbHRlci9GbGF0ZURlY29kZS9UeXBlL1hPYmplY3QvV2lkdGggMzc2L0xlbmd0aCA1MTA4L0JpdHNQZXJDb21wb25lbnQgOD4+c3RyZWFtCnic7Z2/rhxFGsUnu9JKKzmzLDkgsmQ5cWRZkDhBSEROECIjQIjQgYHwBrCklhaILQE5knmAEd4HMJcXwDyB7xv0np6zc/abquqenp4/1dM+P5VGM9PV1VXVX53+qrqrummMMcYYY4wxxhhjjDHGGGOMMcYYY4wxZl9+f/ny6Vdfv//Bhw5nGj77/Iuffv6lth0ZU+bN9TWs9OIf/3SYQbhz994fV1e1bcqYDSAyDx6+V711OBww3Lx121JjJsVHH39SvV04HDzAq8EVpLZxGdOCq171FuFwpPDv73+sbV/GtDz96uvqzcHhSOH9Dz6sbV/GtHj4d96htn0Z02KdmXeobV/GtFhn5h1q25cxLdaZeYfa9mVMi3Vm3qG2fRnTYp2Zd6htX8a0WGfmHWrblzEt1pl5h9r2ZUyLdWbeobZ9GdNinZl3qG1fxrRYZ+YdatuXMS3WmXmH2vZlTIt1Zt6htn0Z02KdmXeobV/GtFhn5h1q25cxLdaZeYfa9mVMi3Vm3qG2fRnTYp2Zd6htX8a0WGfmHWrblzEt1pl5h9r2ZUyLdWbeobZ9GdNinZl3qG1fxrRMR2eQkxcvfnv9+m/k6vr6+veX//ns8y+q5+p4hf3mX98hHLuMte3LmJaT6cyDh+9CRqAexddfornleUPk6oJwpACFOU0ZT2tNxpQ5mc7QUSF37t6Lm27euh2zhJhofY115hDhlLZkTBcn05meg0Znpudl39iEVjmP/pR1xrxVnExnvv/hf+96vrr6s6vR5ZuK0fClulDsGawz5q3ilOPADx6+WzzcwEYnpbLODA91rMqYTaZwv2lgo+OgTWOd2SXUsSpjNtlHZ+7cvYf2wrtIcDY++/yLm7duX6z9liRlROaf2JqkE7tUjMOQDBdjK6P99PMvMdrWfOomMkNxFxwL+cdWJM5RIN50ZonyBJOCqMh5fCSCAiJBVBTS5F79OvPRx58gArOBfZ9++XVeadYZc0aM1hmJQ+T6+hptRIIQ4+ctCw0Q8XvyRr+leKBITyaRQvEQr1//HUVDGS6WKG/j3MTRJJQ33kqL9Ykd4yaBGoCaJbXRv0uzUlfrjDlTxumMmkk//Tqjf7qgzqi7NOQowzOZZ6wLSE3ipWhTfgjVJxSjX0VJ1Bn4VD27jOthbc2AMSdghM7gCq7dcU3HTzRDtBF8Sdpdv86oOyMlwaU87+CwOxPVgJ0ahS5PRvGZrPpZ7OvFmCgFvCbkX64LvsSyoOfS33jZx0FQCtEtUXcJn0gqborqoSPSLdT/yDP2Qg6tM+ZMGaEz6mWgvRSHI5R4v84M2VRsMlvHgSF6iozcFsdYdipp1KWYEwhC8WGeWAnFCEgwL7L09oAPCB3ITIzZi111JjbhYnNAgopQS2dw9VfkZDB5p9CVsa21JxnpeiKomPIxbqgdyEyM2YtddSbKSLEJT0FntjbzY+vM1qwWU449NUQY7YZZZ8zU2FVn4rjHViGqpTNyDIaPnaJRayCIIQ43jdaZrmkUxSLn48C8g2+dMefOvHVmYAdk6y2nnXQm1kBX9XYVuXiX6vXrv0eP2AyzAmOOy7x1Zsg9muT5HHS1eOco/rmTzkArtlZvT5HpWeWP9Pj5GXO+7DM+U3xIdVI6s7XfFIe1X7z4LRkSkQSN7jd1+SFDiozqRQaiezPCq9nLOIw5EPvcbyq29ynoTHwQpT9mvAGdj7uOHgeWOHQ5IcPnNyFXccKFdcacI/s8P1N8Jj/eU66lM1sfX8kP3ZS6gaN1Jgpd8bbRTvMo95l0eQATMWZv9nweGO0IwsLnbNGik2GNw+qMHqNNnprLA5q2PAp8ycuICGz+UZESzcReOuKuOhOduvxBwTixK6bcNVlyp+Em64yZIBXnN+2qM3oqpgmjtZzinUeObhXjo5FyOjb9Me4Vu4HSTM5EiLvvqjMXm9OykDKOy6Mn0yRjys3q1hKndUPMc+kecbKGnCZjjs1h52ujURyv3xSdhCFF6J/orb16onWNJw+pvTiuMjDlntw2vt9kzpk915+BqujBNrr9xxsHviit4YBdemYW8F0tSZHpXcS+TL58BPZiZ7CYsYG1h0MUU0ZF6d53TLl4O7tZOTnJRE7rjDkvDr6eXhy9OWzK+wStQ9UvSl1rVR3q6EMi4+hxFa/RK1wxVDQtY8TBdUZDN3vOLXI4SKhrXcaQw+pMfBR2Bqv4ziBUNC1jxAid4Q1fLggcl4+LYxFdz404nDjUtS5jyDid2Zpsz+veHE4ZTmBCxmxlhM7cuXuvZ81euDpTeFfLCQJfkcB3GSjElxpMIZzSlozpYrQm8Ka2WpmeLhuRFJ9Jq94kh4euG9AHqdjDhtNYkTH9TKE50DvqfxJmOmHr+xfIRIbBj2w+xgxiCjqDXkackjxltbl567betsBhcAV4d9HJGf1k3WFDJbMyZoMp6Izar3IFtZnOEMfwMGQlvROHGjZlTMpEmgNDMtUIyjPx+1YcpJLrEnVmIrf1T25QxhSYlM5crMaEkwlBfFZnap0p5DPOnKL3pVUm4nt164aTGpMxHUxNZy6yPpS4uvoTnkNdweFLEBIl1FxIzbkY9+7IY4RT2JAx25igzjDExaAS6OHwfbsnyAmfk4H3kueHS9YopiJMx/s6rvUYM4zJ6szFyrHpX0OmWS9gBR047Lgx7x/ly1JFkhvxmqg+bqEY64yZMVPWGQa05YHL9zWrts8nBrWaaL/Pg8QZjbeqsXuPsMSj5PXGTMKlmY4zY50xE2H6OjNCbY5HUWEuworEE3lsxjpjJsW56IzUBl7HEJfjsHAJvp6uGe80TarHZJ0x0+G8dEaheN/nGKAXBg3ZOuDM8Zzq1WKdMdPkTHUmCs73P/y4dVbjTsBfgoid7H6WdcbMnnPXGQUuq4teFTyQXWWHb2nhfPNJjeLuH45kNsbsxGx0Jg9xQW+9lCGZAjkzVclDbfsypmXGOuNwYZ0x08A6M+9Q276MabHOzDvUti9jWqwz8w617cuYFuvMvENt+zKmxToz71Dbvoxpsc7MO9S2L2NarDPzDrXty5gW68y8Q237MqbFOjPvUNu+jGmxzsw71LYvY1qsM/MOte3LmBbrzLxDbfsypsU6M+9Q276MabHOzDvUti9jWqwz8w617cuYFuvMvENt+zKm5Ztvv6veFhyOFHARqW1fxrRM543zDgcPU3jjlTHk6VdTfCGIw57hwcP3aluWMf/nzfU1bLJ6u3A4YLh56/YfV1e1LcuYDSA19mpmE97/4MPTv7LTmIHAOL/5tn2HkV5H4nBeAReL31++rG1HxhhjjDHGGGOMMcYYY4wxxhhjjDHGGGOMMcYYY4wxxhhjjDHGnBN/rRgYeblcvnnz5pjZGQmK8OrVq9q5mDSon+Enuh/XttmJ58+fL1ZcXl7i56M13Jr/RMwbN24cylxH5Pb+/fvIwzvvvIPv+v/XX39lKZ48eVIlY9MHNcMqQl3tmRSuNa5tQ2QMIMoCtQJQPR4/fsyf+IKf2oWRu34i8RMXp9kskYSR4HssFP8BVfLZdFR+ksMTo/Me620gz549w14S9ry2zVtLNHVdd2Dz+pNGAu+XHgvd4H6dgb0hJhSpStcpmjdKF8UT35Er/M+2oLKPaFMHIVY+BbyprTOoGZ67Eb5okvOkts3bTDR19HSoDJ9++mmiM4gWL/39OgO7YmTZKv5BmkgKUibnQWkimqRJ3XlsRWT8iR2xey5Z2Av7ci/szgjYXa4XNiW+CnfhNRff1UdgTOYW6cRk86xyK3KFY6lciond8Z3tCzH7RycS1yvWbdQZHIUJIsNdzT9WF7s8+GSe+ZPOBovAE/R8xeMV6iUl504nnTVA3UZSLDV2pIaw0phz9FUVU7Xdc8p0FOQBezHlWspvjkTey8DZj//Q4GVFNIB+nZHvzYYTVYvQ8JRmBFqX52qRdcHyCPfv30fOdehYonwvWnJ+CDQEZKCn+D1QajgulOw+sPIZOX5HiZIEF+vai2j0LMaBxPE7SqSBKaQWT1Ce/+Tc8Tukg1+oEsmOVJWeP+USJ3XLUxbtJ8+PmQcyBtoAL0bxdO+pM2oCSF/+A0iugHETL538Do3C9+jnEFk+NqklIibS0SZ8SZz2aPn4P4kZfSH6BvzOq3Axq8hePLqaNqvxyYohla+cqLpY7Whr/Imj6LiU4gjPHT5xUJ3HpiSPrEYVjc5MTLaoM4LnArB0yiqSVT0gHVZvojPFU8b6Ufp02+JeZh7EmwLRouSE7KkzSlY3qrQpSbO4CUaLnCSdBQ0fsWnoYl3MarGwXTFjg1UN5FmN5dXR2dfQpiHjEsoPYkofYg5VJ0xKW6N3lzhpasLcGt0hFbNLTKJDmGzigwo8EVAV9nRiVpO6Tf6JTnIeuWeTmQc6rfgiM16sLuLxdI/WGX3P3fIencn7C10tK/+5j84sSvTrTDKYnLgQGt3dWvlFNzJp9cnPJJGIfB55RIu1M5Ono+5MPF/Fgbim1AseojPRbc4jF+vTOjMnoqknTnv8PlBn2N0uigk9ZKlHv84wWbgKj0q3WePFEd+P4c9wTJL0Fz/RmWblbqGwarzNavwEPyHjiWMWKx8FicMXjzYfJ0j8mdiLTJozYYQkTQ7ONJmkx+L064w6hkiqX+3zf3pOWbE+uUlV5+f9zppo6smmniYp64W9wQBk//wZbTVebWMXPo7PFPtNHDOJox8xb4qMCMoMXaYROqP4uljz6IDX34E6g+8cneCOiqPvSZaSyo/DuWplqjFVBcdeIvF0PFrTBN8jDuQmtdfVUyvqTBxQiuMzTeZW5ePAShn7JhVSrM9kL9+BOmvG6Ux0nuMgzKLkeycjP/QW8jSLXpB2STyB/OaFLqPDdSa5u3S5utcWO48y/uE6k+xLLy7pPPZUftLqm6yfggznV3bUZ14bURPiiBa+Kz95slv7TXn9KKvJWFBS2/F6lOwYj5LsJY/Ot5/OGrQseh35AyrRA4dx8md8JAa2pKcg4k/skqSpkUNEUApJmsleevwjPmiR5FwPe8Sml2c1L6ziKxEcTkfRofmYTZ5m7FIpTcXUkyrxKJfhMZKeyteBYolUe8XniLSjaoNl6areqDN6gjeeqZilWFLlWXvxELGYrDp8YlNPbV+un+ohxfpU1w+H85N+xpwdxfFkY4w5INYZY8yxyTu2xhhjjDHGGGOMMcYYY4wx5m2Ds366tnItCz0gF+cvHDVXfJrXcwSMmQGch9jTnDlRIs5DhyhhLz29f3mcVZGtM8bMBs766VnClzMd4trmSds/khpYZ8wx4HQVfqevrnkry9V0SP6p+SnL9TKzcXfGyf/kz3xmEA7B9e4erVZ24sNmSiQuNbxcrzeLT0XT+r345EG1kq3QEZnao9XavHqqjSvrLsOSvMVVC+JiwnEB3nyxX1WLEuRUIK0zHJPlSg6ahIiUGe3ZCuZctcHyLtarYMWFkfWPMqa1jt+siBUV889EtC9iKtvRj9L/caVlY0bA2cRsZRwE0KABr7mcAa1hAcbHJ39qVZZFWBKKs3ppw1zwRPHJYr0AAicIc+tyPc+Xm+Kk4BjtcrWyZVy6gZMBi3OQWQQlqHXata8S19IuQjPKtVZDs566zkUnFmEe+mVYmy5ZAJOJRKWlbvAfdqBizLhKAzUzKVf+j84LM4Z0+LRwUlGa0K254dxXNZCs4cDzGJfXMGYctHNerbSCAdsOTIvSwcZFz4G2Ry2ihsTlu5uwJidlgW0qeWdZfFqeTaNZtyy1R9q8XmGgRhH3ZeaTychc0ObViqh+zElc50HeiDIfoUbFnLNojzaXgZL6LTZf4aQ1uvOhGOZQxZf6MfOJzijBmEKylT+Tpf/yiroMy3dELygmFWPmFwhjRsOrXrOyeUqN1qCOPsnlevlcjS3ExiU9od0iDrWoeCnU+gN872RsWRINJq5dYgPX0g08aNSZKA5MIW5dbC6+pP+Tn6qW5H4Q22D0TLh6VX9uE01IHLwYs1m/GDTfq19n8pIWKyrqjOJw3/g2nMSfQVY9W9PsD7VFzRwNB//Qh9E1ESaH/2mu9C4oEepuUIKwF4cI2CT5Z3JN1NLBWgtuJ52hjlEb6ThpF+Y53pHZR2cSBSgmqB2H6wwzqXZ9PJ3Je2RFneHZ14mOKeP0xdXsPWfT7AMNUubHN5tABKIfroFH9ejpOcThQcoO3zWgns5icy3cZi0U2jFpWVt1huqkRZwWm2tmRk2LwyDNuvPFQg3RmTgqRfJ7McrVcJ1J3KTk5zidSUoaCxUrqsefiYP/ybGQgrzcxpg94PWObVBLTEe70vrV1Aet5BnvzGplfgoLnZY4qkx9oCBwnISLVO+kM2z+f62g1nHpthsrtLAb/qE3hfi8axMbS5fOIBo7j8oAew2Aa9zpKGp9l+EtCXlum82Wm2hsEwT8cv0Gt+E6w9ttKqlWX+dqeBJ5lb2oM0yKy5IDVunl+q2XcZTbOmP2hHYo+6dcJH4y+zjUEBlnjCBr508ap96zplsecQlfNtsR/SZCKVtmyxEXI0dvp0tnKKfUxuTlmEwwWchXYjJQZ/LHZqRXi/X714boTBPW8i2WlGqTVFRRZ5rN9Z8VU1cW/e9+k9kT3hqOa8zmz5PwaRb9XJbW711uriW73Fw4N9m0XL+5TAvJLrOFdospMydSvLiUbvRnkmMlC/bGxONPOgDJpuXmLa08wZ7cMj4dsMW2F0Itwm21WMNdtR1rLFnoWLlSRcV1hpOkijGVYB7fGDNNiqMoy9VbtyjssXdjjDEjKDoS8XWTW70dY4wZx1/d75ExxhhjjDHGGGOMMcYYY4wxxrxV/BdAuo2VCmVuZHN0cmVhbQplbmRvYmoKMjQgMCBvYmogPDwvQ29sb3JTcGFjZVsvSUNDQmFzZWQgOSAwIFJdL05hbWUvSW00L1N1YnR5cGUvSW1hZ2UvSGVpZ2h0IDE2MC9GaWx0ZXIvRmxhdGVEZWNvZGUvVHlwZS9YT2JqZWN0L1dpZHRoIDM3Ni9MZW5ndGggNjU4Ni9CaXRzUGVyQ29tcG9uZW50IDg+PnN0cmVhbQp4nO2dP6jj1prApTLFEl91WXhEXFevWDBcN4FAVNjFg8C6mQtvCYur6ybw3Cz3FsNDbKXigWvDA3PZYsCbrItU8wSjZqpZgQcCad5ovSTNQBi5mCZDCu335/yTLNnS/X+z+kI8ks7/3zn6znfOJ+lmWSuttNJKK6200korrbTSSiuttNJKK620oiV8+sXH1t3I53/65r5b+0Dk16ef3xFzIZ9+9ff7bvMDkKe/tyzXj7Z3VFw061nWR1/fUWkPVn76g2V50d2WuRlb1mff322ZD0y+/8Ryo7svdt2zPioBv1n4/mpz57W5e/n+I2t0VwomL+Nd8Nspq/9xSY0W/qJwpQPa8ZbqFmEt1uJkhiclMSJ95lMEfzdalfz0iTW+Zh2vLLvgQe+7ngc8e7uxPcvLX1gjjs3tVI24z8TJ6Ba4/8EaXbOK15Cx9fv35jnUGwfBtqfbrGWHO90bi9upGXGXZDo3z/2p5d6PkmHxrK/MU1fojW2ZAtnh3rPgzrilmxWoelaHj9d4fIA7S23uf/9oN/FdysayQn22UsMX7mwxHNaR1LJF7pB2NpJs+IqIG2mLeBup9AVZ5wOivBUNVH2p4Gd0XEzG3KPITKWjbfIBRfna+tdcU6Y9vKV6Ptch8n1/lq1HHbiCp3QgqxeNXYo703GlLLjJMxgl7liUv6Lr61wWIH+2vsxVe6OOKN0Mb3F3BUeo9Tuet9CxF8BlJtlMvcUW1bC7zlaumpjXHlaxM+UYUlTGnRlH8vjUNC+QqlR2I8uTQI1kGGMBpx2q0oKyldEirELHr+bes14ZZ75aTnaoNVTrdYctDGwngebaTVVcd51PK3ThqiNOuDmYlyfy6ukGvrU+0gvXnbvZx2kW0gB4kZnRlhHoorVk41lTzttdcURkvcEJGtNPZWNk7Shjl7Pjoc1tzHH3hILvWL4AWkg24mSrrKDfIaiD0aZV2L+x/ukXfTYzyHW2sqqubLEMGuexC9ujyD3SZ57iLvPSDfzlX6y/GNzzZsyGYsIs28Ebumf1omijQ0m3u0L5QPa9KMMh3xltso1LdRiT3sKLGZqhJB2cOdac8ZjuFqzpdJutXbPXkbvPSmxNh1ZWkgzuqogyzHN3aWSNtSFalD9Z/665b2nAuALOLDOHiCmdTJhwPBZ1hytxZWadHndZMS+tlH/5D0PRWAUNPuXZdcMlFPR7RFfHApbHKmotJuQZ6akeD1hzBlyIVDSuttR1kTBcZuZEiWkiBgfXBdBiMlfkuM5zXwl16VYO+M+tbzT3mRyJI3nArHpTMUzdkStHMw33KVt8zBUmEhBP9gNlBr1OHYS15RDX5yyUovnljfVxJfeeqHhP9FwudEq5LAQseauIm5tRC7VkcAdkHhHhO26M6LgDCwYKnfDoA/UugBaTUVFbimVyn4q6TIsGmNHQ/y1w32ZCRXiSu0cDjg+2JncatL7kLmsruozwRlRHi+52T3QE3wiqgb/8/Dvrf/LcFzz7rfF8kTHU8S73Hp1uROtlYA61UEsrXd6Uh6KcFXAoK9673FnBg3oXQEuTUXiOuye6x69aTafWPxjc0ehaK3qK+2rngOIKmy3HfUu0cUGwkR2k7iJP8OcDg/tn1os8d5/vrogmTbqLeqI2JveNgODy4CrlzhHR6IgUTQxeywukSaq5k4Jf85FVlYyLNrmDGqJqj4tmgpQX1kmOO5Bb+WOvl+Me7RzIBmFc1+Q+smQ81XV81DOy8A9xR4t0jFEKU3Oe+0LMWmO+Ryu4b3DHWXPv8SoxMgBGe7gTXxrcfm6Ey8CsnLuudk3u61GxpdXchSq3DO4rfTLLc7eMlHu4u4qsbFlPmNzTHe5QVbJPPHkXlXBHywOmFKVnZkVNfoA7KZaRwlqfu2suFQ5yXxgddYi7b8Zl7puOGNoyuDF3tTLPA1GhZjM6unhT+ee4gzYfrTUvMak24D4CBd9R0+ZOsm0VdzXh1eHOtqG3WOf1eyl3Htqj1cbX3Cmss7kG97G6MVeHuOt7ge3nUu5W3o4cy+XwxgC42cd9BgtIbSaWJ+Np9BrcScmMJKr93F2JW3PnVddMVdvk3qnH3VcBU8FtwWe0PZDjPlVbCkyzjHsBaKR5yKPcMC7hjgslugfzQGWylc7M5C7tmYVXYb/nuRM3bMzqIHdtrijufLf0xHbQWkWQmdXhvlErWZeiyIWHu2u/95SNtqLO3sdd6Pee3noVCyoYar293EmbjRTQYrKpaGqU5z4WynZcZb+XcMcDWhT19nGPVFxP3iS8MqWUuFNG6nctM/PrccdMyEiB+xujiPVhxFfNXQTdQ7imGJVz34j7xRM6Q4zPTG3AbSj2Pu4jcQsrrLlkVDvzhuDfBafddkqcCFXcF3KsYtID3NdqKt7IRCxQn7HoD14mbWpyx7YsosgH3Y1RoIGjDTpiiSkwGPsioQmxx2qsRL+7uGe3GeHG2hZHgjIywAToUcY4He3jPhOjh4EWk7m9Fe7c+AXuuKaI0HPfqXBt5LnzjspU7OvhfVzJndetHV/ujnmmrW2JsYbgeVtgnNXkLvuxs/a0rW8J/bAQd47oA9WmKaIp5c65daIOXtW10+VQ5+3jvtbL8mw3GVsXPR3BV/uRXHA59gL3cQ4d9mkl9+KOmbvD3TQ09d7mQe60x98Zb7KFv8HT1QgNcIF4MfI8Mcp9w8e98eEukE5vn+8IuIbpV6C4xmvIBdJp9wBGQEdAZ7Q24qp/M/NEFBRxqkIyzL8z3eoIKtqYW1GL+1aaxD3cJsdd9Wrua7W5jn3e29Km3izaZttoNuKoqhuNvfzD3P9fSGHdtCFUro9YyTUxtYRBPpYHOM/wpjRxdBd476FvZ5sbKzQEyO8D3c6jVaUke1NtTLfcSXKOhf2yjYrP9b178/r16x/e6Qu4N3wgl5Z7UX58XxVSKu9fPrtkeXU4spaWe1GeX778UD+r95L65eXzJlVouRflw3eXz17XHvMf3r6lf15fXjbC2HLflZcweF++qw7fkbcfsreXl6+bVKHlXiJvUHl896aeunn36hkM9TeXl2+bVKHlXibvn5PGfnEQ/bvX30K8Z2/hHnnWqAot93J5I+bL7179WMX+/ZuX33L3QIxnzdRMy71KcKYU8u2L12/emvTfv33z+rk0ZL5F/fLj5bMGRlDWct8j719pG5HH/nOU73LXvn3DOV6+aVaFlvse+fDDt5d75aWYSz9cvmxYhZb7fnn3qhK9Oes2sTpJCtzjIctuxDjcvXY1mQ9jPkhPbfumMm0kDbhnNH9+V0D+7PnrRlbjrhS4h3a3gvvwxhAFtujCuT25sc5sJM24k7x7+8NrIT++bTaFlsoO96Ai4i1wVwd3LS+sf5z+Z9Wzwncim7/92+/KuSfzIEjwYIn/pmHfDmMz5TJYcoqADxL6NwnTzNBJIhDOl8GcrsyDROIOJ3YQZpDrEoLSecDZx8E8lWWkUDZdhdClKJVDE1G5RJQei4j15IXwc4z8VVQ/1c3IGp/y4/LLuC9tZ+gAoKRrD7v2MrRBDO2T9J2h3QcEfbvfx4AAFJTdh2SY3ulzLBkIIXyQwi8kZO6YpZ3ZQwwJnW7fnsDFU4jgCISJg2VDhhBKpWGpFBrY/a49V6VSAfZF7ba/yPn1vDHgv/2XzKKVP1UvIFRy7zvQTPs0m9hxljr9op6ZQOtjaClxhjgYFSgsMwfoxiITGQhpA8wgIc2SODk9Y9sTGLldoIrJ50Az7Ypuu8DwrgP/d1PKDPNKnFOo2AR7KJWlzjn/NKspOe5Set7UX1S9jHV12RBvr6zEHHeSIBvi6IGBSLgvhkXuNDb7fbZNgB/1VzycAw2cLxmsDOS0dAAQgV6Oe5eKRVUD/TjEswtBEIvFtDwYoEZ0x8HVALoQOmIpS6WMlJl0WEq5Gx0w8qEHrtMFmyha+P6oHHc592EAQmBAEWMTT7k5Oe4xWCIhqHw6WYLqTx2HFe8chuTE0TExUHMncPl5VVwKID84dPrw70TPt+ncsVUCeR9B50IsSCNLDe3+sgmW/dzzfeDBbQCyoie7y7yBWw7h9/rwMZXamZfpmTTo2hMAkU5suxukBe7itoBL4SmqWJg3u7Z9CngSyKB/KqOJwBrcbZ5ARL7S0OyjglcJVOWG8q6UpQaO7UySW+B+q1LGHVRywkxSaH1Rv6toNNKYSzxxQEED9HQnMM99XsZdXDLnbr7XhqXcVRxRarY8tZ3a4B8u99gW+p1MwlOaG42KJxRtHuDkRtTSMMFEMNAvnNAWAGSg5t6tGO/cFWBqUoSQbUQ2iyCtDFWlTjC/JFClkvUaVC49HhF3+jcFJoJdgXvmoCrpymkXTHvup1NMedoVkWSg5o6zbnFeHWZCcyPWU554OZyCYBKhzBP4oY4Aawfrg70hS0VLaM+S7xFxT+1umg6dfgJrebDhuwhxbsCfCJvvApWsAz9dMCwDtEoyh2ydjJCLQMUdDb7Q3uUOyRPIM4EIF1nsiAz6gPfCgR/MHHtrAgVgFok9TNN+N5OlJmDcp6f2b0DPQPNg3kJwF3DQheEFU9hQc09xarughZDdD8HmiLt8AbXLXEaSgYo75uaclnDH5M6c+gqmV2GIxw7MmktUdiJUlpqBlUOVkqXiuSOLfYzctaRhKg8Mu1hbiIkIj2VoLC4Exvol3jGpk6otGbm3QDpbijqWobLUTFZKlho22ep5yNxLJTw9HEeuNh+w7HIfRyUyK2F1P9wPbz6BvX1Pm4wNZJd7+QtR7kPhflga7Qvel9TlviiB9UC5Pwqpy/2WB7zJXU1cO3JR4oQqCG6MNZSK3azc7FpRSIONsILU5u7fGfdqH1ANh1ODpcuB8vbkpIKu7q6qzX3bKcH18LhfQa7B/epSm3vu80q3yZ19b1kq3GckwpcG3LWzDrdfQRfEdME8wP+1E26e5p2DIl9QZglGUU6/WGaYzqkb4sCehCkY7XFAPjzeY44pX+YO9jqqRPgfrlJoGCz3aKcrct/cEXfe3SWvmzTVtS/tVDjrus7Q7qa43cjOPuMg4BNyKS3tLifQvSryDWxYloba6Sece6E9cXgw00ZvOMRosIKF8p2UYqFzj7jjJhh2GTr8OPQCjvo1b4X63Isv+90Wd77v0a8WiOW+8uApZx1622J7AgAciVod4P/UJRPaNgMaJneZb2A7y0w7/aRzL7TtQC5HubB+TJtztCmD/6dDh4KweMGdajfnHdKrc/f8nEQq6m0O+CJ3vpMFMeXBy2+ik/sNt0qcoXkQ6MQh7zEa3FUQ96ly+knnXqh21CT3RJgvodxcAxsmRD9TP5PcQw4VW5RX5V4QV8f17o4769wh78UoD57kzt42aKdwezpZ7sCAG2Z5R4bK1wjCQ+ncM2ZMwV2cxadw6gxVELuwc9xph7nulHt4f2ah4kYH4940d9Fq6UuT3EPBHQ5ENONgh7uzy90ucpfOvXLuycRxkLutuDs23S857hQ3vjHudzLg93OXvrQS7oHGfVXuc6m4stxw1dzpfgtz3O1510lvl3vuwzs85lUn0BzsW5b6MkB1GEvVHFHkzn61PruNlAdPcmdv24WdCNz9LHeguM/J72nqGZUvc1dOP+ncK+WufEk6Fj62c1HgPkHVc3N6ZudTmdfhPq7JndxnqTAklQdPzas0qfVRm2MMMmzUgcEdHXP4XIjOW+XL3JXTTzr3KriH/AQUeRwndqqm3Bx36uW6LtY6++/RjXHfVhWR536BjxfFqfKCSg+eosCWIDnslsL+UQcGd/QMLsVjeiJzmS9zV04/6dzLcR+GKaVbQj/FfXuSoscxFkYTdOEwzz11usu5c4Pcx0b863H363BP+rBUwR/VBOlL06PvFC6wB5tnOOPA5A6Iu/Mcd5mv2BxQTj/h3DPVxCmtm/BoArHwqdh8rIm9zHEHe9Pu3+R4N/+QwxW4626r3uMp2QeOzW3JuLhHmdB6nCin5kGJkEmuvYP5rJTTb+8Cv16sLBOP+9WQWtyNAY9ELfERO/GRRNfCJ1rl42G7Yfohv+pN/CvuvxsPL5UNM6Hf5/W8g9cVod+vuj9TIsbHmsZ14ufEUDPuHXMHVTC5cPBZ7btwQaV952Ki17v7pZ5fW+/ZNN8siFTaPT6rK3JPgrBwUJDgdBhUeVFuXNJgeFrX61KP+zUGvPEZ/V51rNbPVy4LlQC/mkcHnlVrXtUp920z1OA+rP8yxSOQmtzzmwXy3xrca+4y1OBuH3auPiKp+9zSQqWIGnHXn61cV2fecq8UQ027DbjPak4MOe7Cq5Z31ynu5FeD8LDo8SMHnUzx0KX2c3qRSrJowF1j328ImdylV0276+Z2tz+U3APypg0n3YLHjxx08i27By+1uZtmeP11k0603y1ucFdeNe2uc0x3HfnVhvxjevzQQSffsrsbdteR+s+lblSa+g9L1tkiKHJXXrUKdx3th6gf7fGbZJl6y+4uyF1P6nOvzdCQjUrj749YmFfJq1bhrstxL3j85Ft2twztBqTBc9gblegARCXGvs6BrsrtRwqvWoXbKMd9x/Mk3rJ78NKAe+HL/Z5VY16VcuixVlO/S69agXu3FvfbYHQb0oB74VPmjbi79bkrr5rhrqvUMwWPn3zL7jZI3aw0ed8j/0RTE+4Hn+LOcRdeNcU93jOvao+fUPTGy6kPWZpw7+RSNuHuNeCuvGrabdTtJulpOXft8cPI6i27G3hy9Hal0ftNCzNlA+6HH7wx51XpVdPc0c83KeeuPX4EWr5l99vi3uhRGiPy6GDknB1Z8sLdHvdakg8LH8FLNlnT9/nUHzIRfzVrnzTylbT773ulls+OpZmrpOW+XyKV8JCJ0sw12HLfLyOd0t0fc6Mi+i33XWn6vvamcQm1dnNa7gdkfDjLgvh1sm25H5JN0xLclnuJNOZe+fpThdR70bvlfkiq/tBflbgt9zJp/h2UZgO+5rs5LfeD4h7O1BCv5V4qV/juz6JB9nVfRWu5H5YmA37ccs9Jc9qGePXlWuX8luQmuLfSXIoD/9F91+03Ii33+5Ercu94e14iIOnlP6zfcs/Llbh7EaTcLvZsNNKfeV4f9u+13BuI3JPcuhUROvIVvtpf4Wu5HxZtv1e9SrBQMeqO+Jb7YVno1OVYjYVV3df/HhP35CYeWbgCd2NDsvyJbPNBSve3x/3EOr9+JnvfOioXI3X59kujJ8UeHffEsm7iEZ1rcS9/isbkfsjcfHzc59bxTWTzcWPukU5c/vKM8Q5a5YdPCmK+GROH+OWGkD6nHoYxHaQhKtWUTmNUsfiQWBKqTzzEAX+NGS8ldLjUX3ykEMwn5EyTnciZzoafVUvnfAQlJvwX4mL5wtoT6yzjv2EnI4h32aAE9XnLw/JFY+76WY6qRwX0lyD8ell+atbo3LLCLLTwN7as8xO8wwJL/C4ta5BlA/p9ovrrjHI5oxjBERwu8cqJGTIQRXFe+cii4JTinOCHOY/oKMVqnB/D4fkcfo4IPMWfW0YEkQekDkSpNeRpA+JCFjJtlZXYk1PvuuYbOV+ZNYI2BdSweQaU52fYAYgvwR5JjhEd/BzjBCfueOieQXCMjaeGw9HRETIMjRDFHUgd5yNLUlDIk3Ps0fTIOjqHBOfU/ccnyHxwhNXKsErwewR55CIcc89irHoj/r+bcxcKfFNtnPd4xO9b0ebkr2aNEhyMyOQcf5M5tgTbhOyOEU5Ck1Ka0aBFCfBsiVwCwkbIz8W5CEE5wYMzzAkuPcHAM4qsyj3hm2iOZUPAEWI9Sjkd32mQ/EmGl5cYYUDcKUKcUT9QxrXk8yuA74x9f/+SqOf7U7dudp/+mqvREbRnYMF/AOEIh2wAiAfwe8SwwtA6PrbCWOJERc1ag38yS6iTwAjh7jkmRhlGOTEjZzyS4XQ+GMSk6ZBjGkq1RpoPuR/jeE5xZokF9wErQI6V1VY0f7kC9xuWr/I1giEHiKEtwGhA3YCIrUGKYPAQjqEbltROwQyngRLuOgR650iAYe5WkXug+nEguYc73GO83TKcZkmtaO6B4r6z114hn90fcJZPfs5XCFoBzYMWJEQCtMvSGsAYD0XDzs4R/VmgWhijRn9Sxl2HZKwHrss9oOFMM/DZNbl/c5/MUZ4WKhTi6D6GwR5QS7BR1llK1gJBgcE+X1onT+i+R3mCt3lYxl2HCC1zkDsal3u4n1DfBNSF1+Se/fFeqVtf7lQIxiho8jPrhAxFnNugWcdwesJtPrJinASx7WTDU9tLuesQoWVYbfOcaERG21t1T0jqmiaCInexWB2IviPuJ8L0HajJtq7cq6b57Ked+ihdzTc1xqJmkZ5Y4imvs5eMKSMz7rycuwyBkX9C66YztI/mwlqSkRFkKoxTC0PP2LwpcheL1QFq+aW0Z+L0GM8HykaqKz/dI/gS7GStk3rgNuDCJaVuQNMYeGB3YDckgjsyLZ9XdYgsD2dGMI5wEVTgLiODCYlG/ZGoQ447L1ZJZ6l5lWSgV2YN3uL86cu7gbwrZdhp0RTTOA9ENxzxOI9FNzyhQXyUCe4xMMJZ92x3XlUhskDIHdeXx8q+tBR3Xq+epTxRW0dBVuSeisVtCt1zNMd1G16F4OOY5+0jq+Fm5df3wBzkj+8b1bJSqv82XllIXLWDnqodn6Q0x1RdVX+UDzsmlhNHWPfPexj1u4ch//k3DSv58CTU8+igkYoxsvjq07uE/vGXfz1cpwcvN8A9y34N//zPX1xl36Ch9L744ul//Xy4Po9A4sFAKvTzweBxvLPcSiuttNJKK6200korrbTSSiuttNJKK3co/wcLjk64CmVuZHN0cmVhbQplbmRvYmoKMjUgMCBvYmogPDwvQ29sb3JTcGFjZS9EZXZpY2VSR0IvTmFtZS9JbTEvU3VidHlwZS9JbWFnZS9IZWlnaHQgMTk4L0ZpbHRlclsvRmxhdGVEZWNvZGUvRENURGVjb2RlXS9UeXBlL1hPYmplY3QvV2lkdGggNjc2L0xlbmd0aCAzMjY5MC9CaXRzUGVyQ29tcG9uZW50IDg+PnN0cmVhbQp4nMy6ZVBcQdc1OrgT3J3BJbgECBIywACDO0EGdwtOgru7uzuDu7u7S9AQNDgkyM3zvVVvvfe798et+v7cfVadqrNr9Wqp7j57d/Xb6tsPAA4YJAcCwMHBAWT/PYC3VwCBpJ2xl4M9p4mDHY2GGo2tg4XD2wbg0384/0f2H5H/Uw24t14ALipgGL4IAY4OAI8Lh4AL9zYIoAbAARAACP+LAfgvQ0PHgENBRUZEgkf4R9DEAQCQEOHg4ZGQMFBRkBFRAPAIiEjIgH8UNHRcPHwCWm7J38Iqxu4ehERCTv7x+bDmll0yutSmvrnVK2ISeqCgVisDC6+mdhkjk+UKs4xOaFrp0j9dgv+u77/tP17c/6d3HYCJAPevxQi4AHFAzxALGOn/F9B3b2nY3/97frMemKGahP4fZL8B2v3B7o+Pz9/X3fNUs8+vrq6e7P63cjtbV1fPH72UWSBvAGpq6pzFS4fYmT9dvhH+YNX/Kuyb8l9y/wNqXd3i7x8MrzeamAmK/wsibwDf+n8SiRsSxSz/jZ39q1kWsPvl//Cxxdxfbhy9AT7uxLbUtrS02Pw9l5z6f+nQzv7+ner/aoSqv2H00FBXfpdLQ/2BnZ2i5ZKQsHBrOQO+t4sXHaZqk/NhBEjVNGyekXkJXYOZqx6VBpZh5c9OR9hm0IYPZYiByOMJCgoWK7f1eR0iFkkH40mBvIojCXPoRhWSeRwxD9Gx+erDmeG5+vyIcKqTapWJlUHqB4bMU/wDekcOs4rowV3MUPD2cLkaW8qkUH1VnULugRoqLpt6r5KTvFnrAxUHjp681Cf9du1120a7Y6ikk9j+Sf3eqXJOjlHrtRL7pn7SrNXxUovNGObpVaFJPi3PvcJpuimnYjwKn0krQHsTVu7J3HiUgYcNRMErQvvxjtm1++cDw4nvbHfY7qqNNgRV1bcYE8Y/Jj8FeYcKguZ2yb4yQyQrfOgFBgldeiMn4b3LHGHwqgVRy08dXpcnf7758vdo2W+9pF7arBAQyzmcWm8nLJeS1xSe6A7/yDRmA2+7Gdtqe36p3URbKRvFHmQQ2OsKNomy0ybXXXmX6B3Rj5lmg6JH1aKQrQI77y442KSR+4mXefkTR1Hn5qtHZRqbx2bO/nvbtHbI8HoaHWEMi9/aaV7Al2ha0j2BX6PT5C0Pzz5epzn/JoPSQU/Hlu/YWd6Wg3qLoJZgKGnpxHpxptFTDapxEKNBN+OBSAcoKwnX7DOPIjRGOi+574BY0ZulA7MtOX1nLghJU+brMJHli7C2/bHAG8A2lnwDi2h1yzqNYoDeQigpoSzRv2JR4jpJ1ALRvk40nBpSzK0U/1Oqii3JSAFy0XtAkECyECnt39Y1JTC9LtniMePTJI96XkJPGtx/I8oBDGs2AydWuYSNJlLlZ1gheiun0Si4HJAZrT7guwtzAZJVlxOTC+sSqLL6QitZCYXpf45ICjVNxCuQwk6v94+UkqXwDhy6OhUBMuyd3xmPzs5GR7uljx4Yw5sYle+7/M+YycmX24QJkhvlKeZbCg1mXaKWlrW7TH3Zglx6XuMcd/bvpuofpk4e3C43ehxc/+oGzPRMAPfE3wAc52+AW360N8DFiwIKaWe71SW7im0xw5KW3DHhbmjaBOhTuGlruBOLGqmaeiw4/9KNoWtberwpRc6js5jNczT3na5u6366PTNOwLHClwgG7dpQbFX1K6f8Tpii096B2VmM49f550+1QTkEIzHiNAZLrRhmpAqL6GHq2c6iZtRYm/EJ1TwpS7xMQiYm5cgCbRVIgRL2kA8BHs97T1uaQmOq5cjQVR7cpLJNtGD8xtFip9lK5sSN6NGZ6ZhqFQUP3f53kNbmlVC1I5TGMtqtduBesJTJdPVibo3kysUvt1Cn9sBLy2DK6M5cK/Uvd3PMENROIwQDxAZv9EDUuonrIGFc0yya7KMcwKa/8rQaayLEdB4ta1rqgT1V3iAb6BwuHT1lsxggQ+Ot7RI4sLExEH5CQUmZTcwiZhMYsIRSqV22r6RsFUJCa/jzpZEs5Be1dkjgmWlbJvXZNF+ulWmARDkP46+gGOmV5LqEz9aceJGK9KNRk7NPfEaMFVx1BL8vo9EigUomKz2jaWq4TtgbdpxTSueKsVUQu0uFWMXjrfRtXc0SJ2s3ayeI7vCDwAiiOUW8t9dtKF452WApWbqqi7xoILJyP9sP7rNn38pfb4D3hi3XWzclgjnLlEbDpKUQdzUaZmyiQe1Yup9d9vFtmWaxUUUmw+4sOXK97LRiEhBvHvD8p6ARQpkBKHL0ebshFYO13yg9lmK+z7eR7W9qs6LqbmYbChPEg1z5ZcJtMdN7mvuM6WJJOSe1VAEDOmpC4+ZKbnTLorfc98UrErHDeyd8HEHMuorSvCt63bixH9v5TiHRprY5w5Lj+AKT16/6xoP0GoeYEghxtE4e5rqqXoSC7zKyP7SpsQUvpOlEjEuXDBcisQozHZiIO7s3LFDGTteCq1SH5Mmc3AQhP5zTUiiHizlF71EpVIQiITm7shL9BVIvplapE6ag2jjhIFbN1QAfBMn8ZoJT/+EaB0u9ZjbJktnR3zMAfbM0C4F601BoGLdEFXeJv6JRWhNLADjlB7HxgcQf4z5qIzqsARs9Q+k2hgUD2avxqPhy4fhUWkYLdAmFXRs0FYqxtCvoaY7uV7Uy/F78Hh4knePlhOVrgpNCHowCbR+7811aeDVtT62Kxhbnm+EgW02xtWr3FNYopTzOQRZh1EWOv+uZI5hCtR2zLAuq/HLsbwCsJDpAnEgzmZM5wqB8IjToHTO5viryr8Vanl85c32Cehq/QUv7emFlIc4j7Lxf7YZzdJOdRjRMwylKqPby8vwSs/OdGx1UBF1k38UJCmi4M+6x9dJ62XR1f/wosvEzT93t0p3tA+nwPik2uUluIZcnb6HlFr1QdVJxg2gPu6J3GQdPiq07Ke3Qiwh4yUrJQYklDmcMm8XTRKFdOuKQhAb0TkVc6OBKLZQEJoDdwXZxbJFCrKFMt9mfPtaA0TvBdR+skwAa9rSIuziwgVlncFd5TtY1iI6SU1FRSR1M4TtF9xyyJTmXyE01PEHUJ27KNcFjvO5m7xPWOpfOfUIUXk/KvkxrHpwcOJ3E4nwySpDU4gICFXRYVJPAUDAznK9i8KnniF2NzfqdFqh4ATkv6gdNxs+qogV41fecK2iM5DE07KTwwwmOlz89ahNxqXQ+B+WX3hDoyz4Da6srvYerZWetNYsYpfhR0MhcoZM2GnFxIPRfY6p1WVF+ydbojD/gxsESYQ2TqNHw0n2U4UicHYZ3r7E3TSK+viMjFQEPJA7Vf9npHEGgvuPvIglNZGa9e6ch14sJOD/C8qksq8opvttRjMplY7KIjbEeYyfyBt30W+e2s/gZqaaa54i2R7TOHGjcRVL0OdtM40/XI6CCFoKa0Zq63wHj2zMAuuezOh9Q3De9+bWLNaZYiuMrNNEK0xnWlAvkOJwINEW2usV7jmrv4cJgQb2oElwwbSOS946YCKhVNGF0WGf/Xi3e6CtXuvctNuv3TWfkf3TfAJX7K+s7Iys3Bm0VBl9LXczimp9CPueRm/E7Z/20VcLlsQq8zsp3TiXVVB6cHG5pGjW+EUxO4XpC/ElJ1QVz+erf55z6y0e4T6cmOKcd++Tx7usH7K4JTxB+IExTcUqHZf1M6Oe7/bUc1dF3/qDqQdvZLF8KlxRwtpR/ZzLbClpbeLFXb6F01p21vVJcWiKEBKNLNcm17MpAKnCMDelyvH9MaD5SM5fQ5litAs4EB5DnV7nOVxEgt9sUzcyqxqRihCogFKBQF4n593roZG8mYaYO+8D9uf00vkfJxvv8pOnvX451GErewuWT9aaO76jYG+A1kn/lWUPtf/vWNEr6/0ZaRlD3ahCa2PgWPui1QOx/Ps2fbv5r0YFM0lTHkHRvMS7xJ6/gV5XjIM8xN7GpJyKJ7a8JEnv+yfC7oGgrBZgxY0erAA2sEr1hCPpu8HI8fbi0sucNEHsu+FTtazBkkus78+ztdr/hEIHkiu0ysyqv49RxZLSqUI404DEshKOzbRNXZAcr4SGgwEeMb/qQij/WEbkQF86VksI4y+7prmEDtJWqVu6VbZfxFcxFuZqq9KS3vtkvDy9Q/mpaBI1qzkqZz4oXdYMVDcIGOegKMdB/USQ7fWoKYvtDTs8qU662VrZf9Q4bjfvpYCHpvORQ/IOw2ZR/Mqscrq3+D036fkwIG0c0162x5MqtW9mpUZUkZwKDXEDOfPl+gd4YA3gMpSgjOBmfHkPIEMXKa9rjDTDEeaFybtn/1GBD2ELiaSav/gUZbdiBSnuKPZGMsoSjniLekdTZzzLoPJxMtB4baLZ647OHFvPjbkyh6uEXwr45D/NlFrdOmYj2CUolU5s7zOzL659HPkEhJg8F0jeA93e4ZJskgkNMA0dLjfU3wMbTRzHD2KXzGma+y6XpBoOPPBMDpi6mjhcOS5+nwfhxwnZ+S5kwb7Fo8TG+ldJWf0wdkoQJtKCyuvUrXhgU2JQqNNgPyWKzMwzinSZe07fvS/2NrRx0+GzGzHhUr2jEg4jHyG8CkMcLApVLOLVInfmB/FJBFgAGNysN7yJn6XhlkKwj9M7t8enFt3Dt4v5PV0s50z5bMkf53oCwoFn095QqLWxXRztsg77YGHnxuRjTaHqeXe560DwG1SfTeLJPySFMH23lrdgVVQOrkpaiIndIqALt0LWivmJ4ObrTe7XFNFfCfcgqYOAHQ4kfJ9LCGgvJZ3TlIaTmwmnFNCYc7BE8TZKSuMX6sgYZhHxy6e+0ex+X27Qayuj0EiFWjbnhd61NiZZZ9apPBobg51O8MGiZCt7DrEyheZHsIdCTvPDMOLm84kI+kb8ALhb5g1Ble2aBrRi9d0KRk9ZzPYT+DXB9tPMG+KSc+3q5SHPX+UrQLn76z4lA993giVD84Klr+/LL5UmFpLId3LCvwB/B8f1XcKgNxrCsXTZ02G2NLwQ/edcK+u6Uzt5GYpn0EyAjt+jsIurYABs8f0wugnew2eRityQIxxbAwPQSC8waPB4hvGpe6hBLIKKMM52y6JT8rSlgr8Q12SBwDeHnz5obHt5bXCKLzSnnGbQ8VHtCBLHvtXUNwWC7NgX4syjC37O2zgOdOugO3O3Mi0q+qsXeetySXNPl7aP/fZnAs92IJOWeFNiO4WKvZ5JjLYoA2uAiDmHCgDzEfwmMFWXUCWxTRs2/tU6tYUAFkB3P4667Id9VX5jiRai4dfupoZlrOnOPNekJvNT2LGzs5h824clWtWfzre0gUtLFRDidIaDTCIhef9BqYkxDChdO2QbQe/QvDfLPUAK31irZpM/7Qq5z+KyV671cs8lcvqwrOGOAnnSTyducBtda66A/4OW+xvXs5Kal6t69BEqRkZuUqhcyS5IObYgzcm4XhR6X7DxVP9f8vq2LINTHCJhy+BLi2mB97pvRdrnR1RM7cnJvuGFjEOgpilTXKm/lpKAmZDOtGH44qcIv8yt+rEh4kBMr4pm4jjBPWzlAPpBtX+cV6OC00mg3MwlBytm8Xg/2RGF638ZG+5p1oV0q6my/iUEtVzQxvTiVkQly6rYOq3VSszyTu1DuorBnySKbx5iGLN+hOKrueEIHyzFVaEeGRBpywMnHJmBd/h95qkl57g8+Pr7UGXc/s/PWX/5F9aCnbIFAjsG6o/dGhIll8eDlNAT9LWxaqSLYagcenuaoOV4pPG4lfz0bobVHXGtKskpY5jt4tSDDv+vVGYNeVrWkNhYuRX7cnV9CoWyHCRp+bfBOjqRSLPjs1h/TpYKChcaLL0/JCmCgzHIIkw75gIkHhB1sa+TUdSietBV+aGOSdWNu5UwO5AgaEdCnD87QYv5AGvpjPBnQy99MIEJAbKU3i1egI5LJqqCWVbz0wRGxpHVDwVpgkJhp/WNzFAuxt2vsCXKOYv2Es3FWOjZoKrFRqLhaZSiJC84L05fEJOejMF9GwQ9+sexibmI1VZSXX11tct2qvoTAU++1OlYF/nXuAIJ8+ABp2AeFzDhcVjFmBUASTrrxVAxikbKVAKlpOT/6/CiXJJ+VOoCY2VV37lXogTc+/OTMtyxNSiZJk1uH5PDaMWOHYqROfZpYZHZximme387JDZ8w5Z9INJgC9PyIyIYBtfoqJhwU4sZISSHaNmzMOJ5ucEdOKw7HkYRC9umGrw1zcd/BZmcW3fDjEI6tI6Fmj41HzChifmut/fgFub46kdkcpI4Dbs0Kgw/BwuiGgyG9m4UDj4W96QlbsU9T3bFKVf2iTTI9g+sNQIyXWGtDcsg6gsXNWP/ZFft60GqCtLlZnndCNDy74hO1q903o292Utejj5NtbJkiEyIZqqkC7U3CBXy3EKwa1FgGKOHYO2HWxlKCfOESAiMUHxre+04LSq1xZ30GmHa9WahzlfTexLdgCOmMpRtNMtwNTlshbgqZYAoxVJSNFKgwrq9tqEwCvncnLIvhkD3/vIA/3Tii7sJOLNHsZVu8r9qA6Nc6DIyT06TIm3Ibn9c4TOlPxqhIxCSjkPnopfJA+RJ2+ZeRicVdUkjCG8Oqoe9jSPZtqZfyd1cbPMFi1TavouO4AohzUyRhjqppOR4juwyzarFkPxyQiSYPcyGGz4i9oJ82ShLVwVvjDeDzod5jeVHr4cwzNCrVPkYRKnTLxhGswSuwW8RZroQLJl8xb2mItfV4FyTAlWprRTLGbr79QGNvRqXTywnbkt2SuLHrGbvZoll4+vN4nyWRKiR1HdTOWv9L/exsLH/QaqdGsDHwJrZfITJBp55n3G+2WSlWoRt6673PFiSHQKkC46Qd1zmlG3KiIhq5PitOLMWWtAqXpJmE4AnznvxNgibKSyyvy52AwHxBZQYHdUbwAs2LFZubBIwS8eCAgBMe7FasINXjouPmYOMSc5UwgxD/NwDX1EFNEMIg2yqrAXlBPF/EkJE+UWuE6Sdj7BVqP1pCP6qNfjFHDM7nbKpRS1VlEAEr5/hBqww3mc2NFHkUkcCvXfzWdI5rQk2kSVJjXpphtNZenU+UVUYxnMKOpaAeFlJGghit+J8ZXX5fNlXj8qI8hXImGVxAC1g0TKjhuK3jSNxBtLhQLqCbZfqoImyoAjaYAD9KVlZwIB8I+dw6KunpNgAFOmOKuQYJEufU7fe1YeAldan0KiTT3yRn6hRzmWhWCH/QhB0IJcmmNlkgkdPWsIKFEgI1LsSejOe6IQVswA+lqXPdY6sJBPU2oSVo329u2DELpVXcPn5C1vccYbeAjcSlzK8m/OID3g2jtfElPeCpFzHKnP/59gagqujLRLDoHKjSscbN5vo3iCaY1THbCT3SR7jJqeAu9MFhRBCjukS/NXGIFp2tifeZ5y7PkTCCarDie8XTTI1ysH2XPun1E+bhiPq5sKPszLs1BVB+BTWsda3MHK4J489BaFZfXP6vxXL1VqE3AB2jxI/qrOCRrCb/qwpFAtXi2RK+2Etj/juDbzMn9vvvQc0itCcXqmo5Zmu8hBUONtnUkbBItecvCVJNTbwwWE+HKkuV9AiKZpFN/NaioqUmf7F6nl2fM38feYR6CkmV8dNjz8u1ftDG1mvu1huA2reVXVxm5o+YZlfH9i1Zt9PCGKTOOTKI8ma5qhO89LnQJVTUnR2aAxoiSPtcHB0d/a5F4mxR96fm/kIt9LuFm72LuYernbLUAh6aDqT6ovq402zcXRk7wkm/btMzLseg4eNIxmr2kED3Hq+uPrCKZaWURU13Mp/eoC7tlVf6KF7K9Ti+Hcunr68YLFMS5l0olaC5naiv4XxiSLBJawd9tm9qPK9ZpRyLk7j7BbNC1QdHhrcgQ3lLnqLXsnuRi52tRpj118PtJMp5DmLUwoGS+JHWw3DdW6KM1h9DL3c400+MNDEDp3hLpL7HUbJPtuU43kMtszzn1dtU766rypNMR7xwsWPR4Q8OPtktCU8cdIftWu5jzCeVlNbpIsssvwHqwFSb2GHH5hQWMVgSF4s79BM3VuTrpjlsq3lCos2f98qIWX6vfjQuT9Ob6DpjA1TqrVgRz1E1UBpM8QxjunaQcNyoa4kxgxdzuaSLZcxsJfQi4lO0iSY2PpPHTHPfCIeQH78mRTPKgAL7CWcgdtEB6uWIGEcxMZzqTiH8Ak4oKadE3gkttGQttlrR9Yh9OeZ5RsCGEGlFN8hInVDgY8Tvcrjg1jF6m1gSvJRV/9FDKIOY1pHjWIhQyhiNMDwbXeA07QyEu832g/7kPkUSyBQLPUr6ZITG2B0SP5e/1mTMWKy/3cbjaqVOxBPDjowwYLS9zM0UPWUSxYtqGxyjh5vU0Dxqgpc2UY9yfBDtv0vKifUu6r2dNWGzKG97nrLU0YrAHViFU9aTVY3KihEAfb5BS5KhGjLlLT8rEEcPltZZGTVT/aU34POdnxxqrQAdYUodadFA09PNmAUyD7ERc6hKmR74gciMJMi3Gw6PHw8D1BfiN1thUzyukN/hkQop3uUR2cmo+wX4qH+0SDXlBo8xhRjJHzrYY3Cu4gqPfW6P06j3ruvnE8rVf8DJ7FGVYdsW/c2sw+uiWFkLKCILr004ZMd3rcJQguSKH3m0F86OGlARxDtLncbAbtasV43OidpLBEeal55wLERSKoiEfkMufrtmWBuQJYr0fe1BHm+estEppVVLOrjmJzHBj1YBYcJrEIHbOYC5CnNEdBfa0YbU/kCNffSUFkGC7yo/hzve690P45z4rRGF7sqAeBYBkOjJNuuvvScwho6noDyPL5QJhXbK77WCwSHE0IkW73DanJrZ3ZFaRS1G0eC+dOdne2qMlU0GRgfIFc6YHsHvv3iIV/na9lALbXZGEbIUyNbaCiMuhi6wBnaEGvnXK0GLS3ZztGvEIeMgslQvfpsv36b+AhQ24AJBtsI19BFOBIh2yWsbDZV/OCgQoQMd4I4W7QGDbjh1UOQUYt4H0hoU4+uWW1JsbqhxYKF4wE5456iUDDZg8tDSqa9rZuXjeOy0qVp8+vhmxjQy7+nxDpDnEobEuLv7el6i2ghAjk1CSo6fMcVgZIfhFn/qKEx9vKK6eMfoHbDjf1F8m+u68n+J7tOTX6BfANenNv1nwvB/OBoBDY2Navt7MSCygaoC1Bn5CiMAAzVKig/sDhR5v6SCPD3XRGIWbfADkQWmKqvwZDVELVxt1b/zT1rjMjUlFuuIOpj5WsWnSKjF+eyRszlLbS9OH837xWWxH2ZdUTXueXa7TC80i1pFcE8I7ZjzCqNFsG/Ph4wkJAVMKIQFx0BqYuQXi2xMoeZSPx6ff8MwRNi8Hj6X45ETSRwVEAR3+byaFFCESdy9pHNkmZH+2x4x9lPVcZiv0In8l50SuLKYeCcE3DCQLaUkOTmxYz2kS48WC/VNbo2TRRwXqW+zqtjDeQ8g7Ac6GXvYzfXO3FhE+sqNLJ/x7VLbf9heT1TV4s+xnUUDNTrMN7m1u3+1RzWDxtDm7qwHUeDXFZCgjpxEiUOe1n62RTNEUFH3cKWfK0mdBvu5+dG8sstTowapO6wTUNzOMaB+0jHbdqN4+CT3rTQgQeNBzG7Ky9Yed6gj+MbopsxcMWSonlE0qU4xL9GNqE36oyHxV9AdZaIM1VxSV/F5/BpcaYGTnZO0J1iVTcEpmEzFH+ezl7r278yHbvNshZV9umSmFZRvQ1sy3+QgXFGYh6ciIaOmCFd9swXTzMUM+6n00veXMQElP/WVS5F58bIYrwpR1alkTtI2VqKV9kTYmwazl3imBWK+EC8dERPdBOkuRvEWKc1H5fvCQWHNUlcRxrzaXk0CbWYlAqoxK1xMGUnIBw43bwCLq9f1LYSvjXqaT6gDgb1/bSL1rnM+cEoE6M+H/bQ43t80111O9kL+JWqgROxiHsfVySRxcrxpPEoBZnDRS7gvpSMS4FCPO8bOvXW9wuAh7Y8pW+9UMlHA6dVH4Ie1+gvZF4wgFjgkwYbluT4HdXyhsc+zyDzexwkySl5llSINP91GHSMXz+KGdEKRjXoONic+xJocmxgPh3/g7pLdRJOQavnsx/B5Y00PYFUTG+MOnN8gnatx5CNm4fp74povxp5lal0iEJWaw/0JsvvTSSDvg1M8z1osBs2Kz28C70gfcvyEgxyqNUIGbU49j0OO+nDVpD7qLZTTUk0bK8Pr/djiS0WPvY3W7u3+G2znifrJr1MMMlH7Ojy/ndMLZuaP/xZ9PtLsfCwyCxvDiOd2b0kZ5itlkggu/xQ/fYTXNkYvFbLBpay54/vHUGh6g0KRVTLgd5WMisuJm8awiW1yGKpT9EBYfEKYgLX9KMkcfGD+JYzwFJ+B5eqTHsbXPhZkVu3JP+BC9bgOpudghGRo2+0+ybTupEmmSXhQCgq3mZBZho1oCc1tsziNDd+8NE8UN0KD82Ag6SdSE1xBHESa7ZXuB0776KhRdeFOoXwrO1mcaL3IhMFjnGLAMLwfA/oSiQpikHmajnFbCHw/ST+54TDN+mOOC1HJx5bkGGmKgvEMrboOe2xPT+m9wWK65VK2TlOmEX572WVarsWK3r0m3eSQCuCCRN4dcrZT2KeuY3rzxq7josmL+S3CtTOa4z3n21Ub0wQwffkimVOAudFEHaN/eDkJiBwnu9FZGzP9uB6VS6ojc6E/0O+dLDvIPFiX6uuHN0D/tHSH788Xp+fcZ5HUf/lNz8r5vTeHzfYtcWdL/VIKkw7T+C7FXxrj2g3bHt3sZe1s01y2IJcz/5Aeu/oXJOXK+xbBVpFaqfn5YJPCyoLBqano6LEY3R97PxqvF+ilgvGgU8owaBMjgZRpEZHxSJYROjMsjSvkDRBR5TJfXa1yHnVEjt0KXkf7HntahEF35nkOnl1zRqCm/ZVhXCfI34zL6M9cqINPK13MIY/iXKV2Qy8LsMiwMqOgilyZjtlWbhDgNBtdaEtps2nISi7G6IBwjden/+mMWv8U8F6BJr3jKzXFuA6dfCcKkP0osqdDvJkZ78Ch6uyCmTNcmtKZftht3ck7Hdc4uiq4uaFVNgM7rtfCCcV7DFYjH8ugjCi/ZezMTW+n6ZiRGoW6rb2ztsqg0C/WplaIzvgQohEj4rME+TBcx+cNdDIYSn9nC1Tdcl7V93idfcZL2HLWbgazLJCBC1+dAyP5sN7HKjm6uw4WsEgVZkGjTHAAYcGlRKQWWuKpf79962GqXLq9P3+sjDjaejGo9W1peune0Lv+0/Ol66Vfx2NxV82nTV33vkM3j509rZ+rddiT0X1iBlu58+MSLXvQSTCpwuPqAFP82IEZo2Yy+D5NQVOhPJq1+U4Vh6/ExZ7fsEVwprglEQt/IGHL74utIIykBn5Z8Ol6EokxtWPSGPcrC8B7E/YHVzBQSn95eDX0J9lZO5UP7JM8LvLYDi4fT76H+lIpMSVZx7sFDsqlc7tgGL2xXDTXJm23jGmZ1LbLkaSTqexNglnNoHZBqPuYz3m/JL0HbrGfm8GBpzEcQ4oOw18GZwKIZXJzGz14b/NHshThMTETiy5DWXIC38lcJdgDk1ZVQ5qEh4BJFJbkxIjZAE+DvttqxAC+3KoZsSMJtRJQwq/TpoUOkz5w7zcr+9Gjws+hjyYILdj54SNb+WPFN+MbBQ0oUFav95MMbWods2KpffhQLx91Gmbm5FqBLSZAtr65EK0rLzCsjUaeTcJdY8j9wSZasg+XznV8OiLYOULWMKfo95ZQNFpEsm7t3cOruGLX8t1fu52t7eUK/1v15xSdm/M3QNIb4NeKhvVOdnekzxug5g0gOOI9C3WfNN8Su3gDMHi75GxSLArJaB22sZOTJFj7U2S34O0TjkhcjZuari6o0d4+LqALY1R8lcJTM1VSYqvindZ1TizkaZqb3mLf/GBhFdTtUYo8feWTY//7itZbJdz5ef7jvN7GcbRym7eoxotpCeOw4RbCtDNkcX44Opon/48wh7FtnXKI+M8ugiyPjgzjx1OzB+R3LpAb6Ripm9Cf0WO9DLRcVrMpuQQD+n6KCY+J8a7TNawA1wlVp5Mq7cQMnImFygX3aPd9QtyOjeGvrV6Pp3Tu0P5HXc6zbqpKrgDWYxv2EiuedH6SDNBSrGpPgXt4I9+AFkwul1pnm0IhkYe/kV6euZ2+Fk5fsFPKd+5Pt1jTbcr7z3jiStZmXtDMp09ofa1vgHk98lM3mdtK7atNttc/L0LwaHTwfuEfnAhpMgZq0vZVmvaxTjnpwD8yKYwxfSlHYDadrBw287fssn5ZDy2H5Qv0CvWSNIa/HGLHEwqkoh4sCEuBUtFc47KGPg/338SpqVOPupBBYp/1uUaEklZtv/4Kyi6Kbe+CTTmPZJmrw7Qpi7SvdTB1F0lr+L76MCdI3y6vhyiRuId+/EDwaSBTPmI5FLdTeGjzgLNpqZq8fb2oThDry4I58PJYc0ZPgNWaizfP1VzVxZvECfYjDmurswnBU7aMk3HIL0wT2JjCPpSv500lcIohTIam92OFp/NHzpfWh4z6Km7cWD3BGG7e/HwhYBLztrsrNkGCv3aUUEBNFLva4VFZW2ErOprmx17SIj/Bdbe64f2NpZ4PYuuvPXc9y5l/X7SaLYLd7jaazp/en9z//ZaZNevK+G2AWzGqz7PU4GeoCFPgTx/5+LrFqU/oBcrd8fhjvx/SpMOWNcbx2WnJYYrvKpHXy26t/6WxwCyoST8c0QdV5mO3QaiyR4IzZXwYmD4aD6puM2imGqC2CW1joCqqqolqQiH60o+gxEGnTCGC2stEQweT7GpiZg5+DtXXegOcMMWJvwqNiO28Xr4aDp3ea/kL8/76vrjN3fSO3u6j3oGm8xEjniTlsDLgB/OGTbP2xHsrLVLskajxnvn2BbzwyUE2ip0vERI9l87AGdqErHCoWyBeZjH5/gafWfK2HJ2rAzR2ZH2TmppqKznOvzw7vZh2YouSIrWCVC4uz0Fa8mSZTdUSVSEpEcJlxA6P44x2gnJ4/jP5S6+BLdbcGGeRkYaScrZ7LLnr6FhisiuNEvOUkp11jfLE9zHWuFLhvGDLuGpjf9pIGIfUp10eJE44ePXzq8fTnJXHP99FvhHfedtsZ1k7vaR6cm90WlQ8RguPDjHoE3cmCpOpsFoOpWUn63IrXuMvdX7G42rMV1clIhpIAv49SBWWMUFbwik/SJQggv8luqf4BrC6j86Nw6yZLUfuPBEdI68RRB8sTdJUWz1KCHeJjFt2jAAoGg8Mlznkigsxquv4p6YimrKnIoIPeu0WkdvfabkW4lod9exVW/ZxJ12J6oTuWUJhIfUBGkus1/bWpZ4YewkRbBdFBZqP5eNKrF58ROp3dO4uX9W8c6omdk4bzFVM+h4tOI7Sxb2Hr5C5cw5Jsfx3RtxkETXWtS+N7psu99qRkyk7dB8ufTU6Zs2ZlmpHJ7oXLsf+xGN/u3F0S/wjLX3LrE76lUf/+TkaL6vkdQ9nM49ZBR2g5+TtrCGdYYKf5WWf36UYlszBWRFo6JBwTVDKcWD1qpktFL3tkGg+XxXI7UYZbNwOLomdOEggxl014XPDW7FgiPhsnGZ0Ru+bZXS5qXFFgK/Xl4vjHGPglj1DKTT6BfbaDUXWbKvpPTDnUS7zlZi+6AKTNOJR6YUpSalv0FTae7Fkz02h36cgh6BR+EOJXdbjZXJuZWTmL/s/6H+zxG33cd6/WAeaK2mab17ZBtgEj9d9osGfV4iL7yylYUYlQoJ3g5oI8n2MMvWwK6HwmyG9dTQyz3Iaqsrl2UITSLS1H1La0RBZlQ5XDYvhA81EFyoMe37C6U0ukZ4Sc1bQjfNXjI3n4FluV8OLuJ/NG1nIjSFNccKqxwWtUoT/ca0SPmx6lQliwIOO8qhR65Bqb0sPkEym5skGNy3dsxKXoBC8G51twaWJ7WnJdru9sDSQo0OsVK+p26SKZLjipCg8dsfvbjv7yJyddKDqneFsjta67Xja4VTqUW16Po39BM9edWWwAovBwpJ+wBrR0Z3VHSmaiF/5orwzSJbL56OMSdrBSB+QsF2uZT467evAvOAknc3CGCXJIt3rZhixUiql/M7dKWlgiZNPbh9tArvszv8rqzHtSANJ1ivdnrd0zTtGaOowZeL2rljNJ7Kv4OvxPYlAxzAmOiepdDktrAHHqLzt/qZzvSabEBOr1WzPPtKXoPoxs2kSF1Kz5QJ03ikO3u+ywfWTeIyoz59au0hC/XB9cQbZUDtW3VurvTD//Nn2Rbej+uM3mtupfifL2nGpN7OrWLijTIr/EJFTjiT5R6JaxypWWjcZqUgT6pJUDDcMxxOo6rkLtjrQ1mTex9eRimTi6g2TXr2p0vY3Uairshn+qfCeliJ0b/T1O9mkZ9WjgPmm/CC8TnpsPLZeAkkKET15MU6VN6mFswuFYn8f3PnM9QpJhMXyEhrVXm9cQ6VCq1PokEE53dbFZ34MrZxEimaWJF6+r1V+8VyTDKpVxu84TP+yMILz8gGDFTEjKlbCVprIe7e8g7qVfr3xjob2mikhJcglw4yMw/cgkOatHebkFHOiQBNfk0ApB7mLiYkXzxjIvwtdSg4cTqYKhiRzf46T/JwCCOmLjmUVJbafwSseKhZeAhLT7KZPLO1z226EbVNS0b4BvrlLVc+WBeU3S01QJEkfjOvOVjtKyEdyVymhzQnpBEic5VfPJnFN6YFvCgMKOzAG3V6kDjjQtqaibEI+T8bHRdMkVcvw9ClYOz+byfOP09xJf6A6xOKo6iisw9JOHtJTGfbCu5Og4lCneHjqmfn51OGbW9OiMCfa2WHEl6jUUFixT/EvF6kzss14bLxKgBbmljy2c9B/uz0tFVOhceTYGjLmRIST3I5pbEJE7cOEGdTVV5d5CHqB3wCDxJQ28Sl59WFLUUajXBVoRgInhQq7MZxcAswLgV+PaqxFFwNa+43DkWWnmQOleTDQtIrZIl+oD5/FtO68X7LPrTGfPswLPo0ppCJc9SB5XaeTZczZ2nszoPu7GzzuuQy6vxvIupD1o5sws1VkzmxrZL1ZqOaRhPeEfaZajuubn1F29aW/SJk2xtIaHBN9unAYxaxLSWZk4lcVQESfImtjH/qRgIh/0YcSCfRKGx3+/LynrZ0FYaYngSf/85D5JUSnkaLWwV5T2CNLD6GOl92OQO2n/sFRP1dFwO9bfima7Vd41dYRo/PpZmBdyLbzviLPiugASPj0uGY5ZTKy4y9DgwMzfu173TpvPggPPyKB5VqpCODxZDP6DreQHcSoTUIkOHp1yRWfRJsfX8MMxTVHJcZC4AuU57Kg/rMSfedraALkluL+KwzkdOMLWk6YKY6QQDBdQ7Y9kDSL3tZhc4FNc6LLeDNIyM5z15faxzY2NuXf5Tq4nttvX045uDuMXPZUHvfYuIBR8U9w92JIx0LiIPr0sCDB9S3vqFFnUOdEQgHSwTsUWAH3kUqIRhmNP5uT8di57DtbKzu+vflmpUDCuLD++fyvCruxrSAuTAR2KTg/whC4gAdAL1dkqzSu7j1hEK62IGq1t0F/S+XTdvvOwuPz99r986pCT7RfZYccDW73waBW608lkF48/ZW4o1VrHcysvSKFYRPPzuxxDTwof/tnnshOQdHPZJQJQ7kqZSZSWZxnp1hWt80meinlGK+/tdpr9M6Hjmp/ytn01aZLtrPW4psTIwXWba4cdCp0PpoM/K3rpU0SZOlQc2GUFP2M3vt7As4mIcmNTNXHewluVK05/KqQI9qk5Yak4ft6R3btb8LRF0gULop5svwNcAjnbxKwRFASYCvWNUda3AYb+zlXWvJ7cxCZKrO2XfNrqvOtHuiGSGTj2b4SpzzHVj4HMkLycrdW3G/kXzxBJUk3M/OubI24HWgs+7sdGISlML6Md6G9kU383lZa9TaUo0pJ52KQeTQrYrfaB7T7C3fLccWt6WTjrHASw+3TFA8xmmjSRGldJ9qUHmIsNBFpu0/0N57yZNfYrbc2y/n6w+Pj839uclTb/XF49A29mii9ouiLL4iuEKQ2gyok+uTSO0lkPWyaYcYVQx7w+zHZIEpmCgf1zVmbu/Gj0mZ+D+LtjSca7Q1DeyHAqciwaHu7jRxFvkzDncxIzwhFl03hYedJSyk6A+5NwMI1SNpasLXvB8f4OxxfKWP/K8w6KnqAzgAqmNduHcnntoaBOAcExCnvdIoxxE5uFBopxtXhVSVevQ0PXmMpyybNWcsaVd8+Xt6LulGtkWgS/qXByNUPAX6OkuyZnQ+kts/fZsa5Mu4I4d7nO/VdijXW3aqPAbTnV6gzJ3y9yJMRHmSlSH6MdVGJzk4VQCvXKWXXtiVTQePe8hqZa8/hhP14H8f0UeSJrc+Tduz30aRIMpWKUdRj1MCZmkrrDn17SzMhozNaBSpE22myjnvtDVDiK6xiSX8Rhv848VU9W/H4wwD/UC27k7KmIzrNy/VJjdMIL99FHkA6ryxE9et7zDFSZFr369kOOUZlYn5izmU+efro5rozjWnAdffzV/JnTa7NZ596n6AX3443gArXcvUz5Ew+ZtWptujp6Uo7dZyp/S42UNAJt4TcnT0QzxqRnqZ1FRRmoc12BGs59GffV6eQwEZDYVE3hiqbq1L8T6lZjw7ZJnHgpf3Sj2jpoZKgcohFTlFMiZM2fqx1OI+X0FYpbymwGP0Iq5ka09QtSSQlSVC3DQErgQFufDAp346/6I+h/+07/av7+tNOOQpzLCQbYHGNlkISj9xB2Z5cYygNJBs/QaV2Ae3D/CBsbEjmL1/RGj2vHCqJ7voGrkX1nFlQZklWoC2RnasD+vzvkPpqSyNlY7A9Ufh3nawRsvm2BE/TLW7rUKzI6bVeqWCGNC8ZsxImhYKxGX6qTD4Ktzsf724b7+5YB4c5ddlTzv0AzAYiE4j8VadxWyrdCCMmiRY0uZEeXhJCWy8tjz6MX06dCF1g8QkSb0oG4PcNkV3/HuGO4mPXNFFgieZh21SndaUemMh0zO2uZixisNh89kZKTdH5mZYFTEeYIpm3JTTiyycS56fefllMyh1yXo7KK1BGUCsXSkSgyZJP4SHCGHOs6otg4Iyrio05QAX+flJj9S92Mfd0oByUKhohdozYpkHth6B8H8GxKoptxSTUeQMUx4Y+Lr+eqVfa3LNyKhRq35m9JDU/Vl4bKsYesT9WIcqSQmfCkwj+LY4YcYt2lqiraMvO0GSHWUYZIGRuZAxdRP2imepXRV+1B1sIhxAoubMxVgciyD7tPNQM7Mf2+jquZoVwSh6Ql4Gjbw700yXpzfqBPJTjn0w6FDYbM5yGXwNArENECqIBIBgR/YsDAACcVVpUSRvkPlQ7G90+rGob3eVzG70lh8Mernqwdk4hnnnIGqIzaH5vgAbqCcoeyz+P3yKduqjGrh6f4KcPtIkgZMWtli4rxGdV8CyiUI4kCfQ9nQ8k3nbho02N4QQ3HHVJLrAqhukTNBxG0anCSIzm9inSQi6j5/pjRReS2eQR4tblYxKE2EUPp60KGIQ5+Kw1Mq2TqWJEjy5jPNXjoFw1gpcOUP4R+uHkRoOfXrTzUV5NoUwyBoN4qFwfs+aHKDP5i/EKE4HoOmxx9vRE0bhSl9PNadXxlyklii8kcQafypYjtsxiSJKoLd1MxzjvFayd0A9TwPLTQVYoiHCk+7p5sDBJhS/+hGLc76y0PSfmXG0UhBpPshpOOTrjUCfSaiQrhfviG5HhV7AuZLMpcPXnIrsikGpcz305A/V9msK5czyGF0TF0mnmOhMjr8nJyg7mcTnsaNfFaSy0fuCGaxYo4yIP4ziZckg0+uO1wYZYZRgPWShwi1XVjcp2Tb5sa8sNqymG7zyHVwcZjL2vwK+eGr9MY/CxLBpNKPQfxagfbIMYxswSbOeMGTVQFLwByH9rWyW1lIPey5QHFKP8hs5pT1LxU4d8oj+DCQUpJtcLE0UOb+DWyxTGH6lLt/DC6nmbE9K7qour4Z1xBVsKIFLqVmgsH5wjmetCxl/vmAnx9HNc1Aqf1WRUQ8WntMANBMFlkN/R7sMMLbI8k+FoIVTp5KeFXi1ER2l8/FIH4/gBGtwuVgqHx9iDn+PHtjJS+FFesxauHSj3CkE/RVAJqt8/Bci2RhnX/C71leXvlk8FhlEkC7R+LAEZs06F9aVnUjFeJ/RW10+HpY1HlHpaeyTysXQLpsfymL4EjuNHzWUwXmNGJ/Hw/g3KMeFJrBUe2jrcGjzNsAohs7tAHF2A/wFaXY1C29AlVyuytQUnd9GZFgeX3s9UBcaf9e4db1VxVYmV1TUg5mGeJnIxTrUZJURzuQG2GNIp4dLOJSIheD4JGzAE46ByskIs3ToYAwcFyeG24ZPcdCknkfzOebUr6IABXr93KP3IUSggk+UNMATYVSYW6pQfq/ORAPrVByUdkhjLTTA3KAL2I6pXhmTkQkYuugVDbUErBwNNvBhTs8VxUsZoeyC5hskT9ioaYYTrFK1g20mSvuAX2uLW4c0i4sRRingAhlIgvaWzlYc2oXjK9NHC4b1rtoKZmow86tzLfIrslwMIFe9zR44Ih4d2k0zYJXMtn4GFi2jBhTixH9G78b9d27VDl8/dPVrNrxuGTV1LlcgCx8+7f3DCyTxNzEePCIOCJioMP04FmpMtYDH+6BrOJfx81e6MSrPxNCYPhOOjq8PQzLcUNaZyzMowZnev9gmyN4AbtGNdhcVQrmYVxtLYTmTrG2o0fy7EC56yPU7qFCnTPD5wQh1lhlUZlN2B3zuQDkdJdaGE2HyaDlV61BpNXUZufZ02+zOD3Sh6/PUNEOwT/s3IJvbsyDCv69AsfFnuulXm4yiJzf3G++VS1//7Mb6kOmvL5Wet8cxcjjfALLLa/d/aV5hu5fP7m9f2MkK9h4LSQ8WZXCPM0n9/L91KydKdkZH6ysAl/2sGW4dl5I7XZ1pCk5Oad5ooo8mwHm2yy7+/g5RNI/UfHs8dLp7fAGJdGkPMgo/FLJIhgrTiocu/X2rv9R5m+GtptGqf0J4/toSe+9bvdHtWe8IHKnVH5CZMfPzWc5378uGkWu++AmlO7VrHYqblVe0NUPpFo/L7rOuw/kRrD/x+uL3BFP5xDdcwFygxl2EhvbBfgPkU30oQ7bvzCFIJ0ZdpZadgeomwzIy2qd7b6qmxxRr1bOFG/wOMajwmQs/vfk5D/hvXipmLLFKgXltSh7PfK+R+DPATu4/fgm0Qh6Y65zBvcNoViuOWgtPS4jlCWIB/g7j4F90nFxcSsMqSjTF1uLRz5VzXjX6RDJ9hTnRi6gQ4kSt3Jb8BRMX/MA+i9G8lRfe1EPvrhBSZ+OuwZfBhvZ9fs0teHg6wb+hRyeXZALSNrnRtjKbt5rtGswBdEAVBXAnI6iExR17kNsuiZmaZ1IfYezDO96ot7nkB96wXqUCkFNT+0UydRmEfa6wJPfpcnvCIf+ML5qU7oggVIMssR48rv/UfM7WHiswDYNpKN5zv+VgXV2cIY3lHTmp4PUo3DDR5EULYlz0GdDDj8XCEDxV6hDSrM4n3ht25Kziq1tj6gZz28jvU0VIwUXN6ChuPBDEs8oDfNIpUZnHdDQZ6+l5sX50JpjdrLdwEjVwaB+Foho6v7va01Sgw3eswVRMvuCfXiBzLGNJtSml70/+KTdMd5ZhAXaaY0RD4xMd8Nbpjczee3gDitWtnNayHLxBRAUtfEt2HigYs2ZH546JKejNP2w/WD6xjaix9M7Zndf1f4t2lXo/ys9sUUyN+ZniL2HyLquusZWfQZgjJIKDq2d/4/Wfusum18fVpV6Ntyjh6pGX3rnDH5sXq5aa/0iUDiUX5ZSqWVbmGmW9cLQ3Zhm3aOzd4GKlq9XxRSognKy6fRNo2eatNOLjiI0Y0LiurFg8XYz6Da6R5qP9a9ZHRaMmufk71q9k5WnTtXo8+Qz+rCbedncumnDz8XEKJIKqLQvq2oH+EfpqRUCnpOv7sWL2UcxRrXyDVCIG6IPJTYzaMyn3p3hOfdup68d4siUc9e90qrp4g/cSMP6W7vL0lnCJH+kf+3K/doc9n5CTlXncyZYK98ONII4cJOp8BDBv7zOIF8/Mnr8erg4OrP27vb7vw5UVEwAycnDXTNaxg0Obq6mZUQ3KrEKemE4sKnaxchQYLX4Ec8wAIJ9KgO2ebel2oZlF9/BofTCjiWceiYy4CeHk9qTr2clZL85FHg71X2medrCOYSCKnVU26YxhlslclBtRoDyawJ1c0ZwNF+dBdmT6Nu41IPzu2qmVGGuQOUc+SOgNvmos/2fvOaNF+7pcK8cDrp6B7vB1A00T4adYjl7jVNIxGpdsj7YUk/fAhYaium7oN10OiiEVJp0oYmdE4QNVZHovgjpbLArjMFxhGMghrl66mwUeFshjF1n8aU87aVyNn58feknDKG3+0PCCtktNgr9fHq5CHqjyeWSJhJg2bw6mLpOBizCmHR7Gl6r74f5WbgaWifedWNqEFqwYafk8FIIoSWv4IAIQcYZiDjP4FU/vUf36cwbnh1Dpo10InACI7Xc+0AO97rA5t5Mo3wPwPI6Q+SAwiPWqQdrMpsm5/0wXcjqMAYALg1vQGyGBL4SLdJbZ5RIa7/x0m2OXHTDNSrlGOMYKqdxA/GCsHUfhE8CHFMMPpI+GkN0bY9nrsVZzxp56CtpZCtcMwd+BpIlQ9ydkEC+hxsjxB+ekyMNaxped0AkIopRKmS1KjHN0mgMpASveOjFsmiobnAY9na7AKycMWf1hCDmcCLZVLaGFnwzf2Iu9nj0NsyfYbwOHB57VjvVn312EGS87vjpGbvSZguTA2pXOw3zwBkiwglfxArkZUKguE0hXgLK0CxStT8n5MKKNZLGwJ89ddndcc6OKNH4BuUlkGWo2QeCyBbkTD6Pm54reHFATUeCbCWAejFmp4FuLudRArrrTZwMwYkcxxzDAa9Nr/XHtfF7uDi3sDeJSfxGm4b7fk9mfKdOfxMF0SiqmiBwxvMY/hsRzt60wrJwv/FOy7Z4AiKAELo6SrPIhpaMiKArngycwQpJqg3FQSkTy/NFggtKiJU4C7/qp4L6VXvS/Of3pWYzd9tpva867eALTifgK/n3dO/Ot3xLq0+m8MM/7tG3sPBiuX1gNULcIHTL+RtqnrkSe+HzE2hfDDN3v9oD+w2984e5158XjR7l+e0Uf0ie70/pb7LzOspS5j+NPaU7l1+waw8LXw/daxs+WwMOXLfZiP8Ve+kjwdKtz0q8lb3p+GzzRqEcKiAKrKy9Dot5IiRcGk5cr3M9veJXlvvN6f13ttyOScMcGAz5SI6DLYLP3A5MZnwazMq5+btdhewwUmzPAqHOQHT+HEqDqNY3Vd8EZz9aG6z2UWsFKlExVdND4TvV8AbhCjh+auWts7FPTBCrSKcngGjRIQqew/6R1GBUgUQdv81Y2LicKAR6/VZI3oi3n6yVIMo0kU0VB+U7J03c9Wk7iseOUQRwkndFQhBOY9LAAWcy8qAqo0+fq63K3MqGr5/MtIBEAspf7sL2awxPP85Z480xB3yqLxNYKvUA/cHMMDbKApUmTkB+senksVTvs5CxjJaWPjxhd7J/XKsKdj5eRcfz4w9lu3uPjMxfPK3d/aWBTV22LbhsypX7pNkkxrFYrhdKN+aQsK4GQCNUOYCJzcOFxpGYxJ3+4nswtzPpHiNoFQBsGvC8tw5EHCGCZVRLe577YPu81Te7b1VcFw8V5cNbT6GxO/U7hTvgyvkEwSBpwxVbYHLBP64XDUycNJ1IEBXkpFw4wpMlOaGB4qB0917Hp1qoMGVnRc35MaCGSNCm+Ng3q1A7uwLRvaCtH62qg8BTgHRJGTqz+b7sx/vI2uTd/UDVeMuSny0PrIS3Ptb5xqZioZHRdzIk0bcoh7jClfhRsyvH3CKekOkDCbeq+DjsQCRvX6+YeH1AHLJ7ApHHfiBR/hfcJqACapOUC5DegMJUxtRYbTT9nlXuuTc+8JWvylwGxMguKIQTK+zpQjhxYfQ9mG5cx+azVYqc8wdxrtGICHqN96f5xeFLDHVXNKQHAPPN+tu6D9v8p7q6i62q5L8ODu7u7uTnA5SHB3d3cI7u7uTpDgkkDwgx0guEOQA8GDO/T7fqOqxl/VXTc9uvumx5j7Zl/sq71krmc+a3JwMUqpD/XF8hA4oDkJq9IdTXSrrdxbynyZGuthMbdAajBXVAIo6OdCPHfhBMfRkkUL6gpqMZMM3Zlbw8gl4KxkKgGeM0f7kIwDhtO3nee46HaiY/qURptdT0whlu6gOBfz2Wv4WUR0uLM4cRm3+Dzz2KEBqPgL9ayH5gXE+QaS0B/JkwB/2aPp2bEphvHPKy/9dzhocCkBZF/8NdM4KihOU87JTfvnLu/eU6rbRRv+PpN92quhNtV93n+RzzmVnoSTwRD/ANC4Un2yfUg5BRHnnMSSf3mX25YKnr2kWEnkJRxaXiFl2ng/RhcSd72TGPwAtLDpvm69uxq/GPbXLzmpnzoW4/5UeBcKHif8dE/XFP75XVxEI7N0Bqfx6G/13GUtatzL6Ge1sIXA6/U/8svwkm9jEOSC9/+aZ6QQVC6nHMso9IgQHigyGfmF/7OyQLIAwRA/2D/9wqTuVNj7BA535PnvDzZmKfUTFAWmasuS8f34hka6M2hcVpdEC72n8WyTMX+kneXUwvlIj/lvQIcuAn2w+m5OlqgljV7J+1iefTWHzmR7M4WHnCW92rRTUywa7zwK/QXijLJU20INlwJQ4s+iezRgVIDye/K7MJL17y6D3Q33KZ1Us83w9bQT9JssXVVrZXTYGq01WsuhZnsXZ7dEUVotrNSWbKCcKA7KJA1MiLKjm04m/g0amjPdxlpg0M7npq3zkJKVp8CX4IL1x1B3767jdoZUNp0NXd6meku/CEYafSGFsdMA9SRIo2oza66SEDVn+bIwUQpEg7OrqzDAagbYW33HjFOjYQ85t45uk2NcdbJGA3fLhRBNrP6g5K5MlaFvIaAuXCsmwcfFAVASmlqmNSmzxLFUSMcxka0VKmosX0ShpGszAdGn+/ffyXdMD59eNvoag7fXhzPSwV/XCp6wthO/D/0uWhZKvd8xt8aSp81hVFyWMbpuqF1ND0f7pzv1QyB9yVnTDjNnPj+m6QPzSv/MNJYJ+mOhArVilNREyELDM9FVwjG6TXnG2nwU2eQifD1BJcc/tlkmorFaaxqQDlqOAsom7teDDE/WFKe5+dAYRvM2ZWoY2VceHx/vgu5L99qZ/7j7uvAjjJ8i4kZfjFuvD1xUkU3x0rD1LBUIlUvU0iP+wLRT1QLvqYUqVhMXH8ACJRRwq7DWTpbihW39+eOuxsaxvfLi7lg9je+jhVNQIUQs+WnVNqH9a79zjCzTi6Y7hiTZuI4Rl0E7m7qo7ahcQu3JMrXmbDV98N/x/hfxU2GoGSeAEB4r1BwxlDN+CHGIclUSU9ZVrYHy3wOyk75vjhtL+pbe6UFNWLOt8MRvnVO3eFL1wbdar3U9N2dQmR+AExZth1Jt1+34kiwB8S+h3CVvsg6N8CXeRuQNGGkslsHNfDsPu4j1FNpuAc3iMo1JzwMrITuR1BG0BhqZJK//RM4wSWrJ8Klf8MoL28tR/koqy8pr5DuF4fB9/5rrjuhyb9g/ZE3284R1ifjO0+f3Im+dGph85YuXxkIt12iThDoh2LISExv12Kmv5qjIqwf6okzujqRRmIPmj7ryxjb25HOIA4gJLBKyi6H2r7+2MZ6Zg5VYn+4/AJMht6dfOxi2FNSs9TEi+x7M5KRGGQT2X78IEdnJqGEgK5sXcgUDTUp9BSvCvOwxRxaZ3RElcJRd6RSQhISGWK+HSR00MomfvIZPMxkAbkcNn7GWD/8Cb679bXwnOI41soE9qOH6LH4uyVH2P/W0yB66Ulb3uDyk7KMzcYjxmFQgYXaDOvCXb8Wzpp1fQ+YDJ/jJVo0BY9/1HrjNrdactVCzmR7CL86iYBMoLaxbGeqchAOR7dVKmSvRuKpXygYhUR1QBrnrf/YQGIZIUoH7oKZWG5ATCHYxSJ12hoQuy/wxhEBWc3NF32h0sfr2sBYTzqvhrDbiUGj8IDkaZi7PkutW9fOlEqwBfyfEQQ66CbflNGKCNe6qOsz8b6HivHzcsuuEPANUV8/5mXvtKzpnLgRWT6BZ0deUWQ/WFlWyGzTeDRdpDjXEIZqf9W4nxPEFohTgAT+mj1Lpi88jUAYiRCY8s7dwT7IQLO/CWccVySy0qaJW5Azw/lKksIDpMZS/eWAjP98j2NVaqmOlphawI6FNJ9n3rNbnkfkntXzji6tOi0VbgnIWtdZTMqFHFLMWKSW2lAcSYFpwXw56khGa0wIRJnDDi3hApWfUE3jL3uiwKNDDzwr8vd1HVUFlQDeeDJ2ZgF1sPGTOrMnI2BYn2AJrnQtIpG5u3O4s3z1N1yNgDJBN9EGLmmhvKF1dD0dpwcwEMbA5UfC08FfAFpYJZJlt8frou+NxR5Fjbmbsc8Smxw58FxCmlsHGyPZoEhvMBYXkeYxUdeIlXgXJ3nsKZ/3INSNZpcGqhqAnXzAAUkmrJCbkCnfk5WIjVrqZsBjVqjWar5F4iE0snyTHjRpdrKbEfr4tmo3M9SuGO/7k1akMlsHB42sSdZ7MoWa9QxwULKjtrvKEHBBXH4qy0cVRL1S0dJ94+Uafys54NoSMzSAOq9vSqXA1PLUwTY4fUhtUce5U+E3SnzI5xHZXM0pSeVJPxI8fNEoCRU00e9li9yICV33RLReRL88NrVZ60ActqqebCiZbLnriNdJsTDMkLeOtPSWkDZGFRgezmX9lShBsHSuk4xG5eH8Cy5MJbXLD2ZNqduRufiu1X4/QAVMskQ28IlMhDf9NDEttjuYec8+9pcecqTHmNrNw92v4gu1jY+OziOnjn1GnoL+hkYqCcOFek6toIoKkUR8/lL3lAXlING412p4O8/U1hVzbmkdW52q2z4mLjaNXx1qwj/Ltn2UqizvtWUeTD0HTw0SioJahDyKNGvllbPzpenrocq25Nly57Hhr7xo7fjN+L3hdJHlZyXuQRk6w8oax7/hbebxYP71+leeu6YabrRiVfS46ls44Jdy8epUNvUjl5aHQQ51ALBmnzcCkKJ9qT8krxHNVtIgwzkHK3e3Io2OUfrjl4ORhr8iwYtR78t2RiJlccqTGTTJha1uHhGoUfcJgpHkZupz9DzWFD1i1Ux03vZJ1Ue6Me+g6XW5kisq1ZVnieX79OnpSAntRnyafW5euBRJqE4ngzxAxDVDmpKRwi5Q63ysdSANaIy6Sp+kj0Gll7mvWKZeWpZcJhyN4zFBeKyMq0+4pIybwVkDjoBDKQEVSd5y7Pi7+ucO3iyiO9cgo0dtYS5aO44c6dpk09x6gH3DmFsgsMiKjm6s0UfRRTuUUzURKVCHRLXRYvMp+nCpvzjZcl0RHqeVT/XM1pcCwKW8CCwwfOYrg/8wxqEvVHYscnjaJlGAdDVlhBECtyLVvhu5lcUdcWE9rrO5J2hohsSryf22oE9Z9MhkLCjoPekB0kukgZTDmX/rOPd/p89odqa5smWMuL7KNwpjBpkWSOYaTLqBAUXbt28/HTX83Wc7BTTOewslqsvUVkTZtgaYsJ/t90krZqSwdWg1BhiY5WZiW3xPlL/nQlw2ZSfqNtQH2ME0vAelAlpYH6ndrOO/nb617otZDoC/JxA0jLv7D1GaFckPmckYVygaopYNZZPOwkpw/YjvSUI3plCv7kTr5qCZRsIL1RrivFev97oyvHxJ863n22YDLQv+KHKEdaSpus23TLZP5TdEn1gbviJws2lX04Yn/IaeEo1cDgty8U78wfu2U0N1I8KEF+aPwbIxK1wq9Fl/WrJyeX5293nV/AEj8qFxa/iF51YRoW3gbB3ptBpwHBmbZkNUSdq9sZHDQXxPtcCiCI7DWT8YWm1VbmOnuz5wLDTzymfKf7yN7ruEmZqBC2e2FfOhHOMZ9cIQasN0K103ERlPQXCYqVE3MXPQjD/Rz97pvFyGJKNBbdB4eKo5QzTdxa+IRgqr/vZuk+A9rbXpZvnnUtHtYhJd526v5Kqmm91L5AYB0rHDUv8W+jU2fGn6dIPhf32BQeuA8Ql9irMFY93viGAwT/9/io+fPL6/BpucPz6f5auY94+/E6t+CQl2K8fiwYjP2k0qm55Y1Ehlh/ecsuHGergj+SdyZkDCeBr37GJlBxnDkW2trxPIE9nuAc8ocRlFay1HS/nLHFE6UjuKoXxePvAYj92/hQwsk5WgyOhQ/F85s+/hxsPtNDp5bbvkBT41oAiDzlJ2D8S7BPmCTatzpkxOhQW8t+v6+D4R9aiHDYgErz204kBXbLHQorjvu84MUXJDBq0aGGOlRjvW8ZYPiWKlRwyrvnkiAjvvEOPv3c8UuN3Ec7GwikqlptD0181k9n4AoGXlqXPjbh46tTSLCLbScMRL0D4BZuSFJSqIF80Ho3rCzYSDb1dsHoOn2W14UeM+fmAJvHFMIRbkTSzC5dcoCOxsvSrBVYZ+b/uDizDUJZSHWitXZ20knz4NOFlG2uPAajHZpbvAzPyJhp2us7fA+4CWgr/hlp8N2t5fMKhAm5n5MzPzNcTkIin74MYNe6SrSxTJ9i2G2VXGumQFy5atYJy9dlRbNy/UkkmARo7dgrrDMrAwqQ8zpYdRzh2A5Bts+4cRfFZT6V4gzBwBTWMSaeyxVPC16YqfdUo+IMpTiBQPapydHMDnsIoyQr0mby4z4cS2Y/6SGbY1aBngLtjGqlVlELNWLQoBZpA5ZzFrEPJmMav7hnPuzgoOow6qCg7HDKpxSRNGIUoSRLNsPaJF+SJAN6uuXxjVDZggrB/GIJqp8VhzodDZcuE9wEDLalMA7iWjyK1+KTvUsWSWqJ4pmWCompvW1wpA01h1uU9lVWtGleZO8jdjjF2k5ri6KOMjxJ2NR9oFBIC4w5lGCvPlfBZme7m0O/GHQFwIdvyYSf+dLtCiDh2bnQGPb6pTPxrynsVtfbLJ4C0dTPa9r3kqL+etUeePn66foOnLg6BwsE5g0fKndzlyJU0+y7M10wn5lxaICnDKpUF3v0u1Jst3nwNpdv5YjSOkMvEXcFAWQccOJW8yN5q2+hD2tSSxsAvFzcMZZCc+ux3kjFtpa+6eKL6r6uNgFDHDjf9sKCZx2cRogpRdp44e1yHlJJhQSkGLL5CGL3pGm/s5iwYuTkNHwsgdOQKu1mPUSSGW2ZQOGoiVU4H9tvf2Td1JSJkLym/EdPd2tPV387QzqWtaqPOTpx2gZcvMcA+Px4L7KdBCnR+Jo1itUlmfSi7bADiUzwgN0MJ0D1sdig6V3SfP/dJKvHhJEVwmhg53/fglNKCf2WJK8ybRvhOKJznIewVcp2CjBEOOdoUfBhl4coW2whR/glE9XUBxlUr3/l5u8rpQ2ftJx8ZSM9bOf+rWAB8ezDLJmZYuquEd8YSKqs5ngobmLR0jomxBnijM1zEKVIDJjGp7qD12IxWs1Pb/xfXorvixu7FjqmGj3dX3AZ6rTC1DOl472GrvMKta4nWk9ap3yIVaw4M14wTaHN2bJV7ZYB2/BSDRYP8VW0UzA0vAeLlUyaSO8qvzZeKAueGvK/x4GDeFiUl5DTeHsob7KsrvHjSY2SmH4QfyjY8JVq5o3TjlYNCAhsop7pfvGPJiPHWmKky/i9w4023ITzS0LrlAVF0qPHloM9U8BUhIoJct7Nw/f6ctph0GLSUR8fpED2Vq88F2CMA1PKRotIwFRcLf/45PVE9LzB8D3vdy8seBXmcxqHImjA2/j+fSxZplEGLJHOrVUHJWEahYxb92APlJ6NWXrhGph+oA6HQAuGxBKhdaS+l9HG+FjfeglV5pp/2Rd97eKqbsyiG3Mr+MPAJ4Q9nP/CsW6aL8BuRozpf/DE4k/0sun3hATzsV+LYmby2v+a5z7EL/3XvNSEH5nz67SQcp+zllYxAfAwuAU12H9KIoj/VOyaSs8xlucY2nGs0Ym0ZD3f5ttpn3yqzvBFHYNNjRGfB4Y/9T9WqYZ8l8LRK3Ecd+TsxfNKQzl+/51FPyY1n92KzLjLZ2OodbYt1/DdT1mi+7STAhbNKm2+oyDU0eTPxM/8TN1H9f/xq5p+aqLQfhJfhXCgKsvrunENUwlziwIx/8onwNx3avi9o6bVagN16bTUqzLrFZTw1AtE/qNJY/pT78qm69YFSubrx6GicoqBYgIRL7/quORMHcT24lsSTdt5Hdg19QGtpJn9vJbedrQzFtF8pwv8I/+GjFOfUwKIUxDroLihKgPUS90H9uyXFfElqswTjgS2ow6+j58Ef8AWPf9nPX+4dv3ZjL10ASKskHtSlIX5N9vqEorZZs5wi9bgmV3pYsv91TT16Vs28Ru6Smlsh9NZUmHycw2G1Br0pM12+n4bHVUxVBQmzEx7SQ8jDKJqMQ5AC0Pv9oLCMv9G0q7SZ8uBxzvvpC2mLmBh24IxIKPlCE7zFSDlZh5Mfo8Lwm+kHGXS7aO/7sMr1B6il+2wXTUjbPOC+cfAytfLIelm2ukghaqp4Jkj0VuFl4auklJlgknqOMvlYWCS96of8SsU7uvxaRROldcBowA99SQ+4M2bYj9QYhyxHYjNKiETVzIe32X/ozjvXKXOgy/Yl1p1jDQv8t6rzvzuiZ0wv3u+NMKzbj/jJ0SbZ0z7n6+qzURd6QqxRdNx+qpln9xQWOtI4XRjf3oOXX3Ith9zLrYJ5Qu7ym3PYl1oeOUUgs8J4vrlocd/cbpW7mPVNmESjeomZCs6eXKK/CdAOm4bm/nHLrp8SAm0vmizlkg80pOcHp1xjX6ekQs2zvhWkZ7G0cg6md1D3j2KmZETqCHr441Ld0diour69ZbIVm5p08C0WCzinnxeBGXkPa+f6zt5oVlhfhzRQHbuLhvsuPPJssupM3zm7e7Nb/wAEEnaHbJA0yI0Ph8AoNaOAOGPXE4itphO41YYHj2PTzuZ255Hx9dFOOzGjn66Q1X6LF6ohDiHeVDbgsjnM8ejlNhefKosvj1WBdgjuYRtrjBDLKKOlF76ibUFKdRZWVkygm0iE3IDNIl1BgGMXH/6AWM82D+Fg6y5zIcSrLKJEcVvW7a9WsPtwi98GfeANbSbQiBO2MbrHE7kyWXVbwWnZWzqsUYnlJEd3sBDJgvUO2uy+hojUzHOM+clQ8A8kBNbOSKk2U0xUwptyFuP2DkXOp0Vjaw2FiHPfCIsTSYoxVa04jhwa3l4Q9u/ZQ1JHAvMWzauRbcIfI+O/XqUFAkUSREYjrpszt0n2Lgyd8ni1k9pF6ozR5rK5CYhqcTN0lBn9tbGpAsPOBercq0izJEZKfJKnWYFiUNrC4jlcqxMGZV0rPR1nn7U/ek6Xm0CsP0AVjU+brwhCda/QGYnQa8Z96zvDIvhQv7ffe7t43rywP/YGhdC4QFF9roMY0hSFvkcp1SKqbB4RZQWh6fRUszeJVYitoF4Cl8BiUmNUyfK5IpainVf82a1EwSllJB8V/6z1yn77/NdWj2r577uuoptIw9mcpw2JUe76bzE5IPrwoDxQu/ho6btwqVocdID72kHLUcl/vUcSXabRYXrJsiWavNe/4wNBwCeepgH8LbutGRWJSOchd9u7p6+hni29n48hpfrBlydBgaAgzEe8dzPKESwy1MLOFKwEm75wAStzy1wocfzqjFHXqF5JbFx7njB2vjWwWvEC7QNIRmT9WRAAnsYXg8spYpO5KZFLCiI3kXakIuHh/vn/vam8Q+ABTkFx+AgpWT0mGK/3kYxXz9UjJ3NI5vs2GcDs71NfeCyPFNrXXRWyuk+bMb5u/xZB+Ckzh9c9eyZcYIBoXgQqvAtmLEc96NrVUTWwlyCI70DsRzfNQ3HNfM/iofgJJ8qDemM91H7dKSi/iCD8DWf6Zaqh+AF+8l05TqRtu8FlERUQvSmohraihhCU9y4QKLtTY31B65prjZfRd3fWQN1a6bWMH7GGvrdD+2fco5wC/TET0HWkGkvX16d5aKy/Xz3FZYAiqrnm3/QlskgzM7rzAnvCSZZKpkM7S0x5vecjGsEH/abFGb5YuewETrzAER0W+uqoUHdjjsugK8hCaX4gWQItj1xMGvPUVVGkQ2bFnszi5+XERxJAxuowhoyizUoAuqfDystXY3O1PDV4ZcDyhmsTLQRDGJkTrdtYozxWQ/WkDalImwg/mMiovaZgeiVA878yoik934OGd4dsGxPFc3bgO0S/6gDkdXFXvMODT1OO/dFA27e0zVifGotHPWn1MIC87Png2o77tojTGwoxVmxqRFXXtCk+1xU90ZBtKYU0BCiwbVZBiMTCn+oAI2F1opQFYfJkqibJQm0bYJTVMsBSGZUe72emZmT+NukUf1mM5oJfKwakvfIFZlvVxtWCQ1k5QnULDFsFuOZH2la2GGO1l5YpJk6aAwQXgcmK8oJmKQN9LzrlfWIpwBcEgnmQl2c+MiYkaFsiHXMMeFUQbImclQrt8XdZpkk3ts5WQnBRfygjISqTGLTDo/62hRMt//vYeKGvOUfimNZH/4u1m9IOmK9mqJcRM4Bcrx0jk65nnRigm650dKMBuppZOOFlpp07FCzf6bKF6xgacCUT/UYyXTG92U7e7itaZ9oF8GpGXbd313q/clM3I8Z2Ak+iXIoldut8Lu5EgaHZhqzYzftq9sToJRiDtz40/Mf8SDRfi3OY1vjCiVyi0oxOMGsL1Ny3aJjvbz06QgKDDKQ+YSy5pxuJKFZl/R8pbaHSeQ+riI5tpvIT06J7sj6qHbxUJ2QpWVAZsGp5Z9QC13QTNXv3xTSbW9RN+LAqXNTEVr1Emwi7yjkAEPIrAKmWeKTLxWpPL67s/sxqV0C9L6reF1POMXZFS7mHSsxs0XBoNiaWnA/H4HdXvU4mFjd5+ikzXqDjrhae/inVDrj0VNFYtNacdP6mnknYYQ0wH1PHiNkwQNqTOk0xsVzsBRnURMrTaVyhxXapMz7fXHNHv1bryQCLbz7VHC3bjdAq6kvatH9B99UNO809Mkmn+tlNd8qZpLvCaoThCd1Q5mESw8VL/qBWp9lfl9ls+e6Hyg8P4j/D2H8WeOIjF/QchkhPVWZsoPqCmcvU48iywLJ94q6EjgOoNqAinxxlQ+RCsBh9iR8SzzMZOlh905JyhDv+NCyP0DsPssznZ1hix51/uOQvzp8ryZCOPB6/ODaf/05bn+cLriJNo/9PYfnsQJh60ogRVdaB/ejMJYPuiYOIxKdgYAQHVHi6xSdYwGGnTWQyzjkvwhU+gDLeRBGQe1jFyFzwuPXmP4wi2laZQGdVWGOAvAynkfIh7k+5rqJdmnZaH6hXl2ie0q5Hkg9GLCB8Am4e/+mC5ZY6PU8rlbdMU4WmUmHtFQcJK4zPQF81mh4mIjvHR6jdY+IUosgju7VWgGVgKh+BBTZAmAFXPiAwBw65tsmimTuIy+VTLbJiTrDeOdpGt+2YtNaGGW9ETSW2GfAv6+qsG/gt+ZcZc5CYCOCgQsUZKdkYxH2lZu5B/NDwgoIyTuWuB4L2/WsQ+Z39lAfY4t+DJOtjsfZ67r1p/LpTdAzF+vL7RzXxRNDR3cUdR49kZ++aWfoH9lY+Xs8vzbvtgETUsmhCX9U+vYfLFvRWrV+RwK0DPT31JgnBFyVMM63FsO5pkERH5ihaGIGcIyGUx9/QBUPRTEorgnH9rP7sTDHk+LqfwmqoeeJI29buDyhxvTtGq1amKW07cgkm1rF1yd2oTz671RYnQnKOHDcPA2B3cmZayQboXr1ArKUH0AgKsgSwS5P5wbo+7cPePEmPx9GW7PA1VVBiOHBivdaHiNwRttNWpFFh54lWrAzK9xTFLWitOs17gMolC8GeOiLagVioL/ELLEf+8ZYMpBGQl0XL7aawHZhJN/jNBf5b5O0lHKWXreGmiBt6iS+7oK3DYX0c+Sj7P5hWg4JdDAyh1MB8i7GLE/tpf74GHv+kD0H4Aov4WTd5IXwQ3tk/Wfmcn0/Ja0LVa1B8j+DcgKQUwzjmjLxzgy8qSf1QI+VzI7cWNRJQPzzEzcpAxoDy32qOywqbGTpxnYubhvfeSdnKXA65szq4VMJAaplc6uQChCTnqpKCF7DQbEaaLoWNpUdetIOVQb5MEOEBfyQ98QptDFHl1BtooUPxcWW1WJ3d+8S54ij2whYgPahK2sMsYefqY0waJA3HiWDByBTjqSuBVEQjrWK30okHFd3xZMsmQeB7EVqkAqvWA98/AB4QeAZv8JJtPAHiWLv9YuVe6ADVkua7Rxrk8N4I6MU4iyvnJGncwMqVU1ryRWr/iHC3d9e30vqenqP3wIevsh1cUShHWsy3JOEaTPlmdDt7qc3cw3yq9JXCah9QsxA0AlMZqqr/WERiTHmAzJhk1qhcH51ErfcmyB3d2jim6SS7gW/Mn08D7YMPCHT9fC2X3A++QTAaUJLSHCzuLUJSpzdDdnyU/fqZHw7in4YT3CnZovoNay1dYzOnx5yygz6CHMBhTtgvuUSpqsQsaTQx90l5EXd9TPx8iGm1e3dxGkKvDb8u5+22ZP/jP0cSylk81gtmoOznzEjegRWq06/SgGbpGIBQj491bTtMsnHudsfbeNC7coZJjPlYDTNE/WZyEzvYvo/Y4RBBHBt2nOgihJh5FCSf5dtLe3orfibzUvzrc7G+f3PpfGaTFX8J9n14Sxov6OkEumc2crpFInDFFheUmb3L/f9+peZ1TT/7vTTFXRls3Tw8jBptOnfcq4KLvcWaZOH99tiT2lSf3GMMGhL/wT1vEr5wIy17TMjMVsNjclJknIIaNlhIW87uM6xAduyG2Ddo0Z352SILPUmoV1pLSvBJ+U/PgzLta+7u74+llMcYlBMESlU4QWP8lHylfGO5bLE1ad2GJgOrnsK17GkXE0v0ohaSiu9RQtd0WB4RIDRvRv31jGI+z8vIc6GhLWFLs2SfANWWpRO8q84dDZZpYC9eNP5l4rBjEtBmJ6Lx8q7IUEqNlr3x6rnKE0mB+GIqJ7C/v7a0DUXeH/ce2ugrrmq6ROXZ9sL6pTCHtGn8vE+lp2cyLVC6ZMNaeQkaNgt+p+EsrajgmWAvEOzTwhLgZto9h4VlWDRF/WpOh2zpxcnodtGSmMWnLHB2C8DiLwHmyHcFvTYrqrEzFOxvABYKvu+WJ6Jn8h7mqwpc0gyHicsYEuINCY4urjyu/YHlVBPmDr5JabjRQ4kb4isMb7FGUkZn5t3XZmnJDO1drOKSzMqkq/8xOvSTVcTmLcWVUxHu0HGUKqENIrVFfEWVjr8gfg2cJRz1LnxhAuXrKomd1lCF4MWdDEakSIBQ9dBpbKkwSRm5TvhFogtQsJSNmN1MBBXogroJc+eVE8YqyOz30YwV/1Bfe1LMyglGqVDVEjkhkdmQ/5WnWYWitALZeFQQhI7R3mz4RLrBLKk57ZY68IxoxnM0gNH0NlujMUc8O65r86vw+xfe/90n9yt0TZn6Y56fr6+guj5gOQH4h3+vpc77pweffQYec8NtNQO6/CsOysZsxI73AcfsVqg/7EopBpZ7hTqYiTZBUXCOQJx0PSTwoy2LXRwPc1MFRQh3MDtY4lSsoGl5lUqKxFuMzoLKgwTDI4XrCv6B40x1VekK+RG6Zd2wBdpba/m8AczPwpjsFLkCmeFWbeHiOCUSrI74yJuo7hOzaak1/9WVMjX1vvxd15tN9ItdWbWGFG2fMmfRBIYhXHIo2MGpfUSgxPoM9DjTcLRmuEyZGGDNvrk/HRxtFU7EvvV0CDukZQGY3yMx9qxMQ4NvjaA93Ft8iQtWFyPwA1eEmuPZaIU5Ckv0wo9DIwAfy+dU5kT4+VLQx+i6XV11Ya+Csr97agHu4uz3yp5+2TIhSMRTeoBbKWgLAOdbaN7btfggsX5GyPZwIRZa1WC12WNrPKyer1qgzz/GbJtGFNgvMXPUbaSWHbUr/3qbhI2lpzr9Sv1RSgp0RBVzWYEHkFbvquHkZHs4lGEF7qX9g7cBpirIl5YpUePlQ3uSKzVhpBDqXBMVxenLD1Vxy5A8etgl/MAQ7211cV/3ARBsdDzWJz1lmUoLG8z0QTpBaefk5JlGnfucU683YFK6Be97RptbTHTeb/RC6hubIBU3+0Nxis9lBX+H49v3k+f2W7NTy+fFs5t5OCrIRtyUIaI+Nol6c4QdPyREOzSKuPPEC62XBnFUtQOEeGJTKGi3z6buwiIxcQF9mY8KDB1kgiaUo/ldXP1RsZ8e/dYl17obEsppl+gyz7L4d0OHkSElArktAVUxPjj4TwLoTAveOtfFo6enOjnkHLoT+iSXFLhQ8cmKukVIc9og2mFsoxy5eNiLsRmnG3CcIK10SxjRbr3KTPUMjRNCYWgb6EB2nNszukc3T6Ia+cowaqbsqm7zzmNSHN35Knbwuu78J2CidB3UNIHZyR1YgjPKRc6dTOStMyLOvHF/vQk+zNQv7sOc0NcElXB9yZ9ikt2apnHZxSDbMEvF5Pnvz3e1mNFBT+7VHMI5y4jt1l7ve/N+EI3frodt/11n/FCkBZINmwGhXCk7hhGoNK5xMIvTBoe8ucbZl6mnvH2rgrbehSUn8vJbpbKUepCE7hglbqWzvY1PH5UPBr+QZHhtJNpFkCDY6Z0uJgzNmTq7vaWrs6XypqVmLWTJlIXCn8imS3UBYScSjtspxgG25PsVrBqi5CIvX8KNY+rADkHti/OqpGpdkQGY6kARVvf2zQkWyaW/fkYYSXnbOqbHoL8Al+Qt3YF2SfDJ1TqB9/gPrEwv8z2zR634a0qL1LWR2R9iV5pTcKZwaoRsLAxWgZ9SypqthOvcxmPEjsHLlrcNWYe5xDaKEHjVVhlsQToHhIXKaYo3b4+HzxO6Arjjbvygzs9iVd/JvR9UX3IKNR+5xfENX2BFaVKzAPGXZijlqmMDN2rpK2fUKDTlkPSTWP2wNa/98qZMaYjgxAYW2ObB0CwA92gQ5e7RHAq9tfYludvFF/kEIQvbKhRBKEVqdKU0esz6YIaVknLxSjyKGNiHO+Amk084HhQRvEhfMpADSErfkIvAvT7mYwqbpzZAmK8pyo+B4R85gQ+Y9DmgIwXQGDZUEdqwyuw5BSKzaD229WudHUilIsBxKys6I1Zk8absKFihp994mJwzV393yE3LsAmywkV6ty5wnCnwZZmsusU6xoR6QB/P5gZ+77URqx7iXmRMt0TqFWJcshvd+GPCcyLt+QSH24trbwll2k3ZQues/5xZSEw78XEshZH8/8MBqCY1HNA9pM/PyUka6fYKmRXcqsBq2djvNLiFqtJV1i/Ybcw8vOsIT96tbJFZ4hXfSn3eYFRkf3tNQo/Nge9hNqIMYRfGQ0qi29ky9ui6UqXXoAkrgBj9X44Zw02RL8KQySuaprBQOS95Cxs4HDKZqEy5iD3PTANZL9QuZBBGJR/DWYT6MZF8CsF7bum8rZthFgzbA6vNDJaO3JhWUBbUlWCGu8RIRDWnQXbPtWFKb4Nzlxn8D5c/jkHJtV+qE+bhbJe7CvuRv1cVZpLFQSl3qDhMdiDYtMB6Rq3p6NFoYqSEcb05NmXhalyVj8dZqPXW27PqVhzyFyCFGZxI7qklHWPZTe4HhznY6lQDt6kKyBqkrHD2AbWxZLH2qG0Xy3UAWT2/v7dlt5H33AO5u+Via33LLgukFwh4mhYFfb6u+m3m66ralXqHY0bWyxJVgRj8IjgWuaCIa4FbobDL5MrwHV3n6pW1ekSxehsrGyspFq2b17utvVVzA0yC/AzCwbnFubCwjXoJJIzVhm1/NnGmdcSATA1aeofEZhJ7U63ZhxXmU8gQ0saWlUkOZ2wcxg149qUWTW/CH/94ldcVqt0HVGFWluitu9sToMqRX26s9S5IERGslCjaU7wYJ/TZCpEJ8NE/Iwv6+Tek3Hr0NFsDeKkScJk9in9M5HR4otw0EsAZgEojj0fJFeBfoKBT77fDLLJNWZxK9O2hIN5j3/05FCsUN4YaNXmSs75PIedC4HOxtQWkBmqx/Gs+j0FCybs6upd1p+GH3gispsWJbrycjV3VWuE6nrHMQsl6YUb9norexsk+VGTGb86/RksjDuzfQ/YsdGDOZvb0K9RGcImHLnuPWW18XkxcqePYRDn8ith9g1df8s4PpSkPG4ax1cv4iE7Y9Jb5uQ0lUxqK5GhSoYznUQlq7wHfVEiTiXMuJhir7svB/+dy3kJ4ojjTcTzgMlHTJfTwV+Ry1C68a/8puQ+B1zVo8VHwj7NOs3IGxRIDBGizFFtRk0bIRZDfztwJXbSM2HwKqc5j4xECdh3rj+r+NTwVFjk5/GE60gpOsDoPAf5WRtPHp8scYFpx74pVQSboSCMCyTn9YCkSPkCJESvwEfK375MhB6n5W6r9ExUewy0K6+0uCPT7n9fevJdR6Rx103BqJlkA6EmYrxF32a8LDFdMPMN8TUPHfiuL7BUe4qtmMMziWmNMLCKgTSUnhSyjs6pv/aQYb0at0hkgn0s1VHfjGzotiMVqsfvsP/y9Nm9N3K2WXi+1rWX2W6Mcyhak5FrUDB75/3FbLLA7/IUjr6p/yUaLlXUmtU04IywDtXZW2UxVbQZFX9U2mTn1h7vztWFeLFK5/agNNlSFbIF7YHFYla8CmNZEij4RtT7gTtt2YkUaak3t+qfztFrBwsgV9VHqzXBtBVWWbomqYZN+JJcrZ0Bvog/4tLlT9BUOdahkwHiqjzSDwW9QTrKj+JvUW3qLaUl6sGSq2/g4VyZg2cVZ3qcmRW8gGrv68Q9QAvd7WbRyufr+If0PNJf+YjaJre2jdpfJ34AGsNxTCC5gVmP0DeRrdT1yq61MvW18FE8Y/R+uav4gAG+LtK8jQL6Rnl0gwkXrPS0J/cAWl+TwdRljqipJnnFqs1srQCpUP+LZAyRHp0ZBfK1AL8A42mZA5hLbdXWAYzUayeoQ4CsrkRDAXjpeTBT2v6o55T82t0wwjTXShJggntw+ibjrRr8tprShpX3KOypvN4PxTd1k3vZzxRveQdyVvhVQqt/26gjqLyB8eFLqY+du1b1g2nkX+SpBjj8b2+3zDJbSjqFvzG9/1b7wz7TP4uqwLXsjm0L5KTx3EcY6XKOfZI22qEMDVrWjnzJpXR3+iuvrcdiqEsqM2AO8qrnP6DFpAGxMO7TVAYMTPa3DlOZwnJ6PeJ84AajuQ14jJRcg2UUQ2YKasDNQp1zd8cKX5XxYK31SEocOP2/vXnztLpq0m/NXRm1f8OCGm+pgF8MSlGtMcl9230nfux2LIjrTzY1FoiQJvrKvvMetcYib3GXb5tdXqjPORRc6lt2XEXJ7nfz/d1jVEt03NksAL9DQtO3I4FYF1JLmVd/d+SgnvErcnq9paTPYPgVNgBei9m3j0CVjEkok4RsDaF0URWUNsCAxF3QiofElpf8uNVTrVo9pg7WNO9RQNcPB1P26QRq6iysBkGpbXcd07MizGRbAzIkMtmcw2QC4NiwfkqOpj1J5J82wOPqmf4+gChpiTq1Qkbhnz/Ba17TSBiqvcpWHJypT9bHxIzD5dpk4HFfIEHbxScSdO7wKpYbSRXrUwdwfh6OnFJHcjeaFG4//2FQ1zhx7yTk7uPcW6HlCLyfCTwF2WofJGBOZt1gyoVZkPn+DP3IkOLVSFWOvQABVq9TmNAFUsOs1qHxxICi16LPqhOp9EgQyYgCqhFSoOmVnojR5MiOBe7oIwHJoykMtLdeBYYLSFyh2Lg5hmLTuGuA0aiVXWXLYkSajbPhcKGQ5j9enFVEyJd0/WWyFvMj+oF8cXUYyjciULGBPcqEhOTw5SCZlOSOP2QM4PDfFEcVSkdgxVSZm4TsFfo7GIp03gMxoxs9JzZY5MueCL2/xZ6RxPmDXvA3pAJRZ1STFf90k1NbIlaJ6P1TxSKtIr8lc7CYWUQpR2pnSI+Gg9a/40EWhXoSMQzhKkxLTCRRj5g0e/g4DpzAa6KBc1w24o6XeKAZye5RaO5W7zMr7SAkhjj2IPdM3kRFMESq5Jg/tS6xNokwr1NQ0J8fHDLt9rvlY9NS9t8nSKm2x3c4scpV8vjkkezM6sdSjfgrGQStlAFQo0elljB8aWeTxVmqVQ5ncSIzw++3PqFJLpvk0IkwiOs5z8lj7+CXIik87eoXRUUh+Qxs6xYc68byvwA2xHke0S3WUyTcBbTfXEGr3F5uVQtvje4wpgKIEkbNEmqFrr/JCF0seD+uauboPefHwCBzuXW+/uX4E/yR+9Turz/WgT8X2K/K8T1YePq/o3gPvD8tOMTXuPrlxCCu8APAJ3nf89t7hu3QUHBpt6XDoeZGAtXV08hYuJi/Vvr/5tP/p8RFNx/+dihTZOfnCuK0V+R5cRfSYS3/PZTD8/lMvYL7PIZW9FWTIjgnGoB0UItNxq/mwDI7prVusqObClxCkzjqqSzLrfkGLDfNTb35Hz5sHH9YseZuW4MXD1nnkrJ5bFzZ/2GcQDCuK9umUgLnyAnQeWdTx8vd/2jokuRK0dAqFgqSZco9L21njNiIbMdiMNI+y3QLkeksMD9j6EmPPZqTJ0Jsz0SLFaEp7s8JEgNCDv6ZMXHxfeTjVkRypn9HqlcJdGaM4HULGQ70g2VF97R/Ps6Ul1v1J63nqeTO6wvPSgcLwa0rYXPvwaGghio078cjC3YvKRRxvXhuTLKJfVZF2wlUKJdwz9ER+AKkBXVMJHRiQR+ZeiHsFGp3fUIUmZkNfXSdXQIiTDBOmHkuyGaorqyVr+2Z3H50DGSXfXIThppE/MgJBaQLBD3SvLlmy5n1OcBOu1A146vNyaVMYRZxGd5sUnkJiN7mFGOzEE+iYXwQWVYrsxDBFFFYV1sgkZwfg3tc0UHBA5JpjvnTFv0lRf5bW2PtnyJB5+8PqdIV0/Tz+Srr9IlyilcLYSNM7S4A+hzPJHSQOuzLCAKztLt+wY+0g3SPNJCfRPDFV5RXjad5bP5w8RbdOdkBVzCtEDPeDzr9DriA2A8H3d7PJBJypqOULEq1Dr2X6++S/arFf6H1Zf6DJINjfIcflGeJq3VP5sdgmfceKVuMvbIc18pfZon1Qr3kll1mkMpd55W0J4CN27mWwaWZsqbTDskOqtri3eUWyPFaumwCcWHK5xn0Jixqllpq5xR4z2KguVtneXUrTOzSB08RtRhsw08wnF/t/g0B/fc/TigBchsdOZLYmYkmigY0RvcpJlQ6VJSYUq5x7WA5OWFoffR4EBmANHSZX0b6NF7TPSeyOho3K8Tv382bpO2/sHG4x3vKTAyyoyFfUa3H+amOx6q1EH7YXyEXJKztlDZE96NYnDQ0p2KT1LcNQcTuMr9Eiskux8hDsfIhX/rULk0aF8M9LtmhaSoKY1YT3H+ihy3HL7xak7hLMfgrK+1YWSAEg7Dc18T2gY8fGG5LjeKmqa2hV+KM8Jko+1gXC5X02h3WMgnQa1YYKK0wfanh7/HssnkApi/u8kGBpzeqTEB/4Hv+2VKimv9hkSVyGtQsPDryVKlRBbw7kSzXCpOaHKZVcfdPr1SIbeFtAyYhYAIm1oYLsGjKjgX2poAiOaCpeo7oyjP2nUanrgkfYQ/iQWLTHz9pcsrxYtMk5w3RU7B0RwLB/IDRvP3N2XV9vD/jdxgsBEYMI/EZQcVK6DGNFwu9Q1XU9HZuwXF5d5LLnEklgrsNvDROAMtmmGDMY1veAviiNHkLaUApL9Xy359Yw32oGTvMttk3QLOfrIU3rrGvl7hP9FJld6H6SrExWGLGf+GY1hijihJOyq4T0oTDoUUFT5AOEBaeqIfQTb1bofUvagbYf89Wsh3w8Ion4EkQnjM7YgyVzL1SwhTPzw3HNK2CPlq3Q4F5pzgF2cNGFhgpUdc4DEwY/s4U2/koccXzfS+Jk2FeF5udEZEvX3VFY1ZIhEdOl6hajzHyvsYlXks2xGdJeXL2oTGZ96CyPmpFFlSMGcLQBLuzIwjoMKlyyAYJjr9mPBXn0e2TRwARUhYuao4mYdk32rylVk0hr4bBXqMNIrL8WqNDKxwex63KXvIhBZpmq442Vbi6ekR6G50cf27Z21tWn+qsIh0qbhvdNAhA94hKiGHhXMJmMXIa8F5h5DPo8KpSxSOebldKZKSzOgbk6Hf4/7FcLhplkuLXUYA3CZTy0z9aEgRORV+hK4ippO56EAaq+qcFU1H+Jm6gEPJvNY5iY22QplVRNP9CQQhCaKlK8j2yaAcq9DxgI59pxucyt59dZv3uzSjocx9JQIi6BtUgnKdCwqPvvEQIaszCUYmD1BOYZJxPvuWv4zh7N1gUu4s9SC+trfKcviGd0IgSkuQ2ENsd7FecPBEalJgc2WlGGBDXEiAbzKzLo1LlnS/gZ3SGcfeKakIZkRXxab4qpJxxmw8W75dJGBdDz8ju+qsUwkRpGqbaTz7Obplr4od0UCHkqypSFw6LfdcSFRSrSf10YBAO6317DNtGpd0xbfrY6VWfEKwftayPM0XT1EkQ309fczig4uz37W+LfZz/zq6tujvn0HHUg7DBcKKl4I1G8bMMqyKlbiyUR30j9GeN5rcIzHign6l/LJ9Mj/eoRSBhsC/xd38ltuHI+h+IfxEFkn5ZKIyevIzblrZmHKGxPIHhzO9waekqxf+HwOFWdgilcbc403TbXm7jXKbaQS2CoUGJLgyv0UYafdIWoXgYDOmjOzjTsy/v6Dw77ydzG7AZ9U93au3Ci1LnMPHVanWbUvh+x79bbbIRbGgHRCyjD9UyFPXcwTyjhKWqWwsMFCWoUrCOQ4Oy1P88VvcX4FzBGaOO6dLLLrIz8zENuT6+E17Xs9/Bw/Q1Ulhb2mMJLDVkDV/j1/m8zLBB7lnI76EDzJpVf0P/93/p/GvgPhfO9+d0ypGasvUGezuy4NR5jGxyKHaNY+JulApsvCR6vKxpyqKdDUFFVtFbn5IkmelPc6qYmr7QTZShpK9HDF/uwIeYgfdXEKwsLjvD1O0wLxj8FeentxpzhSdYlaJ1H3lx/loUL7zhDpOqlA8ARdKAAcleR5ufhb1Fz2cpq2UNF/VwoBwzf39/auH0kEOpj3SxgibptUNXkv9Q7pxZXqjmlo5hbifQ9bOFjeojAIGgzRSm4eFbai0v9Ld4xoUt/UiFUAkJncr3j+WhxyaOs4Tpf6K5QwK1gjLAvDEIxcydlGxN3iKG37gBjyerFgBSS3ZVwDSFsWWJxvGmrEALuGhRICsw6hzidwsmX6saorBWOoNerliFpxEahudx0+XUSQMXhgsaVKD4IMcqlhKTTcTCMPURAaUQK4kUyhLpjlVzs7c48+U3cvz7bf3+GCT7dmpjauggA8A2cmqCMfczqQOJdfoMPz339MNyWmW8JvZRELw3OPIiMzbVHEgjuy4xJF0RrJeEw2d3vR38+WuSOqWciiBcPD56CX1M9hhw5aNfs0PxQODZ572lIt0cBslhFYaaTsplaYxBTNqWzcCzPOrElkUNlcqzIdKyhMVg3X/bCLHnbuefdRfk9aSb/DTfHgXharV31FJbUvMImt+dVJPF+Gx1cPlfSfvmEP/pD8xjqv2CljBqxE3GJM/RaEtLF7jXRQV1ZHo5rp+OtEr5fJVQpMgMmXQO+SMmxrGSmHG73L1VpZKleIjajEIj2Zjgh4cX4OCTJfOaxxfRAIuz7r274MDkH4k3VF4WuMhAzpdQTLaNgvKfGIHqaJM3FjhvAuqeDnxpok7sCfG0W4N+ujheoqysOnIrK2a6XoiGE/qm6cfgNOCf5qLdf93CgeAveSnncnkI9MPgNHOi83C7N8XUxy6E9XDE31vX68dxVbDBsU/tl/OLUnhUJuRm2FR0CjNH4tiGfHu8T4AO6lr3BsJzRcVIBLkX65AJM5CT54pfJnW1Al//E1O7LaOLJpE28QwWInSfqmXYgtLw9/f1Edo5AIctyfTkD6VswjaMGspfo3mGdvdjCjgYTdUo/M3ZDs4y6+1DZBYPGA6+2pXChQdTroZ8UpaG58jO2RSsy1wfyrmQ0gZ3eOdSmhw7GqR+3nUe30Ib0QS6XFv68elppa9EM/VMEvdk9uuwygpB4sFrxslrUVZSympit3kfOau2yxCiIHpjfe21f9pOvDhzuSt8TnwRcy4z6cmZO65nyB45e75xefu+TKgxNn3OO3Q0DxJqpS4oQJxDF+P3KrCgp4r0UQDg9O48fhX24qItdkjmzmhbcX+kriepFnKP8UdsIHgsCJjYZHs6LdlPM+lqPPXeJ/sFJZ/olequYFqDd5ssk4VbxKTzaMXYZca3B8P8i/7+2SB+v5iur//enlyv9hlNwa/meeB7kjShLa8Jg3PGXhrOTGTl5rl9w19rVEhGkW0fcKK0elIxJV2ABEZJlGPAR02fqV5403qvFauyI1n1CalcPsulk8Kh7akK34ML/NGe/HQ/lhJ2iySXQXF1tLbKoYdyQxJhx9+GA8h/DAesjnKHO014NM/+1ZSf/wsVty0vY7seQWZ26nxQwTVXdzMkArgUNd8147crw4vbvkJldLISlfkkU3t4sMemIEq0/4DTbhrrLvQeI4oujx4DCWe7GWjFQk60uJPkC/5vp6XKcWABZr9GnVqrSGKkQkTsa0CSRSLJuqXLGb66uzb+77XylgNTAlRqW5Jx38CvDMTuTFGm/7KUF2wZZzwhPeeHlzTPTN6OqJXWZNYA23GXqfsM/2NgJUADmwvQl6lnbKSSB9qxTY+De+HDgeG0xfvvSy5Y9C7b8DE7tSEsQD/KSEAcvCE0k5iLtvHM4JTZeC4pn5RBwDrKKd0JK2gBvvUmUlIXdIQV2g0LGAtaRk35isGnSInWJUyv3RFfbWyNycsvL0hnDR0KRq9Wz1YhhLBfAfvZe8rDK0CM6RjoCsmTP5cw3BLaWDz8Q2SWxmg6T4iQutB65shAgCRAACbWSgUWqNZJBXGP0/HDz2qjsaNjj6djpDv/x+42aN/Sjnu+/H2aefyQffTp+mrnf3b10k1/0tWB9WvtvUlQUcN+U/57DWT38OsE7mSudkWjTJQeetL/COdDw+f5Ejdo56KOaCi8znv/2nVK7YDFy6H+eWJDhX8ngcb99Qtoa1Lraeu9GAsNZ85mxT9hjQx2TgbTG8SsM0yuTN9dLQH6t/6qrkz8WZTvar+xl47ZJB0BFvjQB27FLZavhA1A5XkvKcwxYTEIPrUym5LelItndBt2tTdAyRIZJNX2bb6IbQi0TuvWSUfgBrbnQ8AuF58PrwN/IU2iIM33z/hitwyA7A3BD0eOLuGJKQYieXXZE+Q1JhSTIPgeog8qaqhG73cjQz4ABSZRER5TbvFwHExZ58TmwUaFLbj8xmtcDqxbnbUlKK9sGjNMU6xp/3ObdiIhUzMtVW6alBtltG0quEW9mxXIYS5++IV2u+GyPMQ2clVyBZiwzZjvupAgVw8CgDlmpiY6AZ3pB4ZxOQTSILmfKtThPXTRtbRHcCKpAdNQyVDnACdbLnle59KuifGAuK5GldXORUQ1WZlbTo+0AryCKJ6RGaEukKUOt/U7QuRyiT9JwavTh/vHu7eVTdf/t//Qf5/C4yPtf8DUvQX2gplbmRzdHJlYW0KZW5kb2JqCjI2IDAgb2JqIDw8L0NvbG9yU3BhY2VbL0lDQ0Jhc2VkIDcgMCBSXS9OYW1lL0ltMi9TdWJ0eXBlL0ltYWdlL0hlaWdodCA2ODIvRmlsdGVyL0ZsYXRlRGVjb2RlL1R5cGUvWE9iamVjdC9XaWR0aCAzNjYvTGVuZ3RoIDI0MDAvQml0c1BlckNvbXBvbmVudCA4Pj5zdHJlYW0KeJzt2jFOW1kUgOGdIMRCkqwC1mB2gFcQNhAWYPpM7961a9du3Vp0mSPRRBpAnvkVSx6+T7dAfu9Zt/p1zzO/fgEAXIbj8bgDPqXD4RDrsV6v7xeLm6vrWcuHpWVZn3B9+/J1CnB3e/u8Wv3bqmy323l8vmT+KDkC/h/mcPL042mS8tfPnyc+MvdPRubBP7ox4OK8vLzMnDJnjJlZPr5zJprJyH6/P8/GgIszMXn8/vjBDTMEzelFRoCP3d3ebjab967OXHP6EAR8WrvdbmLy5qUZfOZAMnPQmbcEXKIpyZu/yMyHy4fl+fcDXKKZX55Xq9M/B/in984eUxIvSYAT7XY7JQEiJQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQE6JQG6D0ryvFqdfz/AJdput2+WZApzv1icfz/AJZqDx5tTzPF4vLm6fnl5Of+WgItzd3s7x5I3Lz39eJp15v0AF2ez2UxJ3rt6OBy+ffk6Y845twRcnAnFeweSV5OauWe/359tS8BluV8sThleXmPycXCAT+j1d5nT34FMRmYIWj4spyrewQLThMfvj3PG+A//dTYZeX325urasqzPvOZcsV6vj8fjn8gUAAAAAAC/+xt439y+CmVuZHN0cmVhbQplbmRvYmoKNSAwIG9iajw8L0NvbG9yU3BhY2U8PC9EZWZhdWx0UkdCIDYgMCBSL0lDQzEzIDggMCBSPj4vUHJvY1NldFsvUERGL0ltYWdlQi9JbWFnZUMvVGV4dF0vRm9udDw8L0YzIDEwIDAgUi9GMjYgMTEgMCBSL0YyNCAxNyAwIFI+Pi9YT2JqZWN0PDwvSW0zIDIzIDAgUi9JbTQgMjQgMCBSL0ltMSAyNSAwIFIvSW0yIDI2IDAgUj4+Pj4KZW5kb2JqCjMgMCBvYmo8PC9Db250ZW50cyA0IDAgUi9CbGVlZEJveFswIDAgNjEyIDc5Ml0vVHlwZS9QYWdlL1Jlc291cmNlcyA1IDAgUi9Dcm9wQm94WzAgMCA2MTIgNzkyXS9QYXJlbnQgMSAwIFIvTWVkaWFCb3hbMCAwIDYxMiA3OTJdL1RyaW1Cb3hbMCAwIDYxMiA3OTJdPj4KZW5kb2JqCjEgMCBvYmo8PC9LaWRzWzMgMCBSXS9UeXBlL1BhZ2VzL0NvdW50IDE+PgplbmRvYmoKMjcgMCBvYmo8PC9UeXBlL0NhdGFsb2cvUGFnZXMgMSAwIFI+PgplbmRvYmoKMjggMCBvYmo8PC9Nb2REYXRlKEQ6MjAxOTAxMzAyMjQxMTBaKS9DcmVhdGlvbkRhdGUoRDoyMDE5MDEzMDIyNDExMFopL1Byb2R1Y2VyKGlUZXh0IDEuNCBcKGJ5IGxvd2FnaWUuY29tXCkpPj4KZW5kb2JqCnhyZWYKMCAyOQowMDAwMDAwMDAwIDY1NTM1IGYgCjAwMDAwNjU1NjkgMDAwMDAgbiAKMDAwMDAwMDAwMCA2NTUzNiBuIAowMDAwMDY1NDEwIDAwMDAwIG4gCjAwMDAwMDAwMTUgMDAwMDAgbiAKMDAwMDA2NTIxNyAwMDAwMCBuIAowMDAwMDA1OTcxIDAwMDAwIG4gCjAwMDAwMDMzMDcgMDAwMDAgbiAKMDAwMDAwNjgzMiAwMDAwMCBuIAowMDAwMDA2MDAzIDAwMDAwIG4gCjAwMDAwMDY4NjQgMDAwMDAgbiAKMDAwMDAxMjAwNSAwMDAwMCBuIAowMDAwMDA2OTU3IDAwMDAwIG4gCjAwMDAwMTE1OTggMDAwMDAgbiAKMDAwMDAxMTM3OSAwMDAwMCBuIAowMDAwMDA3NDgyIDAwMDAwIG4gCjAwMDAwMTEyODYgMDAwMDAgbiAKMDAwMDAxNzU5NCAwMDAwMCBuIAowMDAwMDEyMTQzIDAwMDAwIG4gCjAwMDAwMTcxNjAgMDAwMDAgbiAKMDAwMDAxNjkzOCAwMDAwMCBuIAowMDAwMDEyNjk4IDAwMDAwIG4gCjAwMDAwMTY4NDEgMDAwMDAgbiAKMDAwMDAxNzczNSAwMDAwMCBuIAowMDAwMDIzMDE2IDAwMDAwIG4gCjAwMDAwMjk3NzUgMDAwMDAgbiAKMDAwMDA2MjY0NCAwMDAwMCBuIAowMDAwMDY1NjE5IDAwMDAwIG4gCjAwMDAwNjU2NjQgMDAwMDAgbiAKdHJhaWxlcgo8PC9JbmZvIDI4IDAgUi9JRCBbPGIxMjZlODIwMDdjNzZhNmUxNTFlNzQ0Zjg2MjBmNDJmPjw1YmZlYjU4YTM5MjllZmRiZmQ2ZjU5MWM2MGIwNjc0MD5dL1Jvb3QgMjcgMCBSL1NpemUgMjk+PgpzdGFydHhyZWYKNjU3ODIKJSVFT0YK", "contentType": "application/pdf"}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}, "x-amzn-api-sandbox": {"dynamic": {}}}}}, "definitions": {"PackingSlip": {"type": "object", "required": ["content", "purchaseOrderNumber"], "properties": {"purchaseOrderNumber": {"type": "string", "description": "Purchase order number of the shipment that the packing slip is for.", "pattern": "^[a-zA-Z0-9]+$"}, "content": {"type": "string", "description": "A Base64encoded string of the packing slip PDF."}, "contentType": {"type": "string", "description": "The format of the file such as PDF, JPEG etc.", "enum": ["application/pdf"], "x-docgen-enum-table-extension": [{"value": "application/pdf", "description": "Portable Document Format (pdf)."}]}}, "description": "Packing slip information."}, "PackingSlipList": {"type": "object", "properties": {"pagination": {"$ref": "#/definitions/Pagination", "description": "The pagination elements required to retrieve the remaining data."}, "packingSlips": {"type": "array", "items": {"$ref": "#/definitions/PackingSlip"}, "description": "An array of packing slip objects."}}, "description": "A list of packing slips."}, "CreateShippingLabelsRequest": {"type": "object", "required": ["sellingParty", "shipFromParty"], "properties": {"sellingParty": {"description": "ID of the selling party or vendor.", "$ref": "#/definitions/PartyIdentification"}, "shipFromParty": {"description": "Warehouse code of vendor.", "$ref": "#/definitions/PartyIdentification"}, "containers": {"type": "array", "description": "A list of the packages in this shipment.", "items": {"$ref": "#/definitions/Container"}}}, "description": "The request body for the createShippingLabels operation."}, "SubmitShippingLabelsRequest": {"type": "object", "properties": {"shippingLabelRequests": {"type": "array", "items": {"$ref": "#/definitions/ShippingLabelRequest"}, "description": "An array of shipping label requests you want to process."}}, "description": "The request schema for the `submitShippingLabelRequest` operation."}, "ShippingLabelRequest": {"type": "object", "required": ["purchaseOrderNumber", "sellingParty", "shipFromParty"], "properties": {"purchaseOrderNumber": {"type": "string", "description": "Purchase order number of the order for which to create a shipping label.", "pattern": "^[a-zA-Z0-9]+$"}, "sellingParty": {"description": "ID of the selling party or vendor.", "$ref": "#/definitions/PartyIdentification"}, "shipFromParty": {"description": "Warehouse code of vendor.", "$ref": "#/definitions/PartyIdentification"}, "containers": {"type": "array", "description": "A list of the packages in this shipment.", "items": {"$ref": "#/definitions/Container"}}}, "description": "Represents the request payload to create a shipping label. Contains the purchase order number, selling party, ship from party, and a list of containers or packages in the shipment."}, "Item": {"type": "object", "required": ["itemSequenceNumber", "shippedQuantity"], "properties": {"itemSequenceNumber": {"type": "integer", "description": "Item Sequence Number for the item. This must be the same value as sent in order for a given item."}, "buyerProductIdentifier": {"type": "string", "description": "Buyer's Standard Identification Number (ASIN) of an item. Either buyerProductIdentifier or vendorProductIdentifier is required."}, "vendorProductIdentifier": {"type": "string", "description": "The vendor selected product identification of the item. Should be the same as was sent in the purchase order, like SKU Number."}, "shippedQuantity": {"description": "Total item quantity shipped in this shipment.", "$ref": "#/definitions/ItemQuantity"}}, "description": "Details of the item being shipped."}, "PackedItem": {"type": "object", "required": ["itemSequenceNumber", "packedQuantity"], "properties": {"itemSequenceNumber": {"type": "integer", "description": "Item Sequence Number for the item. This must be the same value as sent in the order for a given item."}, "buyerProductIdentifier": {"type": "string", "description": "Buyer's Standard Identification Number (ASIN) of an item. Either buyerProductIdentifier or vendorProductIdentifier is required."}, "pieceNumber": {"type": "integer", "description": "The piece number of the item in this container. This is required when the item is split across different containers."}, "vendorProductIdentifier": {"type": "string", "description": "The vendor selected product identification of the item. Should be the same as was sent in the Purchase Order, like SKU Number."}, "packedQuantity": {"description": "Total item quantity packed in the container.", "$ref": "#/definitions/ItemQuantity"}}, "description": "Represents an item packed into a container for shipping."}, "PartyIdentification": {"description": "Name, address, and tax details for a party.", "type": "object", "required": ["partyId"], "properties": {"partyId": {"type": "string", "description": "Assigned Identification for the party."}, "address": {"description": "Identification of the party by address.", "$ref": "#/definitions/Address"}, "taxRegistrationDetails": {"type": "array", "description": "Tax registration details of the entity.", "items": {"$ref": "#/definitions/TaxRegistrationDetails"}}}}, "ShipmentDetails": {"type": "object", "required": ["shipmentStatus", "shippedDate"], "properties": {"shippedDate": {"type": "string", "format": "date-time", "description": "This field indicates the date of the departure of the shipment from vendor's location. Vendors are requested to send ASNs within 30 minutes of departure from their warehouse/distribution center or at least 6 hours prior to the appointment time at the Amazon destination warehouse, whichever is sooner. Shipped date mentioned in the Shipment Confirmation should not be in the future."}, "shipmentStatus": {"type": "string", "description": "Indicate the shipment status.", "enum": ["SHIPPED", "FLOOR_DENIAL"], "x-docgen-enum-table-extension": [{"value": "SHIPPED", "description": "Orders that have left the warehouse have shipped status."}, {"value": "FLOOR_DENIAL", "description": "Status for orders rejected due to quality issues with products on the floor, or the physical and virtual inventory do not match."}]}, "isPriorityShipment": {"type": "boolean", "description": "Provide the priority of the shipment."}, "vendorOrderNumber": {"type": "string", "description": "The vendor order number is a unique identifier generated by a vendor for their reference."}, "estimatedDeliveryDate": {"type": "string", "format": "date-time", "description": "Date on which the shipment is expected to reach the buyer's warehouse. It needs to be an estimate based on the average transit time between the ship-from location and the destination. The exact appointment time will be provided by buyer and is potentially not known when creating the shipment confirmation."}}, "description": "Details about a shipment."}, "StatusUpdateDetails": {"type": "object", "required": ["reasonCode", "statusCode", "statusDateTime", "statusLocationAddress", "trackingNumber"], "properties": {"trackingNumber": {"type": "string", "description": "This is required to be provided for every package and should match with the trackingNumber sent for the shipment confirmation."}, "statusCode": {"type": "string", "description": "The shipment status code for the package that provides transportation information for Amazon tracking systems and the final customer. For more information, refer to the [Additional Fields Explanation](https://developer-docs.amazon.com/sp-api/docs/vendor-direct-fulfillment-shipping-api-use-case-guide#additional-fields-explanation)."}, "reasonCode": {"type": "string", "description": "Provides a reason code for the package status that provides additional information about the transportation status. For more information, refer to the [Additional Fields Explanation](https://developer-docs.amazon.com/sp-api/docs/vendor-direct-fulfillment-shipping-api-use-case-guide#additional-fields-explanation)."}, "statusDateTime": {"type": "string", "format": "date-time", "description": "The date and time when the shipment status was updated. This field is expected to be in ISO-8601 date/time format, with UTC time zone or UTC offset. For example, 2020-07-16T23:00:00Z or 2020-07-16T23:00:00+01:00."}, "statusLocationAddress": {"$ref": "#/definitions/Address"}, "shipmentSchedule": {"$ref": "#/definitions/ShipmentSchedule"}}, "description": "Details for the shipment status update given by the vendor for the specific package."}, "TaxRegistrationDetails": {"type": "object", "required": ["taxRegistrationNumber"], "properties": {"taxRegistrationType": {"type": "string", "description": "Tax registration type for the entity.", "enum": ["VAT", "GST"], "x-docgen-enum-table-extension": [{"value": "VAT", "description": "Value-added tax."}, {"value": "GST", "description": "Goods and Services Tax (GST)."}]}, "taxRegistrationNumber": {"type": "string", "description": "Tax registration number for the party. For example, VAT ID."}, "taxRegistrationAddress": {"description": "Address associated with the tax registration number.", "$ref": "#/definitions/Address"}, "taxRegistrationMessages": {"type": "string", "description": "Tax registration message that can be used for additional tax related details."}}, "description": "Tax registration details of the entity."}, "Address": {"type": "object", "required": ["addressLine1", "countryCode", "name"], "properties": {"name": {"type": "string", "description": "The name of the person, business or institution at that address."}, "addressLine1": {"type": "string", "description": "First line of the address."}, "addressLine2": {"type": "string", "description": "Additional street address information, if required."}, "addressLine3": {"type": "string", "description": "Additional street address information, if required."}, "city": {"type": "string", "description": "The city where the person, business or institution is located."}, "county": {"type": "string", "description": "The county where person, business or institution is located."}, "district": {"type": "string", "description": "The district where person, business or institution is located."}, "stateOrRegion": {"type": "string", "description": "The state or region where person, business or institution is located."}, "postalCode": {"type": "string", "description": "The postal code of that address. It contains a series of letters or digits or both, sometimes including spaces or punctuation."}, "countryCode": {"type": "string", "description": "The two digit country code in ISO 3166-1 alpha-2 format."}, "phone": {"type": "string", "description": "The phone number of the person, business or institution located at that address."}}, "description": "Address of the party."}, "ShipmentSchedule": {"type": "object", "properties": {"estimatedDeliveryDateTime": {"type": "string", "format": "date-time", "description": "Date on which the shipment is expected to reach the customer delivery location. This field is expected to be in ISO-8601 date/time format, with UTC time zone or UTC offset. For example, 2020-07-16T23:00:00Z or 2020-07-16T23:00:00+01:00."}, "apptWindowStartDateTime": {"type": "string", "format": "date-time", "description": "This field indicates the date and time at the start of the appointment window scheduled to deliver the shipment. This field is expected to be in ISO-8601 date/time format, with UTC time zone or UTC offset. For example, 2020-07-16T23:00:00Z or 2020-07-16T23:00:00+01:00."}, "apptWindowEndDateTime": {"type": "string", "format": "date-time", "description": "This field indicates the date and time at the end of the appointment window scheduled to deliver the shipment. This field is expected to be in ISO-8601 date/time format, with UTC time zone or UTC offset. For example, 2020-07-16T23:00:00Z or 2020-07-16T23:00:00+01:00."}}, "description": "Details about the estimated delivery window."}, "Dimensions": {"type": "object", "required": ["height", "length", "unitOfMeasure", "width"], "properties": {"length": {"description": "The length of the container.", "$ref": "#/definitions/Decimal"}, "width": {"description": "The width of the container.", "$ref": "#/definitions/Decimal"}, "height": {"description": "The height of the container.", "$ref": "#/definitions/Decimal"}, "unitOfMeasure": {"type": "string", "description": "The unit of measure for dimensions.", "enum": ["IN", "CM"], "x-docgen-enum-table-extension": [{"value": "IN", "description": "Inches"}, {"value": "CM", "description": "Centimeters"}]}}, "description": "Physical dimensional measurements of a container."}, "Weight": {"type": "object", "required": ["unitOfMeasure", "value"], "properties": {"unitOfMeasure": {"type": "string", "description": "The unit of measurement.", "enum": ["KG", "LB"], "x-docgen-enum-table-extension": [{"value": "KG", "description": "Kilogram"}, {"value": "LB", "description": "Pounds (Libra for Latin)."}]}, "value": {"description": "The measurement value.", "$ref": "#/definitions/Decimal"}}, "description": "The weight."}, "Decimal": {"type": "string", "description": "A decimal number with no loss of precision. Useful when precision loss is unacceptable, as with currencies. Follows RFC7159 for number representation.  <br>**Pattern** : `^-?(0|([1-9]\\\\d*))(\\\\.\\\\d+)?([eE][+-]?\\\\d+)?$`."}, "ItemQuantity": {"type": "object", "required": ["amount", "unitOfMeasure"], "properties": {"amount": {"type": "integer", "description": "Quantity of units shipped for a specific item at a shipment level. If the item is present only in certain packages or pallets within the shipment, please provide this at the appropriate package or pallet level."}, "unitOfMeasure": {"type": "string", "description": "Unit of measure for the shipped quantity."}}, "description": "Details of item quantity."}, "ShippingLabelList": {"type": "object", "properties": {"pagination": {"$ref": "#/definitions/Pagination"}, "shippingLabels": {"type": "array", "items": {"$ref": "#/definitions/ShippingLabel"}, "description": "An array that contains the details of the generated shipping labels."}}, "description": "Response payload with the shipping labels list."}, "LabelData": {"type": "object", "required": ["content"], "properties": {"packageIdentifier": {"type": "string", "description": "Identifier for the package. The first package will be 001, the second 002, and so on. This number is used as a reference to refer to this package from the pallet level."}, "trackingNumber": {"type": "string", "description": "Package tracking identifier from the shipping carrier."}, "shipMethod": {"type": "string", "description": "Ship method to be used for shipping the order. Amazon defines Ship Method Codes indicating shipping carrier and shipment service level. Ship Method Codes are case and format sensitive. The same ship method code should returned on the shipment confirmation. Note that the Ship Method Codes are vendor specific and will be provided to each vendor during the implementation."}, "shipMethodName": {"type": "string", "description": "Shipping method name for internal reference."}, "content": {"type": "string", "description": "This field will contain the Base64encoded string of the shipment label content."}}, "description": "Details of the shipment label."}, "ShippingLabel": {"type": "object", "required": ["labelData", "labelFormat", "purchaseOrderNumber", "sellingParty", "shipFromParty"], "properties": {"purchaseOrderNumber": {"type": "string", "description": "This field will contain the Purchase Order Number for this order.", "pattern": "^[a-zA-Z0-9]+$"}, "sellingParty": {"description": "ID of the selling party or vendor.", "$ref": "#/definitions/PartyIdentification"}, "shipFromParty": {"description": "Warehouse code of vendor.", "$ref": "#/definitions/PartyIdentification"}, "labelFormat": {"type": "string", "description": "Format of the label.", "enum": ["PNG", "ZPL"], "x-docgen-enum-table-extension": [{"value": "PNG", "description": "Portable Network Graphics (png) format."}, {"value": "ZPL", "description": "Zebra Programming Language (zpl) format."}]}, "labelData": {"type": "array", "description": "Provides the details of the packages in this shipment.", "items": {"$ref": "#/definitions/LabelData"}}}, "description": "Shipping label information for an order. Includes the purchase order number, selling party, ship from party, label format, and package details."}, "SubmitShipmentConfirmationsRequest": {"type": "object", "properties": {"shipmentConfirmations": {"type": "array", "items": {"$ref": "#/definitions/ShipmentConfirmation"}, "description": "An array of `ShipmentConfirmation` objects, each represents confirmation details for a specific shipment."}}, "description": "The `submitShipmentConfirmations` request schema."}, "ShipmentConfirmation": {"type": "object", "required": ["items", "purchaseOrderNumber", "sellingParty", "shipFromParty", "shipmentDetails"], "properties": {"purchaseOrderNumber": {"type": "string", "description": "Purchase order number corresponding to the shipment.", "pattern": "^[a-zA-Z0-9]+$"}, "shipmentDetails": {"description": "Shipment information.", "$ref": "#/definitions/ShipmentDetails"}, "sellingParty": {"description": "ID of the selling party or vendor.", "$ref": "#/definitions/PartyIdentification"}, "shipFromParty": {"description": "Warehouse code of vendor.", "$ref": "#/definitions/PartyIdentification"}, "items": {"type": "array", "description": "Provide the details of the items in this shipment. If any of the item details field is common at a package or a pallet level, then provide them at the corresponding package.", "items": {"$ref": "#/definitions/Item"}}, "containers": {"type": "array", "description": "Provide the details of the items in this shipment. If any of the item details field is common at a package or a pallet level, then provide them at the corresponding package.", "items": {"$ref": "#/definitions/Container"}}}, "description": "Represents the confirmation details of a shipment. Includes the purchase order number and other shipment details."}, "SubmitShipmentStatusUpdatesRequest": {"type": "object", "properties": {"shipmentStatusUpdates": {"type": "array", "items": {"$ref": "#/definitions/ShipmentStatusUpdate"}, "minItems": 1, "description": "Contains a list of one or more `ShipmentStatusUpdate` objects, each represents a status update of a specific shipment."}}, "description": "The `submitShipmentStatusUpdates` request schema."}, "ShipmentStatusUpdate": {"type": "object", "required": ["purchaseOrderNumber", "sellingParty", "shipFromParty", "statusUpdateDetails"], "properties": {"purchaseOrderNumber": {"type": "string", "description": "Purchase order number of the shipment for which to update the shipment status.", "pattern": "^[a-zA-Z0-9]+$"}, "sellingParty": {"description": "ID of the selling party or vendor.", "$ref": "#/definitions/PartyIdentification"}, "shipFromParty": {"description": "Warehouse code of vendor.", "$ref": "#/definitions/PartyIdentification"}, "statusUpdateDetails": {"description": "Provide the details about the status of the shipment at that particular point of time.", "$ref": "#/definitions/StatusUpdateDetails"}}, "description": "Represents a shipment status update."}, "CustomerInvoiceList": {"type": "object", "properties": {"pagination": {"$ref": "#/definitions/Pagination", "description": "The pagination elements required to retrieve the remaining data."}, "customerInvoices": {"type": "array", "description": "Represents a customer invoice within the `CustomerInvoiceList`.", "items": {"$ref": "#/definitions/CustomerInvoice"}}}, "description": "Represents a list of customer invoices, potentially paginated."}, "Pagination": {"type": "object", "properties": {"nextToken": {"type": "string", "description": "A generated string used to pass information to your next request. If NextToken is returned, pass the value of NextToken to the next request. If NextToken is not returned, there are no more order items to return."}}, "description": "The pagination elements required to retrieve the remaining data."}, "CustomerInvoice": {"type": "object", "required": ["content", "purchaseOrderNumber"], "properties": {"purchaseOrderNumber": {"type": "string", "description": "The purchase order number for this order.", "pattern": "^[a-zA-Z0-9]+$"}, "content": {"type": "string", "description": "The Base64encoded customer invoice."}}, "description": "Represents a customer invoice associated with a purchase order."}, "TransactionReference": {"type": "object", "properties": {"transactionId": {"type": "string", "description": "GUID to identify this transaction. This value can be used with the Transaction Status API to return the status of this transaction."}}, "description": "Response that contains the transaction ID."}, "ErrorList": {"type": "object", "description": "A list of error responses returned when a request is unsuccessful.", "required": ["errors"], "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/Error"}, "description": "An array of error objects that represents individual errors encountered during the request."}}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}, "Container": {"type": "object", "required": ["containerIdentifier", "containerType", "packedItems", "weight"], "properties": {"containerType": {"type": "string", "description": "The type of container.", "enum": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "x-docgen-enum-table-extension": [{"value": "<PERSON><PERSON>", "description": "Packing container type. Typically used for drinks or food."}, {"value": "<PERSON><PERSON><PERSON>", "description": "A flat transport structure which supports goods in a stable fashion while being lifted by a forklift."}]}, "containerIdentifier": {"type": "string", "description": "The container identifier."}, "trackingNumber": {"type": "string", "description": "The tracking number."}, "manifestId": {"type": "string", "description": "The manifest identifier."}, "manifestDate": {"type": "string", "description": "The date of the manifest."}, "shipMethod": {"type": "string", "description": "The shipment method. This property is required when calling the submitShipmentConfirmations operation, and optional otherwise."}, "scacCode": {"type": "string", "description": "SCAC code required for NA VOC vendors only."}, "carrier": {"type": "string", "description": "Carrier required for EU VOC vendors only."}, "containerSequenceNumber": {"type": "integer", "description": "An integer that must be submitted for multi-box shipments only, where one item may come in separate packages."}, "dimensions": {"$ref": "#/definitions/Dimensions"}, "weight": {"$ref": "#/definitions/Weight"}, "packedItems": {"type": "array", "description": "A list of packed items.", "items": {"$ref": "#/definitions/PackedItem"}}}, "description": "A container for shipping and packing items."}}}