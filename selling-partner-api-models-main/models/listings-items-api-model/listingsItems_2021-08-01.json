{"swagger": "2.0", "info": {"description": "The Selling Partner API for Listings Items (Listings Items API) provides programmatic access to selling partner listings on Amazon. Use this API in collaboration with the Selling Partner API for Product Type Definitions, which you use to retrieve the information about Amazon product types needed to use the Listings Items API.\n\nFor more information, see the [Listings Items API Use Case Guide](https://developer-docs.amazon.com/sp-api/docs/listings-items-api-v2021-08-01-use-case-guide).", "version": "2021-08-01", "title": "Selling Partner API for Listings Items", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/listings/2021-08-01/items/{sellerId}/{sku}": {"delete": {"tags": ["listings"], "description": "Delete a listings item for a selling partner.\n\n**Note:** The parameters associated with this operation may contain special characters that must be encoded to successfully call the API. To avoid errors with SKUs when encoding URLs, refer to [URL Encoding](https://developer-docs.amazon.com/sp-api/docs/url-encoding).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "deleteListingsItem", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "sellerId", "description": "A selling partner identifier, such as a merchant account or vendor code.", "in": "path", "required": true, "type": "string"}, {"name": "sku", "description": "A selling partner provided identifier for an Amazon listing.", "in": "path", "required": true, "type": "string"}, {"name": "marketplaceIds", "description": "A comma-delimited list of Amazon marketplace identifiers for the request.", "in": "query", "required": true, "type": "array", "maxItems": 1, "items": {"type": "string"}, "collectionFormat": "csv", "x-example": "ATVPDKIKX0DER"}, {"name": "issueLocale", "description": "A locale for localization of issues. When not provided, the default language code of the first marketplace is used. Examples: `en_US`, `fr_CA`, `fr_FR`. Localized messages default to `en_US` when a localization is not available in the specified locale.", "in": "query", "required": false, "type": "string", "x-example": "en_US"}], "responses": {"200": {"description": "Successfully understood the listings item delete request. See the response to determine whether the submission has been accepted.", "schema": {"$ref": "#/definitions/ListingsItemSubmissionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "examples": {"application/json": {"sku": "GM-ZDPI-9B4E", "status": "ACCEPTED", "submissionId": "f1dc2914-75dd-11ea-bc55-0242ac130003", "issues": []}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"sku": "GM-ZDPI-9B4E", "status": "ACCEPTED", "submissionId": "f1dc2914-75dd-11ea-bc55-0242ac130003", "issues": []}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"sku": {"value": "BadSKU"}}}, "response": {"errors": [{"code": "BAD_REQUEST", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "415": {"description": "The request payload is in an unsupported format.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}}}, "get": {"tags": ["listings"], "description": "Returns details about a listings item for a selling partner.\n\n**Note:** The parameters associated with this operation may contain special characters that must be encoded to successfully call the API. To avoid errors with SKUs when encoding URLs, refer to [URL Encoding](https://developer-docs.amazon.com/sp-api/docs/url-encoding).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getListingsItem", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "sellerId", "description": "A selling partner identifier, such as a merchant account or vendor code.", "in": "path", "required": true, "type": "string"}, {"name": "sku", "description": "A selling partner provided identifier for an Amazon listing.", "in": "path", "required": true, "type": "string"}, {"name": "marketplaceIds", "description": "A comma-delimited list of Amazon marketplace identifiers for the request.", "in": "query", "required": true, "type": "array", "maxItems": 1, "items": {"type": "string"}, "collectionFormat": "csv", "x-example": "ATVPDKIKX0DER"}, {"name": "issueLocale", "description": "A locale for localization of issues. When not provided, the default language code of the first marketplace is used. Examples: `en_US`, `fr_CA`, `fr_FR`. Localized messages default to `en_US` when a localization is not available in the specified locale.", "in": "query", "required": false, "type": "string", "x-example": "en_US"}, {"name": "includedData", "description": "A comma-delimited list of data sets to include in the response. Default: `summaries`.", "in": "query", "required": false, "type": "array", "items": {"enum": ["summaries", "attributes", "issues", "offers", "fulfillmentAvailability", "procurement"], "x-docgen-enum-table-extension": [{"value": "summaries", "description": "Summary details of the listing item."}, {"value": "attributes", "description": "A JSON object containing structured listing item attribute data keyed by attribute name."}, {"value": "issues", "description": "The issues associated with the listing item."}, {"value": "offers", "description": "The current offers for the listing item."}, {"value": "fulfillmentAvailability", "description": "The fulfillment availability details for the listing item."}, {"value": "procurement", "description": "The vendor procurement details for the listing item. "}], "type": "string"}, "collectionFormat": "csv", "x-example": "summaries", "default": ["summaries"]}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/Item"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "examples": {"application/json": {"sku": "GM-ZDPI-9B4E", "summaries": [{"marketplaceId": "ATVPDKIKX0DER", "asin": "B071VG5N9D", "productType": "LUGGAGE", "conditionType": "new_new", "status": ["BUYABLE"], "itemName": "Hardside Carry-On Spinner Suitcase Luggage", "createdDate": "2021-02-01T00:00:00Z", "lastUpdatedDate": "2021-03-01T00:00:00Z", "mainImage": {"link": "https://www.example.com/luggage.png", "height": 500, "width": 500}}], "offers": [{"marketplaceId": "ATVPDKIKX0DER", "offerType": "B2C", "price": {"currencyCode": "USD", "amount": "100.00"}, "audience": {"value": "ALL", "displayName": "Sell on Amazon"}}], "fulfillmentAvailability": [{"fulfillmentChannelCode": "DEFAULT", "quantity": 100}], "issues": [{"code": "90220", "message": "'size' is required but not supplied.", "severity": "ERROR", "attributeNames": ["size"], "categories": ["MISSING_ATTRIBUTE"]}, {"code": "18027", "message": "We believe the main image has text, logo, graphic or watermark which is not permitted for this product type. Please submit a compliant image to lift the suppression. Also refer to Product image requirements.", "severity": "ERROR", "categories": ["INVALID_IMAGE"], "enforcements": {"actions": [{"action": "SEARCH_SUPPRESSED"}], "exemption": {"status": "EXEMPT_UNTIL_EXPIRY_DATE", "expiryDate": "2025-05-28T00:36:48.914Z"}}}, {"code": "99300", "message": "Product Detail Page Rules Violation (Inaccurate information on product detail page)", "severity": "ERROR", "categories": [], "enforcements": {"actions": [{"action": "ATTRIBUTE_SUPPRESSED"}], "exemption": {"status": "EXEMPT"}}}, {"code": "18155", "message": "The 'minimum price' is greater than the selling price (excluding shipping) for the listing. Please review and update your price and/or minimum price.", "severity": "ERROR", "categories": ["INVALID_PRICE"], "enforcements": {"actions": [{"action": "LISTING_SUPPRESSED"}], "exemption": {"status": "NOT_EXEMPT"}}}, {"code": "18742", "message": "Restricted Products Policy Violation", "severity": "ERROR", "categories": [], "enforcements": {"actions": [{"action": "CATALOG_ITEM_REMOVED"}], "exemption": {"status": "NOT_EXEMPT"}}}]}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {}}}, "response": {"sku": "GM-ZDPI-9B4E", "summaries": [{"marketplaceId": "ATVPDKIKX0DER", "asin": "B071VG5N9D", "productType": "LUGGAGE", "conditionType": "new_new", "status": ["BUYABLE"], "itemName": "Hardside Carry-On Spinner Suitcase Luggage", "createdDate": "2021-02-01T00:00:00Z", "lastUpdatedDate": "2021-03-01T00:00:00Z", "mainImage": {"link": "https://www.example.com/luggage.png", "height": 500, "width": 500}}], "offers": [{"marketplaceId": "ATVPDKIKX0DER", "offerType": "B2C", "price": {"currencyCode": "USD", "amount": "100.00"}, "audience": {"value": "ALL", "displayName": "Sell on Amazon"}}], "fulfillmentAvailability": [{"fulfillmentChannelCode": "DEFAULT", "quantity": 100}], "issues": [{"code": "90220", "message": "'size' is required but not supplied.", "severity": "ERROR", "attributeNames": ["size"], "categories": ["MISSING_ATTRIBUTE"]}, {"code": "18027", "message": "We believe the main image has text, logo, graphic or watermark which is not permitted for this product type. Please submit a compliant image to lift the suppression. Also refer to Product image requirements.", "severity": "ERROR", "categories": ["INVALID_IMAGE"], "enforcements": {"actions": [{"action": "SEARCH_SUPPRESSED"}], "exemption": {"status": "EXEMPT_UNTIL_EXPIRY_DATE", "expiryDate": "2025-05-28T00:36:48.914Z"}}}, {"code": "99300", "message": "Product Detail Page Rules Violation (Inaccurate information on product detail page)", "severity": "ERROR", "categories": [], "enforcements": {"actions": [{"action": "ATTRIBUTE_SUPPRESSED"}], "exemption": {"status": "EXEMPT"}}}, {"code": "18155", "message": "The 'minimum price' is greater than the selling price (excluding shipping) for the listing. Please review and update your price and/or minimum price.", "severity": "ERROR", "categories": ["INVALID_PRICE"], "enforcements": {"actions": [{"action": "LISTING_SUPPRESSED"}], "exemption": {"status": "NOT_EXEMPT"}}}, {"code": "18742", "message": "Restricted Products Policy Violation", "severity": "ERROR", "categories": [], "enforcements": {"actions": [{"action": "CATALOG_ITEM_REMOVED"}], "exemption": {"status": "NOT_EXEMPT"}}}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"sku": {"value": "BadSKU"}}}, "response": {"errors": [{"code": "BAD_REQUEST", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "404": {"description": "The resource specified does not exist.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "415": {"description": "The request payload is in an unsupported format.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}}}, "patch": {"tags": ["listings"], "description": "Partially update (patch) a listings item for a selling partner. Only top-level listings item attributes can be patched. Patching nested attributes is not supported.\n\n**Note:** This operation has a throttling rate of one request per second when `mode` is `VALIDATION_PREVIEW`.\n\n**Note:** The parameters associated with this operation may contain special characters that must be encoded to successfully call the API. To avoid errors with SKUs when encoding URLs, refer to [URL Encoding](https://developer-docs.amazon.com/sp-api/docs/url-encoding).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "patchListingsItem", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "sellerId", "description": "A selling partner identifier, such as a merchant account or vendor code.", "in": "path", "required": true, "type": "string"}, {"name": "sku", "description": "A selling partner provided identifier for an Amazon listing.", "in": "path", "required": true, "type": "string"}, {"name": "marketplaceIds", "description": "A comma-delimited list of Amazon marketplace identifiers for the request.", "in": "query", "required": true, "type": "array", "maxItems": 1, "items": {"type": "string"}, "collectionFormat": "csv", "x-example": "ATVPDKIKX0DER"}, {"name": "includedData", "description": "A comma-delimited list of data sets to include in the response. Default: `issues`.", "in": "query", "required": false, "type": "array", "items": {"enum": ["identifiers", "issues"], "x-docgen-enum-table-extension": [{"value": "identifiers", "description": "Identifiers associated with the item in the Amazon catalog, such as Amazon Standard Identification Number (ASIN). Can only be requested when `mode` is `VALIDATION_PREVIEW`."}, {"value": "issues", "description": "The issues associated with the listing item."}], "type": "string"}, "collectionFormat": "csv", "x-example": "issues", "default": ["issues"]}, {"name": "mode", "description": "The mode of operation for the request.", "in": "query", "enum": ["VALIDATION_PREVIEW"], "x-docgen-enum-table-extension": [{"value": "VALIDATION_PREVIEW", "description": "Indicates the submitted data should be validated using the values provided in the payload and validation errors the selling partner account may face. This will synchronously perform the same checks that are preformed on submissions after being accepted for processing, but without persisting to the selling partner's catalog."}], "required": false, "type": "string", "x-example": "VALIDATION_PREVIEW"}, {"name": "issueLocale", "description": "A locale for localization of issues. When not provided, the default language code of the first marketplace is used. Examples: `en_US`, `fr_CA`, `fr_FR`. Localized messages default to `en_US` when a localization is not available in the specified locale.", "in": "query", "required": false, "type": "string", "x-example": "en_US"}, {"name": "body", "description": "The request body schema for the `patchListingsItem` operation.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ListingsItemPatchRequest"}}], "responses": {"200": {"description": "Successfully understood the listings item patch request. See the response to determine if the submission was accepted.", "schema": {"$ref": "#/definitions/ListingsItemSubmissionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "examples": {"application/json": {"sku": "GM-ZDPI-9B4E", "status": "ACCEPTED", "submissionId": "f1dc2914-75dd-11ea-bc55-0242ac130003", "issues": []}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {}}}, "response": {"sku": "GM-ZDPI-9B4E", "status": "ACCEPTED", "submissionId": "f1dc2914-75dd-11ea-bc55-0242ac130003", "issues": []}}, {"request": {"parameters": {"sku": {"value": "VALIDATION_VALID"}, "marketplaceIds": {"value": ["ATVPDKIKX0DER"]}, "includedData": {"value": ["identifiers", "issues"]}, "mode": {"value": "VALIDATION_PREVIEW"}}}, "response": {"sku": "VALIDATION_VALID", "status": "VALID", "submissionId": "1edc94ea-a3d7-210b-42bc-0ec72c1384762", "identifiers": [{"marketplaceId": "ATVPDKIKX0DER", "asin": "B07N4M94X4"}], "issues": []}}, {"request": {"parameters": {"sku": {"value": "VALIDATION_INVALID"}, "marketplaceIds": {"value": ["ATVPDKIKX0DER"]}, "includedData": {"value": ["identifiers", "issues"]}, "mode": {"value": "VALIDATION_PREVIEW"}}}, "response": {"sku": "VALIDATION_INVALID", "status": "INVALID", "submissionId": "a1c562c2-1695-11ee-be56-0242ac120002", "identifiers": [], "issues": [{"code": "90000900", "message": "The attributes are invalid.", "severity": "ERROR", "attributeNames": ["fake_attribute"], "categories": ["INVALID_ATTRIBUTE"], "enforcements": {"actions": [{"action": "ATTRIBUTE_SUPPRESSED"}], "exemption": {"status": "NOT_EXEMPT"}}}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"sku": {"value": "BadSKU"}}}, "response": {"errors": [{"code": "BAD_REQUEST", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "415": {"description": "The request payload is in an unsupported format.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}}}, "put": {"tags": ["listings"], "description": "Creates or fully updates an existing listings item for a selling partner.\n\n**Note:** This operation has a throttling rate of one request per second when `mode` is `VALIDATION_PREVIEW`.\n\n**Note:** The parameters associated with this operation may contain special characters that must be encoded to successfully call the API. To avoid errors with SKUs when encoding URLs, refer to [URL Encoding](https://developer-docs.amazon.com/sp-api/docs/url-encoding).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "putListingsItem", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "sellerId", "description": "A selling partner identifier, such as a merchant account or vendor code.", "in": "path", "required": true, "type": "string"}, {"name": "sku", "description": "A selling partner provided identifier for an Amazon listing.", "in": "path", "required": true, "type": "string"}, {"name": "marketplaceIds", "description": "A comma-delimited list of Amazon marketplace identifiers for the request.", "in": "query", "required": true, "type": "array", "maxItems": 1, "items": {"type": "string"}, "collectionFormat": "csv", "x-example": "ATVPDKIKX0DER"}, {"name": "includedData", "description": "A comma-delimited list of data sets to include in the response. Default: `issues`.", "in": "query", "required": false, "type": "array", "items": {"enum": ["identifiers", "issues"], "x-docgen-enum-table-extension": [{"value": "identifiers", "description": "Identifiers associated with the item in the Amazon catalog, such as Amazon Standard Identification Number (ASIN). Can only be requested when `mode` is `VALIDATION_PREVIEW`."}, {"value": "issues", "description": "The issues associated with the listing item."}], "type": "string"}, "collectionFormat": "csv", "x-example": "issues", "default": ["issues"]}, {"name": "mode", "description": "The mode of operation for the request.", "in": "query", "enum": ["VALIDATION_PREVIEW"], "x-docgen-enum-table-extension": [{"value": "VALIDATION_PREVIEW", "description": "Indicates the submitted data should be validated using the values provided in the payload and validation errors the selling partner account may face. This will synchronously perform the same checks that are preformed on submissions after being accepted for processing, but without persisting to the selling partner's catalog."}], "required": false, "type": "string", "x-example": "VALIDATION_PREVIEW"}, {"name": "issueLocale", "description": "A locale for localization of issues. When not provided, the default language code of the first marketplace is used. Examples: `en_US`, `fr_CA`, `fr_FR`. Localized messages default to `en_US` when a localization is not available in the specified locale.", "in": "query", "required": false, "type": "string", "x-example": "en_US"}, {"name": "body", "description": "The request body schema for the `putListingsItem` operation.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ListingsItemPutRequest"}}], "responses": {"200": {"description": "Successfully understood the request to create or fully-update a listings item. See the response to determine if the submission has been accepted.", "schema": {"$ref": "#/definitions/ListingsItemSubmissionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "examples": {"application/json": {"sku": "GM-ZDPI-9B4E", "status": "ACCEPTED", "submissionId": "f1dc2914-75dd-11ea-bc55-0242ac130003", "issues": []}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {}}}, "response": {"sku": "GM-ZDPI-9B4E", "status": "ACCEPTED", "submissionId": "f1dc2914-75dd-11ea-bc55-0242ac130003", "issues": []}}, {"request": {"parameters": {"sku": {"value": "VALIDATION_VALID"}, "marketplaceIds": {"value": ["ATVPDKIKX0DER"]}, "includedData": {"value": ["identifiers", "issues"]}, "mode": {"value": "VALIDATION_PREVIEW"}}}, "response": {"sku": "VALIDATION_VALID", "status": "VALID", "submissionId": "1edc94ea-a3d7-210b-42bc-0ec72c1384762", "identifiers": [{"marketplaceId": "ATVPDKIKX0DER", "asin": "B07N4M94X4"}], "issues": []}}, {"request": {"parameters": {"sku": {"value": "VALIDATION_INVALID"}, "marketplaceIds": {"value": ["ATVPDKIKX0DER"]}, "includedData": {"value": ["identifiers", "issues"]}, "mode": {"value": "VALIDATION_PREVIEW"}}}, "response": {"sku": "VALIDATION_INVALID", "status": "INVALID", "submissionId": "a1c562c2-1695-11ee-be56-0242ac120002", "identifiers": [], "issues": [{"code": "90220", "message": "'condition_type' is required but not supplied.", "severity": "ERROR", "attributeNames": ["condition_type"], "categories": ["MISSING_ATTRIBUTE"], "enforcements": {"actions": [{"action": "LISTING_SUPPRESSED"}], "exemption": {"status": "NOT_EXEMPT"}}}, {"code": "90000900", "message": "The attributes are invalid.", "severity": "ERROR", "attributeNames": ["fake_attribute"], "categories": []}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"sku": {"value": "BadSKU"}}}, "response": {"errors": [{"code": "BAD_REQUEST", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "415": {"description": "The request payload is in an unsupported format.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}}}}, "/listings/2021-08-01/items/{sellerId}": {"get": {"tags": ["listings"], "description": "Search for and return list of listings items and respective details for a selling partner.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values then those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "searchListingsItems", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "sellerId", "description": "A selling partner identifier, such as a merchant account or vendor code.", "in": "path", "required": true, "type": "string"}, {"name": "marketplaceIds", "description": "A comma-delimited list of Amazon marketplace identifiers for the request.", "in": "query", "required": true, "type": "array", "maxItems": 1, "items": {"type": "string"}, "collectionFormat": "csv", "x-example": "ATVPDKIKX0DER"}, {"name": "identifiers", "description": "A comma-delimited list of product identifiers to search for listings items by. \n\n**Note**: \n1. Required when `identifiersType` is provided.", "in": "query", "required": false, "type": "array", "maxItems": 20, "items": {"type": "string"}, "collectionFormat": "csv", "x-example": "GM-ZDPI-9B4E"}, {"name": "identifiersType", "description": "Type of product identifiers to search for listings items by. \n\n**Note**: \n1. Required when `identifiers` is provided.", "in": "query", "required": false, "type": "string", "enum": ["ASIN", "EAN", "GTIN", "ISBN", "JAN", "MINSAN", "SKU", "UPC"], "x-docgen-enum-table-extension": [{"value": "ASIN", "description": "Amazon Standard Identification Number."}, {"value": "EAN", "description": "European Article Number."}, {"value": "GTIN", "description": "Global Trade Item Number."}, {"value": "ISBN", "description": "International Standard Book Number."}, {"value": "JAN", "description": "Japanese Article Number."}, {"value": "MINSAN", "description": "Minsan Code."}, {"value": "SKU", "description": "Stock Keeping Unit, a seller-specified identifier for an Amazon listing."}, {"value": "UPC", "description": "Universal Product Code."}], "x-example": "SKU"}, {"name": "pageSize", "description": "Number of results to be returned per page.", "in": "query", "required": false, "type": "integer", "maximum": 20, "default": 10, "x-example": 9}, {"name": "pageToken", "description": "A token to fetch a certain page when there are multiple pages worth of results.", "in": "query", "required": false, "type": "string", "x-example": "sdlkj234lkj234lksjdflkjwdflkjsfdlkj234234234234"}, {"name": "includedData", "description": "A comma-delimited list of data sets to include in the response. Default: summaries.", "in": "query", "required": false, "type": "array", "items": {"enum": ["summaries", "attributes", "issues", "offers", "fulfillmentAvailability", "procurement"], "x-docgen-enum-table-extension": [{"value": "summaries", "description": "Summary details of the listing item."}, {"value": "attributes", "description": "JSON object containing structured listing item attribute data keyed by attribute name."}, {"value": "issues", "description": "Issues associated with the listing item."}, {"value": "offers", "description": "Current offers for the listing item."}, {"value": "fulfillmentAvailability", "description": "Fulfillment availability details for the listing item."}, {"value": "procurement", "description": "Vendor procurement details for the listing item. "}], "type": "string"}, "collectionFormat": "csv", "x-example": "summaries", "default": ["summaries"]}, {"name": "issueLocale", "description": "A locale for localization of issues. When not provided, the default language code of the first marketplace is used. Examples: \"en_US\", \"fr_CA\", \"fr_FR\". Localized messages default to \"en_US\" when a localization is not available in the specified locale.", "in": "query", "required": false, "type": "string", "x-example": "en_US"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/ItemSearchResults"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "examples": {"application/json": {"numberOfResults": 3, "pagination": {"nextToken": "xsdflkj324lkjsdlkj3423klkjsdfkljlk2j34klj2l3k4jlksdjl234", "previousToken": "ilkjsdflkj234lkjds234234lkjl234lksjdflkj234234lkjsfsdflkj333d"}, "items": [{"sku": "GM-ZDPI-9B4E", "summaries": [{"marketplaceId": "ATVPDKIKX0DER", "asin": "B071VG5N9D", "productType": "LUGGAGE", "conditionType": "new_new", "status": ["BUYABLE"], "itemName": "Hardside Carry-On Spinner Suitcase Luggage", "createdDate": "2021-02-01T00:00:00Z", "lastUpdatedDate": "2021-03-01T00:00:00Z", "mainImage": {"link": "https://www.example.com/luggage.png", "height": 500, "width": 500}}], "attributes": {"total_hdmi_ports": [{"value": 4, "marketplace_id": "ATVPDKIKX0DER"}], "resolution": [{"language_tag": "en_US", "value": "4K", "marketplace_id": "ATVPDKIKX0DER"}], "item_weight": [{"unit": "pounds", "value": 107.6, "marketplace_id": "ATVPDKIKX0DER"}], "product_subcategory": [{"value": "50400120", "marketplace_id": "ATVPDKIKX0DER"}], "bullet_point": [{"language_tag": "en_US", "value": "SMART TV WITH UNIVERSAL GUIDE: Simple on-screen Guide is an easy way to find streaming content and live TV shows", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "100% COLOR VOLUME WITH QUANTUM DOTS: Powered by Quantum dots, Samsung’s 4K QLED TV offers over a billion shades of brilliant color and 100% color volume for exceptional depth of detail that will draw you in to the picture for the best 4K TV experience", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "QUANTUM PROCESSOR 4K: Intelligently powered processor instantly upscales content to 4K for sharp detail and refined color", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "QUANTUM HDR 4X: 4K depth of detail with high dynamic range powered by HDR10+ delivers the lightest to darkest colors, scene by scene, for amazing picture realism", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "AMBIENT MODE: Customizes and complements your living space by turning a blank screen of this big screen TV into enticing visuals including décor, info, photos and artwork", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "SMART TV FEATURES: OneRemote to control all compatible devices, Bixby voice command, on-screen universal guide, SmartThings to control compatible home appliances and devices, smart speaker expandability with Alexa and Google Assistant compatibility, and more", "marketplace_id": "ATVPDKIKX0DER"}], "item_dimensions": [{"width": {"unit": "inches", "value": 72.4}, "length": {"unit": "inches", "value": 2.4}, "height": {"unit": "inches", "value": 41.4}, "marketplace_id": "ATVPDKIKX0DER"}], "brand": [{"language_tag": "en_US", "value": "SAMSUNG", "marketplace_id": "ATVPDKIKX0DER"}], "generic_keyword": [{"language_tag": "en_US", "value": "oled 65 inch smart tv qled 75 inch tv 85 inch 4k tv smart tv 4k tv 65 inch qled 82 inch tv 8k tv oled 65 smart tv qled 65 inch 80 inch tv 4k 4k tv shield tv 2018", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "q9fn qled 2018 oled 4k 4k oled toshiba antenna dvd player outdoor tv kitchen tv fire tv firetv hdtv hd tv android shield tv gaming deals tv ears dvr speakers digital tv antenna tv android tv frame mount", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "4k hdr tv 70 tv shield tv 90 inch tv gaming tv 75 tv 65 inch 4k tv tv 65 inch smart tv 4k 65 inch 4k tv 65 inch 4k tv 4k tv uhd tv uhd tv 4k", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "smart tv 4k tv tv tv oled tv 65 inch smart tv 4k tv 65 inch smart tv shield tv 2018 tv 4k oled tv 65 4k tv 4k smart tv 4k hdr tv shield tv gaming tv 65 inch 4k tv tv 65 inch smart tv 4k 65 inch 4k tv 65 inch 4k tv 4k tv uhd tv uhd tv 4k", "marketplace_id": "ATVPDKIKX0DER"}], "control_method": [{"value": "voice", "marketplace_id": "ATVPDKIKX0DER"}], "item_package_dimensions": [{"length": {"unit": "centimeters", "value": 26.67}, "width": {"unit": "centimeters", "value": 121.92}, "height": {"unit": "centimeters", "value": 203.2}, "marketplace_id": "ATVPDKIKX0DER"}], "image_aspect_ratio": [{"language_tag": "en_US", "value": "16:9", "marketplace_id": "ATVPDKIKX0DER"}], "part_number": [{"value": "QN82Q60RAFXZA", "marketplace_id": "ATVPDKIKX0DER"}], "includes_remote": [{"value": true, "marketplace_id": "ATVPDKIKX0DER"}], "style": [{"language_tag": "en_US", "value": "TV only", "marketplace_id": "ATVPDKIKX0DER"}], "item_type_name": [{"language_tag": "en_US", "value": "TV", "marketplace_id": "ATVPDKIKX0DER"}], "battery": [{"cell_composition": [{"value": "alkaline"}], "marketplace_id": "ATVPDKIKX0DER"}], "image_contrast_ratio": [{"language_tag": "en_US", "value": "QLED 4K", "marketplace_id": "ATVPDKIKX0DER"}], "manufacturer": [{"language_tag": "en_US", "value": "Samsung", "marketplace_id": "ATVPDKIKX0DER"}], "number_of_boxes": [{"value": 1, "marketplace_id": "ATVPDKIKX0DER"}], "total_usb_ports": [{"value": 2, "marketplace_id": "ATVPDKIKX0DER"}], "model_number": [{"value": "QN82Q60RAFXZA", "marketplace_id": "ATVPDKIKX0DER"}], "supplier_declared_dg_hz_regulation": [{"value": "not_applicable", "marketplace_id": "ATVPDKIKX0DER"}], "num_batteries": [{"quantity": 2, "type": "aaa", "marketplace_id": "ATVPDKIKX0DER"}], "california_proposition_65": [{"compliance_type": "on_product_combined_cancer_reproductive", "marketplace_id": "ATVPDKIKX0DER"}, {"compliance_type": "chemical", "chemical_names": ["di_2_ethylhexyl_phthalate_dehp"], "marketplace_id": "ATVPDKIKX0DER"}], "display": [{"resolution_maximum": [{"unit": "pixels", "language_tag": "en_US", "value": "3840 x 2160"}], "size": [{"unit": "inches", "value": 82}], "type": [{"language_tag": "en_US", "value": "QLED"}], "marketplace_id": "ATVPDKIKX0DER"}], "item_name": [{"language_tag": "en_US", "value": "Samsung QN82Q60RAFXZA Flat 82-Inch QLED 4K Q60 Series (2019) Ultra HD Smart TV with HDR and Alexa Compatibility", "marketplace_id": "ATVPDKIKX0DER"}], "list_price": [{"currency": "USD", "value": 3799.99, "marketplace_id": "ATVPDKIKX0DER"}], "batteries_required": [{"value": false, "marketplace_id": "ATVPDKIKX0DER"}], "includes_rechargable_battery": [{"value": false, "marketplace_id": "ATVPDKIKX0DER"}], "product_site_launch_date": [{"value": "2019-03-11T08:00:01.000Z", "marketplace_id": "ATVPDKIKX0DER"}], "product_category": [{"value": "50400100", "marketplace_id": "ATVPDKIKX0DER"}], "batteries_included": [{"value": false, "marketplace_id": "ATVPDKIKX0DER"}], "connectivity_technology": [{"language_tag": "en_US", "value": "Bluetooth", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "USB", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "Wireless", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "HDMI", "marketplace_id": "ATVPDKIKX0DER"}], "included_components": [{"language_tag": "en_US", "value": "QLED Standard Smart Remote", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "Power Cable", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "Stand", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "Samsung Smart Control", "marketplace_id": "ATVPDKIKX0DER"}], "specification_met": [{"language_tag": "en_US", "value": "", "marketplace_id": "ATVPDKIKX0DER"}], "parental_control_technology": [{"value": "V-Chip", "marketplace_id": "ATVPDKIKX0DER"}], "power_consumption": [{"unit": "watts", "value": 120, "marketplace_id": "ATVPDKIKX0DER"}], "cpsia_cautionary_statement": [{"value": "no_warning_applicable", "marketplace_id": "ATVPDKIKX0DER"}], "item_type_keyword": [{"value": "qled-televisions", "marketplace_id": "ATVPDKIKX0DER"}], "number_of_items": [{"value": 1, "marketplace_id": "ATVPDKIKX0DER"}], "warranty_description": [{"language_tag": "en_US", "value": "1 year manufacturer", "marketplace_id": "ATVPDKIKX0DER"}], "max_resolution": [{"unit": "pixels", "value": 8.3, "marketplace_id": "ATVPDKIKX0DER"}], "color": [{"language_tag": "en_US", "value": "Black", "marketplace_id": "ATVPDKIKX0DER"}], "screen_surface_description": [{"language_tag": "en_US", "value": "Flat", "marketplace_id": "ATVPDKIKX0DER"}], "item_package_weight": [{"unit": "kilograms", "value": 62.142, "marketplace_id": "ATVPDKIKX0DER"}], "speaker_type": [{"language_tag": "en_US", "value": "2CH", "marketplace_id": "ATVPDKIKX0DER"}], "supported_internet_services": [{"language_tag": "en_US", "value": "Amazon Instant Video", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "YouTube", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "Netflix", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "<PERSON><PERSON>", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "Browser", "marketplace_id": "ATVPDKIKX0DER"}], "tuner_technology": [{"language_tag": "en_US", "value": "Analog <PERSON>", "marketplace_id": "ATVPDKIKX0DER"}], "controller_type": [{"language_tag": "en_US", "value": "SmartThings", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "Voice Control", "marketplace_id": "ATVPDKIKX0DER"}], "special_feature": [{"language_tag": "en_US", "value": "100% Color Volume with Quantum Dot; Quantum Processor 4K; Ambient Mode; Quantum HDR 4X; Real Game Enhancer", "marketplace_id": "ATVPDKIKX0DER"}], "wireless_communication_technology": [{"language_tag": "en_US", "value": "Wi-Fi::Wi-Fi Direct::Bluetooth", "marketplace_id": "ATVPDKIKX0DER"}], "model_year": [{"value": 2019, "marketplace_id": "ATVPDKIKX0DER"}], "power_source_type": [{"language_tag": "en_US", "value": "Corded Electric", "marketplace_id": "ATVPDKIKX0DER"}], "street_date": [{"value": "2019-03-21T00:00:01Z", "marketplace_id": "ATVPDKIKX0DER"}], "mounting_type": [{"language_tag": "en_US", "value": "Table Mount", "marketplace_id": "ATVPDKIKX0DER"}, {"language_tag": "en_US", "value": "Wall Mount", "marketplace_id": "ATVPDKIKX0DER"}], "refresh_rate": [{"unit": "hertz", "language_tag": "en_US", "value": "120", "marketplace_id": "ATVPDKIKX0DER"}]}, "offers": [{"marketplaceId": "ATVPDKIKX0DER", "offerType": "B2C", "price": {"currencyCode": "USD", "amount": "100.00"}}], "fulfillmentAvailability": [{"fulfillmentChannelCode": "DEFAULT", "quantity": 100}], "issues": [{"code": "90220", "message": "'size' is required but not supplied.", "severity": "ERROR", "attributeNames": ["size"], "categories": ["MISSING_ATTRIBUTE"]}, {"code": "18027", "message": "We believe the main image has text, logo, graphic or watermark which is not permitted for this product type. Please submit a compliant image to lift the suppression. Also refer to Product image requirements.", "severity": "ERROR", "categories": ["INVALID_IMAGE"], "enforcements": {"actions": [{"action": "SEARCH_SUPPRESSED"}], "exemption": {"status": "EXEMPT_UNTIL_EXPIRY_DATE", "expiryDate": "2025-05-28T00:36:48.914Z"}}}, {"code": "99300", "message": "Product Detail Page Rules Violation (Inaccurate information on product detail page)", "severity": "ERROR", "categories": [], "enforcements": {"actions": [{"action": "ATTRIBUTE_SUPPRESSED"}], "exemption": {"status": "EXEMPT"}}}, {"code": "18155", "message": "The 'minimum price' is greater than the selling price (excluding shipping) for the listing. Please review and update your price and/or minimum price.", "severity": "ERROR", "categories": ["INVALID_PRICE"], "enforcements": {"actions": [{"action": "LISTING_SUPPRESSED"}], "exemption": {"status": "NOT_EXEMPT"}}}, {"code": "18742", "message": "Restricted Products Policy Violation", "severity": "ERROR", "categories": [], "enforcements": {"actions": [{"action": "CATALOG_ITEM_REMOVED"}], "exemption": {"status": "NOT_EXEMPT"}}}]}]}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"identifiersType": {"value": "SKU"}, "identifiers": {"value": ["GM-ZDPI-9B4E", "HW-ZDPI-9B4E", "TC-ZDPI-9B4E"]}, "marketplaceIds": {"value": ["ATVPDKIKX0DER"]}, "includedData": {"value": ["summaries", "offers", "fulfillmentAvailability", "issues"]}, "pageSize": {"value": 1}}}, "response": {"numberOfResults": 3, "pagination": {"nextToken": "xsdflkj324lkjsdlkj3423klkjsdfkljlk2j34klj2l3k4jlksdjl234", "previousToken": "ilkjsdflkj234lkjds234234lkjl234lksjdflkj234234lkjsfsdflkj333d"}, "items": [{"sku": "GM-ZDPI-9B4E", "summaries": [{"marketplaceId": "ATVPDKIKX0DER", "asin": "B071VG5N9D", "productType": "LUGGAGE", "conditionType": "new_new", "status": ["BUYABLE"], "itemName": "Hardside Carry-On Spinner Suitcase Luggage", "createdDate": "2021-02-01T00:00:00Z", "lastUpdatedDate": "2021-03-01T00:00:00Z", "mainImage": {"link": "https://www.example.com/luggage.png", "height": 500, "width": 500}}], "offers": [{"marketplaceId": "ATVPDKIKX0DER", "offerType": "B2C", "price": {"currencyCode": "USD", "amount": "100.00"}}], "fulfillmentAvailability": [{"fulfillmentChannelCode": "DEFAULT", "quantity": 100}], "issues": [{"code": "90220", "message": "'size' is required but not supplied.", "severity": "ERROR", "attributeNames": ["size"], "categories": ["MISSING_ATTRIBUTE"]}, {"code": "18027", "message": "We believe the main image has text, logo, graphic or watermark which is not permitted for this product type. Please submit a compliant image to lift the suppression. Also refer to Product image requirements.", "severity": "ERROR", "categories": ["INVALID_IMAGE"], "enforcements": {"actions": [{"action": "SEARCH_SUPPRESSED"}], "exemption": {"status": "EXEMPT_UNTIL_EXPIRY_DATE", "expiryDate": "2025-05-28T00:36:48.914Z"}}}, {"code": "99300", "message": "Product Detail Page Rules Violation (Inaccurate information on product detail page)", "severity": "ERROR", "categories": [], "enforcements": {"actions": [{"action": "ATTRIBUTE_SUPPRESSED"}], "exemption": {"status": "EXEMPT"}}}, {"code": "18155", "message": "The 'minimum price' is greater than the selling price (excluding shipping) for the listing. Please review and update your price and/or minimum price.", "severity": "ERROR", "categories": ["INVALID_PRICE"], "enforcements": {"actions": [{"action": "LISTING_SUPPRESSED"}], "exemption": {"status": "NOT_EXEMPT"}}}, {"code": "18742", "message": "Restricted Products Policy Violation", "severity": "ERROR", "categories": [], "enforcements": {"actions": [{"action": "CATALOG_ITEM_REMOVED"}], "exemption": {"status": "NOT_EXEMPT"}}}]}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"sellerId": {"value": "SellerId"}, "marketplaceIds": {"value": ["invalid"]}}}, "response": {"errors": [{"code": "BAD_REQUEST", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "404": {"description": "The resource specified does not exist.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "415": {"description": "The request payload is in an unsupported format.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}}}}}, "definitions": {"Error": {"description": "Error response returned when the request is unsuccessful.", "properties": {"code": {"description": "An error code that identifies the type of error that occurred.", "type": "string"}, "message": {"description": "A message that describes the error condition.", "type": "string"}, "details": {"description": "Additional details that can help the caller understand or fix the issue.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "ErrorList": {"description": "A list of error responses returned when a request is unsuccessful.", "type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/Error"}}}, "required": ["errors"]}, "ItemSearchResults": {"description": "Selling partner listings items and search related metadata.", "properties": {"numberOfResults": {"description": "The total number of selling partner listings items found for the search criteria (only results up to the page count limit will be returned per request regardless of the number found).\n\nNote: The maximum number of items (SKUs) that can be returned and paged through is 1000.", "type": "integer"}, "pagination": {"description": "If available, the `nextToken` and/or `previousToken` values required to return paginated results.", "$ref": "#/definitions/Pagination"}, "items": {"description": "A list of listings items.", "type": "array", "items": {"$ref": "#/definitions/Item"}}}, "required": ["numberOfResults", "items"], "type": "object"}, "Item": {"description": "A listings item.", "properties": {"sku": {"description": "A selling partner provided identifier for an Amazon listing.", "type": "string"}, "summaries": {"$ref": "#/definitions/ItemSummaries"}, "attributes": {"$ref": "#/definitions/ItemAttributes"}, "issues": {"$ref": "#/definitions/ItemIssues"}, "offers": {"$ref": "#/definitions/ItemOffers"}, "fulfillmentAvailability": {"description": "The fulfillment availability for the listings item.", "type": "array", "items": {"$ref": "#/definitions/FulfillmentAvailability"}}, "procurement": {"description": "The vendor procurement information for the listings item.", "type": "array", "items": {"$ref": "#/definitions/ItemProcurement"}}}, "required": ["sku"], "type": "object"}, "ItemSummaries": {"description": "Summary details of a listings item.", "items": {"$ref": "#/definitions/ItemSummaryByMarketplace"}, "type": "array"}, "ItemSummaryByMarketplace": {"description": "Summary details of a listings item for an Amazon marketplace.", "properties": {"marketplaceId": {"description": "A marketplace identifier. Identifies the Amazon marketplace for the listings item.", "type": "string"}, "asin": {"description": "Amazon Standard Identification Number (ASIN) of the listings item.", "type": "string"}, "productType": {"description": "The Amazon product type of the listings item.", "type": "string"}, "conditionType": {"description": "Identifies the condition of the listings item.", "enum": ["new_new", "new_open_box", "new_oem", "refurbished_refurbished", "used_like_new", "used_very_good", "used_good", "used_acceptable", "collectible_like_new", "collectible_very_good", "collectible_good", "collectible_acceptable", "club_club"], "x-docgen-enum-table-extension": [{"value": "new_new", "description": "New."}, {"value": "new_open_box", "description": "New - Open Box."}, {"value": "new_oem", "description": "New - OEM."}, {"value": "refurbished_refurbished", "description": "Refurbished."}, {"value": "used_like_new", "description": "Used - Like New."}, {"value": "used_very_good", "description": "Used - Very Good."}, {"value": "used_good", "description": "Used - Good."}, {"value": "used_acceptable", "description": "Used - Acceptable."}, {"value": "collectible_like_new", "description": "Collectible - Like New."}, {"value": "collectible_very_good", "description": "Collectible - Very Good."}, {"value": "collectible_good", "description": "Collectible - Good."}, {"value": "collectible_acceptable", "description": "Collectible - Acceptable."}, {"value": "club_club", "description": "Club."}], "type": "string"}, "status": {"description": "Statuses that apply to the listings item.", "type": "array", "items": {"type": "string", "enum": ["BUYABLE", "DISCOVERABLE"], "x-docgen-enum-table-extension": [{"value": "BUYABLE", "description": "The listings item can be purchased by shoppers. This status does not apply to vendor listings."}, {"value": "DISCOVERABLE", "description": "The listings item is visible to shoppers."}]}}, "fnSku": {"description": "The fulfillment network stock keeping unit is an identifier used by Amazon fulfillment centers to identify each unique item.", "type": "string"}, "itemName": {"description": "The name or title associated with an Amazon catalog item.", "type": "string"}, "createdDate": {"description": "The date the listings item was created in ISO 8601 format.", "type": "string", "format": "date-time"}, "lastUpdatedDate": {"description": "The date the listings item was last updated in ISO 8601 format.", "type": "string", "format": "date-time"}, "mainImage": {"description": "The main image for the listings item.", "$ref": "#/definitions/ItemImage"}}, "required": ["marketplaceId", "asin", "productType", "status", "itemName", "createdDate", "lastUpdatedDate"], "type": "object"}, "ItemImage": {"description": "The image for the listings item.", "properties": {"link": {"description": "The link, or URL, to the image.", "type": "string"}, "height": {"description": "The height of the image in pixels.", "type": "integer"}, "width": {"description": "The width of the image in pixels.", "type": "integer"}}, "required": ["link", "height", "width"], "type": "object"}, "ItemAttributes": {"description": "A JSON object containing structured listings item attribute data keyed by attribute name.", "type": "object", "additionalProperties": true}, "ItemIssues": {"description": "The issues associated with the listings item.", "items": {"$ref": "#/definitions/Issue"}, "type": "array"}, "Issue": {"description": "An issue with a listings item.", "properties": {"code": {"description": "An issue code that identifies the type of issue.", "type": "string"}, "message": {"description": "A message that describes the issue.", "type": "string"}, "severity": {"description": "The severity of the issue.", "enum": ["ERROR", "WARNING", "INFO"], "x-docgen-enum-table-extension": [{"value": "ERROR", "description": "Indicates an issue has occurred preventing the submission from processing, such as a validation error."}, {"value": "WARNING", "description": "Indicates an issue has occurred that should be reviewed, but has not prevented the submission from processing."}, {"value": "INFO", "description": "Indicates additional information has been provided that should be reviewed."}], "type": "string"}, "attributeNames": {"description": "The names of the attributes associated with the issue, if applicable.", "type": "array", "items": {"type": "string"}}, "categories": {"description": "List of issue categories. \n\nPossible vales: \n\n* `INVALID_ATTRIBUTE` - Indicating an invalid attribute in the listing. \n\n* `MISSING_ATTRIBUTE` - Highlighting a missing attribute in the listing. \n\n* `INVALID_IMAGE` - Signifying an invalid image in the listing. \n\n* `MISSING_IMAGE` - Noting the absence of an image in the listing. \n\n* `INVALID_PRICE` - Pertaining to issues with the listing's price-related attributes. \n\n* `MISSING_PRICE` - Pointing out the absence of a price attribute in the listing. \n\n* `DUPLICATE` - Identifying listings with potential duplicate problems, such as this ASIN potentially being a duplicate of another ASIN. \n\n* `QUALIFICATION_REQUIRED` - Indicating that the listing requires qualification-related approval.", "type": "array", "items": {"type": "string"}, "example": ["INVALID_ATTRIBUTE"]}, "enforcements": {"description": "This field provides information about the enforcement actions taken by Amazon that affect the publishing or status of a listing. It also includes details about any associated exemptions.", "$ref": "#/definitions/IssueEnforcements"}}, "required": ["code", "message", "severity", "categories"], "type": "object"}, "IssueEnforcements": {"description": "This field provides information about the enforcement actions taken by Amazon that affect the publishing or status of a listing. It also includes details about any associated exemptions.", "type": "object", "properties": {"actions": {"description": "List of enforcement actions taken by Amazon that affect the publishing or status of a listing.", "type": "array", "items": {"$ref": "#/definitions/IssueEnforcementAction"}}, "exemption": {"description": "The \"exemption\" field serves to convey the status of enforcement actions by Amazon.", "$ref": "#/definitions/IssueExemption"}}, "required": ["actions", "exemption"]}, "IssueEnforcementAction": {"description": "The enforcement action taken by Amazon that affect the publishing or status of a listing", "type": "object", "properties": {"action": {"description": "The enforcement action name. \n\nPossible values: \n\n* `LISTING_SUPPRESSED` - This enforcement takes down the current listing item's buyability. \n\n* `ATTRIBUTE_SUPPRESSED` - An attribute's value on the listing item is invalid, which causes it to be rejected by Amazon. \n\n* `CATALOG_ITEM_REMOVED` - This catalog item is inactive on Amazon, and all offers against it in the applicable marketplace are non-buyable. \n\n* `SEARCH_SUPPRESSED` - This value indicates that the catalog item is hidden from search results.", "type": "string", "example": "LISTING_SUPPRESSED"}}, "required": ["action"]}, "IssueExemption": {"description": "Conveying the status of the listed enforcement actions and, if applicable, provides information about the exemption's expiry date.", "type": "object", "properties": {"status": {"description": "This field indicates the current exemption status for the listed enforcement actions. It can take values such as `EXEMPT`, signifying permanent exemption, `EXEMPT_UNTIL_EXPIRY_DATE` indicating temporary exemption until a specified date, or `NOT_EXEMPT` signifying no exemptions, and enforcement actions were already applied.", "enum": ["EXEMPT", "EXEMPT_UNTIL_EXPIRY_DATE", "NOT_EXEMPT"], "x-docgen-enum-table-extension": [{"value": "EXEMPT", "description": "This status is assigned to a product listing when it has been exempted from the listed enforcement actions. In cases of `EXEMPT`, Amazon acknowledges the existence of enforcement actions but ensures the user that these actions will not be applied to the listing. The user is advised to consider addressing the issue, although enforcement actions will not be taken."}, {"value": "EXEMPT_UNTIL_EXPIRY_DATE", "description": "This status indicates that a product listing has been granted a temporary exemption from the listed enforcement actions. The exemption will remain in effect until a specified expiry date. After this date, Amazon will enforce the listed actions. The `expiryDate` field provides the timestamp indicating when the temporary exemption will end, adhering to the ISO 8601 format"}, {"value": "NOT_EXEMPT", "description": "This status is assigned to a product listing when it has no exemptions from the listed enforcement actions. When the status is `NOT_EXEMPT`, it signifies that Amazon has already taken the specified enforcement actions. There is no exemption in place, and the listed actions are actively enforced"}], "type": "string"}, "expiryDate": {"description": "This field represents the timestamp, following the ISO 8601 format, which specifies the date when temporary exemptions, if applicable, will expire, and Amazon will begin enforcing the listed actions.", "type": "string", "format": "date-time", "example": "2023-10-28T00:36:48.914Z"}}, "required": ["status"]}, "ItemOffers": {"description": "Offer details for the listings item.", "items": {"$ref": "#/definitions/ItemOfferByMarketplace"}, "type": "array"}, "ItemOfferByMarketplace": {"description": "Offer details of a listings item for an Amazon marketplace.", "properties": {"marketplaceId": {"description": "The Amazon marketplace identifier.", "type": "string"}, "offerType": {"description": "Type of offer for the listings item.", "enum": ["B2C", "B2B"], "x-docgen-enum-table-extension": [{"value": "B2C", "description": "The offer on this listings item is available for Business to Consumer purchase, meaning that it is available to shoppers on Amazon retail sites."}, {"value": "B2B", "description": "The offer on this listings item is available for Business to Business purchase."}], "type": "string"}, "price": {"description": "The purchase price of the listings item", "$ref": "#/definitions/Money"}, "points": {"$ref": "#/definitions/Points"}, "audience": {"description": "Buyer segment or program this offer is applicable to.", "$ref": "#/definitions/Audience"}}, "required": ["marketplaceId", "offerType", "price"], "type": "object"}, "ItemProcurement": {"description": "The vendor procurement information for the listings item.", "properties": {"costPrice": {"description": "The price (numeric value) that you want Amazon to pay you for this product.", "$ref": "#/definitions/Money"}}, "required": ["costPrice"], "type": "object"}, "FulfillmentAvailability": {"description": "The fulfillment availability details for the listings item.", "properties": {"fulfillmentChannelCode": {"description": "The code of the fulfillment network that will be used.", "type": "string"}, "quantity": {"description": "The quantity of the item you are making available for sale.", "type": "integer", "minimum": 0}}, "required": ["fulfillmentChannelCode"], "type": "object"}, "Money": {"description": "The currency type and amount.", "properties": {"currencyCode": {"type": "string", "description": "Three-digit currency code in ISO 4217 format."}, "amount": {"description": "The currency amount.", "$ref": "#/definitions/Decimal"}}, "required": ["amount", "currencyCode"], "type": "object"}, "Decimal": {"type": "string", "description": "A decimal number with no loss of precision. Useful when precision loss is unnaceptable, as with currencies. Follows RFC7159 for number representation."}, "Points": {"description": "The number of Amazon Points offered with the purchase of an item, and their monetary value. Note that the `Points` element is only returned in Japan (JP).", "type": "object", "properties": {"pointsNumber": {"type": "integer"}}, "required": ["pointsNumber"]}, "Audience": {"description": "Buyer segment or program this offer is applicable to.", "type": "object", "properties": {"value": {"description": "Name of the audience an offer is applicable to. \n\nCommon values: \n\n* 'ALL' - Standard offer audience for buyers on Amazon retail websites. \n\n* 'B2B' - Offer audience for Amazon Business website buyers.", "type": "string", "example": "ALL"}, "displayName": {"description": "Localized display name for the audience.", "type": "string"}}}, "PatchOperation": {"description": "Individual JSON Patch operation for an HTTP PATCH request.", "properties": {"op": {"description": "Type of JSON Patch operation. Supported JSON Patch operations include add, replace, and delete. Refer to [JavaScript Object Notation (JSON) Patch](https://tools.ietf.org/html/rfc6902) for more information.", "enum": ["add", "replace", "delete"], "x-docgen-enum-table-extension": [{"value": "add", "description": "The `add` operation adds or replaces the target property."}, {"value": "replace", "description": "The `replace` operation adds or replaces the target property."}, {"value": "delete", "description": "The `delete` operation removes the target property. Not supported for vendors (vendors will receive an HTTP status code 400 response)."}], "type": "string"}, "path": {"description": "JSON Pointer path of the element to patch. Refer to [JavaScript Object Notation (JSON) Patch](https://tools.ietf.org/html/rfc6902) for more information.", "type": "string"}, "value": {"description": "JSON value to add, replace, or delete.", "type": "array", "items": {"type": "object", "additionalProperties": true}}}, "required": ["op", "path"], "type": "object"}, "ListingsItemPatchRequest": {"description": "The request body schema for the `patchListingsItem` operation.", "properties": {"productType": {"description": "The Amazon product type of the listings item.", "type": "string"}, "patches": {"description": "One or more JSON Patch operations to perform on the listings item.", "type": "array", "items": {"$ref": "#/definitions/PatchOperation"}, "minItems": 1}}, "required": ["productType", "patches"], "type": "object"}, "ListingsItemPutRequest": {"description": "The request body schema for the `putListingsItem` operation.", "properties": {"productType": {"description": "The Amazon product type of the listings item.", "type": "string"}, "requirements": {"description": "The name of the requirements set for the provided data.", "enum": ["LISTING", "LISTING_PRODUCT_ONLY", "LISTING_OFFER_ONLY"], "x-docgen-enum-table-extension": [{"value": "LISTING", "description": "Indicates the submitted data contains product facts and sales terms."}, {"value": "LISTING_PRODUCT_ONLY", "description": "Indicates the submitted data contains product facts only."}, {"value": "LISTING_OFFER_ONLY", "description": "Indicates the submitted data contains sales terms only. Not supported for vendors (vendors will receive an HTTP status code 400 response)."}], "type": "string"}, "attributes": {"description": "A JSON object containing structured listings item attribute data keyed by attribute name.", "type": "object", "additionalProperties": true}}, "required": ["productType", "attributes"], "type": "object"}, "ListingsItemSubmissionResponse": {"description": "Response containing the results of a submission to the Selling Partner API for Listings Items.", "properties": {"sku": {"description": "A selling partner provided identifier for an Amazon listing.", "type": "string"}, "status": {"description": "The status of the listings item submission.", "enum": ["ACCEPTED", "INVALID", "VALID"], "x-docgen-enum-table-extension": [{"value": "ACCEPTED", "description": "The listings submission was accepted for processing."}, {"value": "INVALID", "description": "The listings submission was not valid and was not accepted for processing."}, {"value": "VALID", "description": "The listings submission was valid. Only returned when the `mode` is `VALIDATION_PREVIEW`."}], "type": "string"}, "submissionId": {"description": "The unique identifier of the listings item submission.", "type": "string"}, "issues": {"description": "Listings item issues related to the listings item submission.", "type": "array", "items": {"$ref": "#/definitions/Issue"}}, "identifiers": {"description": "Identity attributes associated with the item in the Amazon catalog, such as the ASIN.", "$ref": "#/definitions/ItemIdentifiers"}}, "required": ["sku", "status", "submissionId"], "type": "object"}, "ItemIdentifiers": {"description": "Identity attributes associated with the item in the Amazon catalog, such as the ASIN.", "items": {"$ref": "#/definitions/ItemIdentifiersByMarketplace"}, "type": "array"}, "ItemIdentifiersByMarketplace": {"description": "Identity attributes associated with the item in the Amazon catalog for the indicated Amazon marketplace.", "properties": {"marketplaceId": {"description": "A marketplace identifier. Identifies the Amazon marketplace for the listings item.", "type": "string"}, "asin": {"description": "Amazon Standard Identification Number (ASIN) of the listings item.", "type": "string"}}, "type": "object"}, "Pagination": {"description": "When a request produces a response that exceeds the `pageSize`, pagination occurs. This means the response is divided into individual pages. To retrieve the next page or the previous page, you must pass the `nextToken` value or the `previousToken` value as the `pageToken` parameter in the next request. When you receive the last page, there will be no `nextToken` key in the pagination object.", "properties": {"nextToken": {"description": "A token that can be used to fetch the next page.", "type": "string"}, "previousToken": {"description": "A token that can be used to fetch the previous page.", "type": "string"}}, "type": "object"}}}