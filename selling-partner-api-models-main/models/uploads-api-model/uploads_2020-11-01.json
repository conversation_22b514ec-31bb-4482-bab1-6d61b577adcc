{"swagger": "2.0", "info": {"description": "The Uploads API lets you upload files that you can programmatically access using other Selling Partner APIs, such as the A+ Content API and the Messaging API.", "version": "2020-11-01", "title": "Selling Partner API for Uploads", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/uploads/2020-11-01/uploadDestinations/{resource}": {"post": {"tags": ["uploads"], "description": "Creates an upload destination, returning the information required to upload a file to the destination and to programmatically access the file.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createUploadDestinationForResource", "parameters": [{"name": "marketplaceIds", "in": "query", "description": "A list of marketplace identifiers. This specifies the marketplaces where the upload will be available. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"name": "contentMD5", "in": "query", "description": "An MD5 hash of the content to be submitted to the upload destination. This value is used to determine if the data has been corrupted or tampered with during transit.", "required": true, "type": "string"}, {"name": "resource", "in": "path", "description": "The resource for the upload destination that you are creating. For example, if you are creating an upload destination for the createLegalDisclosure operation of the Messaging API, the `{resource}` would be `/messaging/v1/orders/{amazonOrderId}/messages/legalDisclosure`, and the entire path would be `/uploads/2020-11-01/uploadDestinations/messaging/v1/orders/{amazonOrderId}/messages/legalDisclosure`. If you are creating an upload destination for an Aplus content document, the `{resource}` would be `aplus/2020-11-01/contentDocuments` and the path would be `/uploads/v1/uploadDestinations/aplus/2020-11-01/contentDocuments`.", "required": true, "type": "string", "x-amazon-spds-greedy-path-parameter": true}, {"name": "contentType", "in": "query", "description": "The content type of the file to be uploaded.", "required": false, "type": "string"}], "responses": {"201": {"description": "Success.", "schema": {"$ref": "#/definitions/CreateUploadDestinationResponse"}, "examples": {"application/json": {"payload": {"uploadDestinationId": "5d4e42b5-1d6e-44e8-a89c-2abfca0625bb", "url": "https://s3.amazonaws.com/buyer-seller-messaging-test-draft-attachment-namarketplace/%2F067/5d4e42b5-1d6e-44e8-a89c-2abfca0625bb?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20190701T214102Z&X-Amz-SignedHeaders=content-md5%3Bhost%3Bx-amz-server-side-encryption&X-Amz-Expires=900&X-Amz-Credential=AKIAW5VUA47ENEOYT7RC%2F20190701%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Signature=d4f85c5f1a32a788a8d54e3f00a2a08af45be5b83551cdd81c82ae353dfcdfd4", "headers": {"Content-MD5": "5d41402abc4b2a76b9719d911017c592", "x-amz-server-side-encryption": "aws:kms"}}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateUploadDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateUploadDestinationResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateUploadDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateUploadDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateUploadDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateUploadDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateUploadDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateUploadDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}}, "definitions": {"CreateUploadDestinationResponse": {"type": "object", "properties": {"payload": {"$ref": "#/definitions/UploadDestination"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createUploadDestination operation."}, "UploadDestination": {"type": "object", "properties": {"uploadDestinationId": {"type": "string", "description": "The unique identifier for the upload destination."}, "url": {"type": "string", "description": "The URL for the upload destination."}, "headers": {"type": "object", "description": "The headers to include in the upload request."}}, "description": "Information about an upload destination."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition in a human-readable form."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}}}