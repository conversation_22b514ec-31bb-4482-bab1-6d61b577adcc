{"swagger": "2.0", "info": {"description": "Effective **June 27, 2024**, the Selling Partner API for Reports v2020-09-04 will no longer be available and all calls to it will fail. Integrations that rely on the Reports API must migrate to [Reports v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference) to avoid service disruption.", "version": "2020-09-04", "title": "Selling Partner API for Reports", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "basePath": "/", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/reports/2020-09-04/reports": {"get": {"tags": ["reports"], "description": "Effective **June 27, 2023**, the `getReports` operation will no longer be available in the Selling Partner API for Reports v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Reports v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "getReports", "deprecated": true, "parameters": [{"name": "reportTypes", "in": "query", "description": "A list of report types used to filter reports. When reportTypes is provided, the other filter parameters (processingStatuses, marketplaceIds, createdSince, createdUntil) and pageSize may also be provided. Either reportTypes or nextToken is required.", "required": false, "type": "array", "items": {"type": "string"}, "maxItems": 10, "minItems": 1}, {"name": "processingStatuses", "in": "query", "description": "A list of processing statuses used to filter reports.", "required": false, "type": "array", "items": {"type": "string", "enum": ["CANCELLED", "DONE", "FATAL", "IN_PROGRESS", "IN_QUEUE"], "x-docgen-enum-table-extension": [{"value": "CANCELLED", "description": "The report was cancelled. There are two ways a report can be cancelled: an explicit cancellation request before the report starts processing, or an automatic cancellation if there is no data to return."}, {"value": "DONE", "description": "The report has completed processing."}, {"value": "FATAL", "description": "The report was aborted due to a fatal error."}, {"value": "IN_PROGRESS", "description": "The report is being processed."}, {"value": "IN_QUEUE", "description": "The report has not yet started processing. It may be waiting for another IN_PROGRESS report."}]}, "minItems": 1}, {"name": "marketplaceIds", "in": "query", "description": "A list of marketplace identifiers used to filter reports. The reports returned will match at least one of the marketplaces that you specify.", "required": false, "type": "array", "items": {"type": "string"}, "maxItems": 10, "minItems": 1}, {"name": "pageSize", "in": "query", "description": "The maximum number of reports to return in a single call.", "required": false, "type": "integer", "default": 10, "maximum": 100, "minimum": 1}, {"name": "createdSince", "in": "query", "description": "The earliest report creation date and time for reports to include in the response, in ISO 8601 date time format. The default is 90 days ago. Reports are retained for a maximum of 90 days.", "required": false, "type": "string", "format": "date-time"}, {"name": "createdUntil", "in": "query", "description": "The latest report creation date and time for reports to include in the response, in ISO 8601 date time format. The default is now.", "required": false, "type": "string", "format": "date-time"}, {"name": "nextToken", "in": "query", "description": "A string token returned in the response to your previous request. nextToken is returned when the number of results exceeds the specified pageSize value. To get the next page of results, call the getReports operation and include this token as the only parameter. Specifying nextToken with any other parameters will cause the request to fail.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetReportsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportTypes": {"value": ["FEE_DISCOUNTS_REPORT", "GET_AFN_INVENTORY_DATA"]}, "processingStatuses": {"value": ["IN_QUEUE", "IN_PROGRESS"]}}}, "response": {"nextToken": "VGhpcyB0b2tlbiBpcyBvcGFxdWUgYW5kIGludGVudGlvbmFsbHkgb2JmdXNjYXRlZA==", "payload": [{"reportId": "ReportId1", "reportType": "FEE_DISCOUNTS_REPORT", "dataStartTime": "2019-12-11T13:47:20.677Z", "dataEndTime": "2019-12-12T13:47:20.677Z", "createdTime": "2019-12-10T13:47:20.677Z", "processingStatus": "IN_PROGRESS", "processingStartTime": "2019-12-10T13:47:20.677Z", "processingEndTime": "2019-12-12T13:47:20.677Z"}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetReportsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportTypes": {"value": ["FEE_DISCOUNTS_REPORT", "GET_AFN_INVENTORY_DATA"]}, "processingStatuses": {"value": ["BAD_VALUE", "IN_PROGRESS"]}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input in processing status"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetReportsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetReportsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetReportsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/GetReportsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetReportsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetReportsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetReportsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "post": {"tags": ["reports"], "description": "Effective **June 27, 2023**, the `createReport` operation will no longer be available in the Selling Partner API for Reports v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Reports v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "createReport", "deprecated": true, "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateReportSpecification"}}], "responses": {"202": {"description": "Success.", "schema": {"$ref": "#/definitions/CreateReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"reportType": "GET_MERCHANT_LISTINGS_ALL_DATA", "dataStartTime": "2019-12-10T20:11:24.000Z", "marketplaceIds": ["A1PA6795UKMFR9", "ATVPDKIKX0DER"]}}}}, "response": {"payload": {"reportId": "ID323"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"reportType": "BAD_FEE_DISCOUNTS_REPORT", "dataStartTime": "2019-12-10T20:11:24.000Z", "marketplaceIds": ["A1PA6795UKMFR9", "ATVPDKIKX0DER"]}}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/CreateReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateReportResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/CreateReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/CreateReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/reports/2020-09-04/reports/{reportId}": {"get": {"tags": ["reports"], "description": "Effective **June 27, 2023**, the `getReport` operation will no longer be available in the Selling Partner API for Reports v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Reports v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "getReport", "deprecated": true, "parameters": [{"name": "reportId", "in": "path", "description": "The identifier for the report. This identifier is unique only in combination with a seller ID.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportId": {"value": "ID323"}}}, "response": {"payload": {"reportId": "ReportId1", "reportType": "FEE_DISCOUNTS_REPORT", "dataStartTime": "2019-12-11T13:47:20.677Z", "dataEndTime": "2019-12-12T13:47:20.677Z", "createdTime": "2019-12-10T13:47:20.677Z", "processingStatus": "IN_PROGRESS", "processingStartTime": "2019-12-10T13:47:20.677Z", "processingEndTime": "2019-12-12T13:47:20.677Z"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportId": {"value": "badReportId1"}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetReportResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/GetReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "delete": {"tags": ["reports"], "description": "Effective **June 27, 2023**, the `cancelReport` operation will no longer be available in the Selling Partner API for Reports v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Reports v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "cancelReport", "deprecated": true, "parameters": [{"name": "reportId", "in": "path", "description": "The identifier for the report. This identifier is unique only in combination with a seller ID.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CancelReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportId": {"value": "ID"}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CancelReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/CancelReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CancelReportResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/CancelReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/CancelReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CancelReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CancelReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CancelReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/reports/2020-09-04/schedules": {"get": {"tags": ["reports"], "description": "Effective **June 27, 2023**, the `getReportSchedules` operation will no longer be available in the Selling Partner API for Reports v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Reports v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "getReportSchedules", "deprecated": true, "parameters": [{"name": "reportTypes", "in": "query", "description": "A list of report types used to filter report schedules.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 10, "minItems": 1}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetReportSchedulesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportTypes": {"value": ["FEE_DISCOUNTS_REPORT", "GET_FBA_FULFILLMENT_CUSTOMER_TAXES_DATA"]}}}, "response": {"payload": [{"reportType": "FEE_DISCOUNTS_REPORT", "marketplaceIds": ["ATVPDKIKX0DER"], "reportScheduleId": "ID1", "period": "PT5M", "nextReportCreationTime": "2019-12-11T15:03:44.973Z"}, {"reportType": "GET_FBA_FULFILLMENT_CUSTOMER_TAXES_DATA", "reportScheduleId": "ID2", "period": "PT5M", "nextReportCreationTime": "2019-12-11T15:03:44.973Z"}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetReportSchedulesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportTypes": {"value": ["BAD_FEE_DISCOUNTS_REPORT", "BAD_GET_FBA_FULFILLMENT_CUSTOMER_TAXES_DATA"]}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetReportSchedulesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetReportSchedulesResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetReportSchedulesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/GetReportSchedulesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetReportSchedulesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetReportSchedulesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetReportSchedulesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "post": {"tags": ["reports"], "description": "Effective **June 27, 2023**, the `createReportSchedule` operation will no longer be available in the Selling Partner API for Reports v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Reports v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "createReportSchedule", "deprecated": true, "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateReportScheduleSpecification"}}], "responses": {"201": {"description": "Success.", "schema": {"$ref": "#/definitions/CreateReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"reportType": "FEE_DISCOUNTS_REPORT", "period": "PT5M", "nextReportCreationTime": "2019-12-10T20:11:24.000Z", "marketplaceIds": ["A1PA6795UKMFR9", "ATVPDKIKX0DER"]}}}}, "response": {"payload": {"reportScheduleId": "ID323"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"reportType": "BAD_FEE_DISCOUNTS_REPORT", "period": "PT5M", "nextReportCreationTime": "2019-12-10T20:11:24.000Z"}}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/CreateReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateReportScheduleResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/CreateReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/CreateReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/reports/2020-09-04/schedules/{reportScheduleId}": {"get": {"tags": ["reports"], "description": "Effective **June 27, 2023**, the `getReportSchedule` operation will no longer be available in the Selling Partner API for Reports v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Reports v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "getReportSchedule", "deprecated": true, "parameters": [{"name": "reportScheduleId", "in": "path", "description": "The identifier for the report schedule. This identifier is unique only in combination with a seller ID.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportScheduleId": {"value": "ID323"}}}, "response": {"payload": {"reportScheduleId": "ReportScheduleId1", "reportType": "FEE_DISCOUNTS_REPORT", "period": "PT5M", "nextReportCreationTime": "2019-12-12T13:47:20.677Z"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportScheduleId": {"value": "badReportId1"}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetReportScheduleResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/GetReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "delete": {"tags": ["reports"], "description": "Effective **June 27, 2023**, the `cancelReportSchedule` operation will no longer be available in the Selling Partner API for Reports v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Reports v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "cancelReportSchedule", "deprecated": true, "parameters": [{"name": "reportScheduleId", "in": "path", "description": "The identifier for the report schedule. This identifier is unique only in combination with a seller ID.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CancelReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportScheduleId": {"value": "ID"}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CancelReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/CancelReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CancelReportScheduleResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/CancelReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/CancelReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CancelReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CancelReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CancelReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/reports/2020-09-04/documents/{reportDocumentId}": {"get": {"tags": ["reports"], "description": "Effective **June 27, 2023**, the `getReportDocument` operation will no longer be available in the Selling Partner API for Reports v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Reports v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "getReportDocument", "deprecated": true, "parameters": [{"name": "reportDocumentId", "in": "path", "description": "The identifier for the report document.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetReportDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportDocumentId": {"value": "0356cf79-b8b0-4226-b4b9-0ee058ea5760"}}}, "response": {"payload": {"reportDocumentId": "0356cf79-b8b0-4226-b4b9-0ee058ea5760", "url": "https://d34o8swod1owfl.cloudfront.net/SampleResult%2BKey%3DSample%2BINITVEC%3D58+fa+bf+a7+08+11+95+0f+c1+a8+c6+e0+d5+6f+ae+c8", "encryptionDetails": {"standard": "AES", "initializationVector": "58 fa bf a7 08 11 95 0f c1 a8 c6 e0 d5 6f ae c8", "key": "<PERSON><PERSON>"}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetReportDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportDocumentId": {"value": "badDocumentId1"}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetReportDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetReportDocumentResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetReportDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/GetReportDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetReportDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetReportDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetReportDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}}}}}, "definitions": {"ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition in a human-readable form."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}, "ReportDocumentEncryptionDetails": {"type": "object", "required": ["initializationVector", "key", "standard"], "properties": {"standard": {"type": "string", "description": "The encryption standard required to decrypt the document contents.", "enum": ["AES"], "x-docgen-enum-table-extension": [{"value": "AES", "description": "The Advanced Encryption Standard (AES)."}]}, "initializationVector": {"type": "string", "description": "The vector to decrypt the document contents using Cipher Block Chaining (CBC)."}, "key": {"type": "string", "description": "The encryption key used to decrypt the document contents."}}, "description": "Encryption details required for decryption of a report document's contents."}, "Report": {"type": "object", "required": ["createdTime", "processingStatus", "reportId", "reportType"], "properties": {"marketplaceIds": {"type": "array", "description": "A list of marketplace identifiers for the report.", "items": {"type": "string"}}, "reportId": {"type": "string", "description": "The identifier for the report. This identifier is unique only in combination with a seller ID."}, "reportType": {"type": "string", "description": "The report type."}, "dataStartTime": {"type": "string", "format": "date-time", "description": "The start of a date and time range used for selecting the data to report."}, "dataEndTime": {"type": "string", "format": "date-time", "description": "The end of a date and time range used for selecting the data to report."}, "reportScheduleId": {"type": "string", "description": "The identifier of the report schedule that created this report (if any). This identifier is unique only in combination with a seller ID."}, "createdTime": {"type": "string", "format": "date-time", "description": "The date and time when the report was created."}, "processingStatus": {"type": "string", "description": "The processing status of the report.", "enum": ["CANCELLED", "DONE", "FATAL", "IN_PROGRESS", "IN_QUEUE"], "x-docgen-enum-table-extension": [{"value": "CANCELLED", "description": "The report was cancelled. There are two ways a report can be cancelled: an explicit cancellation request before the report starts processing, or an automatic cancellation if there is no data to return."}, {"value": "DONE", "description": "The report has completed processing."}, {"value": "FATAL", "description": "The report was aborted due to a fatal error."}, {"value": "IN_PROGRESS", "description": "The report is being processed."}, {"value": "IN_QUEUE", "description": "The report has not yet started processing. It may be waiting for another IN_PROGRESS report."}]}, "processingStartTime": {"type": "string", "format": "date-time", "description": "The date and time when the report processing started, in ISO 8601 date time format."}, "processingEndTime": {"type": "string", "format": "date-time", "description": "The date and time when the report processing completed, in ISO 8601 date time format."}, "reportDocumentId": {"type": "string", "description": "The identifier for the report document. Pass this into the getReportDocument operation to get the information you will need to retrieve and decrypt the report document's contents."}}}, "ReportList": {"type": "array", "items": {"$ref": "#/definitions/Report"}}, "CreateReportScheduleSpecification": {"type": "object", "required": ["marketplaceIds", "period", "reportType"], "properties": {"reportType": {"type": "string", "description": "The report type."}, "marketplaceIds": {"type": "array", "description": "A list of marketplace identifiers for the report schedule.", "items": {"type": "string"}, "maxItems": 25, "minItems": 1}, "reportOptions": {"$ref": "#/definitions/ReportOptions"}, "period": {"type": "string", "description": "One of a set of predefined ISO 8601 periods that specifies how often a report should be created.", "enum": ["PT5M", "PT15M", "PT30M", "PT1H", "PT2H", "PT4H", "PT8H", "PT12H", "P1D", "P2D", "P3D", "PT84H", "P7D", "P14D", "P15D", "P18D", "P30D", "P1M"], "x-docgen-enum-table-extension": [{"value": "PT5M", "description": "5 minutes"}, {"value": "PT15M", "description": "15 minutes"}, {"value": "PT30M", "description": "30 minutes"}, {"value": "PT1H", "description": "1 hour"}, {"value": "PT2H", "description": "2 hours"}, {"value": "PT4H", "description": "4 hours"}, {"value": "PT8H", "description": "8 hours"}, {"value": "PT12H", "description": "12 hours"}, {"value": "P1D", "description": "1 day"}, {"value": "P2D", "description": "2 days"}, {"value": "P3D", "description": "3 days"}, {"value": "PT84H", "description": "84 hours"}, {"value": "P7D", "description": "7 days"}, {"value": "P14D", "description": "14 days"}, {"value": "P15D", "description": "15 days"}, {"value": "P18D", "description": "18 days"}, {"value": "P30D", "description": "30 days"}, {"value": "P1M", "description": "1 month"}]}, "nextReportCreationTime": {"type": "string", "format": "date-time", "description": "The date and time when the schedule will create its next report, in ISO 8601 date time format."}}}, "CreateReportSpecification": {"type": "object", "required": ["marketplaceIds", "reportType"], "properties": {"reportOptions": {"$ref": "#/definitions/ReportOptions"}, "reportType": {"type": "string", "description": "The report type."}, "dataStartTime": {"type": "string", "format": "date-time", "description": "The start of a date and time range, in ISO 8601 date time format, used for selecting the data to report. The default is now. The value must be prior to or equal to the current date and time. Not all report types make use of this."}, "dataEndTime": {"type": "string", "format": "date-time", "description": "The end of a date and time range, in ISO 8601 date time format, used for selecting the data to report. The default is now. The value must be prior to or equal to the current date and time. Not all report types make use of this."}, "marketplaceIds": {"type": "array", "description": "A list of marketplace identifiers. The report document's contents will contain data for all of the specified marketplaces, unless the report type indicates otherwise.", "items": {"type": "string"}, "maxItems": 25, "minItems": 1}}}, "ReportOptions": {"type": "object", "description": "Additional information passed to reports. This varies by report type.", "additionalProperties": {"type": "string"}}, "ReportSchedule": {"type": "object", "required": ["period", "reportScheduleId", "reportType"], "properties": {"reportScheduleId": {"type": "string", "description": "The identifier for the report schedule. This identifier is unique only in combination with a seller ID."}, "reportType": {"type": "string", "description": "The report type."}, "marketplaceIds": {"type": "array", "description": "A list of marketplace identifiers. The report document's contents will contain data for all of the specified marketplaces, unless the report type indicates otherwise.", "items": {"type": "string"}}, "reportOptions": {"$ref": "#/definitions/ReportOptions"}, "period": {"type": "string", "description": "An ISO 8601 period value that indicates how often a report should be created."}, "nextReportCreationTime": {"type": "string", "format": "date-time", "description": "The date and time when the schedule will create its next report, in ISO 8601 date time format."}}, "description": "Detailed information about a report schedule."}, "ReportScheduleList": {"type": "array", "items": {"$ref": "#/definitions/ReportSchedule"}}, "CreateReportResult": {"type": "object", "required": ["reportId"], "properties": {"reportId": {"type": "string", "description": "The identifier for the report. This identifier is unique only in combination with a seller ID."}}}, "GetReportsResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the getReports operation.", "$ref": "#/definitions/ReportList"}, "nextToken": {"type": "string", "description": "Returned when the number of results exceeds pageSize. To get the next page of results, call getReports with this token as the only parameter."}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response for the getReports operation."}, "CreateReportResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the createReport operation.", "$ref": "#/definitions/CreateReportResult"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response for the createReport operation."}, "CancelReportResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response for the cancelReport operation."}, "CancelReportScheduleResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response for the cancelReportSchedule operation."}, "GetReportResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the getReport operation.", "$ref": "#/definitions/Report"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response for the getReport operation."}, "GetReportSchedulesResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the getReportSchedules operation.", "$ref": "#/definitions/ReportScheduleList"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response for the getReportSchedules operation."}, "GetReportScheduleResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the getReportSchedule operation.", "$ref": "#/definitions/ReportSchedule"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response for the getReportSchedule operation."}, "CreateReportScheduleResult": {"type": "object", "required": ["reportScheduleId"], "properties": {"reportScheduleId": {"type": "string", "description": "The identifier for the report schedule. This identifier is unique only in combination with a seller ID."}}}, "CreateReportScheduleResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the createReportSchedule operation.", "$ref": "#/definitions/CreateReportScheduleResult"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response for the createReportSchedule operation."}, "ReportDocument": {"type": "object", "required": ["encryptionDetails", "reportDocumentId", "url"], "properties": {"reportDocumentId": {"type": "string", "description": "The identifier for the report document. This identifier is unique only in combination with a seller ID."}, "url": {"type": "string", "description": "A presigned URL for the report document. If `compressionAlgorithm` is not returned, you can download the report directly from this URL. This URL expires after 5 minutes."}, "encryptionDetails": {"$ref": "#/definitions/ReportDocumentEncryptionDetails"}, "compressionAlgorithm": {"type": "string", "description": "If the report document contents have been compressed, the compression algorithm used is returned in this property and you must decompress the report when you download. Otherwise, you can download the report directly. Refer to [Step 2. Download and decrypt the report](doc:reports-api-v2020-09-04-use-case-guide#step-2-download-and-decrypt-the-report) in the use case guide, where sample code is provided.", "enum": ["GZIP"], "x-docgen-enum-table-extension": [{"value": "GZIP", "description": "The gzip compression algorithm."}]}}}, "GetReportDocumentResponse": {"type": "object", "properties": {"payload": {"$ref": "#/definitions/ReportDocument"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "Response schema."}}}