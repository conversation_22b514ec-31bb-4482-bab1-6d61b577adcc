{"swagger": "2.0", "info": {"description": "The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.", "version": "2021-06-30", "title": "Selling Partner API for Reports", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/reports/2021-06-30/reports": {"get": {"tags": ["reports"], "operationId": "getReports", "description": "Returns report details for the reports that match the filters that you specify.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.0222 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "parameters": [{"name": "reportTypes", "in": "query", "required": false, "type": "array", "minItems": 1, "maxItems": 10, "description": "A list of report types used to filter reports. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information. When reportTypes is provided, the other filter parameters (processingStatuses, marketplaceIds, createdSince, createdUntil) and pageSize may also be provided. Either reportTypes or nextToken is required.", "items": {"type": "string"}}, {"name": "processingStatuses", "in": "query", "description": "A list of processing statuses used to filter reports.", "required": false, "type": "array", "minItems": 1, "items": {"type": "string", "enum": ["CANCELLED", "DONE", "FATAL", "IN_PROGRESS", "IN_QUEUE"], "x-docgen-enum-table-extension": [{"value": "CANCELLED", "description": "The report was cancelled. There are two ways a report can be cancelled: an explicit cancellation request before the report starts processing, or an automatic cancellation if there is no data to return."}, {"value": "DONE", "description": "The report has completed processing."}, {"value": "FATAL", "description": "The report was aborted due to a fatal error."}, {"value": "IN_PROGRESS", "description": "The report is being processed."}, {"value": "IN_QUEUE", "description": "The report has not yet started processing. It may be waiting for another `IN_PROGRESS` report."}]}}, {"name": "marketplaceIds", "description": "A list of marketplace identifiers used to filter reports. The reports returned will match at least one of the marketplaces that you specify.", "in": "query", "required": false, "type": "array", "minItems": 1, "maxItems": 10, "items": {"type": "string"}}, {"name": "pageSize", "in": "query", "description": "The maximum number of reports to return in a single call.", "required": false, "type": "integer", "minimum": 1, "maximum": 100, "default": 10}, {"name": "createdSince", "in": "query", "description": "The earliest report creation date and time for reports to include in the response, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format. The default is 90 days ago. Reports are retained for a maximum of 90 days.", "required": false, "type": "string", "format": "date-time"}, {"name": "createdUntil", "in": "query", "description": "The latest report creation date and time for reports to include in the response, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format. The default is now.", "required": false, "type": "string", "format": "date-time"}, {"name": "nextToken", "in": "query", "description": "A string token returned in the response to your previous request. `nextToken` is returned when the number of results exceeds the specified `pageSize` value. To get the next page of results, call the `getReports` operation and include this token as the only parameter. Specifying `nextToken` with any other parameters will cause the request to fail.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetReportsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportTypes": {"value": ["FEE_DISCOUNTS_REPORT", "GET_AFN_INVENTORY_DATA"]}, "processingStatuses": {"value": ["IN_QUEUE", "IN_PROGRESS"]}}}, "response": {"nextToken": "VGhpcyB0b2tlbiBpcyBvcGFxdWUgYW5kIGludGVudGlvbmFsbHkgb2JmdXNjYXRlZA==", "reports": [{"reportId": "ReportId1", "reportType": "FEE_DISCOUNTS_REPORT", "dataStartTime": "2024-03-11T13:47:20.677Z", "dataEndTime": "2024-03-12T13:47:20.677Z", "createdTime": "2024-03-10T13:47:20.677Z", "processingStatus": "IN_PROGRESS", "processingStartTime": "2024-03-10T13:47:20.677Z", "processingEndTime": "2024-03-12T13:47:20.677Z"}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportTypes": {"value": ["FEE_DISCOUNTS_REPORT", "GET_AFN_INVENTORY_DATA"]}, "processingStatuses": {"value": ["BAD_VALUE", "IN_PROGRESS"]}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input in processing status"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "post": {"tags": ["reports"], "operationId": "createReport", "description": "Creates a report.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.0167 | 15 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateReportSpecification"}, "description": "Information required to create the report."}], "responses": {"202": {"description": "Success.", "schema": {"$ref": "#/definitions/CreateReportResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"reportType": "GET_MERCHANT_LISTINGS_ALL_DATA", "dataStartTime": "2024-03-10T20:11:24.000Z", "marketplaceIds": ["A1PA6795UKMFR9", "ATVPDKIKX0DER"]}}}}, "response": {"reportId": "ID323"}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"reportType": "BAD_FEE_DISCOUNTS_REPORT", "dataStartTime": "2024-03-10T20:11:24.000Z", "marketplaceIds": ["A1PA6795UKMFR9", "ATVPDKIKX0DER"]}}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/reports/2021-06-30/reports/{reportId}": {"delete": {"tags": ["reports"], "operationId": "cancelReport", "description": "Cancels the report that you specify. Only reports with `processingStatus=IN_QUEUE` can be cancelled. Cancelled reports are returned in subsequent calls to the `getReport` and `getReports` operations.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.0222 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "parameters": [{"name": "reportId", "in": "path", "description": "The identifier for the report. This identifier is unique only in combination with a seller ID.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportId": {"value": "ID"}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "get": {"tags": ["reports"], "operationId": "getReport", "description": "Returns report details (including the `reportDocumentId`, if available) for the report that you specify.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 2 | 15 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "parameters": [{"name": "reportId", "in": "path", "required": true, "description": "The identifier for the report. This identifier is unique only in combination with a seller ID.", "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/Report"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportId": {"value": "ID323"}}}, "response": {"reportId": "ReportId1", "reportType": "FEE_DISCOUNTS_REPORT", "dataStartTime": "2024-03-11T13:47:20.677Z", "dataEndTime": "2024-03-12T13:47:20.677Z", "createdTime": "2024-03-10T13:47:20.677Z", "processingStatus": "IN_PROGRESS", "processingStartTime": "2024-03-10T13:47:20.677Z", "processingEndTime": "2024-03-12T13:47:20.677Z"}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportId": {"value": "badReportId1"}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/reports/2021-06-30/schedules": {"get": {"tags": ["reports"], "operationId": "getReportSchedules", "description": "Returns report schedule details that match the filters that you specify.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.0222 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "parameters": [{"name": "reportTypes", "in": "query", "required": true, "type": "array", "minItems": 1, "maxItems": 10, "description": "A list of report types used to filter report schedules. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information.", "items": {"type": "string"}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/ReportScheduleList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportTypes": {"value": ["FEE_DISCOUNTS_REPORT", "GET_FBA_FULFILLMENT_CUSTOMER_TAXES_DATA"]}}}, "response": {"reportSchedules": [{"reportType": "FEE_DISCOUNTS_REPORT", "marketplaceIds": ["ATVPDKIKX0DER"], "reportScheduleId": "ID1", "period": "PT5M", "nextReportCreationTime": "2024-03-11T15:03:44.973Z"}, {"reportType": "GET_FBA_FULFILLMENT_CUSTOMER_TAXES_DATA", "reportScheduleId": "ID2", "period": "PT5M", "nextReportCreationTime": "2024-03-11T15:03:44.973Z"}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportTypes": {"value": ["BAD_FEE_DISCOUNTS_REPORT", "BAD_GET_FBA_FULFILLMENT_CUSTOMER_TAXES_DATA"]}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "post": {"tags": ["reports"], "operationId": "createReportSchedule", "description": "Creates a report schedule. If a report schedule with the same report type and marketplace IDs already exists, it will be cancelled and replaced with this one.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.0222 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateReportScheduleSpecification"}, "description": "Information required to create the report schedule."}], "responses": {"201": {"description": "Success.", "schema": {"$ref": "#/definitions/CreateReportScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"reportType": "FEE_DISCOUNTS_REPORT", "period": "PT5M", "nextReportCreationTime": "2024-03-10T20:11:24.000Z", "marketplaceIds": ["A1PA6795UKMFR9", "ATVPDKIKX0DER"]}}}}, "response": {"reportScheduleId": "ID323"}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"reportType": "BAD_FEE_DISCOUNTS_REPORT", "period": "PT5M", "nextReportCreationTime": "2024-03-10T20:11:24.000Z"}}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/reports/2021-06-30/schedules/{reportScheduleId}": {"delete": {"tags": ["reports"], "operationId": "cancelReportSchedule", "description": "Cancels the report schedule that you specify.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.0222 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "parameters": [{"name": "reportScheduleId", "in": "path", "description": "The identifier for the report schedule. This identifier is unique only in combination with a seller ID.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportScheduleId": {"value": "ID"}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "get": {"tags": ["reports"], "operationId": "getReportSchedule", "description": "Returns report schedule details for the report schedule that you specify.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.0222 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "parameters": [{"name": "reportScheduleId", "in": "path", "required": true, "description": "The identifier for the report schedule. This identifier is unique only in combination with a seller ID.", "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/ReportSchedule"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportScheduleId": {"value": "ID323"}}}, "response": {"reportScheduleId": "ReportScheduleId1", "reportType": "FEE_DISCOUNTS_REPORT", "period": "PT5M", "nextReportCreationTime": "2024-03-12T13:47:20.677Z"}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportScheduleId": {"value": "badReportId1"}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/reports/2021-06-30/documents/{reportDocumentId}": {"get": {"tags": ["reports"], "description": "Returns the information required for retrieving a report document's contents.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.0167 | 15 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getReportDocument", "parameters": [{"name": "reportDocumentId", "in": "path", "description": "The identifier for the report document.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/ReportDocument"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportDocumentId": {"value": "0356cf79-b8b0-4226-b4b9-0ee058ea5760"}}}, "response": {"reportDocumentId": "0356cf79-b8b0-4226-b4b9-0ee058ea5760", "url": "https://d34o8swod1owfl.cloudfront.net/Report_47700__GET_MERCHANT_LISTINGS_ALL_DATA_.txt"}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reportDocumentId": {"value": "badDocumentId1"}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}}, "definitions": {"ErrorList": {"type": "object", "description": "A list of error responses returned when a request is unsuccessful.", "required": ["errors"], "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/Error"}, "description": "Error response returned when the request is unsuccessful."}}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}, "Report": {"type": "object", "description": "Detailed information about the report.", "required": ["processingStatus", "reportId", "reportType", "createdTime"], "properties": {"marketplaceIds": {"description": "A list of marketplace identifiers for the report.", "type": "array", "items": {"type": "string"}}, "reportId": {"description": "The identifier for the report. This identifier is unique only in combination with a seller ID.", "type": "string"}, "reportType": {"description": "The report type. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information.", "type": "string"}, "dataStartTime": {"description": "The start of a date and time range used for selecting the data to report.", "type": "string", "format": "date-time"}, "dataEndTime": {"description": "The end of a date and time range used for selecting the data to report.", "type": "string", "format": "date-time"}, "reportScheduleId": {"description": "The identifier of the report schedule that created this report (if any). This identifier is unique only in combination with a seller ID.", "type": "string"}, "createdTime": {"description": "The date and time when the report was created.", "type": "string", "format": "date-time"}, "processingStatus": {"description": "The processing status of the report.", "type": "string", "enum": ["CANCELLED", "DONE", "FATAL", "IN_PROGRESS", "IN_QUEUE"], "x-docgen-enum-table-extension": [{"value": "CANCELLED", "description": "The report was cancelled. There are two ways a report can be cancelled: an explicit cancellation request before the report starts processing, or an automatic cancellation if there is no data to return."}, {"value": "DONE", "description": "The report has completed processing."}, {"value": "FATAL", "description": "The report was aborted due to a fatal error."}, {"value": "IN_PROGRESS", "description": "The report is being processed."}, {"value": "IN_QUEUE", "description": "The report has not yet started processing. It may be waiting for another `IN_PROGRESS` report."}]}, "processingStartTime": {"description": "The date and time when the report processing started, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format.", "type": "string", "format": "date-time"}, "processingEndTime": {"description": "The date and time when the report processing completed, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format.", "type": "string", "format": "date-time"}, "reportDocumentId": {"description": "The identifier for the report document. Pass this into the `getReportDocument` operation to get the information you will need to retrieve the report document's contents.", "type": "string"}}}, "ReportList": {"type": "array", "description": "A list of reports.", "items": {"$ref": "#/definitions/Report"}}, "CreateReportScheduleSpecification": {"type": "object", "description": "Information required to create the report schedule.", "required": ["marketplaceIds", "period", "reportType"], "properties": {"reportType": {"description": "The report type. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information.", "type": "string"}, "marketplaceIds": {"description": "A list of marketplace identifiers for the report schedule.", "type": "array", "minItems": 1, "maxItems": 25, "items": {"type": "string"}}, "reportOptions": {"$ref": "#/definitions/ReportOptions"}, "period": {"description": "One of a set of predefined <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> periods that specifies how often a report should be created.", "type": "string", "enum": ["PT5M", "PT15M", "PT30M", "PT1H", "PT2H", "PT4H", "PT8H", "PT12H", "P1D", "P2D", "P3D", "PT84H", "P7D", "P14D", "P15D", "P18D", "P30D", "P1M"], "x-docgen-enum-table-extension": [{"value": "PT5M", "description": "5 minutes"}, {"value": "PT15M", "description": "15 minutes"}, {"value": "PT30M", "description": "30 minutes"}, {"value": "PT1H", "description": "1 hour"}, {"value": "PT2H", "description": "2 hours"}, {"value": "PT4H", "description": "4 hours"}, {"value": "PT8H", "description": "8 hours"}, {"value": "PT12H", "description": "12 hours"}, {"value": "P1D", "description": "1 day"}, {"value": "P2D", "description": "2 days"}, {"value": "P3D", "description": "3 days"}, {"value": "PT84H", "description": "84 hours"}, {"value": "P7D", "description": "7 days"}, {"value": "P14D", "description": "14 days"}, {"value": "P15D", "description": "15 days"}, {"value": "P18D", "description": "18 days"}, {"value": "P30D", "description": "30 days"}, {"value": "P1M", "description": "1 month"}]}, "nextReportCreationTime": {"description": "The date and time when the schedule will create its next report, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format.", "type": "string", "format": "date-time"}}}, "CreateReportSpecification": {"type": "object", "description": "Information required to create the report.", "required": ["marketplaceIds", "reportType"], "properties": {"reportOptions": {"$ref": "#/definitions/ReportOptions"}, "reportType": {"description": "The report type. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information.", "type": "string"}, "dataStartTime": {"description": "The start of a date and time range, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format, used for selecting the data to report. The default is now. The value must be prior to or equal to the current date and time. Not all report types make use of this.", "type": "string", "format": "date-time"}, "dataEndTime": {"description": "The end of a date and time range, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format, used for selecting the data to report. The default is now. The value must be prior to or equal to the current date and time. Not all report types make use of this.", "type": "string", "format": "date-time"}, "marketplaceIds": {"description": "A list of marketplace identifiers. The report document's contents will contain data for all of the specified marketplaces, unless the report type indicates otherwise.", "type": "array", "minItems": 1, "maxItems": 25, "items": {"type": "string"}}}}, "ReportOptions": {"description": "Additional information passed to reports. This varies by report type.", "type": "object", "additionalProperties": {"type": "string"}}, "ReportSchedule": {"description": "Detailed information about a report schedule.", "type": "object", "required": ["period", "reportScheduleId", "reportType"], "properties": {"reportScheduleId": {"description": "The identifier for the report schedule. This identifier is unique only in combination with a seller ID.", "type": "string"}, "reportType": {"description": "The report type. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information.", "type": "string"}, "marketplaceIds": {"description": "A list of marketplace identifiers. The report document's contents will contain data for all of the specified marketplaces, unless the report type indicates otherwise.", "type": "array", "items": {"type": "string"}}, "reportOptions": {"$ref": "#/definitions/ReportOptions"}, "period": {"description": "An <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> period value that indicates how often a report should be created.", "type": "string"}, "nextReportCreationTime": {"description": "The date and time when the schedule will create its next report, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format.", "type": "string", "format": "date-time"}}}, "ReportScheduleList": {"type": "object", "description": "A list of report schedules.", "required": ["reportSchedules"], "properties": {"reportSchedules": {"type": "array", "items": {"$ref": "#/definitions/ReportSchedule"}, "description": "Detailed information about a report schedule."}}}, "CreateReportResponse": {"type": "object", "description": "The response schema.", "required": ["reportId"], "properties": {"reportId": {"description": "The identifier for the report. This identifier is unique only in combination with a seller ID.", "type": "string"}}}, "GetReportsResponse": {"type": "object", "required": ["reports"], "properties": {"reports": {"description": "The reports.", "$ref": "#/definitions/ReportList"}, "nextToken": {"description": "Returned when the number of results exceeds `pageSize`. To get the next page of results, call `getReports` with this token as the only parameter.", "type": "string"}}, "description": "The response for the `getReports` operation."}, "CreateReportScheduleResponse": {"type": "object", "description": "Response schema.", "required": ["reportScheduleId"], "properties": {"reportScheduleId": {"description": "The identifier for the report schedule. This identifier is unique only in combination with a seller ID.", "type": "string"}}}, "ReportDocument": {"type": "object", "description": "Information required for the report document.", "required": ["reportDocumentId", "url"], "properties": {"reportDocumentId": {"description": "The identifier for the report document. This identifier is unique only in combination with a seller ID.", "type": "string"}, "url": {"description": "A presigned URL for the report document. If `compressionAlgorithm` is not returned, you can download the report directly from this URL. This URL expires after 5 minutes.", "type": "string"}, "compressionAlgorithm": {"description": "If the report document contents have been compressed, the compression algorithm used is returned in this property and you must decompress the report when you download. Otherwise, you can download the report directly. Refer to [Step 2. Download the report](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-retrieve-a-report#step-2-download-the-report) in the use case guide, where sample code is provided.", "type": "string", "enum": ["GZIP"], "x-docgen-enum-table-extension": [{"value": "GZIP", "description": "The gzip compression algorithm."}]}}}}, "basePath": "/"}