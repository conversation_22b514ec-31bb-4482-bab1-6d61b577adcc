{"swagger": "2.0", "info": {"description": "The [Selling Partner API for Sellers](https://developer-docs.amazon.com/sp-api/docs/sellers-api-v1-reference) (Sellers API) provides essential information about seller accounts, such as:\n\n- The marketplaces a seller can list in\n- The default language and currency of a marketplace\n- Whether the seller has suspended listings\n\nRefer to the [Sellers API reference](https://developer-docs.amazon.com/sp-api/docs/sellers-api-v1-reference) for details about this API's operations, data types, and schemas.", "version": "v1", "title": "Selling Partner API for Sellers", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/sellers/v1/marketplaceParticipations": {"get": {"tags": ["sellers"], "description": "Returns a list of marketplaces where the seller can list items and information about the seller's participation in those marketplaces.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.016 | 15 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getMarketplaceParticipations", "parameters": [], "responses": {"200": {"description": "Marketplace participations successfully retrieved.", "schema": {"$ref": "#/definitions/GetMarketplaceParticipationsResponse"}, "examples": {"payload": [{"marketplace": {"id": "ATVPDKIKX0DER", "name": "Amazon.com", "countryCode": "US", "defaultCurrencyCode": "USD", "defaultLanguageCode": "en_US", "domainName": "www.amazon.com"}, "participation": {"isParticipating": true, "hasSuspendedListings": false}}]}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": [{"marketplace": {"id": "ATVPDKIKX0DER", "countryCode": "US", "name": "Amazon.com", "defaultCurrencyCode": "USD", "defaultLanguageCode": "en_US", "domainName": "www.amazon.com"}, "participation": {"isParticipating": true, "hasSuspendedListings": false}}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetMarketplaceParticipationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/GetMarketplaceParticipationsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetMarketplaceParticipationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/GetMarketplaceParticipationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The entity of the request is in a format not supported by the requested resource.", "schema": {"$ref": "#/definitions/GetMarketplaceParticipationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetMarketplaceParticipationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetMarketplaceParticipationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetMarketplaceParticipationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/sellers/v1/account": {"get": {"tags": ["sellers"], "description": "Returns information about a seller account and its marketplaces.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.016 | 15 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getAccount", "parameters": [], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "examples": {"application/json": {"payload": {"businessType": "PRIVATE_LIMITED", "marketplaceLevelAttributes": [{"marketplace": {"id": "ATVPDKIKX0DER", "name": "United States", "countryCode": "US", "domainName": "www.amazon.com"}, "storeName": "BestSellerStore", "listingStatus": "ACTIVE", "sellingPlan": "PROFESSIONAL"}], "business": {"name": "BestSeller Inc.", "nonLatinName": "ベストセラー株式会社", "registeredBusinessAddress": {"addressLine1": "123 Main St", "addressLine2": "Suite 500", "city": "Seattle", "stateOrProvinceCode": "WA", "postalCode": "98101", "countryCode": "US"}, "companyRegistrationNumber": "*********"}, "primaryContact": {"name": "<PERSON>", "nonLatinName": "ジョン・ドゥ", "address": {"addressLine1": "456 Oak St", "addressLine2": "Apt 12", "city": "Seattle", "stateOrProvinceCode": "WA", "postalCode": "98102", "countryCode": "US"}}}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"response": {"payload": {"marketplaceLevelAttributes": [{"marketplace": {"id": "ATVPDKIKX0DER", "name": "United States", "countryCode": "US", "domainName": "www.amazon.com"}, "storeName": "BestSellerStore", "listingStatus": "ACTIVE", "sellingPlan": "PROFESSIONAL"}], "businessType": "SOLE_PROPRIETORSHIP", "business": {"name": "BestSeller Inc.", "registeredBusinessAddress": {"addressLine1": "123 Main St", "addressLine2": "Suite 500", "city": "Seattle", "stateOrProvinceCode": "WA", "postalCode": "98101", "countryCode": "US"}, "companyTaxIdentificationNumber": "TAX123456"}, "primaryContact": {"name": "<PERSON>", "nonLatinName": "ジョン・ドゥ", "address": {"addressLine1": "456 Oak St", "addressLine2": "Apt 12", "city": "Seattle", "stateOrProvinceCode": "WA", "postalCode": "98102", "countryCode": "US"}}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "examples": {"application/json": {"errors": [{"code": "400", "message": "Validation failed for obfuscatedId:ACUULBDVZHYZ1"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"response": {"errors": [{"code": "400", "message": "Validation failed for obfuscatedId:ACUULBDVZHYZ1"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "schema": {"$ref": "#/definitions/GetAccountResponse"}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}}, "definitions": {"Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition in a human-readable form."}, "details": {"type": "string", "description": "Additional details that can help you understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "MarketplaceParticipation": {"type": "object", "required": ["marketplace", "participation"], "properties": {"marketplace": {"$ref": "#/definitions/Marketplace"}, "participation": {"$ref": "#/definitions/Participation"}}}, "MarketplaceParticipationList": {"type": "array", "description": "List of marketplace participations.", "items": {"$ref": "#/definitions/MarketplaceParticipation"}}, "GetMarketplaceParticipationsResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `getMarketplaceParticipations` operation.", "$ref": "#/definitions/MarketplaceParticipationList"}, "errors": {"description": "The errors encountered by the `getMarketplaceParticipations` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `getMarketplaceParticipations` operation."}, "Marketplace": {"type": "object", "required": ["id", "name", "countryCode", "defaultCurrencyCode", "defaultLanguageCode", "domainName"], "properties": {"id": {"type": "string", "description": "The encrypted marketplace value."}, "name": {"type": "string", "description": "The marketplace name."}, "countryCode": {"type": "string", "description": "The ISO 3166-1 alpha-2 format country code of the marketplace.", "pattern": "^([A-Z]{2})$"}, "defaultCurrencyCode": {"type": "string", "description": "The ISO 4217 format currency code of the marketplace."}, "defaultLanguageCode": {"type": "string", "description": "The ISO 639-1 format language code of the marketplace."}, "domainName": {"type": "string", "description": "The domain name of the marketplace."}}, "description": "Information about an Amazon marketplace where a seller can list items and customers can view and purchase items."}, "Participation": {"type": "object", "required": ["hasSuspendedListings", "isParticipating"], "properties": {"isParticipating": {"type": "boolean", "description": "If `true`, the seller participates in the marketplace. Otherwise `false`."}, "hasSuspendedListings": {"type": "boolean", "description": "Specifies if the seller has suspended listings. `true` if the seller Listing Status is set to Inactive, otherwise `false`."}}, "description": "Information that is specific to a seller in a marketplace."}, "GetAccountResponse": {"description": "The response schema for the `getAccount` operation.", "type": "object", "properties": {"payload": {"$ref": "#/definitions/Account"}, "errors": {"description": "The errors encountered by the `getAccount` operation.", "$ref": "#/definitions/ErrorList"}}}, "MarketplaceLevelAttributes": {"type": "object", "required": ["marketplace", "storeName", "listingStatus", "sellingPlan"], "properties": {"marketplace": {"$ref": "#/definitions/Marketplace"}, "storeName": {"type": "string", "description": "The name of the seller's store as displayed in the marketplace."}, "listingStatus": {"type": "string", "description": "The current status of the seller's listings.", "enum": ["ACTIVE", "INACTIVE"], "x-docgen-enum-table-extension": [{"value": "ACTIVE", "description": "The seller's listings are currently active."}, {"value": "INACTIVE", "description": "The seller's listings are currently inactive."}]}, "sellingPlan": {"type": "string", "description": "The selling plan details.", "enum": ["PROFESSIONAL", "INDIVIDUAL"], "x-docgen-enum-table-extension": [{"value": "PROFESSIONAL", "description": "The seller has a professional selling plan."}, {"value": "INDIVIDUAL", "description": "The seller has an individual selling plan."}]}}, "description": "Attributes that define the seller's presence and status within a specific marketplace. These attributes include the marketplace details, store name, listing status, and the selling plan the seller is subscribed to."}, "Account": {"description": "The response schema for the `getAccount` operation.", "type": "object", "required": ["businessType", "marketplaceLevelAttributes"], "properties": {"marketplaceLevelAttributes": {"type": "array", "description": "A list of details of the marketplaces where the seller account is active.", "items": {"$ref": "#/definitions/MarketplaceLevelAttributes"}}, "businessType": {"type": "string", "description": "The type of business registered for the seller account.", "enum": ["CHARITY", "CRAFTSMAN", "NATURAL_PERSON_COMPANY", "PUBLIC_LISTED", "PRIVATE_LIMITED", "SOLE_PROPRIETORSHIP", "STATE_OWNED", "INDIVIDUAL"], "x-docgen-enum-table-extension": [{"value": "CHARITY", "description": "The business is registered as a charity."}, {"value": "CRAFTSMAN", "description": "The business is registered as a craftsman."}, {"value": "NATURAL_PERSON_COMPANY", "description": "The business is a natural person company."}, {"value": "PUBLIC_LISTED", "description": "The business is a publicly listed company."}, {"value": "PRIVATE_LIMITED", "description": "The business is a private limited company."}, {"value": "SOLE_PROPRIETORSHIP", "description": "The business is a sole proprietorship."}, {"value": "STATE_OWNED", "description": "The business is state-owned."}, {"value": "INDIVIDUAL", "description": "The entity is not a business but an individual."}]}, "business": {"$ref": "#/definitions/Business"}, "primaryContact": {"$ref": "#/definitions/PrimaryContact"}}}, "Business": {"type": "object", "description": "Information about the seller's business. Certain fields may be omitted depending on the seller's `businessType`.", "required": ["name", "registeredBusinessAddress"], "properties": {"name": {"type": "string", "description": "The registered business name."}, "registeredBusinessAddress": {"$ref": "#/definitions/Address", "description": "The registered business address."}, "companyRegistrationNumber": {"type": "string", "description": "The seller's company registration number, if applicable. This field will be absent for individual sellers and sole proprietorships."}, "companyTaxIdentificationNumber": {"type": "string", "description": "The seller's company tax identification number, if applicable. This field will be present for certain business types only, such as sole proprietorships."}, "nonLatinName": {"type": "string", "description": "The non-Latin script version of the registered business name, if applicable."}}}, "Address": {"type": "object", "description": "Represents an address", "required": ["addressLine1", "countryCode"], "properties": {"addressLine1": {"type": "string", "description": "Street address information."}, "addressLine2": {"type": "string", "description": "Additional street address information."}, "countryCode": {"type": "string", "description": "The country code in two-character ISO 3166-1 alpha-2 format."}, "stateOrProvinceCode": {"type": "string", "description": "The state or province code."}, "city": {"type": "string", "description": "The city."}, "postalCode": {"type": "string", "description": "The postal code."}}}, "PrimaryContact": {"type": "object", "description": "Information about the seller's primary contact.", "required": ["name", "address"], "properties": {"name": {"type": "string", "description": "The full name of the seller's primary contact."}, "address": {"$ref": "#/definitions/Address", "description": "The primary contact's residential address."}, "nonLatinName": {"type": "string", "description": "The non-Latin script version of the primary contact's name, if applicable."}}}}}