{"swagger": "2.0", "info": {"title": "The Selling Partner API for Amazon Warehousing and Distribution", "version": "2024-05-09", "description": "The Selling Partner API for Amazon Warehousing and Distribution (AWD) provides programmatic access to information about AWD shipments and inventory. ", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/awd/2024-05-09/inboundShipments/{shipmentId}": {"get": {"produces": ["application/json"], "parameters": [{"description": "ID for the shipment. A shipment contains the cases being inbounded.", "in": "path", "minLength": 1, "name": "shipmentId", "required": true, "type": "string"}, {"description": "If equal to `SHOW`, the response includes the shipment SKU quantity details.\n\nDefaults to `HIDE`, in which case the response does not contain SKU quantities", "enum": ["SHOW", "HIDE"], "in": "query", "name": "skuQuantities", "type": "string", "x-example": "SHOW", "x-docgen-enum-table-extension": [{"description": "Show the additional SKU quantity details.", "value": "SHOW"}, {"description": "Hide the additional SKU quantity details.", "value": "HIDE"}]}], "responses": {"200": {"description": "The 200 response for `getInboundShipment`.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/InboundShipment"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TestShipmentId"}}}, "response": {"createdAt": "2023-06-07T12:12:09.061Z", "destinationAddress": {"addressLine1": "address_line_1", "addressLine2": "address_line_2", "addressLine3": "address_line_3", "city": "City1", "countryCode": "CC", "district": "District1", "name": "DestinationName", "postalCode": "123456", "stateOrRegion": "State1"}, "orderId": "TestOrderId", "originAddress": {"addressLine1": "address_1", "addressLine2": "address_2", "addressLine3": "address_3", "city": "City1", "countryCode": "CC", "district": "District1", "name": "address_name", "postalCode": "123456", "stateOrRegion": "State1"}, "receivedQuantity": [{"quantity": 0, "unitOfMeasurement": "CASES"}], "shipmentContainerQuantities": [{"count": 2, "distributionPackage": {"contents": {"packages": [], "products": [{"quantity": 20, "sku": "testPen"}]}, "measurements": {"dimensions": {"height": 33.02, "length": 45.72, "unitOfMeasurement": "CENTIMETERS", "width": 15.24}, "weight": {"unitOfMeasurement": "KILOGRAMS", "weight": 5.443104}}, "type": "CASE"}}], "shipmentId": "TestShipmentId", "shipmentStatus": "CREATED", "shipmentSkuQuantities": [{"sku": "testPen", "expectedQuantity": {"quantity": 2, "unitOfMeasurement": "CASE"}, "receivedQuantity": {"quantity": 0, "unitOfMeasurement": "CASE"}}], "updatedAt": "2023-06-07T12:12:09.061Z"}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": " "}}}, "response": {"errors": [{"code": "BAD_REQUEST", "details": "This exception is thrown when client inputs are invalid", "message": "ShipmentId should be present for this request"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "404": {"description": "The resource specified does not exist.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "415": {"description": "The request payload is in an unsupported format.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}}, "tags": ["awd"], "description": "Retrieves an AWD inbound shipment.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 2 | 2 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api)", "operationId": "getInboundShipment"}}, "/awd/2024-05-09/inboundShipments": {"get": {"produces": ["application/json"], "parameters": [{"description": "Field to sort results by. By default, the response will be sorted by UPDATED_AT.", "enum": ["UPDATED_AT", "CREATED_AT"], "in": "query", "name": "sortBy", "type": "string", "x-docgen-enum-table-extension": [{"description": "Sort by the time of update.", "value": "UPDATED_AT"}, {"description": "Sort by the time of creation.", "value": "CREATED_AT"}]}, {"description": "Sort the response in ASCENDING or DESCENDING order. By default, the response will be sorted in DESCENDING order.", "enum": ["ASCENDING", "DESCENDING"], "in": "query", "name": "sortOrder", "type": "string", "x-docgen-enum-table-extension": [{"description": "Sorts the collection in ascending order.", "value": "ASCENDING"}, {"description": "Sorts the collection in descending order.", "value": "DESCENDING"}], "x-example": "ASCENDING"}, {"description": "Filter by inbound shipment status.", "enum": ["CREATED", "SHIPPED", "IN_TRANSIT", "RECEIVING", "DELIVERED", "CLOSED", "CANCELLED"], "in": "query", "name": "shipmentStatus", "type": "string", "x-docgen-enum-table-extension": [{"description": "Shipment is created, but hasn't shipped.", "value": "CREATED"}, {"description": "Shipment was picked up by the carrier or was dropped off with the carrier.", "value": "SHIPPED"}, {"description": "The carrier has notified AWD that the shipment is in transit between the origin and destination nodes.", "value": "IN_TRANSIT"}, {"description": "The shipment has been partially received.", "value": "RECEIVING"}, {"description": "The shipment has reached the destination node and has been delivered to the facility yard. The shipment `receive` process at the warehouse will start soon.", "value": "DELIVERED"}, {"description": "No more actions are required for the shipment. This is a final state.", "value": "CLOSED"}, {"description": "The shipment is cancelled. This is a final state.", "value": "CANCELLED"}], "x-example": "CREATED"}, {"description": "List the inbound shipments that were updated after a certain time (inclusive). The date must be in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> format.", "format": "date-time", "in": "query", "name": "updatedAfter", "type": "string", "x-example": "2023-01-12T10:00:00.000Z"}, {"description": "List the inbound shipments that were updated before a certain time (inclusive). The date must be in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> format.", "format": "date-time", "in": "query", "name": "updatedBefore", "type": "string", "x-example": "2023-01-12T10:00:00.000Z"}, {"default": 25, "description": "Maximum number of results to return.", "format": "int32", "in": "query", "maximum": 200, "minimum": 1, "name": "maxResults", "type": "integer", "x-example": "10"}, {"description": "Token to retrieve the next set of paginated results.", "in": "query", "name": "nextToken", "type": "string", "x-example": "SampleToken"}], "responses": {"200": {"description": "The 200 response for `listInboundShipments`.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ShipmentListing"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"shipments": [{"createdAt": "2023-06-07T12:12:09.061Z", "orderId": "TestOrderId", "shipmentId": "TestShipmentId", "shipmentStatus": "CREATED", "updatedAt": "2023-06-07T12:12:09.061Z"}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"sortBy": {"value": "TEST"}}}, "response": {"errors": [{"code": "BAD_REQUEST", "details": "This exception is thrown when client inputs are invalid", "message": "Invalid shipment sortable field received: TEST"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "404": {"description": "The resource specified does not exist.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "415": {"description": "The request payload is in an unsupported format.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}}, "tags": ["awd"], "description": "Retrieves a summary of all the inbound AWD shipments associated with a merchant, with the ability to apply optional filters.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 1 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "listInboundShipments"}}, "/awd/2024-05-09/inventory": {"get": {"produces": ["application/json"], "parameters": [{"description": "Filter by seller or merchant SKU for the item.", "in": "query", "name": "sku", "type": "string", "x-example": "TestSKU"}, {"description": "Sort the response in `ASCENDING` or `DESCENDING` order.", "enum": ["ASCENDING", "DESCENDING"], "in": "query", "name": "sortOrder", "type": "string", "x-docgen-enum-table-extension": [{"description": "Sorts the collection in ascending order.", "value": "ASCENDING"}, {"description": "Sorts the collection in descending order.", "value": "DESCENDING"}]}, {"description": "Set to `SHOW` to return summaries with additional inventory details. Defaults to `HIDE,` which returns only inventory summary totals.", "enum": ["SHOW", "HIDE"], "in": "query", "name": "details", "type": "string", "x-docgen-enum-table-extension": [{"description": "Show the additional summarized inventory details.", "value": "SHOW"}, {"description": "Hide the additional summarized inventory details.", "value": "HIDE"}], "x-example": "SHOW"}, {"description": "Token to retrieve the next set of paginated results.", "in": "query", "name": "nextToken", "type": "string", "x-example": "SampleToken"}, {"default": 25, "description": "Maximum number of results to return.", "format": "int32", "in": "query", "maximum": 200, "minimum": 1, "name": "maxResults", "type": "integer", "x-example": "10"}], "responses": {"200": {"description": "The 200 response for `listInventory`.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/InventoryListing"}, "x-amzn-api-sandbox": {"static": [{"request": {}, "response": {"inventory": [{"totalOnhandQuantity": 20, "totalInboundQuantity": 10, "sku": "100TestSKU1Pen", "inventoryDetails": {"availableDistributableQuantity": 10, "reservedDistributableQuantity": 10}}, {"totalOnhandQuantity": 20, "totalInboundQuantity": 5, "sku": "2U-BKEX-VW4D", "inventoryDetails": {"availableDistributableQuantity": 10, "reservedDistributableQuantity": 10}}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"sortOrder": {"value": "UNKNOWN"}}}, "response": {"errors": [{"code": "BAD_REQUEST", "details": "This exception is thrown when client inputs are invalid", "message": "Invalid value: UNKNOWN passed for sort order. Possible values are: [ASCENDING, DESCENDING]"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "404": {"description": "The resource specified does not exist.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "415": {"description": "The request payload is in an unsupported format.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}}, "tags": ["awd"], "description": "Lists AWD inventory associated with a merchant with the ability to apply optional filters.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 2 | 2 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "listInventory"}}}, "definitions": {"Address": {"description": "Shipping address that represents the origin or destination location.", "properties": {"addressLine1": {"description": "First line of the address text.", "type": "string"}, "addressLine2": {"description": "Optional second line of the address text.", "type": "string"}, "addressLine3": {"description": "Optional third line of the address text.", "type": "string"}, "city": {"description": "Optional city where this address is located.", "type": "string"}, "countryCode": {"description": "Two-digit, ISO 3166-1 alpha-2 formatted country code where this address is located.", "type": "string"}, "county": {"description": "Optional county where this address is located.", "type": "string", "example": "Washington"}, "district": {"description": "Optional district where this address is located.", "type": "string"}, "name": {"description": "Name of the person, business, or institution at this address.", "type": "string"}, "phoneNumber": {"description": "Optional E.164-formatted phone number for an available contact at this address.", "type": "string", "example": "+***********"}, "postalCode": {"description": "Optional postal code where this address is located.", "type": "string"}, "stateOrRegion": {"description": "State or region where this address is located. Note that this is contextual to the specified country code.", "type": "string"}}, "required": ["addressLine1", "countryCode", "name", "stateOrRegion"], "type": "object", "example": {"addressLine1": "address_1", "addressLine2": "address_2", "addressLine3": "address_3", "city": "Seattle", "countryCode": "US", "county": "Washington", "district": "District1", "name": "address_name", "phoneNumber": "+***********", "postalCode": "123456", "stateOrRegion": "Washington"}}, "CarrierCode": {"description": "Identifies the carrier that will deliver the shipment.", "properties": {"carrierCodeType": {"description": "Denotes the carrier type.", "$ref": "#/definitions/CarrierCodeType"}, "carrierCodeValue": {"description": "Value of the carrier code.", "type": "string", "example": "TestCarrierCode"}}, "type": "object", "example": {"carrierCodeType": "SCAC", "carrierCodeValue": "TestCarrierCode"}}, "CarrierCodeType": {"description": "Denotes the type for the carrier.", "enum": ["SCAC"], "type": "string", "x-docgen-enum-table-extension": [{"description": "Standard Carrier Alpha Code. Refer to [What is a Standard Carrier Alpha Code](https://www.help.cbp.gov/s/article/Article-1420?language=en_US) for more information.", "value": "SCAC"}], "example": "SCAC"}, "DimensionUnitOfMeasurement": {"description": "Unit of measurement for package dimensions.", "enum": ["INCHES", "CENTIMETERS"], "type": "string", "x-docgen-enum-table-extension": [{"description": "Denotes package dimensions in inches.", "value": "INCHES"}, {"description": "Denotes package dimensions in centimeters.", "value": "CENTIMETERS"}], "example": "INCHES"}, "DistributionPackage": {"description": "Represents an AWD distribution package.", "properties": {"contents": {"description": "The contents appropriate for the type.", "$ref": "#/definitions/DistributionPackageContents"}, "measurements": {"description": "Measurements of a package, including weight, volume, and dimensions.", "$ref": "#/definitions/MeasurementData"}, "type": {"description": "Type of distribution package.", "$ref": "#/definitions/DistributionPackageType"}}, "required": ["contents", "measurements", "type"], "type": "object", "example": {"contents": {"products": [{"quantity": 1, "sku": "testPen"}]}, "measurements": {"dimensions": {"height": 1, "length": 1, "unitOfMeasurement": "INCHES", "width": 1}, "volume": {"unitOfMeasurement": "CUIN", "volume": 1}, "weight": {"unitOfMeasurement": "POUNDS", "weight": 1}}, "type": "CASE"}}, "DistributionPackageContents": {"description": "Represents the contents inside a package, which can be products or a nested package.", "properties": {"packages": {"description": "This is required only when `DistributionPackageType=PALLET`.", "items": {"$ref": "#/definitions/DistributionPackageQuantity"}, "type": "array"}, "products": {"description": "This is required only when `DistributionPackageType=CASE`.", "items": {"$ref": "#/definitions/ProductQuantity"}, "type": "array"}}, "type": "object"}, "DistributionPackageQuantity": {"description": "Represents a distribution package with its respective quantity.", "properties": {"count": {"description": "Number of cases or pallets with the same package configuration.", "format": "int32", "type": "integer"}, "distributionPackage": {"$ref": "#/definitions/DistributionPackage"}}, "required": ["count", "distributionPackage"], "type": "object", "example": {"count": 1, "distributionPackage": {"contents": {"products": [{"quantity": 1, "sku": "testPen"}]}, "measurements": {"dimensions": {"height": 1, "length": 1, "unitOfMeasurement": "INCHES", "width": 1}, "volume": {"unitOfMeasurement": "CUIN", "volume": 1}, "weight": {"unitOfMeasurement": "POUNDS", "weight": 1}}, "type": "CASE"}}}, "DistributionPackageType": {"description": "Type of distribution packages.", "enum": ["CASE", "PALLET"], "type": "string", "x-docgen-enum-table-extension": [{"description": "Package type `CASE`.", "value": "CASE"}, {"description": "Package type `PALLET`. Currently, the `PALLET` package type is not supported.", "value": "PALLET"}], "example": "CASE"}, "Error": {"description": "Error response returned when the request is unsuccessful.", "properties": {"code": {"description": "An error code that identifies the type of error that occurred.", "type": "string"}, "details": {"description": "Additional details that can help the caller understand or fix the issue.", "type": "string"}, "message": {"description": "A message that describes the error condition.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "ErrorList": {"description": "This exception is thrown when client inputs are invalid.", "properties": {"errors": {"description": "A list of errors describing the failures.", "items": {"$ref": "#/definitions/Error"}, "type": "array"}}, "type": "object", "required": ["errors"]}, "InboundShipment": {"description": "Represents an AWD inbound shipment.", "properties": {"carrierCode": {"description": "The shipment carrier code.", "$ref": "#/definitions/CarrierCode"}, "createdAt": {"description": "Timestamp when the shipment was created. The date is returned in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> format.", "format": "date-time", "type": "string"}, "destinationAddress": {"description": "Destination address for this shipment.", "$ref": "#/definitions/Address"}, "externalReferenceId": {"description": "Client-provided reference ID that can correlate this shipment to client resources. For example, to map this shipment to an internal bookkeeping order record.", "type": "string", "example": "TestReferenceId"}, "orderId": {"description": "The AWD inbound order ID that this inbound shipment belongs to.", "minLength": 1, "type": "string"}, "originAddress": {"description": "Origin address for this shipment.", "$ref": "#/definitions/Address"}, "receivedQuantity": {"description": "Quantity received (at the receiving end) as part of this shipment.", "items": {"$ref": "#/definitions/InventoryQuantity"}, "type": "array"}, "shipBy": {"description": "Timestamp when the shipment will be shipped.", "format": "date-time", "type": "string"}, "shipmentContainerQuantities": {"description": "Packages that are part of this shipment.", "items": {"$ref": "#/definitions/DistributionPackageQuantity"}, "type": "array"}, "shipmentId": {"description": "Unique shipment ID.", "minLength": 1, "type": "string"}, "shipmentSkuQuantities": {"description": "Quantity details at SKU level for the shipment. This attribute will only appear if the skuQuantities parameter in the request is set to SHOW.", "items": {"$ref": "#/definitions/SkuQuantity"}, "type": "array"}, "shipmentStatus": {"description": "Current status of this shipment.", "$ref": "#/definitions/InboundShipmentStatus"}, "trackingId": {"description": "Carrier-unique tracking ID for this shipment.", "minLength": 1, "type": "string"}, "updatedAt": {"description": "Timestamp when the shipment was updated. The date is returned in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> format.", "format": "date-time", "type": "string"}, "warehouseReferenceId": {"description": "An AWD-provided reference ID that you can use to interact with the warehouse. For example, a carrier appointment booking.", "type": "string", "example": "TestWarehouseReferenceId"}}, "required": ["destinationAddress", "orderId", "origin<PERSON><PERSON><PERSON>", "shipmentContainerQuantities", "shipmentId", "shipmentStatus"], "type": "object"}, "InboundShipmentStatus": {"description": "Possible shipment statuses used by shipments.", "enum": ["CREATED", "SHIPPED", "IN_TRANSIT", "RECEIVING", "DELIVERED", "CLOSED", "CANCELLED"], "type": "string", "x-docgen-enum-table-extension": [{"description": "Shipment is created but has not yet shipped.", "value": "CREATED"}, {"description": "Shipment was picked up by the carrier or was dropped off with the carrier.", "value": "SHIPPED"}, {"description": "The carrier has notified AWD that the shipment is in transit between origin and destination node.", "value": "IN_TRANSIT"}, {"description": "The shipment has been partially received.", "value": "RECEIVING"}, {"description": "The shipment has reached the destination node and has been delivered to the facility yard. The shipment receive process at the warehouse will start soon.", "value": "DELIVERED"}, {"description": "No more actions required on the shipment. This is a final state.", "value": "CLOSED"}, {"description": "Shipment has been cancelled. This is a final state.", "value": "CANCELLED"}]}, "InboundShipmentSummary": {"description": "Summary for an AWD inbound shipment containing the shipment ID, which can be used to retrieve the actual shipment.", "properties": {"createdAt": {"description": "Timestamp when the shipment was created.", "format": "date-time", "type": "string"}, "externalReferenceId": {"description": "Optional client-provided reference ID that can be used to correlate this shipment with client resources. For example, to map this shipment to an internal bookkeeping order record.", "type": "string", "example": "TestReferenceId"}, "orderId": {"description": "The AWD inbound order ID that this inbound shipment belongs to.", "minLength": 1, "type": "string"}, "shipmentId": {"description": "A unique shipment ID.", "minLength": 1, "type": "string"}, "shipmentStatus": {"$ref": "#/definitions/InboundShipmentStatus"}, "updatedAt": {"description": "Timestamp when the shipment was updated.", "format": "date-time", "type": "string"}}, "required": ["orderId", "shipmentId", "shipmentStatus"], "type": "object"}, "InventoryDetails": {"description": "Additional inventory details. This object is only displayed if the details parameter in the request is set to `SHOW`.", "properties": {"availableDistributableQuantity": {"description": "Quantity that is available for downstream channel replenishment.", "format": "int64", "type": "integer"}, "reservedDistributableQuantity": {"description": "Quantity that is reserved for a downstream channel replenishment order that is being prepared for shipment.", "format": "int64", "type": "integer"}}, "type": "object"}, "InventoryDetailsVisibility": {"description": "Enum to specify if returned summaries should include additional summarized inventory details and quantities.", "enum": ["SHOW", "HIDE"], "type": "string", "x-docgen-enum-table-extension": [{"description": "Show the additional summarized inventory details.", "value": "SHOW"}, {"description": "Hide the additional summarized inventory details.", "value": "HIDE"}], "example": "SHOW"}, "InventoryListing": {"description": "AWD inventory payload.", "properties": {"inventory": {"description": "List of inventory summaries.", "items": {"$ref": "#/definitions/InventorySummary"}, "type": "array"}, "nextToken": {"description": "Token to retrieve the next set of paginated results.", "type": "string", "example": "SampleToken"}}, "required": ["inventory"], "type": "object"}, "InventoryQuantity": {"description": "Quantity of inventory with an associated measurement unit context.", "properties": {"quantity": {"description": "Quantity of the respective inventory.", "type": "number"}, "unitOfMeasurement": {"description": "Unit of measurement for the inventory.", "$ref": "#/definitions/InventoryUnitOfMeasurement"}}, "required": ["quantity", "unitOfMeasurement"], "type": "object"}, "InventorySummary": {"description": "Summary of inventory per SKU.", "properties": {"inventoryDetails": {"$ref": "#/definitions/InventoryDetails"}, "sku": {"description": "The seller or merchant SKU.", "type": "string"}, "totalInboundQuantity": {"description": "Total quantity that is in-transit from the seller and has not yet been received at an AWD Distribution Center", "format": "int64", "type": "integer"}, "totalOnhandQuantity": {"description": "Total quantity that is present in AWD distribution centers.", "format": "int64", "type": "integer"}}, "required": ["sku"], "type": "object"}, "InventoryUnitOfMeasurement": {"description": "Unit of measurement for the inventory.", "enum": ["PRODUCT_UNITS", "CASES", "PALLETS"], "type": "string", "x-docgen-enum-table-extension": [{"description": "Measures a discrete amount of product units.", "value": "PRODUCT_UNITS"}, {"description": "Measures a discrete amount of cases.", "value": "CASES"}, {"description": "Measures a discrete amount of pallets.", "value": "PALLETS"}]}, "MeasurementData": {"description": "Package weight and dimension.", "properties": {"dimensions": {"description": "Dimensions of the package. Dimensions are required when creating an inbound or outbound order.", "$ref": "#/definitions/PackageDimensions"}, "volume": {"description": "Volume of the package.", "$ref": "#/definitions/PackageVolume"}, "weight": {"description": "Weight of the package.", "$ref": "#/definitions/PackageWeight"}}, "required": ["weight"], "type": "object"}, "PackageDimensions": {"description": "Dimensions of the package.", "properties": {"height": {"description": "Height of the package.", "format": "double", "type": "number"}, "length": {"description": "Length of the package.", "format": "double", "type": "number"}, "unitOfMeasurement": {"description": "Unit of measurement for package dimensions.", "$ref": "#/definitions/DimensionUnitOfMeasurement"}, "width": {"description": "Width of the package.", "format": "double", "type": "number"}}, "required": ["height", "length", "unitOfMeasurement", "width"], "type": "object"}, "PackageVolume": {"description": "Represents the volume of the package with a unit of measurement.", "properties": {"unitOfMeasurement": {"description": "Unit of measurement for the package volume.", "$ref": "#/definitions/VolumeUnitOfMeasurement"}, "volume": {"description": "The package volume value.", "format": "double", "type": "number"}}, "required": ["unitOfMeasurement", "volume"], "type": "object"}, "PackageWeight": {"description": "Represents the weight of the package with a unit of measurement.", "properties": {"unitOfMeasurement": {"description": "Unit of measurement for the package weight.", "$ref": "#/definitions/WeightUnitOfMeasurement"}, "weight": {"description": "The package weight value.", "format": "double", "type": "number"}}, "required": ["unitOfMeasurement", "weight"], "type": "object"}, "ProductAttribute": {"description": "Product instance attribute that is not described at the SKU level in the catalog.", "properties": {"name": {"description": "Product attribute name.", "type": "string", "example": "TestAttribute"}, "value": {"description": "Product attribute value.", "type": "string", "example": "TestAttributeValue"}}, "type": "object", "example": {"name": "TestAttribute", "value": "TestAttributeValue"}}, "ProductQuantity": {"description": "Represents a product with the SKU details and the corresponding quantity.", "properties": {"attributes": {"description": "Attributes for this instance of the product. For example, already-prepped, or other attributes that distinguish the product beyond the SKU.", "items": {"$ref": "#/definitions/ProductAttribute"}, "type": "array"}, "quantity": {"description": "Product quantity.", "format": "int32", "type": "integer"}, "sku": {"description": "The seller or merchant SKU.", "type": "string"}}, "required": ["quantity", "sku"], "type": "object"}, "ShipmentListing": {"description": "A list of inbound shipment summaries filtered by the attributes specified in the request.", "properties": {"nextToken": {"description": "Token to retrieve the next set of paginated results.", "type": "string", "example": "SampleToken"}, "shipments": {"description": "List of inbound shipment summaries.", "items": {"$ref": "#/definitions/InboundShipmentSummary"}, "type": "array"}}, "type": "object"}, "ShipmentSortableField": {"description": "Denotes the field name on which the shipments are to be sorted.", "enum": ["UPDATED_AT", "CREATED_AT"], "type": "string", "x-docgen-enum-table-extension": [{"description": "Sort by updated at timestamp.", "value": "UPDATED_AT"}, {"description": "Sort by created at timestamp.", "value": "CREATED_AT"}], "example": "CREATED_AT"}, "SkuQuantitiesVisibility": {"description": "Enum to specify if returned shipment should include SKU quantity details", "enum": ["SHOW", "HIDE"], "type": "string", "example": "SHOW", "x-docgen-enum-table-extension": [{"description": "Show the additional SKU quantity details", "value": "SHOW"}, {"description": "Hide the additional SKU quantity details", "value": "HIDE"}]}, "SkuQuantity": {"description": "Quantity details for a SKU as part of a shipment", "properties": {"expectedQuantity": {"$ref": "#/definitions/InventoryQuantity"}, "receivedQuantity": {"$ref": "#/definitions/InventoryQuantity"}, "sku": {"description": "The merchant stock keeping unit", "type": "string"}}, "required": ["expectedQuantity", "sku"], "type": "object"}, "SortOrder": {"description": "Sort order for a collection of items. For example, order or shipment.", "enum": ["ASCENDING", "DESCENDING"], "type": "string", "x-docgen-enum-table-extension": [{"description": "Sorts the collection in ascending order.", "value": "ASCENDING"}, {"description": "Sorts the collection in descending order.", "value": "DESCENDING"}], "example": "ASCENDING"}, "VolumeUnitOfMeasurement": {"description": "Unit of measurement for the package volume.", "enum": ["CU_IN", "CBM", "CC"], "type": "string", "x-docgen-enum-table-extension": [{"description": "Denotes volume measurement in cubic inches.", "value": "CU_IN"}, {"description": "Denotes volume measurement in cubic meters.", "value": "CBM"}, {"description": "Denotes volume measurement in cubic centimeters.", "value": "CC"}]}, "WeightUnitOfMeasurement": {"description": "Unit of measurement for the package weight.", "enum": ["POUNDS", "KILOGRAMS"], "type": "string", "x-docgen-enum-table-extension": [{"description": "Denotes weight measurement in pounds.", "value": "POUNDS"}, {"description": "Denotes weight measurement in kilograms.", "value": "KILOGRAMS"}]}}}