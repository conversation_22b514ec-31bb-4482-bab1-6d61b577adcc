{"swagger": "2.0", "info": {"description": "The Selling Partner API for Product Fees lets you programmatically retrieve estimated fees for a product. You can then account for those fees in your pricing.", "version": "v0", "title": "Selling Partner API for Product Fees", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/products/fees/v0/listings/{SellerSKU}/feesEstimate": {"post": {"tags": ["fees"], "description": "Returns the estimated fees for the item indicated by the specified seller SKU in the marketplace specified in the request body.\n\n**Note:** The parameters associated with this operation may contain special characters that require URL encoding to call the API. To avoid errors with SKUs when encoding URLs, refer to [URL Encoding](https://developer-docs.amazon.com/sp-api/docs/url-encoding).\n\nYou can call `getMyFeesEstimateForSKU` for an item on behalf of a selling partner before the selling partner sets the item's price. The selling partner can then take any estimated fees into account. Each fees estimate request must include an original identifier. This identifier is included in the fees estimate so that you can correlate a fees estimate with the original request.\n\n**Note:** This identifier value is used to identify an estimate. Actual costs may vary. Search \"fees\" in [Seller Central](https://sellercentral.amazon.com/) and consult the store-specific fee schedule for the most up-to-date information.\n\n**Note:** When sellers use the `getMyFeesEstimateForSKU` operation with their `SellerSKU`, they get accurate fees based on real item measurements, but only after they've sent their items to Amazon.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 2 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getMyFeesEstimateForSKU", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/GetMyFeesEstimateRequest"}}, {"name": "SellerSKU", "in": "path", "description": "Used to identify an item in the given marketplace. SellerSKU is qualified by the seller's SellerId, which is included with every operation that you submit.", "type": "string", "required": true}], "responses": {"200": {"description": "Success.", "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"FeesEstimateRequest": {"MarketplaceId": "ATVPDKIKX0DER", "IsAmazonFulfilled": false, "PriceToEstimateFees": {"ListingPrice": {"CurrencyCode": "USD", "Amount": 10}, "Shipping": {"CurrencyCode": "USD", "Amount": 10}, "Points": {"PointsNumber": 0, "PointsMonetaryValue": {"CurrencyCode": "USD", "Amount": 0}}}, "Identifier": "UmaS1"}}}}}, "response": {"payload": {"FeesEstimateResult": {"Status": "Success", "FeesEstimateIdentifier": {"MarketplaceId": "ATVPDKIKX0DER", "IdType": "ASIN", "SellerId": "AXXXXXXXXXXXXX", "SellerInputIdentifier": "UmaS1", "IsAmazonFulfilled": false, "IdValue": "B00V5DG6IQ", "PriceToEstimateFees": {"ListingPrice": {"CurrencyCode": "USD", "Amount": 10}, "Shipping": {"CurrencyCode": "USD", "Amount": 10}, "Points": {"PointsNumber": 0, "PointsMonetaryValue": {"CurrencyCode": "USD", "Amount": 0}}}}, "FeesEstimate": {"TimeOfFeesEstimation": "Mon Oct 28 18:49:32 UTC 2019", "TotalFeesEstimate": {"CurrencyCode": "USD", "Amount": 3.0}, "FeeDetailList": []}, "Error": {"Type": "", "Code": "", "Message": "", "Detail": []}}}}}]}, "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"FeesEstimateRequest": {"MarketplaceId": "WRNGMRKTPLCE"}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Incorrect Marketplace identifier.", "details": ""}]}}]}, "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/products/fees/v0/items/{Asin}/feesEstimate": {"post": {"tags": ["fees"], "description": "Returns the estimated fees for the item indicated by the specified ASIN in the marketplace specified in the request body.\n\nYou can call `getMyFeesEstimateForASIN` for an item on behalf of a selling partner before the selling partner sets the item's price. The selling partner can then take estimated fees into account. Each fees request must include an original identifier. This identifier is included in the fees estimate so you can correlate a fees estimate with the original request.\n\n**Note:** This identifier value is used to identify an estimate. Actual costs may vary. Search \"fees\" in [Seller Central](https://sellercentral.amazon.com/) and consult the store-specific fee schedule for the most up-to-date information.\n\n**Note:** When using the `getMyFeesEstimateForASIN` operation with an ASIN, the fee estimates might be different. This is because these estimates use the item's catalog size, which might not always match the actual size of the item sent to Amazon.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 2 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getMyFeesEstimateForASIN", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/GetMyFeesEstimateRequest"}}, {"name": "<PERSON><PERSON>", "in": "path", "description": "The Amazon Standard Identification Number (ASIN) of the item.", "type": "string", "required": true}], "responses": {"200": {"description": "Success.", "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"FeesEstimateRequest": {"MarketplaceId": "ATVPDKIKX0DER", "IsAmazonFulfilled": false, "PriceToEstimateFees": {"ListingPrice": {"CurrencyCode": "USD", "Amount": 10}, "Shipping": {"CurrencyCode": "USD", "Amount": 10}, "Points": {"PointsNumber": 0, "PointsMonetaryValue": {"CurrencyCode": "USD", "Amount": 0}}}, "Identifier": "UmaS1"}}}}}, "response": {"payload": {"FeesEstimateResult": {"Status": "Success", "FeesEstimateIdentifier": {"MarketplaceId": "ATVPDKIKX0DER", "IdType": "ASIN", "SellerId": "AXXXXXXXXXXXXX", "SellerInputIdentifier": "UmaS1", "IsAmazonFulfilled": false, "IdValue": "B00V5DG6IQ", "PriceToEstimateFees": {"ListingPrice": {"CurrencyCode": "USD", "Amount": 10}, "Shipping": {"CurrencyCode": "USD", "Amount": 10}, "Points": {"PointsNumber": 0, "PointsMonetaryValue": {"CurrencyCode": "USD", "Amount": 0}}}}, "FeesEstimate": {"TimeOfFeesEstimation": "Mon Oct 28 18:49:32 UTC 2019", "TotalFeesEstimate": {"CurrencyCode": "USD", "Amount": 3.0}, "FeeDetailList": []}, "Error": {"Type": "", "Code": "", "Message": "", "Detail": []}}}}}]}, "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"FeesEstimateRequest": {"MarketplaceId": "WRNGMRKTPLCE"}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Incorrect Marketplace identifier.", "details": ""}]}}]}, "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetMyFeesEstimateResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/products/fees/v0/feesEstimate": {"post": {"tags": ["fees"], "description": "Returns the estimated fees for a list of products.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.5 | 1 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getMyFeesEstimates", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/GetMyFeesEstimatesRequest"}}], "responses": {"200": {"description": "Success.", "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": [{"FeesEstimateRequest": {"MarketplaceId": "ATVPDKIKX0DER", "IsAmazonFulfilled": false, "PriceToEstimateFees": {"ListingPrice": {"CurrencyCode": "USD", "Amount": 10}, "Shipping": {"CurrencyCode": "USD", "Amount": 10}, "Points": {"PointsNumber": 0, "PointsMonetaryValue": {"CurrencyCode": "USD", "Amount": 0}}}, "Identifier": "UmaS1"}, "IdType": "ASIN", "IdValue": "asin123"}, {"FeesEstimateRequest": {"MarketplaceId": "A1AM78C64UM0Y8", "IsAmazonFulfilled": true, "PriceToEstimateFees": {"ListingPrice": {"CurrencyCode": "MXN", "Amount": 10}, "Shipping": {"CurrencyCode": "MXN", "Amount": 10}, "Points": {"PointsNumber": 0, "PointsMonetaryValue": {"CurrencyCode": "MXN", "Amount": 0}}}, "Identifier": "UmaS2"}, "IdType": "SellerSKU", "IdValue": "sku123"}]}}}, "response": [{"Status": "Success", "FeesEstimateIdentifier": {"MarketplaceId": "ATVPDKIKX0DER", "IdType": "ASIN", "SellerId": "AXXXXXXXXXXXXX", "SellerInputIdentifier": "UmaS1", "IsAmazonFulfilled": false, "IdValue": "asin123", "PriceToEstimateFees": {"ListingPrice": {"CurrencyCode": "USD", "Amount": 10}, "Shipping": {"CurrencyCode": "USD", "Amount": 10}, "Points": {"PointsNumber": 0, "PointsMonetaryValue": {"CurrencyCode": "USD", "Amount": 0}}}}, "FeesEstimate": {"TimeOfFeesEstimation": "Mon Oct 28 18:49:32 UTC 2019", "TotalFeesEstimate": {"CurrencyCode": "USD", "Amount": 3.0}, "FeeDetailList": []}, "Error": {"Type": "", "Code": "", "Message": "", "Detail": []}}, {"Status": "Success", "FeesEstimateIdentifier": {"MarketplaceId": "A1AM78C64UM0Y8", "IdType": "SellerSKU", "SellerId": "AXXXXXXXXXXXXX", "SellerInputIdentifier": "UmaS2", "IsAmazonFulfilled": false, "IdValue": "sku123", "PriceToEstimateFees": {"ListingPrice": {"CurrencyCode": "MXN", "Amount": 10}, "Shipping": {"CurrencyCode": "MXN", "Amount": 10}, "Points": {"PointsNumber": 0, "PointsMonetaryValue": {"CurrencyCode": "MXN", "Amount": 0}}}}, "FeesEstimate": {"TimeOfFeesEstimation": "Mon Oct 28 18:49:32 UTC 2019", "TotalFeesEstimate": {"CurrencyCode": "MXN", "Amount": 3.0}, "FeeDetailList": []}, "Error": {"Type": "", "Code": "", "Message": "", "Detail": []}}]}]}, "schema": {"$ref": "#/definitions/GetMyFeesEstimatesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": [{"FeesEstimateRequest": {"MarketplaceId": "INVALIDMARKETPLACEID", "IsAmazonFulfilled": false, "PriceToEstimateFees": {"ListingPrice": {"CurrencyCode": "USD", "Amount": 10}, "Shipping": {"CurrencyCode": "USD", "Amount": 10}, "Points": {"PointsNumber": 0, "PointsMonetaryValue": {"CurrencyCode": "USD", "Amount": 0}}}, "Identifier": "UmaS1"}, "IdType": "ASIN", "IdValue": "asin123"}]}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Incorrect Marketplace identifier.", "details": ""}]}}]}, "schema": {"$ref": "#/definitions/GetMyFeesEstimatesErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetMyFeesEstimatesErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include **Access Denied**, **Unauthorized**, **Expired Token**, or **Invalid Signature**.", "schema": {"$ref": "#/definitions/GetMyFeesEstimatesErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetMyFeesEstimatesErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetMyFeesEstimatesErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetMyFeesEstimatesErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetMyFeesEstimatesErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}}, "definitions": {"GetMyFeesEstimateRequest": {"description": "Request schema.", "type": "object", "properties": {"FeesEstimateRequest": {"$ref": "#/definitions/FeesEstimateRequest"}}}, "GetMyFeesEstimatesRequest": {"description": "Request for estimated fees for a list of products.", "type": "array", "items": {"$ref": "#/definitions/FeesEstimateByIdRequest"}}, "FeesEstimateByIdRequest": {"type": "object", "description": "A product, marketplace, and proposed price used to request estimated fees.", "required": ["IdType", "IdValue"], "properties": {"FeesEstimateRequest": {"$ref": "#/definitions/FeesEstimateRequest"}, "IdType": {"$ref": "#/definitions/IdType"}, "IdValue": {"description": "The item identifier.", "type": "string"}}}, "FeesEstimateRequest": {"description": "A product, marketplace, and proposed price used to request estimated fees.", "type": "object", "required": ["MarketplaceId", "PriceToEstimateFees", "Identifier"], "properties": {"MarketplaceId": {"description": "A marketplace identifier.", "type": "string"}, "IsAmazonFulfilled": {"description": "When true, the offer is fulfilled by Amazon.", "type": "boolean"}, "PriceToEstimateFees": {"description": "The product price that the fee estimate is based on.", "$ref": "#/definitions/PriceToEstimateFees"}, "Identifier": {"description": "A unique identifier provided by the caller to track this request.", "type": "string"}, "OptionalFulfillmentProgram": {"$ref": "#/definitions/OptionalFulfillmentProgram"}}}, "GetMyFeesEstimateResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the operation.", "$ref": "#/definitions/GetMyFeesEstimateResult"}, "errors": {"$ref": "#/definitions/ErrorList"}}}, "GetMyFeesEstimateResult": {"description": "Response schema.", "type": "object", "properties": {"FeesEstimateResult": {"description": "The item's estimated fees.", "$ref": "#/definitions/FeesEstimateResult"}}}, "GetMyFeesEstimatesResponse": {"description": "Estimated fees for a list of products.", "type": "array", "items": {"$ref": "#/definitions/FeesEstimateResult"}}, "Points": {"type": "object", "properties": {"PointsNumber": {"type": "integer", "format": "int32"}, "PointsMonetaryValue": {"$ref": "#/definitions/MoneyType"}}}, "GetMyFeesEstimatesErrorList": {"type": "object", "description": "A list of error responses returned when a request is unsuccessful.", "required": ["errors"], "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/Error"}}}}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}}, "FeesEstimateResult": {"description": "An item identifier and the estimated fees for the item.", "type": "object", "properties": {"Status": {"description": "The status of the fee request. Possible values: Success, ClientError, ServiceError.", "type": "string"}, "FeesEstimateIdentifier": {"description": "Information used to identify a fees estimate request.", "$ref": "#/definitions/FeesEstimateIdentifier"}, "FeesEstimate": {"description": "The total estimated fees for an item and a list of details.", "$ref": "#/definitions/FeesEstimate"}, "Error": {"description": "An error object with a type, code, and message.", "$ref": "#/definitions/FeesEstimateError"}}}, "FeesEstimateIdentifier": {"description": "An item identifier, marketplace, time of request, and other details that identify an estimate.", "type": "object", "properties": {"MarketplaceId": {"description": "A marketplace identifier.", "type": "string"}, "SellerId": {"description": "The seller identifier.", "type": "string"}, "IdType": {"$ref": "#/definitions/IdType"}, "IdValue": {"description": "The item identifier.", "type": "string"}, "IsAmazonFulfilled": {"description": "When true, the offer is fulfilled by Amazon.", "type": "boolean"}, "PriceToEstimateFees": {"description": "The item price on which the fee estimate is based.", "$ref": "#/definitions/PriceToEstimateFees"}, "SellerInputIdentifier": {"description": "A unique identifier provided by the caller to track this request.", "type": "string"}, "OptionalFulfillmentProgram": {"$ref": "#/definitions/OptionalFulfillmentProgram"}}}, "PriceToEstimateFees": {"description": "Price information for an item, used to estimate fees.", "type": "object", "required": ["ListingPrice"], "properties": {"ListingPrice": {"description": "The price of the item.", "$ref": "#/definitions/MoneyType"}, "Shipping": {"description": "The shipping cost.", "$ref": "#/definitions/MoneyType"}, "Points": {"description": "The number of Amazon Points offered with the purchase of an item.", "$ref": "#/definitions/Points"}}}, "FeesEstimate": {"description": "The total estimated fees for an item and a list of details.", "type": "object", "required": ["TimeOfFeesEstimation"], "properties": {"TimeOfFeesEstimation": {"description": "The time at which the fees were estimated. This defaults to the time the request is made.", "type": "string", "format": "date-time"}, "TotalFeesEstimate": {"description": "Total estimated fees for a given item, price, and fulfillment channel.", "$ref": "#/definitions/MoneyType"}, "FeeDetailList": {"$ref": "#/definitions/FeeDetailList"}}}, "FeeDetailList": {"description": "A list of other fees that contribute to a given fee.", "type": "array", "items": {"$ref": "#/definitions/FeeDetail"}}, "FeesEstimateError": {"description": "An unexpected error occurred during this operation.", "type": "object", "required": ["Code", "Detail", "Message", "Type"], "properties": {"Type": {"description": "An error type, identifying either the receiver or the sender as the originator of the error.", "type": "string"}, "Code": {"description": "An error code that identifies the type of error that occurred.", "type": "string"}, "Message": {"description": "A message that describes the error condition.", "type": "string"}, "Detail": {"$ref": "#/definitions/FeesEstimateErrorDetail"}}}, "FeesEstimateErrorDetail": {"description": "Additional information that can help the caller understand or fix the issue.", "type": "array", "items": {"type": "object"}}, "FeeDetail": {"description": "The type of fee, fee amount, and other details.", "type": "object", "required": ["FeeType", "FeeAmount", "FinalFee"], "properties": {"FeeType": {"description": "The type of fee charged to a seller.", "type": "string"}, "FeeAmount": {"description": "The amount charged for a given fee.", "$ref": "#/definitions/MoneyType"}, "FeePromotion": {"description": "The promotion amount for a given fee.", "$ref": "#/definitions/MoneyType"}, "TaxAmount": {"description": "The tax amount for a given fee.", "$ref": "#/definitions/MoneyType"}, "FinalFee": {"description": "The final fee amount for a given fee.", "$ref": "#/definitions/MoneyType"}, "IncludedFeeDetailList": {"$ref": "#/definitions/IncludedFeeDetailList"}}}, "IncludedFeeDetailList": {"description": "A list of other fees that contribute to a given fee.", "type": "array", "items": {"$ref": "#/definitions/IncludedFeeDetail"}}, "IncludedFeeDetail": {"description": "The type of fee, fee amount, and other details.", "type": "object", "required": ["FeeType", "FeeAmount", "FinalFee"], "properties": {"FeeType": {"description": "The type of fee charged to a seller.", "type": "string"}, "FeeAmount": {"description": "The amount charged for a given fee.", "$ref": "#/definitions/MoneyType"}, "FeePromotion": {"description": "The promotion amount for a given fee.", "$ref": "#/definitions/MoneyType"}, "TaxAmount": {"description": "The tax amount for a given fee.", "$ref": "#/definitions/MoneyType"}, "FinalFee": {"description": "The final fee amount for a given fee.", "$ref": "#/definitions/MoneyType"}}}, "MoneyType": {"type": "object", "properties": {"CurrencyCode": {"description": "The currency code in ISO 4217 format.", "type": "string"}, "Amount": {"description": "The monetary value.", "type": "number"}}}, "OptionalFulfillmentProgram": {"type": "string", "description": "An optional enrollment program to return the estimated fees when the offer is fulfilled by Amazon (IsAmazonFulfilled is set to true).", "enum": ["FBA_CORE", "FBA_SNL", "FBA_EFN"], "x-docgen-enum-table-extension": [{"value": "FBA_CORE", "description": "Returns the standard Amazon fulfillment fees for the offer. This is the default."}, {"value": "FBA_SNL", "description": "Returns the FBA Small and Light (SNL) fees for the offer. The SNL program offers reduced fulfillment costs on qualified items. To check item eligibility for the SNL program, use the getSmallAndLightEligibilityBySellerSKU operation of the FBA Small And Light API.\n\n**Note:** The FBA Small and Light program sunset in the US and EU regions on **September 26, 2023**. The program will be deprecated in the JP region on **March 31, 2024** and sunset on **April 1, 2024**. APIs announcing deprecation will no longer be supported after their deprecation date. Calls to deprecated APIs will fail beginning on their sunset date. For more information, refer to [FBA Small and Light program deprecation](https://developer-docs.amazon.com/sp-api/changelog/fba-small-and-light-program-deprecation)."}, {"value": "FBA_EFN", "description": "Returns the cross-border European Fulfillment Network fees across EU countries for the offer."}]}, "IdType": {"type": "string", "description": "The type of product identifier used in a `FeesEstimateByIdRequest`.", "enum": ["ASIN", "SellerSKU"], "x-docgen-enum-table-extension": [{"value": "ASIN", "description": "An Amazon Standard Identification Number (ASIN) of a listings item."}, {"value": "SellerSKU", "description": "A selling partner provided identifier for an Amazon listing."}]}}}