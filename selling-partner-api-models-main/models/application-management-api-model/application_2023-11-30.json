{"swagger": "2.0", "info": {"title": "Selling Partner API for Application Management", "description": "The Selling Partner API for Application Management lets you programmatically update the client secret on registered applications.", "version": "2023-11-30", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/applications/2023-11-30/clientSecret": {"post": {"tags": ["Applications"], "description": "Rotates application client secrets for a developer application. Developers must register a destination queue in the developer console before calling this operation. When this operation is called a new client secret is generated and sent to the developer-registered queue. For more information, refer to [Rotate your application client secret](https://developer-docs.amazon.com/sp-api/v0/docs/application-management-api-v2023-11-30-use-case-guide#tutorial-rotate-your-applications-client-secret).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.0167 | 1 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "rotateApplicationClientSecret", "parameters": [], "responses": {"204": {"description": "Success.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}}, "400": {"description": "Application is not enrolled for rotation notification. Please update application notification preference in Developer console.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "404": {"description": "The resource specified does not exist.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "415": {"description": "The request payload is in an unsupported format.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ErrorList"}}}}}}, "definitions": {"ErrorList": {"type": "object", "description": "A list of error responses returned when a request is unsuccessful.", "required": ["errors"], "properties": {"errors": {"type": "array", "description": "array of errors", "items": {"$ref": "#/definitions/Error"}}}}, "Error": {"description": "Error response returned when the request is unsuccessful.", "properties": {"code": {"description": "An error code that identifies the type of error that occurred.", "type": "string"}, "message": {"description": "A message that describes the error condition.", "type": "string"}, "details": {"description": "Additional details that can help the caller understand or fix the issue.", "type": "string"}}, "required": ["code", "message"], "type": "object"}}}