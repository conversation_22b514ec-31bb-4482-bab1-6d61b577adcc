{"swagger": "2.0", "info": {"description": "The Selling Partner API for Retail Procurement Transaction Status provides programmatic access to status information on specific asynchronous POST transactions for vendors.", "version": "v1", "title": "Selling Partner API for Retail Procurement Transaction Status", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/vendor/transactions/v1/transactions/{transactionId}": {"get": {"tags": ["vendorTransaction"], "description": "Returns the status of the transaction that you specify.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getTransaction", "parameters": [{"name": "transactionId", "in": "path", "description": "The GUID provided by Amazon in the 'transactionId' field in response to the post request of a specific transaction.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetTransactionResponse"}, "examples": {"application/json": {"payload": {"transactionStatus": {"transactionId": "20190108091302-6ca0ac50-d06e-45f5-a1e2-eb448eadac50", "status": "Processing"}}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amazon-spds-sandbox-behaviors": [{"request": {"parameters": {"transactionId": {"value": "20190904190535-eef8cad8-418e-4ed3-ac72-789e2ee6214a"}}}, "response": {"payload": {"transactionStatus": {"transactionId": "20190904190535-eef8cad8-418e-4ed3-ac72-789e2ee6214a", "status": "Processing"}}}}, {"request": {"parameters": {"transactionId": {"value": "20190918190535-eef8cad8-418e-456f-ac72-789e2ee6813c"}}}, "response": {"payload": {"transactionStatus": {"transactionId": "20190918190535-eef8cad8-418e-456f-ac72-789e2ee6813c", "status": "Failure", "errors": [{"code": "INVALID_ORDER_ID", "message": "Invalid order ID."}]}}}}, {"request": {"parameters": {}}, "response": {"payload": {"transactionStatus": {"transactionId": "20190904190535-eef8cad8-418e-4ed3-ac72-789e2ee6214a", "status": "Processing"}}}}]}, "400": {"description": "Request has missing or not valid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetTransactionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amazon-spds-sandbox-behaviors": [{"request": {"parameters": {"transactionId": {"value": "Tran0904190535-eef8cad8-418e-4ed3-ac72-789e2ee6214a"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid transmission ID.", "details": ""}]}}]}, "401": {"description": "The request's authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetTransactionResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetTransactionResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetTransactionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetTransactionResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetTransactionResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetTransactionResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetTransactionResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}}, "definitions": {"GetTransactionResponse": {"type": "object", "properties": {"payload": {"description": "The response payload for the `getTransaction` operation.", "$ref": "#/definitions/TransactionStatus"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `getTransaction` operation."}, "TransactionStatus": {"type": "object", "description": "Represents the status of a transaction.", "properties": {"transactionStatus": {"$ref": "#/definitions/Transaction"}}}, "Transaction": {"type": "object", "required": ["status", "transactionId"], "properties": {"transactionId": {"type": "string", "description": "The unique identifier returned in the 'transactionId' field in response to the post request of a specific transaction."}, "status": {"type": "string", "description": "Current processing status of the transaction.", "enum": ["Failure", "Processing", "Success"], "x-docgen-enum-table-extension": [{"value": "Failure", "description": "Transaction has failed."}, {"value": "Processing", "description": "Transaction is in process."}, {"value": "Success", "description": "Transaction has completed successfully."}]}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The transaction status."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}}}