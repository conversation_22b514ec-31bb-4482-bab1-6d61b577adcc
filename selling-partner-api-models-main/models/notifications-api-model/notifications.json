{"swagger": "2.0", "info": {"description": "The Selling Partner API for Notifications lets you subscribe to notifications that are relevant to a selling partner's business. Using this API you can create a destination to receive notifications, subscribe to notifications, delete notification subscriptions, and more.\n\nFor more information, refer to the [Notifications Use Case Guide](https://developer-docs.amazon.com/sp-api/docs/notifications-api-v1-use-case-guide).", "version": "v1", "title": "Selling Partner API for Notifications", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/notifications/v1/subscriptions/{notificationType}": {"get": {"tags": ["notifications"], "description": "Returns information about subscription of the specified notification type and payload version. `payloadVersion` is an optional parameter. When `payloadVersion` is not provided, it will return latest payload version subscription's information. You can use this API to get subscription information when you do not have a subscription identifier.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getSubscription", "parameters": [{"$ref": "#/parameters/NotificationType"}, {"name": "payloadVersion", "in": "query", "description": "The version of the payload object to be used in the notification.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetSubscriptionResponse"}, "examples": {"Successful Response": {"payload": {"subscriptionId": "7fcacc7e-727b-11e9-8848-1681be663d3e", "payloadVersion": "1.0", "destinationId": "3acafc7e-121b-1329-8ae8-1571be663aa2", "processingDirective": {"eventFilter": {"marketplaceIds": ["ATVPDKIKX0DER", "A2EUQ1WTGCTBG2"], "aggregationSettings": {"aggregationTimePeriod": "FiveMinutes"}, "eventFilterType": "ANY_OFFER_CHANGED"}}}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": {"subscriptionId": "TEST_CASE_200_SUBSCRIPTION_ID", "payloadVersion": "1.0", "destinationId": "TEST_CASE_200_DESTINATION_ID"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetSubscriptionResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/GetSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "post": {"tags": ["notifications"], "description": "Creates a subscription for the specified notification type to be delivered to the specified destination. Before you can subscribe, you must first create the destination by calling the `createDestination` operation. In cases where the specified notification type supports multiple payload versions, you can utilize this API to subscribe to a different payload version if you already have an existing subscription for a different payload version.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createSubscription", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateSubscriptionRequest"}}, {"$ref": "#/parameters/NotificationType"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CreateSubscriptionResponse"}, "examples": {"Successful Response": {"payload": {"subscriptionId": "7fcacc7e-727b-11e9-8848-1681be663d3e", "payloadVersion": "1.0", "destinationId": "3acafc7e-121b-1329-8ae8-1571be663aa2"}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": {"subscriptionId": "TEST_CASE_200_SUBSCRIPTION_ID", "payloadVersion": "1.0", "destinationId": "TEST_CASE_200_DESTINATION_ID"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateSubscriptionResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "409": {"description": "The resource specified conflicts with the current state.", "schema": {"$ref": "#/definitions/CreateSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/notifications/v1/subscriptions/{notificationType}/{subscriptionId}": {"get": {"tags": ["notifications"], "description": "Returns information about a subscription for the specified notification type. The `getSubscriptionById` operation is grantless. For more information, refer to [Grantless operations](https://developer-docs.amazon.com/sp-api/docs/grantless-operations).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getSubscriptionById", "parameters": [{"name": "subscriptionId", "in": "path", "description": "The identifier for the subscription that you want to get.", "required": true, "type": "string"}, {"$ref": "#/parameters/NotificationType"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetSubscriptionByIdResponse"}, "examples": {"Successful Response": {"payload": {"subscriptionId": "7fcacc7e-727b-11e9-8848-1681be663d3e", "payloadVersion": "1.0", "destinationId": "3acafc7e-121b-1329-8ae8-1571be663aa2"}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": {"subscriptionId": "TEST_CASE_200_SUBSCRIPTION_ID", "payloadVersion": "1.0", "destinationId": "TEST_CASE_200_DESTINATION_ID"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetSubscriptionByIdResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetSubscriptionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "409": {"description": "The resource specified conflicts with the current state.", "schema": {"$ref": "#/definitions/GetSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/GetSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "delete": {"tags": ["notifications"], "description": "Deletes the subscription indicated by the subscription identifier and notification type that you specify. The subscription identifier can be for any subscription associated with your application. After you successfully call this operation, notifications will stop being sent for the associated subscription. The `deleteSubscriptionById` operation is grantless. For more information, refer to [Grantless operations](https://developer-docs.amazon.com/sp-api/docs/grantless-operations).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "deleteSubscriptionById", "parameters": [{"name": "subscriptionId", "in": "path", "description": "The identifier for the subscription that you want to delete.", "required": true, "type": "string"}, {"$ref": "#/parameters/NotificationType"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/DeleteSubscriptionByIdResponse"}, "examples": {"Successful Operation Response": {}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/DeleteSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/DeleteSubscriptionByIdResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/DeleteSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "409": {"description": "The resource specified conflicts with the current state.", "schema": {"$ref": "#/definitions/DeleteSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/DeleteSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/DeleteSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/DeleteSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/DeleteSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/DeleteSubscriptionByIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/notifications/v1/destinations": {"get": {"tags": ["notifications"], "description": "Returns information about all destinations. The `getDestinations` operation is grantless. For more information, refer to [Grantless operations](https://developer-docs.amazon.com/sp-api/docs/grantless-operations).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getDestinations", "parameters": [], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetDestinationsResponse"}, "examples": {"Successful Response": {"payload": [{"destinationId": "9e7a83ee-7730-11e9-8f9e-2a86e4085a59", "name": "SQSDestination", "resource": {"sqs": {"arn": "arn:aws:sqs:us-east-2:444455556666:queue1"}}}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": [{"destinationId": "TEST_CASE_200", "resource": {"sqs": {"arn": "arn:aws:sqs:us-east-2:444455556666:queue1"}}, "name": "SQSDestination"}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetDestinationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetDestinationsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetDestinationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "409": {"description": "The resource specified conflicts with the current state.", "schema": {"$ref": "#/definitions/GetDestinationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/GetDestinationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetDestinationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetDestinationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetDestinationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetDestinationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "post": {"tags": ["notifications"], "description": "Creates a destination resource to receive notifications. The `createDestination` operation is grantless. For more information, refer to [Grantless operations](https://developer-docs.amazon.com/sp-api/docs/grantless-operations).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createDestination", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateDestinationRequest"}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CreateDestinationResponse"}, "examples": {"Successful Response": {"payload": {"destinationId": "9e7a83ee-7730-11e9-8f9e-2a86e4085a59", "resource": {"sqs": {"arn": "arn:aws:sqs:us-east-2:444455556666:queue1"}}, "name": "SQSDestination"}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": {"destinationId": "TEST_CASE_200_DESTINATION_ID", "resource": {"sqs": {"arn": "arn:aws:sqs:us-east-2:444455556666:queue1"}}, "name": "SQSDestination"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateDestinationResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "409": {"description": "The resource specified conflicts with the current state.", "schema": {"$ref": "#/definitions/CreateDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/notifications/v1/destinations/{destinationId}": {"get": {"tags": ["notifications"], "description": "Returns information about the destination that you specify. The `getDestination` operation is grantless. For more information, refer to [Grantless operations](https://developer-docs.amazon.com/sp-api/docs/grantless-operations).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getDestination", "parameters": [{"name": "destinationId", "in": "path", "description": "The identifier generated when you created the destination.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetDestinationResponse"}, "examples": {"Successful Response": {"payload": {"destinationId": "9e7a83ee-7730-11e9-8f9e-2a86e4085a59", "name": "SQSDestination", "resource": {"sqs": {"arn": "arn:aws:sqs:us-east-2:444455556666:queue1"}}}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": {"destinationId": "TEST_CASE_200_DESTINATION", "resource": {"sqs": {"arn": "arn:aws:sqs:us-east-2:444455556666:queue1"}}, "name": "SQSDestination"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetDestinationResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "409": {"description": "The resource specified conflicts with the current state.", "schema": {"$ref": "#/definitions/GetDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/GetDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "delete": {"tags": ["notifications"], "description": "Deletes the destination that you specify. The `deleteDestination` operation is grantless. For more information, refer to [Grantless operations](https://developer-docs.amazon.com/sp-api/docs/grantless-operations).\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "deleteDestination", "parameters": [{"name": "destinationId", "in": "path", "description": "The identifier for the destination that you want to delete.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/DeleteDestinationResponse"}, "examples": {"Successful Response": {}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/DeleteDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/DeleteDestinationResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/DeleteDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "409": {"description": "The resource specified conflicts with the current state.", "schema": {"$ref": "#/definitions/DeleteDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/DeleteDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/DeleteDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/DeleteDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/DeleteDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/DeleteDestinationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}}, "definitions": {"ProcessingDirective": {"description": "Additional information passed to the subscription to control the processing of notifications. For example, you can use an `eventFilter` to customize your subscription to send notifications for only the specified `marketplaceId`s, or select the aggregation time period at which to send notifications (for example: limit to one notification every five minutes for high frequency notifications). The specific features available vary depending on the `notificationType`.\n\nThis feature is currently only supported by the `ANY_OFFER_CHANGED` and `ORDER_CHANGE` `notificationType`s.", "type": "object", "properties": {"eventFilter": {"description": "A `notificationType` specific filter.", "$ref": "#/definitions/EventFilter"}}}, "EventFilter": {"description": "A `notificationType` specific filter. This object contains all of the currently available filters and properties that you can use to define a `notificationType` specific filter.", "allOf": [{"$ref": "#/definitions/AggregationFilter"}, {"$ref": "#/definitions/MarketplaceFilter"}, {"$ref": "#/definitions/OrderChangeTypeFilter"}, {"type": "object", "properties": {"eventFilterType": {"type": "string", "enum": ["ANY_OFFER_CHANGED", "ORDER_CHANGE"], "description": "An `eventFilterType` value that is supported by the specific `notificationType`. This is used by the subscription service to determine the type of event filter. Refer to [Notification Type Values](https://developer-docs.amazon.com/sp-api/docs/notification-type-values) to determine if an `eventFilterType` is supported."}}, "required": ["eventFilterType"]}]}, "MarketplaceFilter": {"description": "An event filter to customize your subscription to send notifications for only the specified `marketplaceId`s.", "type": "object", "properties": {"marketplaceIds": {"$ref": "#/definitions/MarketplaceIds"}}}, "MarketplaceIds": {"type": "array", "items": {"type": "string"}, "description": "A list of marketplace identifiers to subscribe to (for example: ATVPDKIKX0DER). To receive notifications in every marketplace, do not provide this list."}, "AggregationFilter": {"type": "object", "properties": {"aggregationSettings": {"$ref": "#/definitions/AggregationSettings"}}, "description": "A filter used to select the aggregation time period at which to send notifications (for example: limit to one notification every five minutes for high frequency notifications)."}, "AggregationSettings": {"type": "object", "description": "A container that holds all of the necessary properties to configure the aggregation of notifications.", "properties": {"aggregationTimePeriod": {"$ref": "#/definitions/AggregationTimePeriod", "description": "The supported time period to use to perform marketplace-ASIN level aggregation."}}, "required": ["aggregationTimePeriod"]}, "AggregationTimePeriod": {"description": "The supported aggregation time periods. For example, if FiveMinutes is the value chosen, and 50 price updates occur for an ASIN within 5 minutes, Amazon will send only two notifications; one for the first event, and then a subsequent notification 5 minutes later with the final end state of the data. The 48 interim events will be dropped.", "type": "string", "enum": ["FiveMinutes", "TenMinutes"], "x-docgen-enum-table-extension": [{"value": "FiveMinutes", "description": "An aggregated notification will be sent every five minutes."}, {"value": "TenMinutes", "description": "An aggregated notification will be sent every ten minutes."}]}, "OrderChangeTypeFilter": {"description": "An event filter to customize your subscription to send notifications for only the specified `orderChangeType`.", "type": "object", "properties": {"orderChangeTypes": {"$ref": "#/definitions/OrderChangeTypes"}}}, "OrderChangeTypes": {"type": "array", "items": {"$ref": "#/definitions/OrderChangeTypeEnum"}, "description": "A list of order change types to subscribe to (for example: `BuyerRequestedChange`). To receive notifications of all change types, do not provide this list."}, "OrderChangeTypeEnum": {"type": "string", "enum": ["OrderStatusChange", "BuyerRequestedChange"], "description": "The supported order change type of ORDER_CHANGE notification."}, "Subscription": {"type": "object", "required": ["destinationId", "payloadVersion", "subscriptionId"], "properties": {"subscriptionId": {"type": "string", "description": "The subscription identifier generated when the subscription is created."}, "payloadVersion": {"type": "string", "description": "The version of the payload object to be used in the notification."}, "destinationId": {"type": "string", "description": "The identifier for the destination where notifications will be delivered."}, "processingDirective": {"$ref": "#/definitions/ProcessingDirective"}}, "description": "Information about the subscription."}, "CreateSubscriptionResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `createSubscription` operation.", "$ref": "#/definitions/Subscription"}, "errors": {"description": "One or more unexpected errors occurred during the `createSubscription` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `createSubscription` operation."}, "CreateSubscriptionRequest": {"type": "object", "properties": {"payloadVersion": {"type": "string", "description": "The version of the payload object to be used in the notification."}, "destinationId": {"type": "string", "description": "The identifier for the destination where notifications will be delivered."}, "processingDirective": {"$ref": "#/definitions/ProcessingDirective"}}, "required": ["destinationId", "payloadVersion"], "description": "The request schema for the `createSubscription` operation."}, "GetSubscriptionByIdResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `getSubscriptionById` operation.", "$ref": "#/definitions/Subscription"}, "errors": {"description": "An unexpected condition occurred during the `getSubscriptionById` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `getSubscriptionById` operation."}, "GetSubscriptionResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `getSubscription` operation.", "$ref": "#/definitions/Subscription"}, "errors": {"description": "One or more unexpected errors occurred during the `getSubscription` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `getSubscription` operation."}, "DeleteSubscriptionByIdResponse": {"type": "object", "properties": {"errors": {"description": "An unexpected condition occurred during the `deleteSubscriptionById` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `deleteSubscriptionById` operation."}, "DestinationList": {"type": "array", "description": "A list of destinations.", "items": {"$ref": "#/definitions/Destination"}}, "Destination": {"type": "object", "required": ["destinationId", "name", "resource"], "properties": {"name": {"type": "string", "description": "The developer-defined name for this destination.", "maxLength": 256}, "destinationId": {"type": "string", "description": "The destination identifier generated when you created the destination."}, "resource": {"description": "The resource that will receive notifications associated with this destination.", "$ref": "#/definitions/DestinationResource"}}, "description": "Information about the destination created when you call the `createDestination` operation."}, "DestinationResource": {"type": "object", "properties": {"sqs": {"description": "An Amazon Simple Queue Service (SQS) queue destination.", "$ref": "#/definitions/SqsResource"}, "eventBridge": {"description": "An Amazon EventBridge destination.", "$ref": "#/definitions/EventBridgeResource"}}, "description": "The destination resource types."}, "DestinationResourceSpecification": {"type": "object", "properties": {"sqs": {"description": "The information required to create an Amazon Simple Queue Service (SQS) queue destination.", "$ref": "#/definitions/SqsResource"}, "eventBridge": {"description": "The information required to create an Amazon EventBridge destination.", "$ref": "#/definitions/EventBridgeResourceSpecification"}}, "description": "The information required to create a destination resource. Applications should use one resource type (sqs or eventBridge) per destination."}, "SqsResource": {"type": "object", "required": ["arn"], "properties": {"arn": {"type": "string", "description": "The Amazon Resource Name (ARN) associated with the SQS queue.", "maxLength": 1000, "pattern": "^arn:aws:sqs:\\S+:\\S+:\\S+"}}, "description": "The information required to create an Amazon Simple Queue Service (Amazon SQS) queue destination."}, "EventBridgeResourceSpecification": {"type": "object", "required": ["accountId", "region"], "properties": {"region": {"type": "string", "description": "The AWS region in which you will be receiving the notifications."}, "accountId": {"type": "string", "description": "The identifier for the AWS account that is responsible for charges related to receiving notifications."}}, "description": "The information required to create an Amazon EventBridge destination."}, "EventBridgeResource": {"type": "object", "required": ["accountId", "name", "region"], "properties": {"name": {"type": "string", "description": "The name of the partner event source associated with the destination.", "maxLength": 256}, "region": {"type": "string", "description": "The AWS region in which you receive the notifications. For AWS regions that are supported in Amazon EventBridge, refer to [Amazon EventBridge endpoints and quotas](https://docs.aws.amazon.com/general/latest/gr/ev.html)."}, "accountId": {"type": "string", "description": "The identifier for the AWS account that is responsible for charges related to receiving notifications."}}, "description": "The Amazon EventBridge destination."}, "CreateDestinationRequest": {"type": "object", "required": ["name", "resourceSpecification"], "properties": {"resourceSpecification": {"description": "The information required to create a destination resource. Applications should use one resource type (sqs or eventBridge) per destination.", "$ref": "#/definitions/DestinationResourceSpecification"}, "name": {"type": "string", "description": "A developer-defined name to help identify this destination."}}, "description": "The request schema for the `createDestination` operation."}, "CreateDestinationResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `createDestination` operation.", "$ref": "#/definitions/Destination"}, "errors": {"description": "One or more unexpected errors occurred during the `createDestination` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createDestination operation."}, "GetDestinationResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `getDestination` operation.", "$ref": "#/definitions/Destination"}, "errors": {"description": "One or more unexpected errors occurred during the `getDestination` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `getDestination` operation."}, "GetDestinationsResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `getDestinations` operation.", "$ref": "#/definitions/DestinationList"}, "errors": {"description": "One or more unexpected errors occurred during the `getDestinations` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `getDestinations` operation."}, "DeleteDestinationResponse": {"type": "object", "properties": {"errors": {"description": "One or more unexpected errors occurred during the `deleteDestination` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `deleteDestination` operation."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "An error response returned when the request is unsuccessful."}}, "parameters": {"NotificationType": {"name": "notificationType", "in": "path", "description": "The type of notification.\n\n For more information about notification types, refer to [Notification Type Values](https://developer-docs.amazon.com/sp-api/docs/notification-type-values).", "required": true, "type": "string"}}}