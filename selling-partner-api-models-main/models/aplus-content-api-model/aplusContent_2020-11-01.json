{"swagger": "2.0", "info": {"description": "With the A+ Content API, you can build applications that help selling partners add rich marketing content to their Amazon product detail pages. A+ content helps selling partners share their brand and product story, which helps buyers make informed purchasing decisions. Selling partners assemble content by choosing from content modules and adding images and text.", "version": "2020-11-01", "title": "Selling Partner API for A+ Content Management", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/aplus/2020-11-01/contentDocuments": {"get": {"tags": ["ap<PERSON><PERSON><PERSON><PERSON>"], "description": "Returns a list of all A+ Content documents assigned to a selling partner. This operation returns only the metadata of the A+ Content documents. Call the getContentDocument operation to get the actual contents of the A+ Content documents.\n\n**Usage Plans:**\n\n| Plan type | Rate (requests per second) | Burst |\n| ---- | ---- | ---- |\n|Default| 10 | 10 |\n|Selling partner specific| Variable | Variable |\n\nThe x-amzn-RateLimit-Limit response header returns the usage plan rate limits that were applied to the requested operation. Rate limits for some selling partners will vary from the default rate and burst shown in the table above. For more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "searchContentDocuments", "parameters": [{"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}, {"name": "pageToken", "in": "query", "description": "A page token from the nextPageToken response element returned by your previous call to this operation. nextPageToken is returned when the results of a call exceed the page size. To get the next page of results, call the operation and include pageToken as the only parameter. Specifying pageToken with any other parameter will cause the request to fail. When no nextPageToken value is returned there are no more pages to return. A pageToken value is not usable across different operations.", "required": false, "type": "string", "minLength": 1}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/SearchContentDocumentsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "410": {"description": "The specified resource no longer exists.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "post": {"tags": ["ap<PERSON><PERSON><PERSON><PERSON>"], "description": "Creates a new A+ Content document.\n\n**Usage Plans:**\n\n| Plan type | Rate (requests per second) | Burst |\n| ---- | ---- | ---- |\n|Default| 10 | 10 |\n|Selling partner specific| Variable | Variable |\n\nThe x-amzn-RateLimit-Limit response header returns the usage plan rate limits that were applied to the requested operation. Rate limits for some selling partners will vary from the default rate and burst shown in the table above. For more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "createContentDocument", "parameters": [{"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}, {"in": "body", "description": "The content document request details.", "name": "postContentDocumentRequest", "required": true, "schema": {"$ref": "#/definitions/PostContentDocumentRequest"}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/PostContentDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/aplus/2020-11-01/contentDocuments/{contentReferenceKey}": {"get": {"tags": ["ap<PERSON><PERSON><PERSON><PERSON>"], "description": "Returns an A+ Content document, if available.\n\n**Usage Plans:**\n\n| Plan type | Rate (requests per second) | Burst |\n| ---- | ---- | ---- |\n|Default| 10 | 10 |\n|Selling partner specific| Variable | Variable |\n\nThe x-amzn-RateLimit-Limit response header returns the usage plan rate limits that were applied to the requested operation. Rate limits for some selling partners will vary from the default rate and burst shown in the table above. For more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "getContentDocument", "parameters": [{"name": "contentReferenceKey", "in": "path", "description": "The unique reference key for the A+ Content document. A content reference key cannot form a permalink and may change in the future. A content reference key is not guaranteed to match any A+ Content identifier.", "required": true, "type": "string", "minLength": 1}, {"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}, {"name": "includedDataSet", "in": "query", "description": "The set of A+ Content data types to include in the response.", "required": true, "type": "array", "items": {"type": "string", "enum": ["CONTENTS", "METADATA"], "x-docgen-enum-table-extension": [{"value": "CONTENTS", "description": "The contents of the content document."}, {"value": "METADATA", "description": "The metadata of the content document."}]}, "collectionFormat": "csv", "minItems": 1, "uniqueItems": true}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetContentDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "410": {"description": "The specified resource no longer exists.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "post": {"tags": ["ap<PERSON><PERSON><PERSON><PERSON>"], "description": "Updates an existing A+ Content document.\n\n**Usage Plans:**\n\n| Plan type | Rate (requests per second) | Burst |\n| ---- | ---- | ---- |\n|Default| 10 | 10 |\n|Selling partner specific| Variable | Variable |\n\nThe x-amzn-RateLimit-Limit response header returns the usage plan rate limits that were applied to the requested operation. Rate limits for some selling partners will vary from the default rate and burst shown in the table above. For more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "updateContentDocument", "parameters": [{"name": "contentReferenceKey", "in": "path", "description": "The unique reference key for the A+ Content document. A content reference key cannot form a permalink and may change in the future. A content reference key is not guaranteed to match any A+ Content identifier.", "required": true, "type": "string", "minLength": 1}, {"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}, {"in": "body", "name": "postContentDocumentRequest", "description": "The content document request details.", "required": true, "schema": {"$ref": "#/definitions/PostContentDocumentRequest"}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/PostContentDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "410": {"description": "The specified resource no longer exists.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/aplus/2020-11-01/contentDocuments/{contentReferenceKey}/asins": {"get": {"tags": ["ap<PERSON><PERSON><PERSON><PERSON>"], "description": "Returns a list of ASINs related to the specified A+ Content document, if available. If you do not include the asinSet parameter, the operation returns all ASINs related to the content document.\n\n**Usage Plans:**\n\n| Plan type | Rate (requests per second) | Burst |\n| ---- | ---- | ---- |\n|Default| 10 | 10 |\n|Selling partner specific| Variable | Variable |\n\nThe x-amzn-RateLimit-Limit response header returns the usage plan rate limits that were applied to the requested operation. Rate limits for some selling partners will vary from the default rate and burst shown in the table above. For more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "listContentDocumentAsinRelations", "parameters": [{"name": "contentReferenceKey", "in": "path", "description": "The unique reference key for the A+ Content document. A content reference key cannot form a permalink and may change in the future. A content reference key is not guaranteed to match any A+ Content identifier.", "required": true, "type": "string", "minLength": 1}, {"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}, {"name": "includedDataSet", "in": "query", "description": "The set of A+ Content data types to include in the response. If you do not include this parameter, the operation returns the related ASINs without metadata.", "required": false, "type": "array", "items": {"type": "string", "enum": ["METADATA"], "x-docgen-enum-table-extension": [{"value": "METADATA", "description": "The metadata of the content document."}]}, "collectionFormat": "csv", "minItems": 0, "uniqueItems": true}, {"name": "asinSet", "in": "query", "description": "The set of ASINs.", "required": false, "type": "array", "items": {"type": "string", "minLength": 10}, "collectionFormat": "csv", "uniqueItems": true}, {"name": "pageToken", "in": "query", "description": "A page token from the nextPageToken response element returned by your previous call to this operation. nextPageToken is returned when the results of a call exceed the page size. To get the next page of results, call the operation and include pageToken as the only parameter. Specifying pageToken with any other parameter will cause the request to fail. When no nextPageToken value is returned there are no more pages to return. A pageToken value is not usable across different operations.", "required": false, "type": "string", "minLength": 1}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/ListContentDocumentAsinRelationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "410": {"description": "The specified resource no longer exists.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "post": {"tags": ["ap<PERSON><PERSON><PERSON><PERSON>"], "description": "Replaces all ASINs related to the specified A+ Content document, if available. This may add or remove ASINs, depending on the current set of related ASINs. Removing an ASIN has the side effect of suspending the content document from that ASIN.\n\n**Usage Plans:**\n\n| Plan type | Rate (requests per second) | Burst |\n| ---- | ---- | ---- |\n|Default| 10 | 10 |\n|Selling partner specific| Variable | Variable |\n\nThe x-amzn-RateLimit-Limit response header returns the usage plan rate limits that were applied to the requested operation. Rate limits for some selling partners will vary from the default rate and burst shown in the table above. For more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "postContentDocumentAsinRelations", "parameters": [{"name": "contentReferenceKey", "in": "path", "description": "The unique reference key for the A+ Content document. A content reference key cannot form a permalink and may change in the future. A content reference key is not guaranteed to match any A+ content identifier.", "required": true, "type": "string", "minLength": 1}, {"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}, {"in": "body", "name": "postContentDocumentAsinRelationsRequest", "description": "The content document ASIN relations request details.", "required": true, "schema": {"$ref": "#/definitions/PostContentDocumentAsinRelationsRequest"}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/PostContentDocumentAsinRelationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "410": {"description": "The specified resource no longer exists.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/aplus/2020-11-01/contentAsinValidations": {"post": {"tags": ["ap<PERSON><PERSON><PERSON><PERSON>"], "description": "Checks if the A+ Content document is valid for use on a set of ASINs.\n\n**Usage Plans:**\n\n| Plan type | Rate (requests per second) | Burst |\n| ---- | ---- | ---- |\n|Default| 10 | 10 |\n|Selling partner specific| Variable | Variable |\n\nThe x-amzn-RateLimit-Limit response header returns the usage plan rate limits that were applied to the requested operation. Rate limits for some selling partners will vary from the default rate and burst shown in the table above. For more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "validateContentDocumentAsinRelations", "parameters": [{"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}, {"name": "asinSet", "in": "query", "description": "The set of ASINs.", "required": false, "type": "array", "items": {"type": "string", "minLength": 10}, "collectionFormat": "csv", "uniqueItems": true}, {"in": "body", "name": "postContentDocumentRequest", "description": "The content document request details.", "required": true, "schema": {"$ref": "#/definitions/PostContentDocumentRequest"}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/ValidateContentDocumentAsinRelationsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/aplus/2020-11-01/contentPublishRecords": {"get": {"tags": ["ap<PERSON><PERSON><PERSON><PERSON>"], "description": "Searches for A+ Content publishing records, if available.\n\n**Usage Plans:**\n\n| Plan type | Rate (requests per second) | Burst |\n| ---- | ---- | ---- |\n|Default| 10 | 10 |\n|Selling partner specific| Variable | Variable |\n\nThe x-amzn-RateLimit-Limit response header returns the usage plan rate limits that were applied to the requested operation. Rate limits for some selling partners will vary from the default rate and burst shown in the table above. For more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "searchContentPublishRecords", "parameters": [{"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}, {"name": "asin", "in": "query", "description": "The Amazon Standard Identification Number (ASIN).", "required": true, "type": "string", "minLength": 10}, {"name": "pageToken", "in": "query", "description": "A page token from the nextPageToken response element returned by your previous call to this operation. nextPageToken is returned when the results of a call exceed the page size. To get the next page of results, call the operation and include pageToken as the only parameter. Specifying pageToken with any other parameter will cause the request to fail. When no nextPageToken value is returned there are no more pages to return. A pageToken value is not usable across different operations.", "required": false, "type": "string", "minLength": 1}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/SearchContentPublishRecordsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/aplus/2020-11-01/contentDocuments/{contentReferenceKey}/approvalSubmissions": {"post": {"tags": ["ap<PERSON><PERSON><PERSON><PERSON>"], "description": "Submits an A+ Content document for review, approval, and publishing.\n\n**Usage Plans:**\n\n| Plan type | Rate (requests per second) | Burst |\n| ---- | ---- | ---- |\n|Default| 10 | 10 |\n|Selling partner specific| Variable | Variable |\n\nThe x-amzn-RateLimit-Limit response header returns the usage plan rate limits that were applied to the requested operation. Rate limits for some selling partners will vary from the default rate and burst shown in the table above. For more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "postContentDocumentApprovalSubmission", "parameters": [{"name": "contentReferenceKey", "in": "path", "description": "The unique reference key for the A+ Content document. A content reference key cannot form a permalink and may change in the future. A content reference key is not guaranteed to match any A+ content identifier.", "required": true, "type": "string", "minLength": 1}, {"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/PostContentDocumentApprovalSubmissionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "410": {"description": "The specified resource no longer exists.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/aplus/2020-11-01/contentDocuments/{contentReferenceKey}/suspendSubmissions": {"post": {"tags": ["ap<PERSON><PERSON><PERSON><PERSON>"], "description": "Submits a request to suspend visible A+ Content. This neither deletes the content document nor the ASIN relations.\n\n**Usage Plans:**\n\n| Plan type | Rate (requests per second) | Burst |\n| ---- | ---- | ---- |\n|Default| 10 | 10 |\n|Selling partner specific| Variable | Variable |\n\nThe x-amzn-RateLimit-Limit response header returns the usage plan rate limits that were applied to the requested operation. Rate limits for some selling partners will vary from the default rate and burst shown in the table above. For more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "postContentDocumentSuspendSubmission", "parameters": [{"name": "contentReferenceKey", "in": "path", "description": "The unique reference key for the A+ Content document. A content reference key cannot form a permalink and may change in the future. A content reference key is not guaranteed to match any A+ content identifier.", "required": true, "type": "string", "minLength": 1}, {"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/PostContentDocumentSuspendSubmissionResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "410": {"description": "The specified resource no longer exists.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}}, "definitions": {"AplusResponse": {"type": "object", "properties": {"warnings": {"$ref": "#/definitions/MessageSet"}}, "description": "The base response data for all A+ Content operations when a request is successful or partially successful. Individual operations may extend this with additional data."}, "AplusPaginatedResponse": {"allOf": [{"$ref": "#/definitions/AplusResponse"}, {"type": "object", "properties": {"nextPageToken": {"$ref": "#/definitions/PageToken"}}}], "description": "The base response data for paginated A+ Content operations. Individual operations may extend this with additional data. If nextPageToken is not returned, there are no more pages to return."}, "ErrorList": {"type": "object", "required": ["errors"], "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/Error"}, "description": "A list of error responses returned when a request is unsuccessful."}}, "description": "The error response for when a request is unsuccessful."}, "MessageSet": {"type": "array", "description": "A set of messages to the user, such as warnings or comments.", "items": {"$ref": "#/definitions/Error"}, "uniqueItems": true}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "The code that identifies the type of error condition.", "minLength": 1}, "message": {"type": "string", "description": "A human readable description of the error condition.", "minLength": 1}, "details": {"type": "string", "description": "Additional information, if available, to clarify the error condition.", "minLength": 1}}, "description": "Error response returned when the request is unsuccessful."}, "ContentMetadataRecordList": {"type": "array", "description": "A list of A+ Content metadata records.", "items": {"$ref": "#/definitions/ContentMetadataRecord"}, "uniqueItems": false}, "ContentMetadataRecord": {"type": "object", "required": ["contentMetadata", "contentReferenceKey"], "properties": {"contentReferenceKey": {"$ref": "#/definitions/ContentReferenceKey"}, "contentMetadata": {"$ref": "#/definitions/ContentMetadata"}}, "description": "The metadata for an A+ Content document, with additional information for content management."}, "ContentMetadata": {"type": "object", "required": ["badgeSet", "marketplaceId", "name", "status", "updateTime"], "properties": {"name": {"type": "string", "description": "The A+ Content document name.", "minLength": 1, "maxLength": 100}, "marketplaceId": {"$ref": "#/definitions/MarketplaceId"}, "status": {"$ref": "#/definitions/ContentStatus"}, "badgeSet": {"$ref": "#/definitions/ContentBadgeSet"}, "updateTime": {"type": "string", "format": "date-time", "description": "The approximate age of the A+ Content document and metadata."}}, "description": "The metadata of an A+ Content document."}, "ContentType": {"type": "string", "description": "The A+ Content document type.", "enum": ["EBC", "EMC"], "x-docgen-enum-table-extension": [{"value": "EBC", "description": "A+ Content published through the A+ Content Manager in Seller Central."}, {"value": "EMC", "description": "A+ Content published through the A+ Content Manager in Vendor Central."}]}, "ContentSubType": {"type": "string", "minLength": 1, "description": "The A+ Content document subtype. This represents a special-purpose type of an A+ Content document. Not every A+ Content document type will have a subtype, and subtypes may change at any time."}, "ContentStatus": {"type": "string", "description": "The submission status of the content document.", "enum": ["APPROVED", "DRAFT", "REJECTED", "SUBMITTED"], "x-docgen-enum-table-extension": [{"value": "APPROVED", "description": "The content is approved and will be published to applied ASINs."}, {"value": "DRAFT", "description": "The content has not yet been submitted for approval."}, {"value": "REJECTED", "description": "The content has been rejected in moderation and needs to be revised and resubmitted based on the rejection reasons provided."}, {"value": "SUBMITTED", "description": "The content has been submitted for approval and is currently waiting for moderation."}]}, "ContentBadgeSet": {"type": "array", "description": "The set of content badges.", "items": {"$ref": "#/definitions/ContentBadge"}, "uniqueItems": true}, "ContentBadge": {"type": "string", "description": "A flag that provides additional information about an A+ Content document.", "enum": ["BULK", "GENERATED", "LAUNCHPAD", "PREMIUM", "STANDARD"], "x-docgen-enum-table-extension": [{"value": "BULK", "description": "This content is applied to ASINs in bulk."}, {"value": "GENERATED", "description": "This content is generated by an automated process. If any user modifies this content, it will lose the GENERATED badge."}, {"value": "LAUNCHPAD", "description": "Launchpad content."}, {"value": "PREMIUM", "description": "Premium content"}, {"value": "STANDARD", "description": "Standard content."}]}, "AsinBadgeSet": {"type": "array", "description": "The set of ASIN badges.", "items": {"$ref": "#/definitions/AsinBadge"}, "uniqueItems": true}, "AsinBadge": {"type": "string", "description": "A flag that provides additional information about an ASIN. This is contextual and may change depending on the request that generated it.", "enum": ["BRAND_NOT_ELIGIBLE", "CATALOG_NOT_FOUND", "CONTENT_NOT_PUBLISHED", "CONTENT_PUBLISHED"], "x-docgen-enum-table-extension": [{"value": "BRAND_NOT_ELIGIBLE", "description": "This ASIN is not part of the current user's brand. If the current user corrects their brand registration to include this ASIN, it will lose the `BrandNotEligible` badge."}, {"value": "CATALOG_NOT_FOUND", "description": "This ASIN was not found in the Amazon catalog. If any user creates or restores this ASIN, it will lose the `CatalogNotFound` badge."}, {"value": "CONTENT_NOT_PUBLISHED", "description": "This ASIN does not have the specified A+ Content published to it. If the current user publishes the specified content for this ASIN, it will lose the `ContentNotPublished` badge."}, {"value": "CONTENT_PUBLISHED", "description": "This ASIN has the specified A+ Content published to it. If the current user suspends the specified content for this ASIN, it will lose the `ContentPublished` badge."}]}, "MarketplaceId": {"type": "string", "minLength": 1, "description": "The identifier for the marketplace where the A+ Content is published."}, "LanguageTag": {"type": "string", "minLength": 5, "description": "The IETF language tag. This only supports the primary language subtag with one secondary language subtag. The secondary language subtag is almost always a regional designation. This does not support additional subtags beyond the primary and secondary subtags.\n**Pattern:** ^[a-z]{2,}-[A-Z0-9]{2,}$"}, "AsinSet": {"type": "array", "description": "The set of ASINs.", "items": {"$ref": "#/definitions/Asin"}, "uniqueItems": true}, "Asin": {"type": "string", "minLength": 10, "description": "The Amazon Standard Identification Number (ASIN)."}, "AsinMetadataSet": {"type": "array", "description": "The set of ASIN metadata.", "items": {"$ref": "#/definitions/AsinMetadata"}, "uniqueItems": true}, "AsinMetadata": {"type": "object", "required": ["asin"], "properties": {"asin": {"$ref": "#/definitions/Asin"}, "badgeSet": {"$ref": "#/definitions/AsinBadgeSet"}, "parent": {"$ref": "#/definitions/Asin"}, "title": {"type": "string", "description": "The title for the ASIN in the Amazon catalog.", "minLength": 1}, "imageUrl": {"type": "string", "description": "The default image for the ASIN in the Amazon catalog.", "minLength": 1}, "contentReferenceKeySet": {"$ref": "#/definitions/ContentReferenceKeySet"}}, "description": "The A+ Content ASIN with additional metadata for content management. If you don't include the `includedDataSet` parameter in a call to the listContentDocumentAsinRelations operation, the related ASINs are returned without metadata."}, "PublishRecordList": {"type": "array", "description": "A list of A+ Content publishing records.", "items": {"$ref": "#/definitions/PublishRecord"}, "uniqueItems": false}, "PublishRecord": {"type": "object", "required": ["asin", "contentReferenceKey", "contentType", "locale", "marketplaceId"], "properties": {"marketplaceId": {"$ref": "#/definitions/MarketplaceId"}, "locale": {"$ref": "#/definitions/LanguageTag"}, "asin": {"$ref": "#/definitions/Asin"}, "contentType": {"$ref": "#/definitions/ContentType"}, "contentSubType": {"$ref": "#/definitions/ContentSubType"}, "contentReferenceKey": {"$ref": "#/definitions/ContentReferenceKey"}}, "description": "The full context for an A+ Content publishing event."}, "ContentReferenceKeySet": {"type": "array", "description": "A set of content reference keys.", "items": {"$ref": "#/definitions/ContentReferenceKey"}, "uniqueItems": true}, "ContentReferenceKey": {"type": "string", "minLength": 1, "description": "A unique reference key for the A+ Content document. A content reference key cannot form a permalink and may change in the future. A content reference key is not guaranteed to match any A+ content identifier."}, "PageToken": {"type": "string", "minLength": 1, "description": "A page token that is returned when the results of the call exceed the page size. To get another page of results, call the operation again, passing in this value with the pageToken parameter."}, "ImageCropSpecification": {"type": "object", "required": ["size"], "properties": {"size": {"$ref": "#/definitions/ImageDimensions"}, "offset": {"$ref": "#/definitions/ImageOffsets"}}, "description": "The instructions for optionally cropping an image. If no cropping is desired, set the dimensions to the original image size. If the image is cropped and no offset values are provided, then the coordinates of the top left corner of the cropped image, relative to the original image, are defaulted to (0,0)."}, "ImageDimensions": {"type": "object", "required": ["height", "width"], "properties": {"width": {"$ref": "#/definitions/IntegerWithUnits"}, "height": {"$ref": "#/definitions/IntegerWithUnits"}}, "description": "The dimensions extending from the top left corner of the cropped image, or the top left corner of the original image if there is no cropping. Only `pixels` is allowed as the units value for ImageDimensions."}, "ImageOffsets": {"type": "object", "required": ["x", "y"], "properties": {"x": {"$ref": "#/definitions/IntegerWithUnits"}, "y": {"$ref": "#/definitions/IntegerWithUnits"}}, "description": "The top left corner of the cropped image, specified in the original image's coordinate space."}, "IntegerWithUnits": {"type": "object", "required": ["units", "value"], "properties": {"value": {"type": "integer", "description": "The dimension value."}, "units": {"type": "string", "description": "The unit of measurement."}}, "description": "A whole number dimension and its unit of measurement. For example, this can represent 100 pixels."}, "ContentRecord": {"type": "object", "description": "A content document with additional information for content management.", "required": ["contentReferenceKey"], "properties": {"contentReferenceKey": {"$ref": "#/definitions/ContentReferenceKey"}, "contentMetadata": {"$ref": "#/definitions/ContentMetadata"}, "contentDocument": {"$ref": "#/definitions/ContentDocument"}}}, "ContentDocument": {"type": "object", "required": ["contentModuleList", "contentType", "locale", "name"], "properties": {"name": {"type": "string", "description": "The A+ Content document name.", "minLength": 1, "maxLength": 100}, "contentType": {"$ref": "#/definitions/ContentType"}, "contentSubType": {"$ref": "#/definitions/ContentSubType"}, "locale": {"$ref": "#/definitions/LanguageTag"}, "contentModuleList": {"$ref": "#/definitions/ContentModuleList"}}, "description": "The A+ Content document. This is the enhanced content that is published to product detail pages."}, "ContentModuleList": {"type": "array", "description": "A list of A+ Content modules.", "items": {"$ref": "#/definitions/ContentModule"}, "uniqueItems": false, "minItems": 1, "maxItems": 100}, "ContentModule": {"type": "object", "required": ["contentModuleType"], "properties": {"contentModuleType": {"$ref": "#/definitions/ContentModuleType"}, "standardCompanyLogo": {"$ref": "#/definitions/StandardCompanyLogoModule"}, "standardComparisonTable": {"$ref": "#/definitions/StandardComparisonTableModule"}, "standardFourImageText": {"$ref": "#/definitions/StandardFourImageTextModule"}, "standardFourImageTextQuadrant": {"$ref": "#/definitions/StandardFourImageTextQuadrantModule"}, "standardHeaderImageText": {"$ref": "#/definitions/StandardHeaderImageTextModule"}, "standardImageSidebar": {"$ref": "#/definitions/StandardImageSidebarModule"}, "standardImageTextOverlay": {"$ref": "#/definitions/StandardImageTextOverlayModule"}, "standardMultipleImageText": {"$ref": "#/definitions/StandardMultipleImageTextModule"}, "standardProductDescription": {"$ref": "#/definitions/StandardProductDescriptionModule"}, "standardSingleImageHighlights": {"$ref": "#/definitions/StandardSingleImageHighlightsModule"}, "standardSingleImageSpecsDetail": {"$ref": "#/definitions/StandardSingleImageSpecsDetailModule"}, "standardSingleSideImage": {"$ref": "#/definitions/StandardSingleSideImageModule"}, "standardTechSpecs": {"$ref": "#/definitions/StandardTechSpecsModule"}, "standardText": {"$ref": "#/definitions/StandardTextModule"}, "standardThreeImageText": {"$ref": "#/definitions/StandardThreeImageTextModule"}}, "description": "An A+ Content module. An A+ Content document is composed of content modules. The contentModuleType property selects which content module types to use."}, "ContentModuleType": {"type": "string", "description": "The type of A+ Content module.", "enum": ["STANDARD_COMPANY_LOGO", "STANDARD_COMPARISON_TABLE", "STANDARD_FOUR_IMAGE_TEXT", "STANDARD_FOUR_IMAGE_TEXT_QUADRANT", "STANDARD_HEADER_IMAGE_TEXT", "STANDARD_IMAGE_SIDEBAR", "STANDARD_IMAGE_TEXT_OVERLAY", "STANDARD_MULTIPLE_IMAGE_TEXT", "STANDARD_PRODUCT_DESCRIPTION", "STANDARD_SINGLE_IMAGE_HIGHLIGHTS", "STANDARD_SINGLE_IMAGE_SPECS_DETAIL", "STANDARD_SINGLE_SIDE_IMAGE", "STANDARD_TECH_SPECS", "STANDARD_TEXT", "STANDARD_THREE_IMAGE_TEXT"], "x-docgen-enum-table-extension": [{"value": "STANDARD_COMPANY_LOGO", "description": "The standard company logo image."}, {"value": "STANDARD_COMPARISON_TABLE", "description": "The standard product comparison table or chart."}, {"value": "STANDARD_FOUR_IMAGE_TEXT", "description": "Four standard images with text, presented across a single row."}, {"value": "STANDARD_FOUR_IMAGE_TEXT_QUADRANT", "description": "Four standard images with text, presented on a grid of four quadrants."}, {"value": "STANDARD_HEADER_IMAGE_TEXT", "description": "Standard headline text, an image, and body text."}, {"value": "STANDARD_IMAGE_SIDEBAR", "description": "Two images, two paragraphs, and two bulleted lists. One image is smaller and is displayed in the sidebar."}, {"value": "STANDARD_IMAGE_TEXT_OVERLAY", "description": "A standard background image with a floating text box."}, {"value": "STANDARD_MULTIPLE_IMAGE_TEXT", "description": "Standard images with text, presented one at a time. The user clicks on thumbnails to view each block."}, {"value": "STANDARD_PRODUCT_DESCRIPTION", "description": "Standard product description text."}, {"value": "STANDARD_SINGLE_IMAGE_HIGHLIGHTS", "description": "A standard image with several paragraphs and a bulleted list."}, {"value": "STANDARD_SINGLE_IMAGE_SPECS_DETAIL", "description": "A standard image with paragraphs and a bulleted list, and extra space for technical details."}, {"value": "STANDARD_SINGLE_SIDE_IMAGE", "description": "A standard headline and body text with an image on the side."}, {"value": "STANDARD_TECH_SPECS", "description": "The standard table of technical feature names and definitions."}, {"value": "STANDARD_TEXT", "description": "Standard headline and body text."}, {"value": "STANDARD_THREE_IMAGE_TEXT", "description": "Three standard images with text, presented across one row."}]}, "StandardCompanyLogoModule": {"type": "object", "required": ["companyLogo"], "properties": {"companyLogo": {"$ref": "#/definitions/ImageComponent"}}, "description": "The standard company logo image."}, "StandardComparisonTableModule": {"type": "object", "properties": {"productColumns": {"type": "array", "items": {"$ref": "#/definitions/StandardComparisonProductBlock"}, "maxItems": 6, "minItems": 0}, "metricRowLabels": {"type": "array", "items": {"$ref": "#/definitions/PlainTextItem"}, "maxItems": 10, "minItems": 0}}, "description": "The standard product comparison table."}, "StandardFourImageTextModule": {"type": "object", "properties": {"headline": {"$ref": "#/definitions/TextComponent"}, "block1": {"$ref": "#/definitions/StandardImageTextBlock"}, "block2": {"$ref": "#/definitions/StandardImageTextBlock"}, "block3": {"$ref": "#/definitions/StandardImageTextBlock"}, "block4": {"$ref": "#/definitions/StandardImageTextBlock"}}, "description": "Four standard images with text, presented across a single row."}, "StandardFourImageTextQuadrantModule": {"type": "object", "required": ["block1", "block2", "block3", "block4"], "properties": {"block1": {"$ref": "#/definitions/StandardImageTextBlock"}, "block2": {"$ref": "#/definitions/StandardImageTextBlock"}, "block3": {"$ref": "#/definitions/StandardImageTextBlock"}, "block4": {"$ref": "#/definitions/StandardImageTextBlock"}}, "description": "Four standard images with text, presented on a grid of four quadrants."}, "StandardHeaderImageTextModule": {"type": "object", "properties": {"headline": {"$ref": "#/definitions/TextComponent"}, "block": {"$ref": "#/definitions/StandardImageTextBlock"}}, "description": "Standard headline text, an image, and body text."}, "StandardImageSidebarModule": {"type": "object", "properties": {"headline": {"$ref": "#/definitions/TextComponent"}, "imageCaptionBlock": {"$ref": "#/definitions/StandardImageCaptionBlock"}, "descriptionTextBlock": {"$ref": "#/definitions/StandardTextBlock"}, "descriptionListBlock": {"$ref": "#/definitions/StandardTextListBlock"}, "sidebarImageTextBlock": {"$ref": "#/definitions/StandardImageTextBlock"}, "sidebarListBlock": {"$ref": "#/definitions/StandardTextListBlock"}}, "description": "Two images, two paragraphs, and two bulleted lists. One image is smaller and displayed in the sidebar."}, "StandardImageTextOverlayModule": {"type": "object", "required": ["overlayColorType"], "properties": {"overlayColorType": {"$ref": "#/definitions/ColorType"}, "block": {"$ref": "#/definitions/StandardImageTextBlock"}}, "description": "A standard background image with a floating text box."}, "StandardMultipleImageTextModule": {"type": "object", "properties": {"blocks": {"type": "array", "items": {"$ref": "#/definitions/StandardImageTextCaptionBlock"}}}, "description": "Standard images with text, presented one at a time. The user clicks on thumbnails to view each block."}, "StandardProductDescriptionModule": {"type": "object", "required": ["body"], "properties": {"body": {"$ref": "#/definitions/ParagraphComponent"}}, "description": "Standard product description text."}, "StandardSingleImageHighlightsModule": {"type": "object", "properties": {"image": {"$ref": "#/definitions/ImageComponent"}, "headline": {"$ref": "#/definitions/TextComponent"}, "textBlock1": {"$ref": "#/definitions/StandardTextBlock"}, "textBlock2": {"$ref": "#/definitions/StandardTextBlock"}, "textBlock3": {"$ref": "#/definitions/StandardTextBlock"}, "bulletedListBlock": {"$ref": "#/definitions/StandardHeaderTextListBlock"}}, "description": "A standard image with several paragraphs and a bulleted list."}, "StandardSingleImageSpecsDetailModule": {"type": "object", "properties": {"headline": {"$ref": "#/definitions/TextComponent"}, "image": {"$ref": "#/definitions/ImageComponent"}, "descriptionHeadline": {"$ref": "#/definitions/TextComponent"}, "descriptionBlock1": {"$ref": "#/definitions/StandardTextBlock"}, "descriptionBlock2": {"$ref": "#/definitions/StandardTextBlock"}, "specificationHeadline": {"$ref": "#/definitions/TextComponent"}, "specificationListBlock": {"$ref": "#/definitions/StandardHeaderTextListBlock"}, "specificationTextBlock": {"$ref": "#/definitions/StandardTextBlock"}}, "description": "A standard image with paragraphs and a bulleted list, and extra space for technical details."}, "StandardSingleSideImageModule": {"type": "object", "required": ["imagePositionType"], "properties": {"imagePositionType": {"$ref": "#/definitions/PositionType"}, "block": {"$ref": "#/definitions/StandardImageTextBlock"}}, "description": "A standard headline and body text with an image on the side."}, "StandardTechSpecsModule": {"type": "object", "required": ["specificationList"], "properties": {"headline": {"$ref": "#/definitions/TextComponent"}, "specificationList": {"type": "array", "items": {"$ref": "#/definitions/StandardTextPairBlock"}, "description": "The specification list.", "maxItems": 16, "minItems": 4}, "tableCount": {"type": "integer", "description": "The number of tables to present. Features are evenly divided between the tables.", "minimum": 1, "maximum": 2}}, "description": "The standard table of technical feature names and definitions."}, "StandardTextModule": {"type": "object", "required": ["body"], "properties": {"headline": {"$ref": "#/definitions/TextComponent"}, "body": {"$ref": "#/definitions/ParagraphComponent"}}, "description": "A standard headline and body text."}, "StandardThreeImageTextModule": {"type": "object", "properties": {"headline": {"$ref": "#/definitions/TextComponent"}, "block1": {"$ref": "#/definitions/StandardImageTextBlock"}, "block2": {"$ref": "#/definitions/StandardImageTextBlock"}, "block3": {"$ref": "#/definitions/StandardImageTextBlock"}}, "description": "Three standard images with text, presented across a single row."}, "StandardComparisonProductBlock": {"type": "object", "required": ["position"], "properties": {"position": {"type": "integer", "description": "The rank or index of this comparison product block within the module. Different blocks cannot occupy the same position within a single module.", "minimum": 1, "maximum": 6}, "image": {"$ref": "#/definitions/ImageComponent"}, "title": {"type": "string", "description": "The comparison product title.", "minLength": 1, "maxLength": 80}, "asin": {"$ref": "#/definitions/Asin"}, "highlight": {"type": "boolean", "description": "Determines whether this block of content is visually highlighted."}, "metrics": {"type": "array", "description": "Comparison metrics for the product.", "items": {"$ref": "#/definitions/PlainTextItem"}, "maxItems": 10, "minItems": 0}}, "description": "The A+ Content standard comparison product block."}, "StandardHeaderTextListBlock": {"type": "object", "properties": {"headline": {"$ref": "#/definitions/TextComponent"}, "block": {"$ref": "#/definitions/StandardTextListBlock"}}, "description": "The A+ standard fixed-length list of text, with a related headline."}, "StandardTextListBlock": {"type": "object", "required": ["textList"], "properties": {"textList": {"type": "array", "items": {"$ref": "#/definitions/TextItem"}, "maxItems": 8, "minItems": 0}}, "description": "The A+ Content standard fixed length list of text, usually presented as bullet points."}, "StandardImageTextCaptionBlock": {"type": "object", "properties": {"block": {"$ref": "#/definitions/StandardImageTextBlock"}, "caption": {"$ref": "#/definitions/TextComponent"}}, "description": "The A+ Content standard image and text block, with a related caption. The caption may not display on all devices."}, "StandardImageCaptionBlock": {"type": "object", "properties": {"image": {"$ref": "#/definitions/ImageComponent"}, "caption": {"$ref": "#/definitions/TextComponent"}}, "description": "The A+ Content standard image and caption block."}, "StandardImageTextBlock": {"type": "object", "properties": {"image": {"$ref": "#/definitions/ImageComponent"}, "headline": {"$ref": "#/definitions/TextComponent"}, "body": {"$ref": "#/definitions/ParagraphComponent"}}, "description": "The A+ Content standard image and text box block."}, "StandardTextBlock": {"type": "object", "properties": {"headline": {"$ref": "#/definitions/TextComponent"}, "body": {"$ref": "#/definitions/ParagraphComponent"}}, "description": "The A+ Content standard text box block, comprised of a paragraph with a headline."}, "StandardTextPairBlock": {"type": "object", "properties": {"label": {"$ref": "#/definitions/TextComponent"}, "description": {"$ref": "#/definitions/TextComponent"}}, "description": "The A+ Content standard label and description block, comprised of a pair of text components."}, "TextItem": {"type": "object", "required": ["position", "text"], "properties": {"position": {"type": "integer", "description": "The rank or index of this text item within the collection. Different items cannot occupy the same position within a single collection.", "minimum": 1, "maximum": 100}, "text": {"$ref": "#/definitions/TextComponent"}}, "description": "Rich positional text, usually presented as a collection of bullet points."}, "PlainTextItem": {"type": "object", "required": ["position", "value"], "properties": {"position": {"type": "integer", "description": "The rank or index of this text item within the collection. Different items cannot occupy the same position within a single collection.", "minimum": 1, "maximum": 100}, "value": {"type": "string", "description": "The actual plain text.", "minLength": 1, "maxLength": 250}}, "description": "Plain positional text, used in collections of brief labels and descriptors."}, "ImageComponent": {"type": "object", "required": ["altText", "imageCropSpecification", "uploadDestinationId"], "properties": {"uploadDestinationId": {"type": "string", "description": "This identifier is provided by the Selling Partner API for Uploads.", "minLength": 1}, "imageCropSpecification": {"$ref": "#/definitions/ImageCropSpecification"}, "altText": {"type": "string", "description": "The alternative text for the image.", "minLength": 1, "maxLength": 100}}, "description": "A reference to an image, hosted in the A+ Content media library."}, "ParagraphComponent": {"type": "object", "required": ["textList"], "properties": {"textList": {"type": "array", "items": {"$ref": "#/definitions/TextComponent"}, "maxItems": 100, "minItems": 1}}, "description": "A list of rich text content, usually presented in a text box."}, "TextComponent": {"type": "object", "required": ["value"], "properties": {"value": {"type": "string", "description": "The actual plain text.", "minLength": 1, "maxLength": 10000}, "decoratorSet": {"$ref": "#/definitions/DecoratorSet"}}, "description": "Rich text content."}, "ColorType": {"type": "string", "description": "The relative color scheme of content.", "enum": ["DARK", "LIGHT"], "x-docgen-enum-table-extension": [{"value": "DARK", "description": "Dark grey, semi-opaque shaded background for light text overlay box."}, {"value": "LIGHT", "description": "White, semi-opaque shaded background for dark text overlay box."}]}, "PositionType": {"type": "string", "description": "The relative positioning of content.", "enum": ["LEFT", "RIGHT"], "x-docgen-enum-table-extension": [{"value": "LEFT", "description": "Indicates that the content is to be positioned on the left side of the module."}, {"value": "RIGHT", "description": "Indicates that the content is to be positioned on the right side of the module."}]}, "DecoratorSet": {"type": "array", "description": "A set of content decorators.", "items": {"$ref": "#/definitions/Decorator"}, "uniqueItems": true}, "Decorator": {"type": "object", "properties": {"type": {"$ref": "#/definitions/DecoratorType"}, "offset": {"type": "integer", "description": "The starting character of this decorator within the content string. Use zero for the first character.", "minimum": 0, "maximum": 10000}, "length": {"type": "integer", "description": "The number of content characters to alter with this decorator. Decorators such as line breaks can have zero length and fit between characters.", "minimum": 0, "maximum": 10000}, "depth": {"type": "integer", "description": "The relative intensity or variation of this decorator. Decorators such as bullet-points, for example, can have multiple indentation depths.", "minimum": 0, "maximum": 100}}, "description": "A decorator applied to a content string value in order to create rich text."}, "DecoratorType": {"type": "string", "description": "The type of rich text decorator.", "enum": ["LIST_ITEM", "LIST_ORDERED", "LIST_UNORDERED", "STYLE_BOLD", "STYLE_ITALIC", "STYLE_LINEBREAK", "STYLE_PARAGRAPH", "STYLE_UNDERLINE"], "x-docgen-enum-table-extension": [{"value": "LIST_ITEM", "description": "Formatted list item, used in either numbered or bulleted lists, inside the list enclosure."}, {"value": "LIST_ORDERED", "description": "Numbered list enclosure."}, {"value": "LIST_UNORDERED", "description": "Bulleted list enclosure."}, {"value": "STYLE_BOLD", "description": "Bold text formatting."}, {"value": "STYLE_ITALIC", "description": "Italic text formatting."}, {"value": "STYLE_LINEBREAK", "description": "New line of text."}, {"value": "STYLE_PARAGRAPH", "description": "Paragraph text formatting."}, {"value": "STYLE_UNDERLINE", "description": "Underline text formatting."}]}, "SearchContentDocumentsResponse": {"allOf": [{"$ref": "#/definitions/AplusPaginatedResponse"}, {"type": "object", "required": ["contentMetadataRecords"], "properties": {"contentMetadataRecords": {"$ref": "#/definitions/ContentMetadataRecordList", "description": "The content metadata records."}}}]}, "GetContentDocumentResponse": {"allOf": [{"$ref": "#/definitions/AplusResponse"}, {"type": "object", "required": ["contentRecord"], "properties": {"contentRecord": {"$ref": "#/definitions/ContentRecord"}}}]}, "PostContentDocumentRequest": {"type": "object", "required": ["contentDocument"], "properties": {"contentDocument": {"$ref": "#/definitions/ContentDocument"}}}, "PostContentDocumentResponse": {"allOf": [{"$ref": "#/definitions/AplusResponse"}, {"type": "object", "required": ["contentReferenceKey"], "properties": {"contentReferenceKey": {"$ref": "#/definitions/ContentReferenceKey"}}}]}, "ListContentDocumentAsinRelationsResponse": {"allOf": [{"$ref": "#/definitions/AplusPaginatedResponse"}, {"type": "object", "required": ["asinMetadataSet"], "properties": {"asinMetadataSet": {"$ref": "#/definitions/AsinMetadataSet"}}}]}, "PostContentDocumentAsinRelationsRequest": {"type": "object", "required": ["asinSet"], "properties": {"asinSet": {"$ref": "#/definitions/AsinSet"}}}, "PostContentDocumentAsinRelationsResponse": {"allOf": [{"$ref": "#/definitions/AplusResponse"}]}, "ValidateContentDocumentAsinRelationsResponse": {"allOf": [{"$ref": "#/definitions/AplusResponse"}, {"$ref": "#/definitions/ErrorList"}]}, "SearchContentPublishRecordsResponse": {"allOf": [{"$ref": "#/definitions/AplusPaginatedResponse"}, {"type": "object", "required": ["publishRecordList"], "properties": {"publishRecordList": {"$ref": "#/definitions/PublishRecordList"}}}]}, "PostContentDocumentApprovalSubmissionResponse": {"allOf": [{"$ref": "#/definitions/AplusResponse"}]}, "PostContentDocumentSuspendSubmissionResponse": {"allOf": [{"$ref": "#/definitions/AplusResponse"}]}}, "parameters": {"contentReferenceKey": {"name": "contentReferenceKey", "in": "path", "description": "The unique reference key for the A+ Content document. A content reference key cannot form a permalink and may change in the future. A content reference key is not guaranteed to match any A+ content identifier.  ", "required": true, "type": "string", "minLength": 1}, "marketplaceId": {"name": "marketplaceId", "in": "query", "description": "The identifier for the marketplace where the A+ Content is published.", "required": true, "type": "string", "minLength": 1}, "pageToken": {"name": "pageToken", "in": "query", "description": "A page token from the nextPageToken response element returned by your previous call to this operation. nextPageToken is returned when the results of a call exceed the page size. To get the next page of results, call the operation and include pageToken as the only parameter. Specifying pageToken with any other parameter will cause the request to fail. When no nextPageToken value is returned there are no more pages to return. A pageToken value is not usable across different operations.", "required": false, "type": "string", "minLength": 1}, "asinSet": {"name": "asinSet", "in": "query", "description": "The set of ASINs.", "required": false, "type": "array", "items": {"type": "string", "minLength": 10}, "collectionFormat": "csv", "uniqueItems": true}, "asin": {"name": "asin", "in": "query", "description": "The Amazon Standard Identification Number (ASIN).", "required": true, "type": "string", "minLength": 10}, "getContentDocumentIncludedDataSet": {"name": "includedDataSet", "in": "query", "description": "The set of A+ data types to include in the response.", "required": true, "type": "array", "items": {"type": "string", "enum": ["CONTENTS", "METADATA"]}, "collectionFormat": "csv", "minItems": 1, "uniqueItems": true}, "listContentDocumentAsinRelationsIncludedDataSet": {"name": "includedDataSet", "in": "query", "description": "The set of A+ data types to include in the response.", "required": false, "type": "array", "items": {"type": "string", "enum": ["METADATA"]}, "collectionFormat": "csv", "minItems": 0, "uniqueItems": true}}}