{"swagger": "2.0", "info": {"description": "The Selling Partner API for Retail Procurement Payments provides programmatic access to vendors payments data.", "version": "v1", "title": "Selling Partner API for Retail Procurement Payments", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/vendor/payments/v1/invoices": {"post": {"tags": ["vendorPayments"], "description": "Submit new invoices to Amazon.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "submitInvoices", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/SubmitInvoicesRequest", "example": {"payload": {"invoices": [{"invoiceType": "Invoice", "id": "0136981234", "date": "2019-07-24T21:17:59.821Z", "remitToParty": {"partyId": "XYZ12345", "address": {"name": "ANYCOMPANY MANUFACTURING", "addressLine1": "123 ANY STREET", "city": "ANYTOWN", "stateOrRegion": "AZ", "postalOrZipCode": "12345", "countryCode": "USA"}, "taxRegistrationDetails": [{"taxRegistrationType": "VAT", "taxRegistrationNumber": "VENDORVATID"}]}, "shipToParty": {"partyId": "XYZ12345", "address": {"name": "ANYCOMPANY MANUFACTURING", "addressLine1": "123 ANY STREET", "city": "ANYTOWN", "stateOrRegion": "AZ", "countryCode": "USA"}}, "shipFromParty": {"partyId": "XYZ12345", "address": {"name": "ANYCOMPANY MANUFACTURING", "addressLine1": "123 ANY STREET", "city": "ANYTOWN", "stateOrRegion": "AZ", "postalOrZipCode": "12345", "countryCode": "USA"}}, "billToParty": {"partyId": "XYZ12345", "address": {"name": "ANYCOMPANY MANUFACTURING", "addressLine1": "123 ANY STREET", "addressLine2": "", "city": "ANYTOWN", "stateOrRegion": "AZ", "postalOrZipCode": "12345", "countryCode": "USA"}, "taxRegistrationDetails": [{"taxRegistrationType": "VAT", "taxRegistrationNumber": "AMAZONVATNUMBER"}]}, "paymentTerms": {"type": "Basic", "discountPercent": "5", "discountDueDays": 15, "netDueDays": 30}, "invoiceTotal": {"currencyCode": "INR", "amount": "259678.39"}, "taxDetails": [{"taxType": "SGST", "taxRate": "9", "taxAmount": {"currencyCode": "INR", "amount": "19697.98"}, "taxableAmount": {"currencyCode": "INR", "amount": "218866.43"}}, {"taxType": "CGST", "taxRate": "9", "taxAmount": {"currencyCode": "INR", "amount": "19697.98"}, "taxableAmount": {"currencyCode": "INR", "amount": "218866.43"}}], "chargeDetails": [{"type": "Freight", "description": "Freight Charges", "chargeAmount": {"currencyCode": "INR", "amount": "1200.00"}, "taxDetails": [{"taxType": "CGST", "taxRate": "9", "taxAmount": {"currencyCode": "INR", "amount": "108.00"}, "taxableAmount": {"currencyCode": "INR", "amount": "1200.00"}}, {"taxType": "SGST", "taxRate": "9", "taxAmount": {"currencyCode": "INR", "amount": "108.00"}, "taxableAmount": {"currencyCode": "INR", "amount": "1200.00"}}]}], "items": [{"itemSequenceNumber": 1, "amazonProductIdentifier": "ABC123434", "vendorProductIdentifier": "809281-5100", "invoicedQuantity": {"amount": 2, "unitOfMeasure": "Eaches"}, "netCost": {"currencyCode": "INR", "amount": "21060.34"}, "purchaseOrderNumber": "3DY3TK6T", "hsnCode": "***********.00", "taxDetails": [{"taxType": "SGST", "taxRate": "9", "taxAmount": {"currencyCode": "INR", "amount": "1895.43"}, "taxableAmount": {"currencyCode": "INR", "amount": "21060.34"}}, {"taxType": "CGST", "taxRate": "9", "taxAmount": {"currencyCode": "INR", "amount": "1895.43"}, "taxableAmount": {"currencyCode": "INR", "amount": "21060.34"}}], "chargeDetails": [{"type": "Freight", "description": "Freight Charges", "chargeAmount": {"currencyCode": "INR", "amount": "600.00"}}]}, {"itemSequenceNumber": 2, "amazonProductIdentifier": "ABC123435", "vendorProductIdentifier": "795000-0001", "invoicedQuantity": {"amount": 3, "unitOfMeasure": "Eaches"}, "netCost": {"currencyCode": "INR", "amount": "58915.25"}, "purchaseOrderNumber": "3DY3TK6T", "taxDetails": [{"taxType": "SGST", "taxRate": "9", "taxAmount": {"currencyCode": "INR", "amount": "5302.37"}, "taxableAmount": {"currencyCode": "INR", "amount": "58915.25"}}, {"taxType": "CGST", "taxRate": "9", "taxAmount": {"currencyCode": "INR", "amount": "5302.37"}, "taxableAmount": {"currencyCode": "INR", "amount": "58915.25"}}], "chargeDetails": [{"type": "Freight", "description": "Freight Charges", "chargeAmount": {"currencyCode": "INR", "amount": "600.00"}}]}]}]}}}, "description": "The request body containing the invoice data to submit."}], "responses": {"202": {"description": "Success.", "schema": {"$ref": "#/definitions/SubmitInvoicesResponse"}, "examples": {"application/json": {"payload": {"transactionId": "20190904171225-e1275c33-d75b-4bfe-b95c-15a9abfc09cc"}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"invoices": [{"id": "TestInvoice202", "date": "2020-06-08T12:00:00.000Z", "billToParty": {"partyId": "TES1"}, "invoiceType": "Invoice", "remitToParty": {"partyId": "ABCDE"}, "invoiceTotal": {"amount": "112.05", "currencyCode": "USD"}}]}}}}, "response": {"payload": {"transactionId": "20190904171225-e1275c33-d75b-4bfe-b95c-15a9abfc09cc"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/SubmitInvoicesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"invoices": [{"invoiceType": "Invoic"}]}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Value 'Invoic' is not valid with respect to enumeration '[CreditNote, Invoice]'. It must be a value from the enumeration.", "details": ""}, {"code": "InvalidInput", "message": "The value 'Invoic' of element 'invoiceType' is not valid.", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/SubmitInvoicesResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/SubmitInvoicesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/SubmitInvoicesResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/SubmitInvoicesResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/SubmitInvoicesResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/SubmitInvoicesResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/SubmitInvoicesResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}}, "definitions": {"SubmitInvoicesResponse": {"type": "object", "properties": {"payload": {"description": "The response payload for the `submitInvoices` operation.", "$ref": "#/definitions/TransactionId"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `submitInvoices` operation."}, "TransactionId": {"type": "object", "properties": {"transactionId": {"type": "string", "description": "GUID to identify this transaction. This value can be used with the Transaction Status API to return the status of this transaction."}}, "description": "Response containing the transaction ID."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}, "SubmitInvoicesRequest": {"type": "object", "properties": {"invoices": {"type": "array", "items": {"$ref": "#/definitions/Invoice"}, "description": "An array of Invoice objects representing the invoices or credit notes to be submitted."}}, "description": "The request schema for the `submitInvoices` operation."}, "Invoice": {"type": "object", "required": ["date", "id", "invoiceTotal", "invoiceType", "remitToParty"], "properties": {"invoiceType": {"type": "string", "description": "Identifies the type of invoice.", "enum": ["Invoice", "CreditNote"], "x-docgen-enum-table-extension": [{"value": "Invoice", "description": "A commercial document issued by a seller to a buyer, relating to a sale transaction and indicating the products, quantities, and agreed prices for products or services the seller had provided the buyer."}, {"value": "CreditNote", "description": "A commercial document issued by a seller to a buyer. It is evidence of the reduction in sales."}]}, "id": {"type": "string", "description": "Unique number relating to the charges defined in this document. This will be invoice number if the document type is Invoice or CreditNote number if the document type is Credit Note. Failure to provide this reference will result in a rejection."}, "referenceNumber": {"type": "string", "description": "An additional unique reference number used for regulatory or other purposes."}, "date": {"description": "Date when the invoice/credit note information was generated in the origin's accounting system. The invoice date should be on or after the purchase order creation date.", "$ref": "#/definitions/DateTime"}, "remitToParty": {"description": "Name, address, and tax details of the party receiving the payment of this invoice.", "$ref": "#/definitions/PartyIdentification"}, "shipToParty": {"description": "Name, address, and tax details of the party receiving a shipment of products.", "$ref": "#/definitions/PartyIdentification"}, "shipFromParty": {"description": "Name, address, and tax details of the party sending a shipment of products.", "$ref": "#/definitions/PartyIdentification"}, "billToParty": {"description": "Name, address, and tax details of the party to whom this invoice is issued.", "$ref": "#/definitions/PartyIdentification"}, "paymentTerms": {"description": "The payment terms for the invoice.", "$ref": "#/definitions/PaymentTerms"}, "invoiceTotal": {"description": "Total monetary amount charged in the invoice or full value of credit note to be paid including all relevant taxes. It is the total amount of invoice (including charges, less allowances) before terms discount (if discount is applicable).", "$ref": "#/definitions/Money"}, "taxDetails": {"type": "array", "description": "Total tax amount details for all line items.", "items": {"$ref": "#/definitions/TaxDetails"}}, "additionalDetails": {"type": "array", "description": "Additional details provided by the selling party, for tax related or other purposes.", "items": {"$ref": "#/definitions/AdditionalDetails"}}, "chargeDetails": {"type": "array", "description": "Total charge amount details for all line items.", "items": {"$ref": "#/definitions/ChargeDetails"}}, "allowanceDetails": {"type": "array", "description": "Total allowance amount details for all line items.", "items": {"$ref": "#/definitions/AllowanceDetails"}}, "items": {"type": "array", "description": "The list of invoice items.", "items": {"$ref": "#/definitions/InvoiceItem"}}}, "description": "Represents an invoice or credit note document with details about the transaction, parties involved, and line items."}, "PartyIdentification": {"description": "Name, address, and tax details of a party.", "type": "object", "required": ["partyId"], "properties": {"partyId": {"type": "string", "description": "Assigned identification for the party."}, "address": {"description": "Identification of the party by address.", "$ref": "#/definitions/Address"}, "taxRegistrationDetails": {"type": "array", "description": "Tax registration details of the party.", "items": {"$ref": "#/definitions/TaxRegistrationDetails"}}}}, "TaxRegistrationDetails": {"type": "object", "required": ["taxRegistrationNumber", "taxRegistrationType"], "properties": {"taxRegistrationType": {"type": "string", "description": "The tax registration type for the entity.", "enum": ["VAT", "GST"], "x-docgen-enum-table-extension": [{"value": "VAT", "description": "Value-added tax."}, {"value": "GST", "description": "Goods and services tax."}]}, "taxRegistrationNumber": {"type": "string", "description": "The tax registration number for the entity. For example, VAT ID, Consumption Tax ID."}}, "description": "Tax registration details of the entity."}, "Address": {"type": "object", "required": ["addressLine1", "countryCode", "name"], "properties": {"name": {"type": "string", "description": "The name of the person, business or institution at that address."}, "addressLine1": {"type": "string", "description": "First line of street address."}, "addressLine2": {"type": "string", "description": "Additional address information, if required."}, "addressLine3": {"type": "string", "description": "Additional address information, if required."}, "city": {"type": "string", "description": "The city where the person, business, or institution is located."}, "county": {"type": "string", "description": "The county where person, business, or institution is located."}, "district": {"type": "string", "description": "The district where person, business, or institution is located."}, "stateOrRegion": {"type": "string", "description": "The state or region where person, business, or institution is located."}, "postalOrZipCode": {"type": "string", "description": "The postal or zip code of that address. It contains a series of letters, digits, or both, sometimes including spaces or punctuation."}, "countryCode": {"type": "string", "description": "The two digit country code. In ISO 3166-1 alpha-2 format.", "maxLength": 2}, "phone": {"type": "string", "description": "The phone number of the person, business or institution located at that address."}}, "description": "A physical address."}, "InvoiceItem": {"type": "object", "required": ["invoicedQuantity", "itemSequenceNumber", "netCost"], "properties": {"itemSequenceNumber": {"type": "integer", "description": "Unique number related to this line item."}, "amazonProductIdentifier": {"type": "string", "description": "Amazon Standard Identification Number (ASIN) of an item."}, "vendorProductIdentifier": {"type": "string", "description": "The vendor selected product identifier of the item. Should be the same as was provided in the purchase order."}, "invoicedQuantity": {"description": "Invoiced quantity of this item. Quantity must be greater than zero.", "$ref": "#/definitions/ItemQuantity"}, "netCost": {"description": "The item cost to Amazon, which should match the cost on the order. Price information should not be zero or negative. It indicates net unit price. Net cost means VAT is not included in cost.", "$ref": "#/definitions/Money"}, "purchaseOrderNumber": {"type": "string", "description": "The Amazon purchase order number for this invoiced line item. Formatting Notes: 8-character alpha-numeric code. This value is mandatory only when `invoiceType` is `Invoice`, and is not required when `invoiceType` is `CreditNote`."}, "hsnCode": {"type": "string", "description": "The HSN Tax code. The HSN number cannot contain alphabets."}, "creditNoteDetails": {"description": "Details required in order to process a credit note. This information is required only if `invoiceType` is `CreditNote`.", "$ref": "#/definitions/CreditNoteDetails"}, "taxDetails": {"type": "array", "description": "Individual tax details per line item.", "items": {"$ref": "#/definitions/TaxDetails"}}, "chargeDetails": {"type": "array", "description": "Individual charge details per line item.", "items": {"$ref": "#/definitions/ChargeDetails"}}, "allowanceDetails": {"type": "array", "description": "Individual allowance details per line item.", "items": {"$ref": "#/definitions/AllowanceDetails"}}}, "description": "Details of the item being invoiced."}, "TaxDetails": {"type": "object", "required": ["taxAmount", "taxType"], "properties": {"taxType": {"type": "string", "description": "Type of the tax applied.", "enum": ["CGST", "SGST", "CESS", "UTGST", "IGST", "MwSt.", "PST", "TVA", "VAT", "GST", "ST", "Consumption", "MutuallyDefined", "DomesticVAT"], "x-docgen-enum-table-extension": [{"value": "CGST", "description": "Central Goods and Services Tax (CGST) is levied by the Indian government for intrastate movement of goods and services."}, {"value": "SGST", "description": "State Goods and Services Tax (SGST) is an indirect tax levied and collected by a State Government in India on the intra-state supplies."}, {"value": "CESS", "description": "A CESS is a form of tax levied by the government on tax with specific purposes till the time the government gets enough money for that purpose."}, {"value": "UTGST", "description": "Union Territory Goods and Services Tax in India."}, {"value": "IGST", "description": "Integrated Goods and Services Tax (IGST) is a tax levied on all Inter-State supplies of goods and/or services in India."}, {"value": "MwSt.", "description": "Me<PERSON><PERSON>tsteuer, MwSt, is German for value-added tax."}, {"value": "PST", "description": "A provincial sales tax (PST) is imposed on consumers of goods and particular services in many Canadian provinces."}, {"value": "TVA", "description": "Taxe sur la Valeur Ajout&#233;e (TVA) is French for value-added tax."}, {"value": "VAT", "description": "Value-added tax."}, {"value": "GST", "description": "Tax levied on most goods and services sold for domestic consumption."}, {"value": "ST", "description": "Sales tax."}, {"value": "Consumption", "description": "Tax levied on consumption spending on goods and services."}, {"value": "MutuallyDefined", "description": "Tax component that was mutually agreed upon between Amazon and vendor."}, {"value": "DomesticVAT", "description": "Domestic value-added tax."}]}, "taxRate": {"description": "Tax percentage applied. Percentage must be expressed in decimal format.", "$ref": "#/definitions/Decimal"}, "taxAmount": {"description": "Total tax amount applied on an invoice total or item total.", "$ref": "#/definitions/Money"}, "taxableAmount": {"description": "The invoice amount that is taxable at the rate specified in the tax rate field.", "$ref": "#/definitions/Money"}}, "description": "Details of tax amount applied."}, "Money": {"type": "object", "properties": {"currencyCode": {"type": "string", "description": "Three-digit currency code in ISO 4217 format."}, "amount": {"$ref": "#/definitions/Decimal"}}, "description": "An amount of money, including units in the form of currency."}, "AdditionalDetails": {"type": "object", "required": ["detail", "type"], "properties": {"type": {"type": "string", "description": "The type of the additional information provided by the selling party.", "enum": ["SUR", "OCR", "CartonCount"], "x-docgen-enum-table-extension": [{"value": "SUR", "description": "An additional tax on something already taxed, such as a higher rate of tax on incomes above a certain level."}, {"value": "OCR", "description": "OCR."}, {"value": "CartonCount", "description": "The total number of cartons invoiced."}]}, "detail": {"type": "string", "description": "The detail of the additional information provided by the selling party."}, "languageCode": {"type": "string", "description": "The language code of the additional information detail."}}, "description": "Additional information provided by the selling party for tax-related or any other purpose."}, "ChargeDetails": {"type": "object", "required": ["chargeAmount", "type"], "properties": {"type": {"type": "string", "description": "Type of the charge applied.", "enum": ["Freight", "Packing", "Duty", "Service", "SmallOrder", "InsurancePlacementCost", "InsuranceFee", "SpecialHandlingService", "CollectionAndRecyclingService", "EnvironmentalProtectionService", "TaxCollectedAtSource"], "x-docgen-enum-table-extension": [{"value": "Freight", "description": "Freight charges."}, {"value": "Packing", "description": "Packing fee."}, {"value": "Duty", "description": "Duty charges."}, {"value": "Service", "description": "Service fee."}, {"value": "SmallOrder", "description": "Small order fee."}, {"value": "InsurancePlacementCost", "description": "Insurance placement cost."}, {"value": "InsuranceFee", "description": "Insurance fee."}, {"value": "SpecialHandlingService", "description": "Special handling service fee."}, {"value": "CollectionAndRecyclingService", "description": "Collection and recycling service fee."}, {"value": "EnvironmentalProtectionService", "description": "Environmental protection service fee."}, {"value": "TaxCollectedAtSource", "description": "Tax collected at source."}]}, "description": {"type": "string", "description": "Description of the charge."}, "chargeAmount": {"description": "Total monetary amount related to this charge.", "$ref": "#/definitions/Money"}, "taxDetails": {"type": "array", "description": "Tax amount details applied on this charge.", "items": {"$ref": "#/definitions/TaxDetails"}}}, "description": "Monetary and tax details of the charge."}, "AllowanceDetails": {"type": "object", "required": ["allowanceAmount", "type"], "properties": {"type": {"type": "string", "description": "Type of the allowance applied.", "enum": ["Discount", "DiscountIncentive", "Defective", "Promotional", "UnsaleableMerchandise", "Special"], "x-docgen-enum-table-extension": [{"value": "Discount", "description": "Discount allowance."}, {"value": "DiscountIncentive", "description": "Discount incentive allowance."}, {"value": "Defective", "description": "Allowance applied for defective item."}, {"value": "Promotional", "description": "Promotional allowance."}, {"value": "UnsaleableMerchandise", "description": "Allowance applied due to unsaleable merchandise."}, {"value": "Special", "description": "Special allowances."}]}, "description": {"type": "string", "description": "Description of the allowance."}, "allowanceAmount": {"description": "Total monetary amount related to this allowance.", "$ref": "#/definitions/Money"}, "taxDetails": {"type": "array", "description": "Tax amount details applied on this allowance.", "items": {"$ref": "#/definitions/TaxDetails"}}}, "description": "Monetary and tax details of the allowance."}, "PaymentTerms": {"type": "object", "properties": {"type": {"type": "string", "description": "The payment term type for the invoice.", "enum": ["Basic", "EndOfMonth", "FixedDate", "Proximo", "PaymentDueUponReceiptOfInvoice", "LetterofCredit"], "x-docgen-enum-table-extension": [{"value": "Basic", "description": "Payment term that buyer and seller have agreed to."}, {"value": "EndOfMonth", "description": "Payment term where seller gets paid end of month."}, {"value": "FixedDate", "description": "Payment term where seller gets paid on a fixed date as agreed with buyer."}, {"value": "Proximo", "description": "Payment term where seller gets paid end of following month."}, {"value": "PaymentDueUponReceiptOfInvoice", "description": "Payment term where seller gets paid upon receipt of the invoice by the buyer."}, {"value": "LetterofCredit", "description": "A payment undertaking given by a bank to the seller and is issued on behalf of the applicant i.e. the buyer."}]}, "discountPercent": {"description": "The discount percent value, which is good until the discount due date.", "$ref": "#/definitions/Decimal"}, "discountDueDays": {"type": "number", "description": "The number of calendar days from the base date (Invoice date) until the discount is no longer valid."}, "netDueDays": {"type": "number", "description": "The number of calendar days from the base date (invoice date) until the total amount on the invoice is due."}}, "description": "Terms of the payment for the invoice. The basis of the payment terms is the invoice date."}, "CreditNoteDetails": {"type": "object", "properties": {"referenceInvoiceNumber": {"type": "string", "description": "Original invoice number when sending a credit note relating to an existing invoice. One invoice only to be processed per credit note. This is mandatory for AP credit notes."}, "debitNoteNumber": {"type": "string", "description": "Debit note number as generated by Amazon. Recommended for returns and COOP credit notes."}, "returnsReferenceNumber": {"type": "string", "description": "Identifies the returns notice number. Mandatory for all returns credit notes."}, "goodsReturnDate": {"description": "Date that a return is received by the vendor. It is mandatory for the returns credit note.", "$ref": "#/definitions/DateTime"}, "rmaId": {"type": "string", "description": "Identifies the Returned Merchandise Authorization ID, if generated."}, "coopReferenceNumber": {"type": "string", "description": "Identifies the COOP reference used for COOP agreement. Failure to provide the COOP reference number or the debit note number may lead to a rejection of the credit note."}, "consignorsReferenceNumber": {"type": "string", "description": "Identifies the consignor reference number (VRET number), if generated by Amazon."}}, "description": "References required in order to process a credit note. This information is required only if `InvoiceType` is `CreditNote`."}, "ItemQuantity": {"type": "object", "required": ["amount", "unitOfMeasure"], "properties": {"amount": {"type": "integer", "description": "Quantity of an item. This value should not be zero."}, "unitOfMeasure": {"type": "string", "description": "Unit of measure for the quantity.", "enum": ["Cases", "Eaches"], "x-docgen-enum-table-extension": [{"value": "Cases", "description": "Packing of individual items into a case."}, {"value": "Eaches", "description": "Individual items."}]}, "unitSize": {"type": "integer", "description": "The case size, if the unit of measure value is `Cases`."}}, "description": "Details of quantity."}, "Decimal": {"type": "string", "description": "A decimal number with no loss of precision. Useful when precision loss is unacceptable, as with currencies. Follows RFC7159 for number representation. <br>**Pattern** : `^-?(0|([1-9]\\d*))(\\.\\d+)?([eE][+-]?\\d+)?$`."}, "DateTime": {"type": "string", "format": "date-time", "description": "Defines a date and time according to <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a>."}}}