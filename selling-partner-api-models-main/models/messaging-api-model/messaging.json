{"swagger": "2.0", "info": {"description": "With the Messaging API you can build applications that send messages to buyers. You can get a list of message types that are available for an order that you specify, then call an operation that sends a message to the buyer for that order. The Messaging API returns responses that are formed according to the <a href=https://tools.ietf.org/html/draft-kelly-json-hal-08>JSON Hypertext Application Language</a> (HAL) standard.", "version": "v1", "title": "Selling Partner API for Messaging", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/hal+json"], "paths": {"/messaging/v1/orders/{amazonOrderId}": {"get": {"tags": ["messaging"], "description": "Returns a list of message types that are available for an order that you specify. A message type is represented by an actions object, which contains a path and query parameter(s). You can use the path and parameter(s) to call an operation that sends a message.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getMessagingActionsForOrder", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which you want a list of available message types.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}], "responses": {"200": {"description": "Returns hypermedia links under the _links.actions key that specify which messaging actions are allowed for the order.", "schema": {"$ref": "#/definitions/GetMessagingActionsForOrderResponse"}, "examples": {"application/hal+json": {"_links": {"actions": [{"href": "/messaging/v1/orders/903-1671087-0812628/messages/negativeFeedbackRemoval?marketplaceIds=ATVPDKIKX0DER", "name": "negativeFeedbackRemoval"}], "self": {"href": "/messaging/v1/orders/903-1671087-0812628?marketplaceIds=ATVPDKIKX0DER"}}, "_embedded": {"actions": [{"_links": {"schema": {"href": "/messaging/v1/orders/903-1671087-0812628/messages/negativeFeedbackRemoval/schema", "name": "negativeFeedbackRemoval"}, "self": {"href": "/messaging/v1/orders/903-1671087-0812628/messages/negativeFeedbackRemoval?marketplaceIds=ATVPDKIKX0DER", "name": "negativeFeedbackRemoval"}}}]}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}}}, "response": {"_links": {"actions": [{"href": "/messaging/v1/orders/123-1234567-1234567/messages/confirmCustomizationDetails?marketplaceIds=ATVPDKIKX0DER", "name": "confirmCustomizationDetails"}, {"href": "/messaging/v1/orders/123-1234567-1234567/messages/confirmDeliveryDetails?marketplaceIds=ATVPDKIKX0DER", "name": "confirmDeliveryDetails"}, {"href": "/messaging/v1/orders/123-1234567-1234567/messages/legalDisclosure?marketplaceIds=ATVPDKIKX0DER", "name": "legalDisclosure"}, {"href": "/messaging/v1/orders/123-1234567-1234567/messages/negativeFeedbackRemoval?marketplaceIds=ATVPDKIKX0DER", "name": "negativeFeedbackRemoval"}, {"href": "/messaging/v1/orders/123-1234567-1234567/messages/confirmOrderDetails?marketplaceIds=ATVPDKIKX0DER", "name": "confirmOrderDetails"}, {"href": "/messaging/v1/orders/123-1234567-1234567/messages/confirmServiceDetails?marketplaceIds=ATVPDKIKX0DER", "name": "confirmServiceDetails"}, {"href": "/messaging/v1/orders/123-1234567-1234567/messages/amazonMotors?marketplaceIds=ATVPDKIKX0DER", "name": "amazonMotors"}, {"href": "/messaging/v1/orders/123-1234567-1234567/messages/digitalAccessKey?marketplaceIds=ATVPDKIKX0DER", "name": "digitalAccessKey"}, {"href": "/messaging/v1/orders/123-1234567-1234567/messages/unexpectedProblem?marketplaceIds=ATVPDKIKX0DER", "name": "unexpectedProblem"}, {"href": "/messaging/v1/orders/123-1234567-1234567/messages/warranty?marketplaceIds=ATVPDKIKX0DER", "name": "warranty"}, {"href": "/messaging/v1/orders/123-1234567-1234567/attributes?marketplaceIds=ATVPDKIKX0DER", "name": "attributes"}], "self": {"href": "/messaging/v1/orders/123-1234567-1234567?marketplaceIds=ATVPDKIKX0DER"}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetMessagingActionsForOrderResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetMessagingActionsForOrderResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetMessagingActionsForOrderResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/GetMessagingActionsForOrderResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetMessagingActionsForOrderResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetMessagingActionsForOrderResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetMessagingActionsForOrderResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetMessagingActionsForOrderResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/confirmCustomizationDetails": {"post": {"tags": ["messaging"], "description": "Sends a message asking a buyer to provide or verify customization details such as name spelling, images, initials, etc.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "confirmCustomizationDetails", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateConfirmCustomizationDetailsRequest"}}], "responses": {"201": {"description": "The message was created for the order.", "schema": {"$ref": "#/definitions/CreateConfirmCustomizationDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}, "body": {"value": {"text": "My Message", "attachments": [{"uploadDestinationId": "4e936e26-7b72-4b84-af27-e6baee1d546d", "fileName": "AmazonMotors.txt"}, {"uploadDestinationId": "4e936e26-7b72-4b84-af27-e6baee1d546d", "fileName": "AmazonMotors.txt"}]}}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateConfirmCustomizationDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateConfirmCustomizationDetailsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateConfirmCustomizationDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateConfirmCustomizationDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateConfirmCustomizationDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateConfirmCustomizationDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateConfirmCustomizationDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateConfirmCustomizationDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/confirmDeliveryDetails": {"post": {"tags": ["messaging"], "description": "Sends a message to a buyer to arrange a delivery or to confirm contact information for making a delivery.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createConfirmDeliveryDetails", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateConfirmDeliveryDetailsRequest"}}], "responses": {"201": {"description": "The message was created for the order.", "schema": {"$ref": "#/definitions/CreateConfirmDeliveryDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}, "body": {"value": {"text": "My Message"}}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateConfirmDeliveryDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateConfirmDeliveryDetailsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateConfirmDeliveryDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateConfirmDeliveryDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateConfirmDeliveryDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateConfirmDeliveryDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateConfirmDeliveryDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateConfirmDeliveryDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/legalDisclosure": {"post": {"tags": ["messaging"], "description": "Sends a critical message that contains documents that a seller is legally obligated to provide to the buyer. This message should only be used to deliver documents that are required by law.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createLegalDisclosure", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateLegalDisclosureRequest"}}], "responses": {"201": {"description": "The legal disclosure message was created for the order.", "schema": {"$ref": "#/definitions/CreateLegalDisclosureResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}, "body": {"value": {"attachments": [{"uploadDestinationId": "4e936e26-7b72-4b84-af27-e6baee1d546d", "fileName": "AmazonMotors.txt"}, {"uploadDestinationId": "4e936e26-7b72-4b84-af27-e6baee1d546d", "fileName": "AmazonMotors.txt"}]}}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateLegalDisclosureResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateLegalDisclosureResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateLegalDisclosureResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateLegalDisclosureResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateLegalDisclosureResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateLegalDisclosureResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateLegalDisclosureResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateLegalDisclosureResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/negativeFeedbackRemoval": {"post": {"tags": ["messaging"], "description": "Sends a non-critical message that asks a buyer to remove their negative feedback. This message should only be sent after the seller has resolved the buyer's problem.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createNegativeFeedbackRemoval", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}], "responses": {"201": {"description": "The negativeFeedbackRemoval message was created for the order.", "schema": {"$ref": "#/definitions/CreateNegativeFeedbackRemovalResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateNegativeFeedbackRemovalResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateNegativeFeedbackRemovalResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateNegativeFeedbackRemovalResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateNegativeFeedbackRemovalResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateNegativeFeedbackRemovalResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateNegativeFeedbackRemovalResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateNegativeFeedbackRemovalResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateNegativeFeedbackRemovalResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/confirmOrderDetails": {"post": {"tags": ["messaging"], "description": "Sends a message to ask a buyer an order-related question prior to shipping their order.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createConfirmOrderDetails", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateConfirmOrderDetailsRequest"}}], "responses": {"201": {"description": "The message was created for the order.", "schema": {"$ref": "#/definitions/CreateConfirmOrderDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}, "body": {"value": {"text": "My Message"}}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateConfirmOrderDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateConfirmOrderDetailsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateConfirmOrderDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateConfirmOrderDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateConfirmOrderDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateConfirmOrderDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateConfirmOrderDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateConfirmOrderDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/confirmServiceDetails": {"post": {"tags": ["messaging"], "description": "Sends a message to contact a Home Service customer to arrange a service call or to gather information prior to a service call.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createConfirmServiceDetails", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateConfirmServiceDetailsRequest"}}], "responses": {"201": {"description": "The message was created for the order.", "schema": {"$ref": "#/definitions/CreateConfirmServiceDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}, "body": {"value": {"text": "My Message"}}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateConfirmServiceDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateConfirmServiceDetailsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateConfirmServiceDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateConfirmServiceDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateConfirmServiceDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateConfirmServiceDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateConfirmServiceDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateConfirmServiceDetailsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/amazonMotors": {"post": {"tags": ["messaging"], "description": "Sends a message to a buyer to provide details about an Amazon Motors order. This message can only be sent by Amazon Motors sellers.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "CreateAmazonMotors", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateAmazonMotorsRequest"}}], "responses": {"201": {"description": "The message was created for the order.", "schema": {"$ref": "#/definitions/CreateAmazonMotorsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}, "body": {"value": {"attachments": [{"uploadDestinationId": "4e936e26-7b72-4b84-af27-e6baee1d546d", "fileName": "AmazonMotors.txt"}]}}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateAmazonMotorsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateAmazonMotorsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateAmazonMotorsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateAmazonMotorsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateAmazonMotorsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateAmazonMotorsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateAmazonMotorsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateAmazonMotorsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/warranty": {"post": {"tags": ["messaging"], "description": "Sends a message to a buyer to provide details about warranty information on a purchase in their order.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "CreateWarranty", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateWarrantyRequest"}}], "responses": {"201": {"description": "The message was created for the order.", "schema": {"$ref": "#/definitions/CreateWarrantyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}, "body": {"value": {"attachments": [{"uploadDestinationId": "8634452c-4d4f-4703-8cea-2ecc9dcb3279", "fileName": "warranty.txt"}], "coverageStartDate": "2004-12-13T21:39:45.618-08:00", "coverageEndDate": "2005-12-13T21:39:45.618-08:00"}}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateWarrantyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateWarrantyResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateWarrantyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateWarrantyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateWarrantyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateWarrantyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateWarrantyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateWarrantyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/attributes": {"get": {"tags": ["messaging"], "description": "Returns a response containing attributes related to an order. This includes buyer preferences.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |", "operationId": "GetAttributes", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}], "responses": {"200": {"description": "Response has successfully been returned.", "schema": {"$ref": "#/definitions/GetAttributesResponse"}, "examples": {"application/hal+json": {"buyer": {"locale": "en-US"}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}}}, "response": {"buyer": {"locale": "en-US"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetAttributesResponse"}, "examples": {"application/hal+json": {"errors": [{"code": "string", "message": "string", "details": "string"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetAttributesResponse"}, "examples": {"application/hal+json": {"errors": [{"code": "string", "message": "string", "details": "string"}]}}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetAttributesResponse"}, "examples": {"application/hal+json": {"errors": [{"code": "string", "message": "string", "details": "string"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/GetAttributesResponse"}, "examples": {"application/hal+json": {"errors": [{"code": "string", "message": "string", "details": "string"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetAttributesResponse"}, "examples": {"application/hal+json": {"errors": [{"code": "string", "message": "string", "details": "string"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetAttributesResponse"}, "examples": {"application/hal+json": {"errors": [{"code": "string", "message": "string", "details": "string"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetAttributesResponse"}, "examples": {"application/hal+json": {"errors": [{"code": "string", "message": "string", "details": "string"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetAttributesResponse"}, "examples": {"application/hal+json": {"errors": [{"code": "string", "message": "string", "details": "string"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/digitalAccessKey": {"post": {"tags": ["messaging"], "description": "Sends a message to a buyer to share a digital access key needed to utilize digital content in their order.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createDigitalAccessKey", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateDigitalAccessKeyRequest"}}], "responses": {"201": {"description": "The message was created for the order.", "schema": {"$ref": "#/definitions/CreateDigitalAccessKeyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}, "body": {"value": {"text": "My Message", "attachments": [{"uploadDestinationId": "4e936e26-7b72-4b84-af27-e6baee1d546d", "fileName": "AmazonMotors.txt"}, {"uploadDestinationId": "4e936e26-7b72-4b84-af27-e6baee1d546d", "fileName": "AmazonMotors.txt"}]}}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateDigitalAccessKeyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateDigitalAccessKeyResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateDigitalAccessKeyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateDigitalAccessKeyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateDigitalAccessKeyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateDigitalAccessKeyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateDigitalAccessKeyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateDigitalAccessKeyResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/unexpectedProblem": {"post": {"tags": ["messaging"], "description": "Sends a critical message to a buyer that an unexpected problem was encountered affecting the completion of the order.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 5 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createUnexpectedProblem", "parameters": [{"name": "amazonOrderId", "in": "path", "description": "An Amazon order identifier. This specifies the order for which a message is sent.", "required": true, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateUnexpectedProblemRequest"}}], "responses": {"201": {"description": "The message was created for the order.", "schema": {"$ref": "#/definitions/CreateUnexpectedProblemResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}, "body": {"value": {"text": "My Message"}}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateUnexpectedProblemResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-0000000"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateUnexpectedProblemResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateUnexpectedProblemResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateUnexpectedProblemResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateUnexpectedProblemResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateUnexpectedProblemResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateUnexpectedProblemResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateUnexpectedProblemResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/messaging/v1/orders/{amazonOrderId}/messages/invoice": {"post": {"description": "Sends a message providing the buyer an invoice", "operationId": "sendInvoice", "tags": ["messaging"], "responses": {"201": {"description": "The message was created for the order.", "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"description": "Unique request reference id.", "type": "string"}}, "schema": {"$ref": "#/definitions/InvoiceResponse"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "123-1234567-1234567"}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"description": "Unique request reference id.", "type": "string"}}, "schema": {"$ref": "#/definitions/InvoiceResponse"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"amazonOrderId": {"value": "badOrderId"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input", "details": ""}]}}]}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "headers": {"x-amzn-RequestId": {"description": "Unique request reference id.", "type": "string"}}, "schema": {"$ref": "#/definitions/InvoiceResponse"}}, "404": {"description": "The resource specified does not exist.", "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"description": "Unique request reference id.", "type": "string"}}, "schema": {"$ref": "#/definitions/InvoiceResponse"}}, "413": {"description": "The request size exceeded the maximum accepted size.", "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"description": "Unique request reference id.", "type": "string"}}, "schema": {"$ref": "#/definitions/InvoiceResponse"}}, "415": {"description": "The entity of the request is in a format not supported by the requested resource.", "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"description": "Unique request reference id.", "type": "string"}}, "schema": {"$ref": "#/definitions/InvoiceResponse"}}, "429": {"description": "The frequency of requests was greater than allowed.", "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"description": "Unique request reference id.", "type": "string"}}, "schema": {"$ref": "#/definitions/InvoiceResponse"}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"description": "Unique request reference id.", "type": "string"}}, "schema": {"$ref": "#/definitions/InvoiceResponse"}}, "503": {"description": "Temporary overloading or maintenance of the server.", "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"description": "Unique request reference id.", "type": "string"}}, "schema": {"$ref": "#/definitions/InvoiceResponse"}}}, "parameters": [{"name": "amazonOrderId", "type": "string", "in": "path", "required": true, "description": "An Amazon order identifier. This specifies the order for which a message is sent."}, {"name": "marketplaceIds", "type": "array", "in": "query", "required": true, "description": "A marketplace identifier. This specifies the marketplace in which the order was placed. Only one marketplace can be specified.", "items": {"type": "string"}, "maxItems": 1}, {"schema": {"$ref": "#/definitions/InvoiceRequest"}, "in": "body", "name": "body", "required": true}]}}}, "definitions": {"Attachment": {"type": "object", "required": ["fileName", "uploadDestinationId"], "properties": {"uploadDestinationId": {"type": "string", "description": "The identifier of the upload destination. Get this value by calling the [createUploadDestinationForResource](doc:uploads-api-reference#post-uploads2020-11-01uploaddestinationsresource) operation of the Uploads API."}, "fileName": {"type": "string", "description": "The name of the file, including the extension. This is the file name that will appear in the message. This does not need to match the file name of the file that you uploaded."}}, "description": "Represents a file uploaded to a destination that was created by the [createUploadDestinationForResource](doc:uploads-api-reference#post-uploads2020-11-01uploaddestinationsresource) operation of the Selling Partner API for Uploads."}, "LinkObject": {"type": "object", "required": ["href"], "properties": {"href": {"type": "string", "description": "A URI for this object."}, "name": {"type": "string", "description": "An identifier for this object."}}, "description": "A Link object."}, "MessagingAction": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}, "description": "A simple object containing the name of the template."}, "Schema": {"type": "object", "description": "A JSON schema document describing the expected payload of the action. This object can be validated against <a href=http://json-schema.org/draft-04/schema>http://json-schema.org/draft-04/schema</a>.", "additionalProperties": {}}, "GetMessagingActionsForOrderResponse": {"type": "object", "properties": {"_links": {"type": "object", "properties": {"self": {"$ref": "#/definitions/LinkObject"}, "actions": {"type": "array", "description": "Eligible actions for the specified amazonOrderId.", "items": {"$ref": "#/definitions/LinkObject"}}}, "required": ["actions", "self"]}, "_embedded": {"type": "object", "properties": {"actions": {"type": "array", "items": {"$ref": "#/definitions/GetMessagingActionResponse"}}}, "required": ["actions"]}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the getMessagingActionsForOrder operation."}, "GetMessagingActionResponse": {"type": "object", "properties": {"_links": {"type": "object", "properties": {"self": {"$ref": "#/definitions/LinkObject"}, "schema": {"$ref": "#/definitions/LinkObject"}}, "required": ["schema", "self"]}, "_embedded": {"type": "object", "properties": {"schema": {"$ref": "#/definitions/GetSchemaResponse"}}}, "payload": {"$ref": "#/definitions/MessagingAction"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "Describes a messaging action that can be taken for an order. Provides a JSON Hypertext Application Language (HAL) link to the JSON schema document that describes the expected input."}, "GetSchemaResponse": {"type": "object", "properties": {"_links": {"type": "object", "properties": {"self": {"$ref": "#/definitions/LinkObject"}}, "required": ["self"]}, "payload": {"$ref": "#/definitions/Schema"}, "errors": {"$ref": "#/definitions/ErrorList"}}}, "InvoiceRequest": {"description": "The request schema for the sendInvoice operation.", "type": "object", "properties": {"attachments": {"type": "array", "description": "Attachments to include in the message to the buyer.", "maxLength": 5, "minLength": 1, "items": {"$ref": "#/definitions/Attachment"}}}}, "InvoiceResponse": {"description": "The response schema for the sendInvoice operation.", "type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}}, "CreateConfirmCustomizationDetailsRequest": {"type": "object", "properties": {"text": {"type": "string", "description": "The text to be sent to the buyer. Only links related to customization details are allowed. Do not include HTML or email addresses. The text must be written in the buyer's language of preference, which can be retrieved from the GetAttributes operation.", "minLength": 1, "maxLength": 800}, "attachments": {"type": "array", "description": "Attachments to include in the message to the buyer.", "maxLength": 5, "items": {"$ref": "#/definitions/Attachment"}}}, "description": "The request schema for the confirmCustomizationDetails operation."}, "CreateConfirmCustomizationDetailsResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the confirmCustomizationDetails operation."}, "CreateConfirmDeliveryDetailsRequest": {"type": "object", "properties": {"text": {"type": "string", "description": "The text to be sent to the buyer. Only links related to order delivery are allowed. Do not include HTML or email addresses. The text must be written in the buyer's language of preference, which can be retrieved from the GetAttributes operation.", "minLength": 1, "maxLength": 2000}}, "description": "The request schema for the createConfirmDeliveryDetails operation."}, "CreateConfirmDeliveryDetailsResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createConfirmDeliveryDetails operation."}, "CreateNegativeFeedbackRemovalResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createNegativeFeedbackRemoval operation."}, "CreateLegalDisclosureRequest": {"type": "object", "properties": {"attachments": {"type": "array", "description": "Attachments to include in the message to the buyer. If any text is included in the attachment, the text must be written in the buyer's language of preference, which can be retrieved from the GetAttributes operation.", "maxLength": 5, "items": {"$ref": "#/definitions/Attachment"}}}, "description": "The request schema for the createLegalDisclosure operation."}, "CreateLegalDisclosureResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createLegalDisclosure operation."}, "CreateConfirmOrderDetailsRequest": {"type": "object", "properties": {"text": {"type": "string", "description": "The text to be sent to the buyer. Only links related to order completion are allowed. Do not include HTML or email addresses. The text must be written in the buyer's language of preference, which can be retrieved from the GetAttributes operation.", "minLength": 1, "maxLength": 2000}}, "description": "The request schema for the createConfirmOrderDetails operation."}, "CreateConfirmOrderDetailsResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createConfirmOrderDetails operation."}, "CreateConfirmServiceDetailsRequest": {"type": "object", "properties": {"text": {"type": "string", "description": "The text to be sent to the buyer. Only links related to Home Service calls are allowed. Do not include HTML or email addresses. The text must be written in the buyer's language of preference, which can be retrieved from the GetAttributes operation.", "minLength": 1, "maxLength": 2000}}, "description": "The request schema for the createConfirmServiceDetails operation."}, "CreateConfirmServiceDetailsResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createConfirmServiceDetails operation."}, "CreateAmazonMotorsRequest": {"type": "object", "properties": {"attachments": {"type": "array", "description": "Attachments to include in the message to the buyer. If any text is included in the attachment, the text must be written in the buyer's language of preference, which can be retrieved from the GetAttributes operation.", "maxLength": 5, "items": {"$ref": "#/definitions/Attachment"}}}, "description": "The request schema for the createAmazonMotors operation."}, "CreateAmazonMotorsResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createAmazonMotors operation."}, "CreateWarrantyRequest": {"type": "object", "properties": {"attachments": {"type": "array", "description": "Attachments to include in the message to the buyer. If any text is included in the attachment, the text must be written in the buyer's language of preference, which can be retrieved from the GetAttributes operation.", "maxLength": 5, "minLength": 1, "items": {"$ref": "#/definitions/Attachment"}}, "coverageStartDate": {"type": "string", "format": "date-time", "description": "The start date of the warranty coverage to include in the message to the buyer."}, "coverageEndDate": {"type": "string", "format": "date-time", "description": "The end date of the warranty coverage to include in the message to the buyer."}}, "description": "The request schema for the createWarranty operation."}, "CreateWarrantyResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createWarranty operation."}, "GetAttributesResponse": {"type": "object", "properties": {"buyer": {"type": "object", "description": "The list of attributes related to the buyer.", "properties": {"locale": {"type": "string", "description": "The buyer's language of preference, indicated with a locale-specific language tag. Examples: \"en-US\", \"zh-CN\", and \"en-GB\"."}}}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the GetAttributes operation."}, "CreateDigitalAccessKeyRequest": {"type": "object", "properties": {"text": {"type": "string", "description": "The text to be sent to the buyer. Only links related to the digital access key are allowed. Do not include HTML or email addresses. The text must be written in the buyer's language of preference, which can be retrieved from the GetAttributes operation.", "minLength": 1, "maxLength": 400}, "attachments": {"type": "array", "description": "Attachments to include in the message to the buyer.", "maxLength": 5, "items": {"$ref": "#/definitions/Attachment"}}}, "description": "The request schema for the createDigitalAccessKey operation."}, "CreateDigitalAccessKeyResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createDigitalAccessKey operation."}, "CreateUnexpectedProblemRequest": {"type": "object", "properties": {"text": {"type": "string", "description": "The text to be sent to the buyer. Only links related to unexpected problem calls are allowed. Do not include HTML or email addresses. The text must be written in the buyer's language of preference, which can be retrieved from the GetAttributes operation.", "minLength": 1, "maxLength": 2000}}, "description": "The request schema for the createUnexpectedProblem operation."}, "CreateUnexpectedProblemResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createUnexpectedProblem operation."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}}}