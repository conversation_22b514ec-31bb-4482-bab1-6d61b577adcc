{"swagger": "2.0", "info": {"description": "Effective **June 27, 2024**, the Selling Partner API for Feeds v2020-09-04 will no longer be available and all calls to it will fail. Integrations that rely on the Feeds API must migrate to [Feeds v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/feeds-api-v2021-06-30-reference) to avoid service disruption.", "version": "2020-09-04", "title": "Selling Partner API for Feeds", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/feeds/2020-09-04/feeds": {"get": {"tags": ["feeds"], "description": "Effective June 27, 2023, the `getFeeds` operation will no longer be available in the Selling Partner API for Feeds v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Feeds v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/feeds-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "getFeeds", "deprecated": true, "parameters": [{"name": "feedTypes", "in": "query", "description": "A list of feed types used to filter feeds. When feedTypes is provided, the other filter parameters (processingStatuses, marketplaceIds, createdSince, createdUntil) and pageSize may also be provided. Either feedTypes or nextToken is required.", "required": false, "type": "array", "items": {"type": "string"}, "maxItems": 10, "minItems": 1}, {"name": "marketplaceIds", "in": "query", "description": "A list of marketplace identifiers used to filter feeds. The feeds returned will match at least one of the marketplaces that you specify.", "required": false, "type": "array", "items": {"type": "string"}, "maxItems": 10, "minItems": 1}, {"name": "pageSize", "in": "query", "description": "The maximum number of feeds to return in a single call.", "required": false, "type": "integer", "default": 10, "maximum": 100, "minimum": 1}, {"name": "processingStatuses", "in": "query", "description": "A list of processing statuses used to filter feeds.", "required": false, "type": "array", "items": {"type": "string", "enum": ["CANCELLED", "DONE", "FATAL", "IN_PROGRESS", "IN_QUEUE"], "x-docgen-enum-table-extension": [{"value": "CANCELLED", "description": "The feed was cancelled before it started processing."}, {"value": "DONE", "description": "The feed has completed processing. Examine the contents of the result document to determine if there were any errors during processing."}, {"value": "FATAL", "description": "The feed was aborted due to a fatal error. Some, none or all of the operations within the feed may have completed successfully."}, {"value": "IN_PROGRESS", "description": "The feed is being processed."}, {"value": "IN_QUEUE", "description": "The feed has not yet started processing. It may be waiting for another IN_PROGRESS feed."}]}, "minItems": 1}, {"name": "createdSince", "in": "query", "description": "The earliest feed creation date and time for feeds included in the response, in ISO 8601 format. The default is 90 days ago. Feeds are retained for a maximum of 90 days.", "required": false, "type": "string", "format": "date-time"}, {"name": "createdUntil", "in": "query", "description": "The latest feed creation date and time for feeds included in the response, in ISO 8601 format. The default is now.", "required": false, "type": "string", "format": "date-time"}, {"name": "nextToken", "in": "query", "description": "A string token returned in the response to your previous request. nextToken is returned when the number of results exceeds the specified pageSize value. To get the next page of results, call the getFeeds operation and include this token as the only parameter. Specifying nextToken with any other parameters will cause the request to fail.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetFeedsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"feedTypes": {"value": ["POST_PRODUCT_DATA"]}, "pageSize": {"value": 10}, "processingStatuses": {"value": ["CANCELLED", "DONE"]}}}, "response": {"payload": [{"feedId": "FeedId1", "feedType": "POST_PRODUCT_DATA", "createdTime": "2019-12-11T13:16:24.630Z", "processingStatus": "CANCELLED", "processingStartTime": "2019-12-11T13:16:24.630Z", "processingEndTime": "2019-12-11T13:16:24.630Z"}], "nextToken": "VGhpcyB0b2tlbiBpcyBvcGFxdWUgYW5kIGludGVudGlvbmFsbHkgb2JmdXNjYXRlZA=="}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetFeedsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"feedTypes": {"value": ["POST_PRODUCT_DATA"]}, "processingStatuses": {"value": ["BAD_VALUE", "DONE"]}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Dates were not provided"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetFeedsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetFeedsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetFeedsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/GetFeedsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetFeedsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetFeedsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetFeedsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}}}, "post": {"tags": ["feeds"], "description": "Effective June 27, 2023, the `createFeed` operation will no longer be available in the Selling Partner API for Feeds v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Feeds v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/feeds-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "createFeed", "deprecated": true, "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateFeedSpecification"}}], "responses": {"202": {"description": "Success.", "schema": {"$ref": "#/definitions/CreateFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"feedType": "POST_PRODUCT_DATA", "marketplaceIds": ["ATVPDKIKX0DER", "A1F83G8C2ARO7P"], "inputFeedDocumentId": "3d4e42b5-1d6e-44e8-a89c-2abfca0625bb"}}}}, "response": {"payload": {"feedId": "3485934"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"marketplaceIds": ["ATVPDKIKX0DER", "A1F83G8C2ARO7P"], "inputFeedDocumentId": "badDocumentId"}}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/CreateFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateFeedResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/CreateFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/CreateFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}}}}, "/feeds/2020-09-04/feeds/{feedId}": {"get": {"tags": ["feeds"], "description": "Effective June 27, 2023, the `getFeed` operation will no longer be available in the Selling Partner API for Feeds v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Feeds v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/feeds-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "getFeed", "deprecated": true, "parameters": [{"name": "feedId", "in": "path", "description": "The identifier for the feed. This identifier is unique only in combination with a seller ID.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"feedId": {"value": "feedId1"}}}, "response": {"payload": {"feedId": "FeedId1", "feedType": "POST_PRODUCT_DATA", "createdTime": "2019-12-11T13:16:24.630Z", "processingStatus": "CANCELLED", "processingStartTime": "2019-12-11T13:16:24.630Z", "processingEndTime": "2019-12-11T13:16:24.630Z"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"feedId": {"value": "badFeedId1"}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetFeedResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/GetFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}}}, "delete": {"tags": ["feeds"], "description": "Effective June 27, 2023, the `cancelFeed` operation will no longer be available in the Selling Partner API for Feeds v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Feeds v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/feeds-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "cancelFeed", "deprecated": true, "parameters": [{"name": "feedId", "in": "path", "description": "The identifier for the feed. This identifier is unique only in combination with a seller ID.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CancelFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"feedId": {"value": "ID1"}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CancelFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"feedId": {"value": "BADID1"}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/CancelFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CancelFeedResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/CancelFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/CancelFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CancelFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CancelFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CancelFeedResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}}}}, "/feeds/2020-09-04/documents": {"post": {"tags": ["feeds"], "description": "Effective June 27, 2023, the `createFeedDocument` operation will no longer be available in the Selling Partner API for Feeds v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Feeds v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/feeds-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "createFeedDocument", "deprecated": true, "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateFeedDocumentSpecification"}}], "responses": {"201": {"description": "Successfully created a feed document that is ready to receive contents.", "schema": {"$ref": "#/definitions/CreateFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"contentType": "text/tab-separated-values; charset=UTF-8"}}}}, "response": {"payload": {"feedDocumentId": "3d4e42b5-1d6e-44e8-a89c-2abfca0625bb", "url": "https://d34o8swod1owfl.cloudfront.net/Feed_101__POST_PRODUCT_DATA_%2BKEY%3DFeed_101%2BMode%3DCBC%2BINITVEC%3D8f+6c+cc+56+0d+50+a2+d0+31+ec+80+be+f2+6a+1d+0a", "encryptionDetails": {"standard": "AES", "initializationVector": "8f 6c cc 56 0d 50 a2 d0 31 ec 80 be f2 6a 1d 0a", "key": "key"}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {}}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateFeedDocumentResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}}}}, "/feeds/2020-09-04/documents/{feedDocumentId}": {"get": {"tags": ["feeds"], "description": "Effective June 27, 2023, the `getFeedDocument` operation will no longer be available in the Selling Partner API for Feeds v2020-09-04 and all calls to it will fail. Integrations that rely on this operation should migrate to [Feeds v2021-06-30](https://developer-docs.amazon.com/sp-api/docs/feeds-api-v2021-06-30-reference) to avoid service disruption.", "operationId": "getFeedDocument", "deprecated": true, "parameters": [{"name": "feedDocumentId", "in": "path", "description": "The identifier of the feed document.", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"feedDocumentId": {"value": "0356cf79-b8b0-4226-b4b9-0ee058ea5760"}}}, "response": {"payload": {"feedDocumentId": "0356cf79-b8b0-4226-b4b9-0ee058ea5760", "url": "https://d34o8swod1owfl.cloudfront.net/SampleResult%2BKey%3DSample%2BINITVEC%3D58+fa+bf+a7+08+11+95+0f+c1+a8+c6+e0+d5+6f+ae+c8", "encryptionDetails": {"standard": "AES", "initializationVector": "58 fa bf a7 08 11 95 0f c1 a8 c6 e0 d5 6f ae c8", "key": "<PERSON><PERSON>"}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"feedDocumentId": {"value": "badDocumentId1"}}}, "response": {"errors": [{"code": "400", "message": "Invalid input", "details": "Invalid input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetFeedDocumentResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/GetFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/GetFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetFeedDocumentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}}}}}}, "definitions": {"Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition in a human-readable form."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "An error response returned when the request is unsuccessful."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "CancelFeedResponse": {"type": "object", "properties": {"errors": {"$ref": "#/definitions/ErrorList"}}, "description": "Response schema."}, "CreateFeedResult": {"type": "object", "required": ["feedId"], "properties": {"feedId": {"type": "string", "description": "The identifier for the feed. This identifier is unique only in combination with a seller ID."}}}, "Feed": {"type": "object", "required": ["createdTime", "feedId", "feedType", "processingStatus"], "properties": {"feedId": {"type": "string", "description": "The identifier for the feed. This identifier is unique only in combination with a seller ID."}, "feedType": {"type": "string", "description": "The feed type."}, "marketplaceIds": {"type": "array", "description": "A list of identifiers for the marketplaces that the feed is applied to.", "items": {"type": "string"}}, "createdTime": {"type": "string", "format": "date-time", "description": "The date and time when the feed was created, in ISO 8601 date time format."}, "processingStatus": {"type": "string", "description": "The processing status of the feed.", "enum": ["CANCELLED", "DONE", "FATAL", "IN_PROGRESS", "IN_QUEUE"], "x-docgen-enum-table-extension": [{"value": "CANCELLED", "description": "The feed was cancelled before it started processing."}, {"value": "DONE", "description": "The feed has completed processing. Examine the contents of the result document to determine if there were any errors during processing."}, {"value": "FATAL", "description": "The feed was aborted due to a fatal error. Some, none, or all of the operations within the feed may have completed successfully."}, {"value": "IN_PROGRESS", "description": "The feed is being processed."}, {"value": "IN_QUEUE", "description": "The feed has not yet started processing. It may be waiting for another IN_PROGRESS feed."}]}, "processingStartTime": {"type": "string", "format": "date-time", "description": "The date and time when feed processing started, in ISO 8601 date time format."}, "processingEndTime": {"type": "string", "format": "date-time", "description": "The date and time when feed processing completed, in ISO 8601 date time format."}, "resultFeedDocumentId": {"type": "string", "description": "The identifier for the feed document. This identifier is unique only in combination with a seller ID."}}}, "FeedList": {"type": "array", "items": {"$ref": "#/definitions/Feed"}}, "GetFeedsResponse": {"type": "object", "properties": {"payload": {"$ref": "#/definitions/FeedList"}, "nextToken": {"type": "string", "description": "Returned when the number of results exceeds pageSize. To get the next page of results, call the getFeeds operation with this token as the only parameter."}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "Response schema."}, "GetFeedResponse": {"type": "object", "properties": {"payload": {"$ref": "#/definitions/Feed"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "Response schema."}, "FeedDocumentEncryptionDetails": {"type": "object", "required": ["initializationVector", "key", "standard"], "properties": {"standard": {"type": "string", "description": "The encryption standard required to encrypt or decrypt the document contents.", "enum": ["AES"], "x-docgen-enum-table-extension": [{"value": "AES", "description": "The Advanced Encryption Standard (AES)."}]}, "initializationVector": {"type": "string", "description": "The vector to encrypt or decrypt the document contents using Cipher Block Chaining (CBC)."}, "key": {"type": "string", "description": "The encryption key used to encrypt or decrypt the document contents."}}, "description": "Encryption details for required client-side encryption and decryption of document contents."}, "FeedDocument": {"type": "object", "required": ["encryptionDetails", "feedDocumentId", "url"], "properties": {"feedDocumentId": {"type": "string", "description": "The identifier for the feed document. This identifier is unique only in combination with a seller ID."}, "url": {"type": "string", "description": "A presigned URL for the feed document. If `compressionAlgorithm` is not returned, you can download the feed directly from this URL. This URL expires after 5 minutes."}, "encryptionDetails": {"$ref": "#/definitions/FeedDocumentEncryptionDetails"}, "compressionAlgorithm": {"type": "string", "description": "If the feed document contents have been compressed, the compression algorithm used is returned in this property and you must decompress the feed when you download. Otherwise, you can download the feed directly. Refer to [Step 6. Download and decrypt the feed processing report](doc:feeds-api-v2020-09-04-use-case-guide#step-6-download-and-decrypt-the-feed-processing-report) in the use case guide, where sample code is provided.", "enum": ["GZIP"], "x-docgen-enum-table-extension": [{"value": "GZIP", "description": "The gzip compression algorithm."}]}}}, "GetFeedDocumentResponse": {"type": "object", "properties": {"payload": {"$ref": "#/definitions/FeedDocument"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "Response schema."}, "CreateFeedResponse": {"type": "object", "properties": {"payload": {"$ref": "#/definitions/CreateFeedResult"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "Response schema."}, "FeedOptions": {"type": "object", "description": "Additional options to control the feed. For feeds that use the feedOptions parameter, you can find the parameter values in the feed description in [feedType values](https://github.com/amzn/selling-partner-api-docs/blob/main/references/feeds-api/feedtype-values.md).", "additionalProperties": {"type": "string"}}, "CreateFeedSpecification": {"type": "object", "required": ["feedType", "inputFeedDocumentId", "marketplaceIds"], "properties": {"feedType": {"type": "string", "description": "The feed type."}, "marketplaceIds": {"type": "array", "description": "A list of identifiers for marketplaces that you want the feed to be applied to.", "items": {"type": "string"}, "maxItems": 25, "minItems": 1}, "inputFeedDocumentId": {"type": "string", "description": "The document identifier returned by the createFeedDocument operation. Encrypt and upload the feed document contents before calling the createFeed operation."}, "feedOptions": {"$ref": "#/definitions/FeedOptions"}}}, "CreateFeedDocumentSpecification": {"type": "object", "required": ["contentType"], "properties": {"contentType": {"type": "string", "description": "The content type of the feed."}}}, "CreateFeedDocumentResponse": {"type": "object", "properties": {"payload": {"$ref": "#/definitions/CreateFeedDocumentResult"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response for the createFeedDocument operation."}, "CreateFeedDocumentResult": {"type": "object", "required": ["encryptionDetails", "feedDocumentId", "url"], "properties": {"feedDocumentId": {"type": "string", "description": "The identifier of the feed document."}, "url": {"type": "string", "description": "The presigned URL for uploading the feed contents. This URL expires after 5 minutes."}, "encryptionDetails": {"$ref": "#/definitions/FeedDocumentEncryptionDetails"}}, "description": "Information required to encrypt and upload a feed document's contents."}}}