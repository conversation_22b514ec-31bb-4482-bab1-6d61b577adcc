{"swagger": "2.0", "info": {"description": "Provides programmatic access to Amazon Shipping APIs.\n\n **Note:** If you are new to the Amazon Shipping API, refer to the latest version of <a href=\"https://developer-docs.amazon.com/amazon-shipping/docs/shipping-api-v2-reference\">Amazon Shipping API (v2)</a> on the <a href=\"https://developer-docs.amazon.com/amazon-shipping/\">Amazon Shipping Developer Documentation</a> site.", "version": "v1", "title": "Selling Partner API for Shipping", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/shipping/v1/shipments": {"post": {"tags": ["shipping"], "description": "Create a new shipment.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 15 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createShipment", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/CreateShipmentRequest", "example": {"clientReferenceId": "911-7267646-6348616", "shipFrom": {"name": "test name 1", "addressLine1": "some Test address 1", "postalCode": "90013", "city": "Los Angeles", "countryCode": "US", "stateOrRegion": "CA", "email": "<EMAIL>", "phoneNumber": "1234567890"}, "shipTo": {"name": "test name 2", "addressLine1": "some Test address 2", "postalCode": "90013-1805", "city": "LOS ANGELES", "countryCode": "US", "stateOrRegion": "CA", "email": "<EMAIL>", "phoneNumber": "1234567890"}, "containers": [{"containerType": "PACKAGE", "containerReferenceId": "ContainerRefId-01", "items": [{"title": "String", "quantity": 2, "unitPrice": {"unit": "USD", "value": 14.99}, "unitWeight": {"unit": "lb", "value": 0.********}}], "dimensions": {"height": 12, "length": 36, "width": 15, "unit": "CM"}, "weight": {"unit": "lb", "value": 0.********}, "value": {"unit": "USD", "value": 29.98}}]}}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CreateShipmentResponse"}, "examples": {"application/json": {"shipmentId": "**************", "eligibleRates": [{"billedWeight": {"value": 4, "unit": "kg"}, "totalCharge": {"value": 3.25, "unit": "GBP"}, "serviceType": "Amazon Shipping Standard", "promise": {"deliveryWindow": {"start": "2018-08-25T20:22:30.737Z", "end": "2018-08-26T20:22:30.737Z"}, "receiveWindow": {"start": "2018-08-23T09:22:30.737Z", "end": "2018-08-23T11:22:30.737Z"}}, "rateId": "RI123456", "expirationTime": "2018-08-22T09:22:30.737Z"}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"clientReferenceId": "TEST_CASE_200"}}}}, "response": {"payload": {"shipmentId": "TEST_CASE_200", "eligibleRates": [{"billedWeight": {"value": 4, "unit": "kg"}, "totalCharge": {"value": 3.25, "unit": "GBP"}, "serviceType": "Amazon Shipping Standard", "promise": {"deliveryWindow": {"start": "2018-08-25T20:22:30.737Z", "end": "2018-08-26T20:22:30.737Z"}, "receiveWindow": {"start": "2018-08-23T09:22:30.737Z", "end": "2018-08-23T11:22:30.737Z"}}, "rateId": "RI123456", "expirationTime": "2018-08-22T09:22:30.737Z"}]}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"clientReferenceId": "TEST_CASE_400"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid input."}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/CreateShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"clientReferenceId": "TEST_CASE_401"}}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/CreateShipmentResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"clientReferenceId": "TEST_CASE_403"}}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"clientReferenceId": "TEST_CASE_404"}}}}, "response": {"errors": [{"code": "NotFound", "message": "The requested resource doesn't exist."}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"clientReferenceId": "TEST_CASE_429"}}}}, "response": {"errors": [{"code": "Quo<PERSON>Exceeded", "message": "You exceeded your quota for the requested resource."}]}}]}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"clientReferenceId": "TEST_CASE_500"}}}}, "response": {"errors": [{"code": "InternalFailure", "message": "We encountered an internal error. Please try again."}]}}]}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"clientReferenceId": "TEST_CASE_503"}}}}, "response": {"errors": [{"code": "ServiceUnavailable", "message": "Service is temporarily unavailable. Please try again."}]}}]}}}}}, "/shipping/v1/shipments/{shipmentId}": {"get": {"tags": ["shipping"], "description": "Return the entire shipment object for the shipmentId.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 15 |\n\nFor more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "getShipment", "parameters": [{"name": "shipmentId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetShipmentResponse"}, "examples": {"application/json": {"shipmentId": "**************", "clientReferenceId": "911-7267646-6348616", "shipFrom": {}, "shipTo": {}, "acceptedRate": {"billedWeight": {"value": 4, "unit": "kg"}, "totalCharge": {"value": 3.5, "unit": "GBP"}, "serviceType": "Amazon Shipping Standard", "promise": {"deliveryWindow": {"start": "2018-08-25T20:22:30.737Z", "end": "2018-08-26T20:22:30.737Z"}, "receiveWindow": {"start": "2018-08-23T09:22:30.737Z", "end": "2018-08-23T11:22:30.737Z"}}}, "shipper": {"accountId": "**********"}, "containers": [{"containerReferenceId": "CRI123456789", "items": [{"title": "String", "unitWeight": {"value": 0.********, "unit": "kg"}, "quantity": 2, "unitPrice": {"value": 14.99, "unit": "GBP"}}], "dimensions": {"height": 12.0, "length": 36.0, "width": 31.0, "unit": "CM"}, "containerType": "PACKAGE", "weight": {"unit": "kg", "value": 4}, "value": {"value": 29.98, "unit": "GBP"}}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_200"}}}, "response": {"payload": {"shipmentId": "TEST_CASE_200", "clientReferenceId": "911-7267646-6348616", "shipFrom": {}, "shipTo": {}, "acceptedRate": {"billedWeight": {"value": 4, "unit": "kg"}, "totalCharge": {"value": 3.5, "unit": "GBP"}, "serviceType": "Amazon Shipping Standard", "promise": {"deliveryWindow": {"start": "2018-08-25T20:22:30.737Z", "end": "2018-08-26T20:22:30.737Z"}, "receiveWindow": {"start": "2018-08-23T09:22:30.737Z", "end": "2018-08-23T11:22:30.737Z"}}}, "shipper": {"accountId": "**********"}, "containers": [{"containerReferenceId": "CRI123456789", "items": [{"title": "String", "unitWeight": {"value": 0.********, "unit": "kg"}, "quantity": 2, "unitPrice": {"value": 14.99, "unit": "GBP"}}], "dimensions": {"height": 12.0, "length": 36.0, "width": 31.0, "unit": "CM"}, "containerType": "PACKAGE", "weight": {"unit": "kg", "value": 4}, "value": {"value": 29.98, "unit": "GBP"}}]}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_400"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid input."}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_401"}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/GetShipmentResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_403"}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_404"}}}, "response": {"errors": [{"code": "NotFound", "message": "The requested resource doesn't exist."}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_429"}}}, "response": {"errors": [{"code": "Quo<PERSON>Exceeded", "message": "You exceeded your quota for the requested resource."}]}}]}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_500"}}}, "response": {"errors": [{"code": "InternalFailure", "message": "We encountered an internal error. Please try again."}]}}]}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_503"}}}, "response": {"errors": [{"code": "ServiceUnavailable", "message": "Service is temporarily unavailable. Please try again."}]}}]}}}}}, "/shipping/v1/shipments/{shipmentId}/cancel": {"post": {"tags": ["shipping"], "description": "Cancel a shipment by the given shipmentId.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 15 |\n\nFor more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "cancelShipment", "parameters": [{"name": "shipmentId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CancelShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_200"}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CancelShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_400"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid input."}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/CancelShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_401"}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/CancelShipmentResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_403"}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CancelShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_404"}}}, "response": {"errors": [{"code": "NotFound", "message": "The requested resource doesn't exist."}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CancelShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_429"}}}, "response": {"errors": [{"code": "Quo<PERSON>Exceeded", "message": "You exceeded your quota for the requested resource."}]}}]}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CancelShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_500"}}}, "response": {"errors": [{"code": "InternalFailure", "message": "We encountered an internal error. Please try again."}]}}]}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CancelShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_503"}}}, "response": {"errors": [{"code": "ServiceUnavailable", "message": "Service is temporarily unavailable. Please try again."}]}}]}}}}}, "/shipping/v1/shipments/{shipmentId}/purchaseLabels": {"post": {"tags": ["shipping"], "description": "Purchase shipping labels based on a given rate.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 15 |\n\nFor more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "purchaseLabels", "parameters": [{"name": "shipmentId", "in": "path", "required": true, "type": "string"}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/PurchaseLabelsRequest", "example": {"rateId": "rate identifier", "labelSpecification": {"labelFormat": "PNG", "labelStockSize": "4x6"}}}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/PurchaseLabelsResponse"}, "examples": {"application/json": {"shipmentId": "**************", "clientReferenceId": "911-7267646-6348616", "acceptedRate": {"billedWeight": {"value": 4, "unit": "kg"}, "totalCharge": {"value": 3.5, "unit": "GBP"}, "serviceType": "Amazon Shipping Standard", "promise": {"deliveryWindow": {"start": "2018-08-25T20:22:30.737Z", "end": "2018-08-26T20:22:30.737Z"}, "receiveWindow": {"start": "2018-08-23T09:22:30.737Z", "end": "2018-08-23T11:22:30.737Z"}}}, "labelResults": [{"containerReferenceId": "CRI123456789", "trackingId": "1512748795322", "label": {"labelStream": "iVBORw0KGgo...AAAARK5CYII=(Truncated)", "labelSpecification": {"labelFormat": "PNG", "labelStockSize": "4x6"}}}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_200"}}}, "response": {"payload": {"shipmentId": "TEST_CASE_200", "clientReferenceId": "911-7267646-6348616", "acceptedRate": {"billedWeight": {"value": 4, "unit": "kg"}, "totalCharge": {"value": 3.5, "unit": "GBP"}, "serviceType": "Amazon Shipping Standard", "promise": {"deliveryWindow": {"start": "2018-08-25T20:22:30.737Z", "end": "2018-08-26T20:22:30.737Z"}, "receiveWindow": {"start": "2018-08-23T09:22:30.737Z", "end": "2018-08-23T11:22:30.737Z"}}}, "labelResults": [{"containerReferenceId": "CRI123456789", "trackingId": "1512748795322", "label": {"labelStream": "iVBORw0KGgo...AAAARK5CYII=(Truncated)", "labelSpecification": {"labelFormat": "PNG", "labelStockSize": "4x6"}}}]}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/PurchaseLabelsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_400"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid input."}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/PurchaseLabelsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_401"}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/PurchaseLabelsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_403"}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/PurchaseLabelsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_404"}}}, "response": {"errors": [{"code": "NotFound", "message": "The requested resource doesn't exist."}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/PurchaseLabelsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_429"}}}, "response": {"errors": [{"code": "Quo<PERSON>Exceeded", "message": "You exceeded your quota for the requested resource."}]}}]}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/PurchaseLabelsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_500"}}}, "response": {"errors": [{"code": "InternalFailure", "message": "We encountered an internal error. Please try again."}]}}]}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/PurchaseLabelsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_503"}}}, "response": {"errors": [{"code": "ServiceUnavailable", "message": "Service is temporarily unavailable. Please try again."}]}}]}}}}}, "/shipping/v1/shipments/{shipmentId}/containers/{trackingId}/label": {"post": {"tags": ["shipping"], "description": "Retrieve shipping label based on the shipment id and tracking id.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 15 |\n\nFor more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "retrieveShipping<PERSON>abel", "parameters": [{"name": "shipmentId", "in": "path", "required": true, "type": "string"}, {"name": "trackingId", "in": "path", "required": true, "type": "string"}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/RetrieveShippingLabelRequest", "example": {"labelSpecification": {"labelFormat": "PNG", "labelStockSize": "4x6"}}}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/RetrieveShippingLabelResponse"}, "examples": {"application/json": {"labelStream": "iVBORw0KGgo...AAAARK5CYII=(Truncated)", "labelSpecification": {"labelFormat": "PNG", "labelStockSize": "4x6"}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_200"}}}, "response": {"payload": {"labelStream": "iVBORw0KGgo...AAAARK5CYII=(Truncated)", "labelSpecification": {"labelFormat": "PNG", "labelStockSize": "4x6"}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/RetrieveShippingLabelResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_400"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid input."}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/RetrieveShippingLabelResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_401"}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/RetrieveShippingLabelResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_403"}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/RetrieveShippingLabelResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_404"}}}, "response": {"errors": [{"code": "NotFound", "message": "The requested resource doesn't exist."}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/RetrieveShippingLabelResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_429"}}}, "response": {"errors": [{"code": "Quo<PERSON>Exceeded", "message": "You exceeded your quota for the requested resource."}]}}]}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/RetrieveShippingLabelResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_500"}}}, "response": {"errors": [{"code": "InternalFailure", "message": "We encountered an internal error. Please try again."}]}}]}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/RetrieveShippingLabelResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"shipmentId": {"value": "TEST_CASE_503"}}}, "response": {"errors": [{"code": "ServiceUnavailable", "message": "Service is temporarily unavailable. Please try again."}]}}]}}}}}, "/shipping/v1/purchaseShipment": {"post": {"tags": ["shipping"], "description": "Purchase shipping labels.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 15 |\n\nFor more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "purchaseShipment", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/PurchaseShipmentRequest", "example": {"clientReferenceId": "911-7267646-6348616", "shipFrom": {"name": "test name 1", "addressLine1": "some Test address 1", "postalCode": "90013", "city": "Los Angeles", "countryCode": "US", "stateOrRegion": "CA", "email": "<EMAIL>", "phoneNumber": "1234567890"}, "shipTo": {"name": "test name 2", "addressLine1": "some Test address 2", "postalCode": "90013", "city": "Los Angeles", "countryCode": "US", "stateOrRegion": "CA", "email": "<EMAIL>", "phoneNumber": "1234567890"}, "containers": [{"containerType": "PACKAGE", "containerReferenceId": "ContainerRefId-01", "items": [{"title": "String", "quantity": 2, "unitPrice": {"unit": "USD", "value": 14.99}, "unitWeight": {"unit": "lb", "value": 0.********}}], "dimensions": {"height": 12, "length": 36, "width": 15, "unit": "CM"}, "weight": {"unit": "lb", "value": 0.********}, "value": {"unit": "USD", "value": 29.98}}], "labelSpecification": {"labelFormat": "PNG", "labelStockSize": "4x6"}, "serviceType": "Amazon Shipping Standard"}}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/PurchaseShipmentResponse"}, "examples": {"application/json": {"shipmentId": "**************", "serviceRate": {"billableWeight": {"value": 4, "unit": "kg"}, "totalCharge": {"value": 3.5, "unit": "GBP"}, "serviceType": "Amazon Shipping Standard", "promise": {"deliveryWindow": {"start": "2018-08-25T20:22:30.737Z", "end": "2018-08-26T20:22:30.737Z"}, "receiveWindow": {"start": "2018-08-23T09:22:30.737Z", "end": "2018-08-23T11:22:30.737Z"}}}, "labelResults": [{"containerReferenceId": "CRI123456789", "trackingId": "1512748795322", "label": {"labelStream": "iVBORw0KGgo...AAAARK5CYII=(Truncated)", "labelSpecification": {"labelFormat": "PNG", "labelStockSize": "4x6"}}}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"clientReferenceId": "TEST_CASE_200"}}}}, "response": {"payload": {"shipmentId": "TEST_CASE_200", "serviceRate": {"billableWeight": {"value": 4, "unit": "kg"}, "totalCharge": {"value": 3.5, "unit": "GBP"}, "serviceType": "Amazon Shipping Standard", "promise": {"deliveryWindow": {"start": "2018-08-25T20:22:30.737Z", "end": "2018-08-26T20:22:30.737Z"}, "receiveWindow": {"start": "2018-08-23T09:22:30.737Z", "end": "2018-08-23T11:22:30.737Z"}}}, "labelResults": [{"containerReferenceId": "CRI123456789", "trackingId": "1512748795322", "label": {"labelStream": "iVBORw0KGgo...AAAARK5CYII=(Truncated)", "labelSpecification": {"labelFormat": "PNG", "labelStockSize": "4x6"}}}]}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/PurchaseShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/PurchaseShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/PurchaseShipmentResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/PurchaseShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/PurchaseShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/PurchaseShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/PurchaseShipmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}}}}, "/shipping/v1/rates": {"post": {"tags": ["shipping"], "description": "Get service rates.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 15 |\n\nFor more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "getRates", "parameters": [{"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/GetRatesRequest", "example": {"shipFrom": {"name": "test name 1", "addressLine1": "some Test address 1", "postalCode": "90013", "city": "Los Angeles", "countryCode": "US", "stateOrRegion": "CA"}, "shipTo": {"name": "test name 2", "addressLine1": "some Test address 2", "postalCode": "90013", "city": "Los Angeles", "countryCode": "US", "stateOrRegion": "CA"}, "containerSpecifications": [{"dimensions": {"height": 12, "length": 36, "width": 15, "unit": "CM"}, "weight": {"unit": "lb", "value": 0.********}}], "serviceTypes": ["Amazon Shipping Standard"]}}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetRatesResponse"}, "examples": {"application/json": {"serviceRates": [{"billableWeight": {"value": 4, "unit": "kg"}, "totalCharge": {"value": 3.25, "unit": "GBP"}, "serviceType": "Amazon Shipping Standard", "promise": {"deliveryWindow": {"start": "2018-08-25T20:22:30.737Z", "end": "2018-08-26T20:22:30.737Z"}, "receiveWindow": {"start": "2018-08-23T09:22:30.737Z", "end": "2018-08-23T11:22:30.737Z"}}}]}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": {"serviceRates": [{"billableWeight": {"value": 4, "unit": "kg"}, "totalCharge": {"value": 3.25, "unit": "GBP"}, "serviceType": "Amazon Shipping Standard", "promise": {"deliveryWindow": {"start": "2018-08-25T20:22:30.737Z", "end": "2018-08-26T20:22:30.737Z"}, "receiveWindow": {"start": "2018-08-23T09:22:30.737Z", "end": "2018-08-23T11:22:30.737Z"}}}]}}}]}}, "400": {"description": "Request is missing or has invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetRatesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetRatesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/GetRatesResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetRatesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetRatesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetRatesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetRatesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}}}}, "/shipping/v1/account": {"get": {"tags": ["shipping"], "description": "Verify if the current account is valid.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 15 |\n\nFor more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "getAccount", "parameters": [], "responses": {"200": {"description": "The account was valid.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "examples": {"application/json": {"accountId": "**********"}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": {"accountId": "**********"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetAccountResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}}}}, "/shipping/v1/tracking/{trackingId}": {"get": {"tags": ["shipping"], "description": "Return the tracking information of a shipment.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 1 |\n\nFor more information, see \"Usage Plans and Rate Limits\" in the Selling Partner API documentation.", "operationId": "getTrackingInformation", "parameters": [{"name": "trackingId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetTrackingInformationResponse"}, "examples": {"application/json": {"trackingId": "**************", "eventHistory": [{"eventCode": "Delivered", "location": {"city": "San Bernardino", "countryCode": "US", "stateOrRegion": "CA", "postalCode": "92404"}, "eventTime": "2019-04-04T06:45:12Z"}], "promisedDeliveryDate": "2019-04-04T07:05:06Z", "summary": {"status": "Delivered"}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"trackingId": {"value": "TEST_CASE_200"}}}, "response": {"payload": {"trackingId": "TEST_CASE_200", "eventHistory": [{"eventCode": "Delivered", "location": {"city": "San Bernardino", "countryCode": "US", "stateOrRegion": "CA", "postalCode": "92404"}, "eventTime": "2019-04-04T06:45:12Z"}], "promisedDeliveryDate": "2019-04-04T07:05:06Z", "summary": {"status": "Delivered"}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetTrackingInformationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"trackingId": {"value": "TEST_CASE_400"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetTrackingInformationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/GetTrackingInformationResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetTrackingInformationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetTrackingInformationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetTrackingInformationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetTrackingInformationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}}}}}, "definitions": {"Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occured."}, "message": {"type": "string", "description": "A message that describes the error condition in a human-readable form."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "AccountId": {"type": "string", "description": "This is the Amazon Shipping account id generated during the Amazon Shipping onboarding process.", "maxLength": 10}, "ShipmentId": {"type": "string", "description": "The unique shipment identifier."}, "ClientReferenceId": {"type": "string", "description": "Client reference id.", "maxLength": 40}, "ContainerReferenceId": {"type": "string", "description": "An identifier for the container. This must be unique within all the containers in the same shipment.", "maxLength": 40}, "EventCode": {"type": "string", "minLength": 1, "maxLength": 60, "description": "The event code of a shipment, such as Departed, Received, and ReadyForReceive."}, "StateOrRegion": {"type": "string", "description": "The state or region where the person, business or institution is located."}, "City": {"type": "string", "minLength": 1, "maxLength": 50, "description": "The city where the person, business or institution is located."}, "CountryCode": {"type": "string", "minLength": 2, "maxLength": 2, "description": "The two digit country code. In ISO 3166-1 alpha-2 format."}, "PostalCode": {"type": "string", "minLength": 1, "maxLength": 20, "description": "The postal code of that address. It contains a series of letters or digits or both, sometimes including spaces or punctuation."}, "Location": {"type": "object", "properties": {"stateOrRegion": {"$ref": "#/definitions/StateOrRegion"}, "city": {"$ref": "#/definitions/City"}, "countryCode": {"$ref": "#/definitions/CountryCode"}, "postalCode": {"$ref": "#/definitions/PostalCode"}}, "description": "The location where the person, business or institution is located."}, "Event": {"type": "object", "required": ["eventCode", "eventTime"], "properties": {"eventCode": {"$ref": "#/definitions/EventCode"}, "eventTime": {"type": "string", "format": "date-time", "description": "The date and time of an event for a shipment."}, "location": {"$ref": "#/definitions/Location"}}, "description": "An event of a shipment"}, "EventList": {"type": "array", "description": "A list of events of a shipment.", "items": {"$ref": "#/definitions/Event"}}, "TrackingId": {"type": "string", "minLength": 1, "maxLength": 60, "description": "The tracking id generated to each shipment. It contains a series of letters or digits or both."}, "TrackingSummary": {"type": "object", "properties": {"status": {"type": "string", "description": "The derived status based on the events in the eventHistory.", "minLength": 1, "maxLength": 60}}, "description": "The tracking summary."}, "PromisedDeliveryDate": {"type": "string", "format": "date-time", "description": "The promised delivery date and time of a shipment."}, "Address": {"type": "object", "required": ["addressLine1", "city", "countryCode", "name", "postalCode", "stateOrRegion"], "properties": {"name": {"type": "string", "description": "The name of the person, business or institution at that address.", "minLength": 1, "maxLength": 50}, "addressLine1": {"type": "string", "description": "First line of that address.", "minLength": 1, "maxLength": 60}, "addressLine2": {"type": "string", "description": "Additional address information, if required.", "minLength": 1, "maxLength": 60}, "addressLine3": {"type": "string", "description": "Additional address information, if required.", "minLength": 1, "maxLength": 60}, "stateOrRegion": {"$ref": "#/definitions/StateOrRegion"}, "city": {"$ref": "#/definitions/City"}, "countryCode": {"$ref": "#/definitions/CountryCode"}, "postalCode": {"$ref": "#/definitions/PostalCode"}, "email": {"type": "string", "description": "The email address of the contact associated with the address.", "maxLength": 64}, "copyEmails": {"type": "array", "description": "The email cc addresses of the contact associated with the address.", "items": {"type": "string", "maxLength": 64}, "maxItems": 2}, "phoneNumber": {"type": "string", "description": "The phone number of the person, business or institution located at that address.", "minLength": 1, "maxLength": 20}}, "description": "The address."}, "TimeRange": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time", "description": "The start date and time. This defaults to the current date and time."}, "end": {"type": "string", "format": "date-time", "description": "The end date and time. This must come after the value of start. This defaults to the next business day from the start."}}, "description": "The time range."}, "ShippingPromiseSet": {"type": "object", "properties": {"deliveryWindow": {"description": "The time window in which the shipment will be delivered.", "$ref": "#/definitions/TimeRange"}, "receiveWindow": {"description": "The time window in which Amazon Shipping will pick up the shipment.", "$ref": "#/definitions/TimeRange"}}, "description": "The promised delivery time and pickup time."}, "ServiceType": {"type": "string", "description": "The type of shipping service that will be used for the service offering.", "enum": ["Amazon Shipping Ground", "Amazon Shipping Standard", "Amazon Shipping Premium"], "x-docgen-enum-table-extension": [{"value": "Amazon Shipping Ground", "description": "Amazon Shipping Ground."}, {"value": "Amazon Shipping Standard", "description": "Amazon Shipping Standard."}, {"value": "Amazon Shipping Premium", "description": "Amazon Shipping Premium."}]}, "ServiceTypeList": {"type": "array", "description": "A list of service types that can be used to send the shipment.", "items": {"$ref": "#/definitions/ServiceType"}}, "Rate": {"type": "object", "properties": {"rateId": {"type": "string", "description": "An identifier for the rate."}, "totalCharge": {"description": "The total charge that will be billed for the rate.", "$ref": "#/definitions/Currency"}, "billedWeight": {"description": "The weight that was used to calculate the totalCharge.", "$ref": "#/definitions/Weight"}, "expirationTime": {"type": "string", "format": "date-time", "description": "The time after which the offering will expire."}, "serviceType": {"$ref": "#/definitions/ServiceType"}, "promise": {"$ref": "#/definitions/ShippingPromiseSet"}}, "description": "The available rate that can be used to send the shipment"}, "RateList": {"type": "array", "description": "A list of all the available rates that can be used to send the shipment.", "items": {"$ref": "#/definitions/Rate"}}, "RateId": {"type": "string", "description": "An identifier for the rating."}, "AcceptedRate": {"type": "object", "properties": {"totalCharge": {"description": "The total charge that will be billed for the rate.", "$ref": "#/definitions/Currency"}, "billedWeight": {"description": "The weight that was used to calculate the totalCharge.", "$ref": "#/definitions/Weight"}, "serviceType": {"$ref": "#/definitions/ServiceType"}, "promise": {"$ref": "#/definitions/ShippingPromiseSet"}}, "description": "The specific rate purchased for the shipment, or null if unpurchased."}, "ServiceRate": {"type": "object", "required": ["billableWeight", "promise", "serviceType", "totalCharge"], "properties": {"totalCharge": {"description": "The total charge that will be billed for the rate.", "$ref": "#/definitions/Currency"}, "billableWeight": {"description": "The weight that was used to calculate the totalCharge.", "$ref": "#/definitions/Weight"}, "serviceType": {"$ref": "#/definitions/ServiceType"}, "promise": {"$ref": "#/definitions/ShippingPromiseSet"}}, "description": "The specific rate for a shipping service, or null if no service available."}, "ServiceRateList": {"type": "array", "description": "A list of service rates.", "items": {"$ref": "#/definitions/ServiceRate"}}, "Party": {"type": "object", "properties": {"accountId": {"$ref": "#/definitions/AccountId"}}, "description": "The account related with the shipment."}, "Currency": {"type": "object", "required": ["unit", "value"], "properties": {"value": {"type": "number", "description": "The amount of currency."}, "unit": {"type": "string", "description": "A 3-character currency code.", "minLength": 3, "maxLength": 3}}, "description": "The total value of all items in the container."}, "Dimensions": {"type": "object", "required": ["height", "length", "unit", "width"], "properties": {"length": {"type": "number", "description": "The length of the container."}, "width": {"type": "number", "description": "The width of the container."}, "height": {"type": "number", "description": "The height of the container."}, "unit": {"type": "string", "description": "The unit of these measurements.", "enum": ["IN", "CM"], "x-docgen-enum-table-extension": [{"value": "IN", "description": "Inches"}, {"value": "CM", "description": "Centimeters"}]}}, "description": "A set of measurements for a three-dimensional object."}, "Weight": {"type": "object", "required": ["unit", "value"], "properties": {"unit": {"type": "string", "description": "The unit of measurement.", "enum": ["g", "kg", "oz", "lb"], "x-docgen-enum-table-extension": [{"value": "g", "description": "Grams"}, {"value": "kg", "description": "Kilograms"}, {"value": "oz", "description": "<PERSON><PERSON><PERSON>"}, {"value": "lb", "description": "Pounds"}]}, "value": {"type": "number", "description": "The measurement value."}}, "description": "The weight."}, "ContainerItem": {"type": "object", "required": ["quantity", "title", "unitPrice", "unitWeight"], "properties": {"quantity": {"type": "number", "description": "The quantity of the items of this type in the container."}, "unitPrice": {"description": "The unit price of an item of this type (the total value of this item type in the container is unitPrice * quantity).", "$ref": "#/definitions/Currency"}, "unitWeight": {"description": "The unit weight of an item of this type (the total weight of this item type in the container is unitWeight * quantity).", "$ref": "#/definitions/Weight"}, "title": {"type": "string", "description": "A descriptive title of the item.", "maxLength": 30}}, "description": "Item in the container."}, "Container": {"type": "object", "required": ["containerReferenceId", "dimensions", "items", "value", "weight"], "properties": {"containerType": {"type": "string", "description": "The type of physical container being used. (always 'PACKAGE')", "enum": ["PACKAGE"], "x-docgen-enum-table-extension": [{"value": "PACKAGE", "description": "PACKAGE"}]}, "containerReferenceId": {"$ref": "#/definitions/ContainerReferenceId"}, "value": {"description": "The total value of all items in the container.", "$ref": "#/definitions/Currency"}, "dimensions": {"description": "The length, width, height, and weight of the container.", "$ref": "#/definitions/Dimensions"}, "items": {"type": "array", "description": "A list of the items in the container.", "items": {"$ref": "#/definitions/ContainerItem"}}, "weight": {"description": "The weight of the container.", "$ref": "#/definitions/Weight"}}, "description": "Container in the shipment."}, "ContainerList": {"type": "array", "description": "A list of container.", "items": {"$ref": "#/definitions/Container"}}, "ContainerSpecification": {"type": "object", "required": ["dimensions", "weight"], "properties": {"dimensions": {"description": "The length, width, and height of the container.", "$ref": "#/definitions/Dimensions"}, "weight": {"description": "The weight of the container.", "$ref": "#/definitions/Weight"}}, "description": "Container specification for checking the service rate."}, "ContainerSpecificationList": {"type": "array", "description": "A list of container specifications.", "items": {"$ref": "#/definitions/ContainerSpecification"}}, "Label": {"type": "object", "properties": {"labelStream": {"$ref": "#/definitions/LabelStream"}, "labelSpecification": {"$ref": "#/definitions/LabelSpecification"}}, "description": "The label details of the container."}, "LabelResult": {"type": "object", "properties": {"containerReferenceId": {"$ref": "#/definitions/ContainerReferenceId"}, "trackingId": {"type": "string", "description": "The tracking identifier assigned to the container."}, "label": {"$ref": "#/definitions/Label"}}, "description": "Label details including label stream, format, size."}, "LabelResultList": {"type": "array", "description": "A list of label results", "items": {"$ref": "#/definitions/LabelResult"}}, "LabelStream": {"type": "string", "description": "Contains binary image data encoded as a base-64 string."}, "LabelSpecification": {"type": "object", "required": ["labelFormat", "labelStockSize"], "properties": {"labelFormat": {"type": "string", "description": "The format of the label. Enum of PNG only for now.", "enum": ["PNG"], "x-docgen-enum-table-extension": [{"value": "PNG", "description": "PNG"}]}, "labelStockSize": {"type": "string", "description": "The label stock size specification in length and height. Enum of 4x6 only for now.", "enum": ["4x6"], "x-docgen-enum-table-extension": [{"value": "4x6", "description": "4x6"}]}}, "description": "The label specification info."}, "CreateShipmentRequest": {"type": "object", "required": ["clientReferenceId", "containers", "shipFrom", "shipTo"], "properties": {"clientReferenceId": {"$ref": "#/definitions/ClientReferenceId"}, "shipTo": {"$ref": "#/definitions/Address"}, "shipFrom": {"$ref": "#/definitions/Address"}, "containers": {"$ref": "#/definitions/ContainerList"}}, "description": "The request schema for the createShipment operation."}, "PurchaseLabelsRequest": {"type": "object", "required": ["labelSpecification", "rateId"], "properties": {"rateId": {"$ref": "#/definitions/RateId"}, "labelSpecification": {"$ref": "#/definitions/LabelSpecification"}}, "description": "The request schema for the purchaseLabels operation."}, "RetrieveShippingLabelRequest": {"type": "object", "required": ["labelSpecification"], "properties": {"labelSpecification": {"$ref": "#/definitions/LabelSpecification"}}, "description": "The request schema for the retrieveShippingLabel operation."}, "GetRatesRequest": {"type": "object", "required": ["containerSpecifications", "serviceTypes", "shipFrom", "shipTo"], "properties": {"shipTo": {"$ref": "#/definitions/Address"}, "shipFrom": {"$ref": "#/definitions/Address"}, "serviceTypes": {"$ref": "#/definitions/ServiceTypeList"}, "shipDate": {"type": "string", "format": "date-time", "description": "The start date and time. This defaults to the current date and time."}, "containerSpecifications": {"$ref": "#/definitions/ContainerSpecificationList"}}, "description": "The payload schema for the getRates operation."}, "PurchaseShipmentRequest": {"type": "object", "required": ["clientReferenceId", "containers", "labelSpecification", "serviceType", "shipFrom", "shipTo"], "properties": {"clientReferenceId": {"$ref": "#/definitions/ClientReferenceId"}, "shipTo": {"$ref": "#/definitions/Address"}, "shipFrom": {"$ref": "#/definitions/Address"}, "shipDate": {"type": "string", "format": "date-time", "description": "The start date and time. This defaults to the current date and time."}, "serviceType": {"$ref": "#/definitions/ServiceType"}, "containers": {"$ref": "#/definitions/ContainerList"}, "labelSpecification": {"$ref": "#/definitions/LabelSpecification"}}, "description": "The payload schema for the purchaseShipment operation."}, "CreateShipmentResult": {"type": "object", "required": ["eligibleRates", "shipmentId"], "properties": {"shipmentId": {"$ref": "#/definitions/ShipmentId"}, "eligibleRates": {"$ref": "#/definitions/RateList"}}, "description": "The payload schema for the createShipment operation."}, "Shipment": {"type": "object", "required": ["clientReferenceId", "containers", "shipFrom", "shipTo", "shipmentId"], "properties": {"shipmentId": {"$ref": "#/definitions/ShipmentId"}, "clientReferenceId": {"$ref": "#/definitions/ClientReferenceId"}, "shipFrom": {"$ref": "#/definitions/Address"}, "shipTo": {"$ref": "#/definitions/Address"}, "acceptedRate": {"$ref": "#/definitions/AcceptedRate"}, "shipper": {"$ref": "#/definitions/Party"}, "containers": {"$ref": "#/definitions/ContainerList"}}, "description": "The shipment related data."}, "PurchaseLabelsResult": {"type": "object", "required": ["acceptedRate", "labelResults", "shipmentId"], "properties": {"shipmentId": {"$ref": "#/definitions/ShipmentId"}, "clientReferenceId": {"$ref": "#/definitions/ClientReferenceId"}, "acceptedRate": {"$ref": "#/definitions/AcceptedRate"}, "labelResults": {"$ref": "#/definitions/LabelResultList"}}, "description": "The payload schema for the purchaseLabels operation."}, "RetrieveShippingLabelResult": {"type": "object", "required": ["labelSpecification", "labelStream"], "properties": {"labelStream": {"$ref": "#/definitions/LabelStream"}, "labelSpecification": {"$ref": "#/definitions/LabelSpecification"}}, "description": "The payload schema for the retrieveShippingLabel operation."}, "Account": {"type": "object", "required": ["accountId"], "properties": {"accountId": {"$ref": "#/definitions/AccountId"}}, "description": "The account related data."}, "GetRatesResult": {"type": "object", "required": ["serviceRates"], "properties": {"serviceRates": {"$ref": "#/definitions/ServiceRateList"}}, "description": "The payload schema for the getRates operation."}, "PurchaseShipmentResult": {"type": "object", "required": ["labelResults", "serviceRate", "shipmentId"], "properties": {"shipmentId": {"$ref": "#/definitions/ShipmentId"}, "serviceRate": {"$ref": "#/definitions/ServiceRate"}, "labelResults": {"$ref": "#/definitions/LabelResultList"}}, "description": "The payload schema for the purchaseShipment operation."}, "TrackingInformation": {"type": "object", "required": ["eventHistory", "promisedDeliveryDate", "summary", "trackingId"], "properties": {"trackingId": {"$ref": "#/definitions/TrackingId"}, "summary": {"$ref": "#/definitions/TrackingSummary"}, "promisedDeliveryDate": {"$ref": "#/definitions/PromisedDeliveryDate"}, "eventHistory": {"$ref": "#/definitions/EventList"}}, "description": "The payload schema for the getTrackingInformation operation."}, "CreateShipmentResponse": {"type": "object", "properties": {"payload": {"description": "The payload for createShipment operation", "$ref": "#/definitions/CreateShipmentResult"}, "errors": {"description": "Encountered errors for the operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the createShipment operation."}, "GetShipmentResponse": {"type": "object", "properties": {"payload": {"description": "The payload for getShipment operation", "$ref": "#/definitions/Shipment"}, "errors": {"description": "Encountered errors for the operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the getShipment operation."}, "GetRatesResponse": {"type": "object", "properties": {"payload": {"description": "The payload for getRates operation", "$ref": "#/definitions/GetRatesResult"}, "errors": {"description": "Encountered errors for the operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the getRates operation."}, "PurchaseShipmentResponse": {"type": "object", "properties": {"payload": {"description": "The payload for purchaseShipment operation", "$ref": "#/definitions/PurchaseShipmentResult"}, "errors": {"description": "Encountered errors for the operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the purchaseShipment operation."}, "CancelShipmentResponse": {"type": "object", "properties": {"errors": {"description": "Encountered errors for the operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the cancelShipment operation."}, "PurchaseLabelsResponse": {"type": "object", "properties": {"payload": {"description": "The payload for purchaseLabels operation", "$ref": "#/definitions/PurchaseLabelsResult"}, "errors": {"description": "Encountered errors for the operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the purchaseLabels operation."}, "RetrieveShippingLabelResponse": {"type": "object", "properties": {"payload": {"description": "The payload for retrieveShippingLabel operation", "$ref": "#/definitions/RetrieveShippingLabelResult"}, "errors": {"description": "Encountered errors for the operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the retrieveShippingLabel operation."}, "GetAccountResponse": {"type": "object", "properties": {"payload": {"description": "The payload for getAccount operation", "$ref": "#/definitions/Account"}, "errors": {"description": "Encountered errors for the operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the getAccount operation."}, "GetTrackingInformationResponse": {"type": "object", "properties": {"payload": {"description": "The payload for getTrackingInformation operation", "$ref": "#/definitions/TrackingInformation"}, "errors": {"description": "Encountered errors for the operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the getTrackingInformation operation."}}}