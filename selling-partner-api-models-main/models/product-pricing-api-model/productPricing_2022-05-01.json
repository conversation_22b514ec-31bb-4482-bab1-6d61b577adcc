{"swagger": "2.0", "info": {"description": "The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer pricing information for Amazon Marketplace products.\n\nFor more information, refer to the [Product Pricing v2022-05-01 Use Case Guide](https://developer-docs.amazon.com/sp-api/docs/product-pricing-api-v2022-05-01-use-case-guide).", "version": "2022-05-01", "title": "Selling Partner API for Pricing", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/batches/products/pricing/2022-05-01/offer/featuredOfferExpectedPrice": {"post": {"tags": ["productPricing"], "description": "Returns the set of responses that correspond to the batched list of up to 40 requests defined in the request body. The response for each successful (HTTP status code 200) request in the set includes the computed listing price at or below which a seller can expect to become the featured offer (before applicable promotions). This is called the featured offer expected price (FOEP). Featured offer is not guaranteed, because competing offers may change, and different offers may be featured based on other factors, including fulfillment capabilities to a specific customer. The response to an unsuccessful request includes the available error text.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.033 | 1 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getFeaturedOfferExpectedPriceBatch", "parameters": [{"name": "getFeaturedOfferExpectedPriceBatchRequestBody", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GetFeaturedOfferExpectedPriceBatchRequest"}, "description": "The batch of `getFeaturedOfferExpectedPrice` requests."}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetFeaturedOfferExpectedPriceBatchResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "examples": {"application/json": {"responses": [{"request": {"marketplaceId": "MARKETPLACE_ID", "sku": "MY_SKU"}, "status": {"statusCode": 200, "reasonPhrase": "Success"}, "headers": {}, "body": {"offerIdentifier": {"asin": "ASIN", "sku": "MY_SKU", "marketplaceId": "MARKETPLACE_ID", "fulfillmentType": "AFN", "sellerId": "MY_SELLER_ID"}, "featuredOfferExpectedPriceResults": [{"featuredOfferExpectedPrice": {"listingPrice": {"amount": 10.0, "currencyCode": "USD"}, "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}}, "resultStatus": "VALID_FOEP", "competingFeaturedOffer": {"offerIdentifier": {"asin": "ASIN", "marketplaceId": "MARKETPLACE_ID", "fulfillmentType": "AFN", "sellerId": "OTHER_SELLER_ID"}, "condition": "New", "price": {"listingPrice": {"amount": 12.0, "currencyCode": "USD"}, "shippingPrice": {"amount": 0, "currencyCode": "USD"}, "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}}}, "currentFeaturedOffer": {"offerIdentifier": {"asin": "ASIN", "marketplaceId": "MARKETPLACE_ID", "fulfillmentType": "AFN", "sellerId": "OTHER_SELLER_ID"}, "condition": "New", "price": {"listingPrice": {"amount": 12.0, "currencyCode": "USD"}, "shippingPrice": {"amount": 0, "currencyCode": "USD"}, "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}}}}]}}, {"request": {"marketplaceId": "MARKETPLACE_ID", "sku": "MY_UNIQUE_SKU"}, "status": {"statusCode": 200, "reasonPhrase": "Success"}, "headers": {}, "body": {"offerIdentifier": {"asin": "ASIN", "sku": "MY_UNIQUE_SKU", "marketplaceId": "MARKETPLACE_ID", "fulfillmentType": "AFN", "sellerId": "MY_SELLER_ID"}, "featuredOfferExpectedPriceResults": [{"resultStatus": "NO_COMPETING_OFFERS", "currentFeaturedOffer": {"offerIdentifier": {"asin": "ASIN", "marketplaceId": "MARKETPLACE_ID", "fulfillmentType": "AFN", "sellerId": "MY_SELLER_ID"}, "condition": "New", "price": {"listingPrice": {"amount": 12.0, "currencyCode": "USD"}, "shippingPrice": {"amount": 0, "currencyCode": "USD"}, "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}}}}]}}, {"request": {"marketplaceId": "MARKETPLACE_ID", "sku": "MY_NONEXISTENT_SKU"}, "status": {"statusCode": 400, "reasonPhrase": "<PERSON><PERSON>"}, "headers": {}, "body": {"errors": [{"code": "INVALID_SKU", "message": "The requested SKU does not exist for the seller in the requested marketplace."}]}}]}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"requests": [{"marketplaceId": "MARKETPLACE_ID", "sku": "MY_SKU", "method": "GET", "uri": "/products/pricing/2022-05-01/offer/featuredOfferExpectedPrice"}, {"marketplaceId": "MARKETPLACE_ID", "sku": "MY_UNIQUE_SKU", "method": "GET", "uri": "/products/pricing/2022-05-01/offer/featuredOfferExpectedPrice"}, {"marketplaceId": "MARKETPLACE_ID", "sku": "INVALID_SKU", "method": "GET", "uri": "/products/pricing/2022-05-01/offer/featuredOfferExpectedPrice"}]}}}}, "response": {"responses": [{"request": {"marketplaceId": "MARKETPLACE_ID", "sku": "MY_SKU"}, "status": {"statusCode": 200, "reasonPhrase": "Success"}, "headers": {}, "body": {"offerIdentifier": {"asin": "ASIN", "sku": "MY_SKU", "marketplaceId": "MARKETPLACE_ID", "fulfillmentType": "AFN", "sellerId": "MY_SELLER_ID"}, "featuredOfferExpectedPriceResults": [{"featuredOfferExpectedPrice": {"listingPrice": {"amount": 10.0, "currencyCode": "USD"}, "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}}, "resultStatus": "VALID_FOEP", "competingFeaturedOffer": {"offerIdentifier": {"asin": "ASIN", "marketplaceId": "MARKETPLACE_ID", "fulfillmentType": "AFN", "sellerId": "OTHER_SELLER_ID"}, "condition": "New", "price": {"listingPrice": {"amount": 12.0, "currencyCode": "USD"}, "shippingPrice": {"amount": 0, "currencyCode": "USD"}, "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}}}, "currentFeaturedOffer": {"offerIdentifier": {"asin": "ASIN", "marketplaceId": "MARKETPLACE_ID", "fulfillmentType": "AFN", "sellerId": "OTHER_SELLER_ID"}, "condition": "New", "price": {"listingPrice": {"amount": 12.0, "currencyCode": "USD"}, "shippingPrice": {"amount": 0, "currencyCode": "USD"}, "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}}}}]}}, {"request": {"marketplaceId": "MARKETPLACE_ID", "sku": "MY_UNIQUE_SKU"}, "status": {"statusCode": 200, "reasonPhrase": "Success"}, "headers": {}, "body": {"offerIdentifier": {"asin": "ASIN", "sku": "MY_UNIQUE_SKU", "marketplaceId": "MARKETPLACE_ID", "fulfillmentType": "AFN", "sellerId": "MY_SELLER_ID"}, "featuredOfferExpectedPriceResults": [{"resultStatus": "NO_COMPETING_OFFERS", "currentFeaturedOffer": {"offerIdentifier": {"asin": "ASIN", "marketplaceId": "MARKETPLACE_ID", "fulfillmentType": "AFN", "sellerId": "MY_SELLER_ID"}, "condition": "New", "price": {"listingPrice": {"amount": 12.0, "currencyCode": "USD"}, "shippingPrice": {"amount": 0, "currencyCode": "USD"}, "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}}}}]}}, {"request": {"marketplaceId": "MARKETPLACE_ID", "sku": "MY_NONEXISTENT_SKU"}, "status": {"statusCode": 400, "reasonPhrase": "<PERSON><PERSON>"}, "headers": {}, "body": {"errors": [{"code": "INVALID_SKU", "message": "The requested SKU does not exist for the seller in the requested marketplace."}]}}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"requests": [{"marketplaceId": "MARKETPLACE_ID", "sku": "INVALID_SKU", "method": "GET", "uri": "/products/pricing/2022-05-01/offer/featuredOfferExpectedPrice"}]}}}}, "response": {"errors": [{"code": "INVALID_SKU", "message": "The requested SKU does not exist for the seller in the requested marketplace."}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/batches/products/pricing/2022-05-01/items/competitiveSummary": {"post": {"tags": ["productPricing"], "description": "Returns the competitive summary response including featured buying options for the ASIN and `marketplaceId` combination.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.033 | 1 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getCompetitiveSummary", "parameters": [{"name": "requests", "in": "body", "description": "The batch of `getCompetitiveSummary` requests.", "required": true, "schema": {"$ref": "#/definitions/CompetitiveSummaryBatchRequest"}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CompetitiveSummaryBatchResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "examples": {"application/json": {"responses": [{"status": {"statusCode": 200, "reasonPhrase": "Success"}, "body": {"asin": "B00ZIAODGE", "marketplaceId": "ATVPDKIKX0DER", "featuredBuyingOptions": [{"buyingOptionType": "New", "segmentedFeaturedOffers": [{"sellerId": "A3DJR8M9Y3OUPG", "condition": "New", "fulfillmentType": "MFN", "listingPrice": {"amount": 18.11, "currencyCode": "USD"}, "shippingOptions": [{"shippingOptionType": "DEFAULT", "price": {"amount": 2.5, "currencyCode": "USD"}}], "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}, "featuredOfferSegments": [{"customerMembership": "PRIME", "segmentDetails": {"glanceViewWeightPercentage": 72}}, {"customerMembership": "NON_PRIME", "segmentDetails": {"glanceViewWeightPercentage": 18}}]}, {"sellerId": "A2ZWOLFOFDPJL1", "condition": "New", "fulfillmentType": "MFN", "listingPrice": {"amount": 17.15, "currencyCode": "USD"}, "shippingOptions": [{"shippingOptionType": "DEFAULT", "price": {"amount": 2.5, "currencyCode": "USD"}}], "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}, "featuredOfferSegments": [{"customerMembership": "NON_PRIME", "segmentDetails": {"glanceViewWeightPercentage": 10}}]}]}], "referencePrices": [{"name": "CompetitivePriceThreshold", "price": {"amount": 18.11, "currencyCode": "USD"}}], "lowestPricedOffers": [{"lowestPricedOffersInput": {"itemCondition": "New", "offerType": "Consumer"}, "offers": [{"listingPrice": {"currencyCode": "USD", "amount": 17.15}, "shippingOptions": [{"shippingOptionType": "DEFAULT", "price": {"amount": 2.5, "currencyCode": "USD"}}], "points": {"pointsMonetaryValue": {"amount": 0.5, "currencyCode": "USD"}, "pointsNumber": 50}, "primeDetails": {"eligibility": "REGIONAL"}, "subCondition": "New", "sellerId": "A2ZWOLFOFDPJL1", "fulfillmentType": "MFN"}, {"listingPrice": {"amount": 18.11, "currencyCode": "USD"}, "shippingOptions": [{"shippingOptionType": "DEFAULT", "price": {"amount": 2.5, "currencyCode": "USD"}}], "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}, "primeDetails": {"eligibility": "NATIONAL"}, "sellerId": "A3DJR8M9Y3OUPG", "subCondition": "New", "fulfillmentType": "MFN"}]}, {"lowestPricedOffersInput": {"itemCondition": "Used", "offerType": "Consumer"}, "offers": [{"listingPrice": {"CurrencyCode": "USD", "Amount": 12.0}, "shippingOptions": [{"shippingOptionType": "DEFAULT", "price": {"amount": 2.5, "currencyCode": "USD"}}], "primeDetails": {"eligibility": "REGIONAL"}, "subCondition": "Acceptable", "sellerId": "A3DH5AGPM3JVAB", "fulfillmentType": "AFN"}]}]}}]}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"requests": [{"asin": "B00ZIAODGE", "marketplaceId": "ATVPDKIKX0DER", "includedData": ["featuredBuyingOptions", "referencePrices", "lowestPricedOffers"], "lowestPricedOffersInputs": [{"itemCondition": "New", "offerType": "Consumer"}, {"itemCondition": "Used", "offerType": "Consumer"}], "uri": "/products/pricing/2022-05-01/items/competitiveSummary", "method": "GET"}, {"asin": "11_AABB_123", "marketplaceId": "ATVPDKIKX0DER", "includedData": ["featuredBuyingOptions"], "uri": "/products/pricing/2022-05-01/items/competitiveSummary", "method": "GET"}]}}}}, "response": {"responses": [{"status": {"statusCode": 200, "reasonPhrase": "Success"}, "body": {"asin": "B00ZIAODGE", "marketplaceId": "ATVPDKIKX0DER", "featuredBuyingOptions": [{"buyingOptionType": "New", "segmentedFeaturedOffers": [{"sellerId": "A3DJR8M9Y3OUPG", "condition": "New", "fulfillmentType": "MFN", "listingPrice": {"amount": 18.11, "currencyCode": "USD"}, "shippingOptions": [{"shippingOptionType": "DEFAULT", "price": {"amount": 2.5, "currencyCode": "USD"}}], "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}, "featuredOfferSegments": [{"customerMembership": "NON_PRIME", "segmentDetails": {"glanceViewWeightPercentage": 72}}, {"customerMembership": "PRIME", "segmentDetails": {"glanceViewWeightPercentage": 10}}]}, {"sellerId": "A2ZWOLFOFDPJL1", "condition": "New", "fulfillmentType": "MFN", "listingPrice": {"amount": 17.15, "currencyCode": "USD"}, "shippingOptions": [{"shippingOptionType": "DEFAULT", "price": {"amount": 2.5, "currencyCode": "USD"}}], "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}, "featuredOfferSegments": [{"customerMembership": "NON_PRIME", "segmentDetails": {"glanceViewWeightPercentage": 18}}]}]}], "referencePrices": [{"name": "CompetitivePriceThreshold", "price": {"amount": 18.11, "currencyCode": "USD"}}], "lowestPricedOffers": [{"lowestPricedOffersInput": {"itemCondition": "New", "offerType": "Consumer"}, "offers": [{"listingPrice": {"currencyCode": "USD", "amount": 17.15}, "shippingOptions": [{"shippingOptionType": "DEFAULT", "price": {"amount": 2.5, "currencyCode": "USD"}}], "points": {"pointsMonetaryValue": {"amount": 0.5, "currencyCode": "USD"}, "pointsNumber": 50}, "primeDetails": {"eligibility": "REGIONAL"}, "subCondition": "New", "sellerId": "A2ZWOLFOFDPJL1", "fulfillmentType": "MFN"}, {"listingPrice": {"amount": 18.11, "currencyCode": "USD"}, "shippingOptions": [{"shippingOptionType": "DEFAULT", "price": {"amount": 2.5, "currencyCode": "USD"}}], "points": {"pointsNumber": 3, "pointsMonetaryValue": {"amount": 0.03, "currencyCode": "USD"}}, "primeDetails": {"eligibility": "NATIONAL"}, "sellerId": "A3DJR8M9Y3OUPG", "subCondition": "New", "fulfillmentType": "MFN"}]}, {"lowestPricedOffersInput": {"itemCondition": "Used", "offerType": "Consumer"}, "offers": [{"listingPrice": {"currencyCode": "USD", "amount": 12.0}, "shippingOptions": [{"shippingOptionType": "DEFAULT", "price": {"amount": 2.5, "currencyCode": "USD"}}], "primeDetails": {"eligibility": "REGIONAL"}, "subCondition": "Acceptable", "sellerId": "A3DH5AGPM3JVAB", "fulfillmentType": "AFN"}]}]}}, {"status": {"statusCode": 400, "reasonPhrase": "<PERSON><PERSON>"}, "body": {"asin": "11_AABB_123", "marketplaceId": "ATVPDKIKX0DER", "errors": [{"code": "INVALID_ASIN", "message": "11_AABB_123 is not a valid ASIN"}]}}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/Errors"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"requests": [{"asin": "11_AABB_123", "marketplaceId": "ATVPDKIKX0DER", "includedData": ["featuredBuyingOptions"], "uri": "/products/pricing/2022-05-01/items/competitiveSummary", "method": "GET"}]}}}}, "response": {"errors": [{"code": "INVALID_ASIN", "message": "11_AABB_123 is not a valid ASIN"}]}}]}}, "403": {"description": "Indicates access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/Errors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}}, "definitions": {"GetFeaturedOfferExpectedPriceBatchRequest": {"description": "The request body for the `getFeaturedOfferExpectedPriceBatch` operation.", "type": "object", "properties": {"requests": {"$ref": "#/definitions/FeaturedOfferExpectedPriceRequestList"}}}, "FeaturedOfferExpectedPriceRequestList": {"description": "A batched list of featured offer expected price requests.", "type": "array", "items": {"$ref": "#/definitions/FeaturedOfferExpectedPriceRequest"}, "minItems": 1}, "FeaturedOfferExpectedPriceRequest": {"description": "An individual featured offer expected price request for a particular SKU.", "allOf": [{"$ref": "#/definitions/BatchRequest"}, {"$ref": "#/definitions/FeaturedOfferExpectedPriceRequestParams"}]}, "FeaturedOfferExpectedPriceRequestParams": {"description": "The parameters for an individual request.", "type": "object", "required": ["marketplaceId", "sku"], "properties": {"marketplaceId": {"$ref": "#/definitions/MarketplaceId"}, "sku": {"$ref": "#/definitions/Sku"}}}, "GetFeaturedOfferExpectedPriceBatchResponse": {"description": "The response schema for the `getFeaturedOfferExpectedPriceBatch` operation.", "type": "object", "properties": {"responses": {"$ref": "#/definitions/FeaturedOfferExpectedPriceResponseList"}}}, "FeaturedOfferExpectedPriceResponseList": {"description": "A batched list of featured offer expected price responses.", "type": "array", "items": {"$ref": "#/definitions/FeaturedOfferExpectedPriceResponse"}, "minItems": 1}, "FeaturedOfferExpectedPriceResponse": {"allOf": [{"$ref": "#/definitions/BatchResponse"}, {"type": "object", "required": ["request"], "properties": {"request": {"$ref": "#/definitions/FeaturedOfferExpectedPriceRequestParams", "description": "Use these request parameters to map the response back to the request."}, "body": {"$ref": "#/definitions/FeaturedOfferExpectedPriceResponseBody"}}}], "description": "Schema for an individual featured offer expected price response."}, "CompetitiveSummaryBatchRequest": {"description": "The `competitiveSummary` batch request data.", "type": "object", "required": ["requests"], "properties": {"requests": {"description": "A batched list of `competitiveSummary` requests.", "$ref": "#/definitions/CompetitiveSummaryRequestList"}}}, "CompetitiveSummaryRequestList": {"description": "A batched list of `competitiveSummary` requests.", "type": "array", "items": {"$ref": "#/definitions/CompetitiveSummaryRequest"}, "minItems": 1, "maxItems": 20}, "CompetitiveSummaryRequest": {"description": "An individual `competitiveSummary` request for an ASIN and `marketplaceId`.", "type": "object", "required": ["asin", "marketplaceId", "includedData", "method", "uri"], "properties": {"asin": {"description": "The Amazon identifier for the item.", "$ref": "#/definitions/Asin"}, "marketplaceId": {"$ref": "#/definitions/MarketplaceId", "description": "A marketplace identifier."}, "includedData": {"type": "array", "description": "The list of requested competitive pricing data for the product.", "items": {"$ref": "#/definitions/CompetitiveSummaryIncludedData"}, "minItems": 1}, "lowestPricedOffersInputs": {"type": "array", "description": "The list of `lowestPricedOffersInput` parameters used to build the `lowestPricedOffers` in the response. This attribute is valid only if `lowestPricedOffers` is requested in `includedData`.", "items": {"$ref": "#/definitions/LowestPricedOffersInput"}, "minItems": 0, "maxItems": 5}, "method": {"description": "HTTP method type", "$ref": "#/definitions/HttpMethod"}, "uri": {"description": "The URI associated with the individual APIs being called as part of the batch request. For `getCompetitiveSummary`, this should be `/products/pricing/2022-05-01/items/competitiveSummary`.", "$ref": "#/definitions/HttpUri"}}}, "CompetitiveSummaryIncludedData": {"type": "string", "description": "The supported types of data in the `getCompetitiveSummary` API.", "enum": ["featuredBuyingOptions", "referencePrices", "lowestPricedOffers"]}, "LowestPricedOffersInput": {"description": "The input required for building the `LowestPricedOffers` data in the response.", "type": "object", "required": ["itemCondition", "offerType"], "properties": {"itemCondition": {"type": "string", "description": "The condition of the item offer that was requested for the `LowestPricedOffers`. The default `itemCondition` is `New`.", "$ref": "#/definitions/Condition"}, "offerType": {"type": "string", "description": "The type of offers requested for the `LowestPricedOffers`. The `offerType` options are `Consumer` or `Business`. The default `offerType` is `Consumer`.", "enum": ["CONSUMER"]}}}, "CompetitiveSummaryBatchResponse": {"description": "The response schema of the `competitiveSummaryBatch` operation.", "type": "object", "required": ["responses"], "properties": {"responses": {"description": "The response list of the `competitiveSummaryBatch` operation.", "$ref": "#/definitions/CompetitiveSummaryResponseList"}}}, "CompetitiveSummaryResponseList": {"description": "The response list of the `competitiveSummaryBatch` operation.", "type": "array", "items": {"description": "The response for the individual `competitiveSummary` request in the batch operation.", "$ref": "#/definitions/CompetitiveSummaryResponse"}, "minItems": 1, "maxItems": 20}, "CompetitiveSummaryResponse": {"description": "The response for the individual `competitiveSummary` request in the batch operation.", "type": "object", "required": ["status", "body"], "properties": {"status": {"description": "The HTTP status line associated with the response. For more information, refer to [RFC 2616](https://www.w3.org/Protocols/rfc2616/rfc2616-sec6.html).", "$ref": "#/definitions/HttpStatusLine"}, "body": {"description": "The `competitiveSummaryResponse` body for a requested ASIN and `marketplaceId`.", "$ref": "#/definitions/CompetitiveSummaryResponseBody"}}}, "CompetitiveSummaryResponseBody": {"description": "The `competitiveSummaryResponse` body for a requested ASIN and `marketplaceId`.", "type": "object", "required": ["asin", "marketplaceId"], "properties": {"asin": {"description": "The Amazon identifier for the item.", "$ref": "#/definitions/Asin"}, "marketplaceId": {"$ref": "#/definitions/MarketplaceId", "description": "A marketplace identifier."}, "featuredBuyingOptions": {"description": "A list of featured buying options for the given ASIN `marketplaceId` combination.", "type": "array", "items": {"$ref": "#/definitions/FeaturedBuyingOption"}}, "lowestPricedOffers": {"description": "A list of the lowest priced offers for the given ASIN `marketplaceId` combination.", "type": "array", "items": {"$ref": "#/definitions/LowestPricedOffer"}}, "referencePrices": {"description": "A list of reference prices for the given ASIN `marketplaceId` combination.", "type": "array", "items": {"$ref": "#/definitions/ReferencePrice"}}, "errors": {"description": "A list of errors", "$ref": "#/definitions/ErrorList"}}}, "ReferencePrice": {"description": "The reference price for the given ASIN `marketplaceId` combination. ", "type": "object", "required": ["name", "price"], "properties": {"name": {"type": "string", "description": "The name of the reference price like `CompetitivePriceThreshold`."}, "price": {"description": "The reference price for the ASIN `marketplaceId` combination. ", "$ref": "#/definitions/MoneyType"}}}, "FeaturedBuyingOption": {"description": "Describes a featured buying option which includes a list of segmented featured offers for a particular item condition.", "type": "object", "required": ["buyingOptionType", "segmentedFeaturedOffers"], "properties": {"buyingOptionType": {"description": "The buying option type of the featured offer. This field represents the buying options that a customer sees on the detail page. For example, B2B, Fresh, and Subscribe n Save. Currently supports `NEW`", "type": "string", "enum": ["New"], "x-docgen-enum-table-extension": [{"value": "New", "description": "New"}]}, "segmentedFeaturedOffers": {"description": "A list of segmented featured offers for the current buying option type. A segment can be considered as a group of regional contexts that all have the same featured offer. A regional context is a combination of factors such as customer type, region or postal code and buying option.", "type": "array", "items": {"$ref": "#/definitions/SegmentedFeaturedOffer"}, "minItems": 1}}}, "SegmentedFeaturedOffer": {"description": "A product offer with segment information indicating where it's featured.", "allOf": [{"$ref": "#/definitions/Offer"}, {"type": "object", "description": "The list of segment information in which the offer is featured.", "required": ["featuredOfferSegments"], "properties": {"featuredOfferSegments": {"description": "The list of segment information in which the offer is featured.", "type": "array", "items": {"$ref": "#/definitions/FeaturedOfferSegment"}}}}]}, "LowestPricedOffer": {"description": "The lowest priced offer for the requested item condition and offer type.", "type": "object", "required": ["lowestPricedOffersInput", "offers"], "properties": {"lowestPricedOffersInput": {"description": "The filtering criteria used to retrieve this lowest-priced offers correspond to the `lowestPricedOffersInputs` received in the request.", "type": "object", "$ref": "#/definitions/LowestPricedOffersInput"}, "offers": {"description": "A list of up to 20 lowest priced offers that match the criteria specified in the `lowestPricedOffersInput` parameter.", "type": "array", "items": {"$ref": "#/definitions/Offer"}, "minItems": 1, "maxItems": 20}}}, "Offer": {"description": "The offer data of a product.", "type": "object", "required": ["sellerId", "condition", "fulfillmentType", "listingPrice"], "properties": {"sellerId": {"type": "string", "description": "The seller identifier for the offer."}, "condition": {"description": "Item Condition.", "$ref": "#/definitions/Condition"}, "subCondition": {"type": "string", "description": "The item subcondition for the offer.", "enum": ["New", "Mint", "VeryGood", "Good", "Acceptable", "Poor", "Club", "OEM", "Warranty", "RefurbishedWarranty", "Refurbished", "OpenBox", "Other"], "x-docgen-enum-table-extension": [{"value": "New", "description": "New"}, {"value": "Mint", "description": "Mint"}, {"value": "VeryGood", "description": "VeryGood"}, {"value": "Good", "description": "Good"}, {"value": "Acceptable", "description": "Acceptable"}, {"value": "Poor", "description": "Poor"}, {"value": "Club", "description": "Club"}, {"value": "OEM", "description": "OEM"}, {"value": "Warranty", "description": "Warranty"}, {"value": "RefurbishedWarranty", "description": "RefurbishedWarranty"}, {"value": "Refurbished", "description": "Refurbished"}, {"value": "OpenBox", "description": "OpenBox"}, {"value": "Other", "description": "Other"}]}, "fulfillmentType": {"$ref": "#/definitions/FulfillmentType", "description": "The fulfillment type for the offer. Possible values are AFN (Amazon Fulfillment Network) and MFN (Merchant Fulfillment Network)."}, "listingPrice": {"description": "Offer buying price. Does not include shipping, points, or applicable promotions.", "$ref": "#/definitions/MoneyType"}, "shippingOptions": {"description": "A list of shipping options associated with this offer", "type": "array", "items": {"$ref": "#/definitions/ShippingOption"}}, "points": {"description": "The number of Amazon Points offered with the purchase of an item, and their monetary value. Note that the Points element is only returned in Japan (JP).", "$ref": "#/definitions/Points"}, "primeDetails": {"description": "Amazon Prime details.", "$ref": "#/definitions/PrimeDetails"}}}, "PrimeDetails": {"description": "Amazon Prime details.", "type": "object", "required": ["eligibility"], "properties": {"eligibility": {"description": "Indicates whether the offer is an Amazon Prime offer.", "type": "string", "enum": ["NATIONAL", "REGIONAL", "NONE"], "x-docgen-enum-table-extension": [{"value": "NATIONAL", "description": "Indicates that this offer has Prime eligibility throughout the entire marketplace."}, {"value": "REGIONAL", "description": "Indicates that this offer has Prime eligibility in some(but not all) regions in the marketplace."}, {"value": "NONE", "description": "Indicates that this offer is not an Amazon Prime offer in any regions."}]}}}, "ShippingOption": {"description": "The shipping option available for the offer.", "type": "object", "required": ["shippingOptionType", "price"], "properties": {"shippingOptionType": {"description": "The type of the shipping option.", "enum": ["DEFAULT"], "x-docgen-enum-table-extension": [{"value": "DEFAULT", "description": "The estimated shipping cost of the product. Note that the shipping cost is not always available."}]}, "price": {"description": "Shipping price for the offer.", "$ref": "#/definitions/MoneyType"}}}, "FeaturedOfferSegment": {"description": "Describes the segment in which the offer is featured.", "type": "object", "required": ["customerMembership", "segmentDetails"], "properties": {"customerMembership": {"description": "The customer membership type that make up this segment", "type": "string", "enum": ["PRIME", "NON_PRIME"], "x-docgen-enum-table-extension": [{"value": "PRIME", "description": "PRIME"}, {"value": "NON_PRIME", "description": "NON_PRIME"}]}, "segmentDetails": {"description": "The details about the segment.", "$ref": "#/definitions/SegmentDetails"}}}, "SegmentDetails": {"description": "The details about the segment.", "type": "object", "properties": {"glanceViewWeightPercentage": {"description": "Glance view weight percentage for this segment. The glance views for this segment as a percentage of total glance views across all segments on the ASIN. A higher percentage indicates more Amazon customers see this offer as the Featured Offer.", "type": "number"}}}, "Errors": {"type": "object", "description": "A list of error responses returned when a request is unsuccessful.", "required": ["errors"], "properties": {"errors": {"description": "One or more unexpected errors occurred during the operation.", "$ref": "#/definitions/ErrorList"}}}, "FeaturedOfferExpectedPriceResponseBody": {"description": "The featured offer expected price response data for a requested SKU.", "type": "object", "properties": {"offerIdentifier": {"description": "Metadata that identifies the target offer for which the featured offer expected price result data was computed.", "$ref": "#/definitions/OfferIdentifier"}, "featuredOfferExpectedPriceResults": {"description": "The featured offer expected price results for the requested target offer.", "$ref": "#/definitions/FeaturedOfferExpectedPriceResultList"}, "errors": {"description": "The errors that occurred if the operation was not successful (HTTP status code non-200).", "$ref": "#/definitions/ErrorList"}}}, "FeaturedOfferExpectedPriceResultList": {"type": "array", "description": "A list of featured offer expected price results for the requested offer.", "items": {"$ref": "#/definitions/FeaturedOfferExpectedPriceResult"}}, "FeaturedOfferExpectedPriceResult": {"description": "The featured offer expected price result data for the requested offer.", "type": "object", "required": ["resultStatus"], "properties": {"featuredOfferExpectedPrice": {"$ref": "#/definitions/FeaturedOfferExpectedPrice"}, "resultStatus": {"description": "The status of the featured offer expected price computation. Possible values include `VALID_FOEP`, `NO_COMPETING_OFFER`, `OFFER_NOT_ELIGIBLE`, `OFFER_NOT_FOUND`, `ASIN_NOT_ELIGIBLE`. Additional values may be added in the future.", "type": "string"}, "competingFeaturedOffer": {"description": "The offer that will likely be the featured offer if the target offer is priced above its featured offer expected price. If the target offer is currently the featured offer, this property will be different than `currentFeaturedOffer`.", "$ref": "#/definitions/FeaturedOffer"}, "currentFeaturedOffer": {"description": "The offer that is currently the featured offer. If the target offer is not currently featured, then this property will be equal to `competingFeaturedOffer`.", "$ref": "#/definitions/FeaturedOffer"}}}, "FeaturedOfferExpectedPrice": {"description": "The item price at or below which the target offer may be featured.", "type": "object", "required": ["listingPrice"], "properties": {"listingPrice": {"description": "A computed listing price at or below which a seller can expect to become the featured offer (before applicable promotions).", "$ref": "#/definitions/MoneyType"}, "points": {"description": "The number of Amazon Points offered with the purchase of an item, and their monetary value.", "$ref": "#/definitions/Points"}}}, "FeaturedOffer": {"type": "object", "required": ["offerIdentifier"], "properties": {"offerIdentifier": {"description": "An offer identifier used to identify the merchant of the featured offer. Since this may not belong to the requester, the SKU field will be omitted.", "$ref": "#/definitions/OfferIdentifier"}, "condition": {"description": "The item condition.", "$ref": "#/definitions/Condition"}, "price": {"description": "The current active price of the offer.", "$ref": "#/definitions/Price"}}, "description": "Schema for `currentFeaturedOffer` or `competingFeaturedOffer`."}, "HttpHeaders": {"description": "A mapping of additional HTTP headers to send/receive for an individual request within a batch.", "type": "object", "additionalProperties": {"type": "string"}}, "HttpStatusLine": {"description": "The HTTP status line associated with the response to an individual request within a batch. For more information, consult [RFC 2616](https://www.w3.org/Protocols/rfc2616/rfc2616-sec6.html).", "type": "object", "properties": {"statusCode": {"description": "The HTTP response Status-Code.", "type": "integer", "minimum": 100, "maximum": 599}, "reasonPhrase": {"description": "The HTTP response Reason-Phase.", "type": "string"}}}, "HttpBody": {"description": "Additional HTTP body information associated with an individual request within a batch.", "additionalProperties": {}}, "HttpUri": {"description": "The URI associated with the individual APIs being called as part of the batch request.", "type": "string", "minLength": 6, "maxLength": 512}, "HttpMethod": {"description": "The HTTP method associated with an individual request within a batch.", "type": "string", "enum": ["GET", "PUT", "PATCH", "DELETE", "POST"], "x-docgen-enum-table-extension": [{"value": "GET", "description": "GET"}, {"value": "PUT", "description": "PUT"}, {"value": "PATCH", "description": "PATCH"}, {"value": "DELETE", "description": "DELETE"}, {"value": "POST", "description": "POST"}]}, "BatchRequest": {"description": "The common properties for individual requests within a batch.", "type": "object", "required": ["uri", "method"], "properties": {"uri": {"description": "The URI associated with an individual request within a batch. For `FeaturedOfferExpectedPrice`, this should be `/products/pricing/2022-05-01/offer/featuredOfferExpectedPrice`.", "type": "string"}, "method": {"$ref": "#/definitions/HttpMethod"}, "body": {"$ref": "#/definitions/HttpBody"}, "headers": {"$ref": "#/definitions/HttpHeaders"}}}, "BatchResponse": {"description": "The common properties for responses to individual requests within a batch.", "type": "object", "required": ["status", "headers"], "properties": {"headers": {"$ref": "#/definitions/HttpHeaders"}, "status": {"$ref": "#/definitions/HttpStatusLine"}}}, "OfferIdentifier": {"description": "Identifies an offer from a particular seller on an ASIN.", "type": "object", "required": ["marketplaceId", "asin"], "properties": {"marketplaceId": {"$ref": "#/definitions/MarketplaceId", "description": "A marketplace identifier."}, "sellerId": {"type": "string", "description": "The seller identifier for the offer."}, "sku": {"type": "string", "description": "The seller stock keeping unit (SKU) of the item. This will only be present for the target offer, which belongs to the requesting seller."}, "asin": {"$ref": "#/definitions/Asin", "description": "The Amazon identifier for the item."}, "fulfillmentType": {"$ref": "#/definitions/FulfillmentType", "description": "The fulfillment type for the offer."}}}, "MoneyType": {"type": "object", "properties": {"currencyCode": {"type": "string", "description": "The currency code in ISO 4217 format."}, "amount": {"type": "number", "description": "The monetary value."}}, "description": "Currency type and monetary value. Schema for demonstrating pricing info."}, "Price": {"type": "object", "required": ["listingPrice"], "properties": {"listingPrice": {"description": "The listing price of the item excluding any promotions.", "$ref": "#/definitions/MoneyType"}, "shippingPrice": {"description": "The shipping cost of the product. Note that the shipping cost is not always available.", "$ref": "#/definitions/MoneyType"}, "points": {"description": "The number of Amazon Points offered with the purchase of an item, and their monetary value.", "$ref": "#/definitions/Points"}}, "description": "Schema for item's price information, including listing price, shipping price, and Amazon points."}, "Points": {"type": "object", "properties": {"pointsNumber": {"type": "integer", "format": "int32", "description": "The number of points."}, "pointsMonetaryValue": {"description": "The monetary value of the points.", "$ref": "#/definitions/MoneyType"}}, "description": "The number of Amazon Points offered with the purchase of an item, and their monetary value."}, "FulfillmentType": {"type": "string", "description": "Indicates whether the item is fulfilled by Amazon or by the seller (merchant).", "enum": ["AFN", "MFN"], "x-docgen-enum-table-extension": [{"value": "AFN", "description": "Fulfilled by Amazon."}, {"value": "MFN", "description": "Fulfilled by the seller."}]}, "MarketplaceId": {"description": "A marketplace identifier. Specifies the marketplace for which data is returned.", "type": "string"}, "Sku": {"description": "The seller SKU of the item.", "type": "string"}, "Condition": {"description": "The condition of the item.", "type": "string", "enum": ["New", "Used", "Collectible", "Refurbished", "Club"], "x-docgen-enum-table-extension": [{"value": "New", "description": "New"}, {"value": "Used", "description": "Used"}, {"value": "Collectible", "description": "Collectible"}, {"value": "Refurbished", "description": "Refurbished"}, {"value": "Club", "description": "Club"}]}, "Asin": {"description": "The Amazon Standard Identification Number (ASIN) of the item.", "type": "string"}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}}}