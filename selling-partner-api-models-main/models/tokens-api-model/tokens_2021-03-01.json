{"swagger": "2.0", "info": {"description": "The Selling Partner API for Tokens provides a secure way to access a customer's PII (Personally Identifiable Information). You can call the Tokens API to get a Restricted Data Token (RDT) for one or more restricted resources that you specify. The RDT authorizes subsequent calls to restricted operations that correspond to the restricted resources that you specified.\n\nFor more information, see the [Tokens API Use Case Guide](doc:tokens-api-use-case-guide).", "version": "2021-03-01", "title": "Selling Partner API for Tokens ", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/tokens/2021-03-01/restrictedDataToken": {"post": {"operationId": "createRestrictedDataToken", "tags": ["tokens"], "description": "Returns a Restricted Data Token (RDT) for one or more restricted resources that you specify. A restricted resource is the HTTP method and path from a restricted operation that returns Personally Identifiable Information (PII), plus a dataElements value that indicates the type of PII requested. See the Tokens API Use Case Guide for a list of restricted operations. Use the RDT returned here as the access token in subsequent calls to the corresponding restricted operations.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 10 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "parameters": [{"in": "body", "name": "body", "description": "The restricted data token request details.", "required": true, "schema": {"$ref": "#/definitions/CreateRestrictedDataTokenRequest"}}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/CreateRestrictedDataTokenResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"targetApplication": "amzn1.sellerapps.app.target-application", "restrictedResources": [{"method": "GET", "path": "/orders/v0/orders/{orderId}/address"}]}}}}, "response": {"restrictedDataToken": "Atz.sprdt|IQEBLjAsAhRmHjNgHpi0U-Dme37rR6CuUpSR", "expiresIn": 3600}}, {"request": {"parameters": {"body": {"value": {"restrictedResources": [{"method": "GET", "path": "/orders/v0/orders/943-12-123434/address"}]}}}}, "response": {"restrictedDataToken": "Atz.sprdt|AODFNESLjAsAhRmHjNgHpi0U-Dme37rR6CuUpSR", "expiresIn": 3600}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"targetApplication": "amzn1.sellerapps.app.target-application", "restrictedResources": [{"method": "", "path": "/orders/v1/orders/902-1845936-5435065/address"}]}}}}, "response": {"errors": [{"code": "InvalidRequest", "message": "Request is missing or has invalid parameters", "details": "Resource not provided."}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The specified resource does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned.", "type": "string"}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}}, "definitions": {"CreateRestrictedDataTokenRequest": {"description": "The request schema for the createRestrictedDataToken operation.", "type": "object", "properties": {"targetApplication": {"type": "string", "description": "The application ID for the target application to which access is being delegated."}, "restrictedResources": {"type": "array", "description": "A list of restricted resources.\nMaximum: 50", "items": {"$ref": "#/definitions/RestrictedResource"}}}, "required": ["restrictedResources"]}, "RestrictedResource": {"description": "Model of a restricted resource.", "type": "object", "required": ["method", "path"], "properties": {"method": {"type": "string", "description": "The HTTP method in the restricted resource.", "enum": ["GET", "PUT", "POST", "DELETE"], "x-docgen-enum-table-extension": [{"value": "GET", "description": "The GET method."}, {"value": "PUT", "description": "The PUT method."}, {"value": "POST", "description": "The POST method."}, {"value": "DELETE", "description": "The DELETE method."}]}, "path": {"type": "string", "description": "The path in the restricted resource. Here are some path examples:\n- ```/orders/v0/orders```. For getting an RDT for the getOrders operation of the Orders API. For bulk orders.\n- ```/orders/v0/orders/123-1234567-1234567```. For getting an RDT for the getOrder operation of the Orders API. For a specific order.\n- ```/orders/v0/orders/123-1234567-1234567/orderItems```. For getting an RDT for the getOrderItems operation of the Orders API. For the order items in a specific order.\n- ```/mfn/v0/shipments/FBA1234ABC5D```. For getting an RDT for the getShipment operation of the Shipping API. For a specific shipment.\n- ```/mfn/v0/shipments/{shipmentId}```. For getting an RDT for the getShipment operation of the Shipping API. For any of a selling partner's shipments that you specify when you call the getShipment operation."}, "dataElements": {"type": "array", "description": "Indicates the type of Personally Identifiable Information requested. This parameter is required only when getting an RDT for use with the getOrder, getOrders, or getOrderItems operation of the Orders API. For more information, see the [Tokens API Use Case Guide](doc:tokens-api-use-case-guide). Possible values include:\n- **buyerInfo**. On the order level this includes general identifying information about the buyer and tax-related information. On the order item level this includes gift wrap information and custom order information, if available.\n- **shippingAddress**. This includes information for fulfilling orders.\n- **buyerTaxInformation**. This includes information for issuing tax invoices.", "items": {"type": "string"}}}}, "CreateRestrictedDataTokenResponse": {"type": "object", "description": "The response schema for the createRestrictedDataToken operation.", "properties": {"restrictedDataToken": {"description": "A Restricted Data Token (RDT). This is a short-lived access token that authorizes calls to restricted operations. Pass this value with the x-amz-access-token header when making subsequent calls to these restricted resources.", "type": "string"}, "expiresIn": {"description": "The lifetime of the Restricted Data Token, in seconds.", "type": "integer"}}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "An error response returned when the request is unsuccessful."}, "ErrorList": {"type": "object", "properties": {"errors": {"type": "array", "items": {"$ref": "#/definitions/Error"}}}, "description": "A list of error responses returned when a request is unsuccessful."}}}