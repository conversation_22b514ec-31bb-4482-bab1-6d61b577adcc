{"swagger": "2.0", "info": {"description": "The Selling Partner API for Finances provides financial information that is relevant to a seller's business. You can obtain financial events for a given order, financial event group, or date range without having to wait until a statement period closes. You can also obtain financial event groups for a given date range. ", "version": "v0", "title": "Selling Partner API for Finances", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/finances/v0/financialEventGroups": {"get": {"description": "Returns financial event groups for a given date range. Orders from the last 48 hours might not be included in financial events.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.5 | 30 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput can have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits).", "operationId": "listFinancialEventGroups", "parameters": [{"name": "MaxResultsPerPage", "in": "query", "description": "The maximum number of results per page. If the response exceeds the maximum number of transactions or 10 MB, the response is `InvalidInput`.", "required": false, "type": "integer", "default": 10, "maximum": 100, "minimum": 1, "format": "int32"}, {"name": "FinancialEventGroupStartedBefore", "in": "query", "description": "A date that selects financial event groups that opened before (but not at) a specified date and time, in [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) format. The date-time must be after `FinancialEventGroupStartedAfter` and more than two minutes before the time of request. If `FinancialEventGroupStartedAfter` and `FinancialEventGroupStartedBefore` are more than 180 days apart, no financial event groups are returned.", "required": false, "type": "string", "format": "date-time"}, {"name": "FinancialEventGroupStartedAfter", "in": "query", "description": "A date that selects financial event groups that opened after (or at) a specified date and time, in [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) format. The date-time must be more than two minutes before you submit the request.", "required": false, "type": "string", "format": "date-time"}, {"name": "NextToken", "in": "query", "description": "The response includes `nextToken` when the number of results exceeds the specified `pageSize` value. To get the next page of results, call the operation with this token and include the same arguments as the call that produced the token. To get a complete list, call this operation until `nextToken` is null. Note that this operation can return empty pages.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ListFinancialEventGroupsResponse"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"MaxResultsPerPage": {"value": 1}, "FinancialEventGroupStartedBefore": {"value": "2019-10-31"}, "FinancialEventGroupStartedAfter": {"value": "2019-10-13"}}}, "response": {"payload": {"NextToken": "3493805734095308457308475", "FinancialEventGroupList": [{"FinancialEventGroupId": "1", "ProcessingStatus": "PROCESSED", "FundTransferStatus": "TRANSFERED", "OriginalTotal": {"CurrencyCode": "USD", "CurrencyAmount": 10.34}, "ConvertedTotal": {"CurrencyCode": "USD", "CurrencyAmount": 39.43}, "FundTransferDate": "2020-02-07T14:38:42.128Z", "TraceId": "**************", "AccountTail": "**********", "BeginningBalance": {"CurrencyCode": "USD", "CurrencyAmount": 55.33}, "FinancialEventGroupStart": "2020-02-07T14:38:42.128Z", "FinancialEventGroupEnd": "2020-02-07T14:38:42.128Z"}]}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ListFinancialEventGroupsResponse"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"MaxResultsPerPage": {"value": 10}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Date range is invalid."}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ListFinancialEventGroupsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ListFinancialEventGroupsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ListFinancialEventGroupsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ListFinancialEventGroupsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ListFinancialEventGroupsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/finances/v0/financialEventGroups/{eventGroupId}/financialEvents": {"get": {"description": "Returns all financial events for the specified financial event group. Orders from the last 48 hours might not be included in financial events.\n\n**Note:** This operation only retrieves a group's data for the past two years. A request for data spanning more than two years produces an empty response.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.5 | 30 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput can have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits).", "operationId": "listFinancialEventsByGroupId", "parameters": [{"name": "MaxResultsPerPage", "in": "query", "description": "The maximum number of results to return per page. If the response exceeds the maximum number of transactions or 10 MB, the response is `InvalidInput`.", "required": false, "type": "integer", "default": 100, "maximum": 100, "minimum": 1, "format": "int32"}, {"name": "PostedAfter", "in": "query", "description": "The response includes financial events posted after (or on) this date. This date must be in [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format. The date-time must be more than two minutes before the time of the request.", "required": false, "type": "string", "format": "date-time"}, {"name": "PostedBefore", "in": "query", "description": "The response includes financial events posted before (but not on) this date. This date must be in [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format.\n\nThe date-time must be later than `PostedAfter` and more than two minutes before the requestd was submitted. If `PostedAfter` and `PostedBefore` are more than 180 days apart, the response is empty. If you include the `PostedBefore` parameter in your request, you must also specify the `PostedAfter` parameter.\n\n**Default:** Two minutes before the time of the request.", "required": false, "type": "string", "format": "date-time"}, {"name": "eventGroupId", "in": "path", "description": "The identifier of the financial event group to which the events belong.", "required": true, "type": "string"}, {"name": "NextToken", "in": "query", "description": "The response includes `nextToken` when the number of results exceeds the specified `pageSize` value. To get the next page of results, call the operation with this token and include the same arguments as the call that produced the token. To get a complete list, call this operation until `nextToken` is null. Note that this operation can return empty pages.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"MaxResultsPerPage": {"value": 10}, "eventGroupId": {"value": "485734534857"}}}, "response": {"payload": {"NextToken": "Next token value", "FinancialEvents": {"ChargebackEventList": [{"AmazonOrderId": "444-555-3343433", "SellerOrderId": "***************", "MarketplaceName": "1", "OrderChargeList": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "OrderChargeAdjustmentList": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ShipmentFeeList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ShipmentFeeAdjustmentList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "OrderFeeList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "OrderFeeAdjustmentList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "DirectPaymentList": [{"DirectPaymentType": "StoredValueCardRevenue", "DirectPaymentAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "PostedDate": "2020-02-05T13:56:00.363Z", "ShipmentItemList": [{"SellerSKU": "456454455464", "OrderItemId": "*************", "OrderAdjustmentItemId": "456456465464", "QuantityShipped": 0, "ItemChargeList": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ItemChargeAdjustmentList": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ItemFeeList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ItemFeeAdjustmentList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ItemTaxWithheldList": [{"TaxCollectionModel": "Free", "TaxesWithheld": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}]}], "PromotionList": [{"PromotionType": "Free", "PromotionId": "*********", "PromotionAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "PromotionAdjustmentList": [{"PromotionType": "Free", "PromotionId": "*********", "PromotionAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "CostOfPointsGranted": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "CostOfPointsReturned": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ShipmentItemAdjustmentList": [{"SellerSKU": "456454455464", "OrderItemId": "*************", "OrderAdjustmentItemId": "456456465464", "QuantityShipped": 0, "ItemChargeList": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ItemChargeAdjustmentList": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ItemFeeList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ItemFeeAdjustmentList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ItemTaxWithheldList": [{"TaxCollectionModel": "Free", "TaxesWithheld": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}]}], "PromotionList": [{"PromotionType": "Free", "PromotionId": "*********", "PromotionAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "PromotionAdjustmentList": [{"PromotionType": "Free", "PromotionId": "*********", "PromotionAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "CostOfPointsGranted": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "CostOfPointsReturned": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}]}], "ImagingServicesFeeEventList": [{"ImagingRequestBillingItemID": "*********", "ASIN": "4564565456456546456", "PostedDate": "2020-02-05T13:56:00.363Z", "FeeList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}]}], "NetworkComminglingTransactionEventList": [{"TransactionType": "Free", "PostedDate": "2020-02-05T13:56:00.363Z", "NetCoTransactionID": "4565645", "SwapReason": "None", "ASIN": "464567656576", "MarketplaceId": "string", "TaxExclusiveAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "TaxAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "AffordabilityExpenseReversalEventList": [{"AmazonOrderId": "444-555-3343433", "PostedDate": "2020-02-05T13:56:00.363Z", "MarketplaceId": "1", "TransactionType": "Free", "BaseExpense": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "TaxTypeCGST": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "TaxTypeSGST": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "TaxTypeIGST": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "TotalExpense": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "TrialShipmentEventList": [{"AmazonOrderId": "444-555-3343433", "FinancialEventGroupId": "1", "PostedDate": "2020-02-05T13:56:00.363Z", "SKU": "456454455464", "FeeList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}]}], "TaxWithholdingEventList": [{"PostedDate": "2020-02-05T13:56:00.363Z", "BaseAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "WithheldAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "TaxWithholdingPeriod": {"StartDate": "2020-02-05T13:56:00.363Z", "EndDate": "2020-02-05T13:56:00.363Z"}}]}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"eventGroupId": {"value": "BADID"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Bad event group ID provided."}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/finances/v0/orders/{orderId}/financialEvents": {"get": {"description": "Returns all financial events for the specified order. Orders from the last 48 hours might not be included in financial events.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.5 | 30 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput can have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits).", "operationId": "listFinancialEventsByOrderId", "parameters": [{"name": "orderId", "in": "path", "description": "An Amazon-defined order identifier, in 3-7-7 format.", "required": true, "type": "string"}, {"name": "MaxResultsPerPage", "in": "query", "description": "The maximum number of results to return per page. If the response exceeds the maximum number of transactions or 10 MB, the response is `InvalidInput`.", "required": false, "type": "integer", "default": 100, "maximum": 100, "minimum": 1, "format": "int32"}, {"name": "NextToken", "in": "query", "description": "The response includes `nextToken` when the number of results exceeds the specified `pageSize` value. To get the next page of results, call the operation with this token and include the same arguments as the call that produced the token. To get a complete list, call this operation until `nextToken` is null. Note that this operation can return empty pages.", "required": false, "type": "string"}], "responses": {"200": {"description": "Financial Events successfully retrieved.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"MaxResultsPerPage": {"value": 10}, "orderId": {"value": "485-734-5434857"}}}, "response": {"payload": {"NextToken": "Next token value", "FinancialEvents": {"RetrochargeEventList": [{"RetrochargeEventType": "Retrocharge", "AmazonOrderId": "444-555-3343433", "PostedDate": "2020-02-05T13:56:00.363Z", "BaseTax": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "ShippingTax": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "MarketplaceName": "1", "RetrochargeTaxWithheldList": [{"TaxCollectionModel": "Free", "TaxesWithheld": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}]}]}], "RentalTransactionEventList": [{"AmazonOrderId": "444-555-3343433", "RentalEventType": "string", "ExtensionLength": 0, "PostedDate": "2020-02-05T13:56:00.363Z", "RentalChargeList": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "RentalFeeList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "MarketplaceName": "1", "RentalInitialValue": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "RentalReimbursement": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "RentalTaxWithheldList": [{"TaxCollectionModel": "Free", "TaxesWithheld": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}]}]}], "ProductAdsPaymentEventList": [{"postedDate": "2020-02-05T13:56:00.363Z", "transactionType": "Free", "invoiceId": "3454535453", "baseValue": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "taxValue": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "transactionValue": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "ServiceFeeEventList": [{"AmazonOrderId": "444-555-3343433", "FeeReason": "Free", "FeeList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "SellerSKU": "456454455464", "FnSKU": "Fn134", "FeeDescription": "FeeDescription", "ASIN": "KJHJ457648GHD"}]}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"orderId": {"value": "BAD-ORDER"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Bad order ID provided."}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}, "/finances/v0/financialEvents": {"get": {"description": "Returns financial events for the specified data range. Orders from the last 48 hours might not be included in financial events.\n\n**Note:** in `ListFinancialEvents`, deferred events don't show up in responses until in they are released.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 0.5 | 30 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput can have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits).", "operationId": "listFinancialEvents", "parameters": [{"name": "MaxResultsPerPage", "in": "query", "description": "The maximum number of results to return per page. If the response exceeds the maximum number of transactions or 10 MB, the response is `InvalidInput`.", "required": false, "type": "integer", "default": 100, "maximum": 100, "minimum": 1, "format": "int32"}, {"name": "PostedAfter", "in": "query", "description": "The response includes financial events posted after (or on) this date. This date must be in [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format. The date-time must be more than two minutes before the time of the request.", "required": false, "type": "string", "format": "date-time"}, {"name": "PostedBefore", "in": "query", "description": "The response includes financial events posted before (but not on) this date. This date must be in [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format.\n\nThe date-time must be later than `PostedAfter` and more than two minutes before the request was submitted. If `PostedAfter` and `PostedBefore` are more than 180 days apart, the response is empty. If you include the `PostedBefore` parameter in your request, you must also specify the `PostedAfter` parameter.\n\n**Default:** Two minutes before the time of the request.", "required": false, "type": "string", "format": "date-time"}, {"name": "NextToken", "in": "query", "description": "The response includes `nextToken` when the number of results exceeds the specified `pageSize` value. To get the next page of results, call the operation with this token and include the same arguments as the call that produced the token. To get a complete list, call this operation until `nextToken` is null. Note that this operation can return empty pages.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"MaxResultsPerPage": {"value": 10}, "NextToken": {"value": "jehgri34yo7jr9e8f984tr9i4o"}}}, "response": {"payload": {"NextToken": "Next token value", "FinancialEvents": {"PayWithAmazonEventList": [{"SellerOrderId": "***************", "TransactionPostedDate": "2020-02-05T13:56:00.363Z", "BusinessObjectType": "Free", "SalesChannel": "None", "Charge": {"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}, "FeeList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "PaymentAmountType": "Tax", "AmountDescription": "Tax", "FulfillmentChannel": "FulfillmentChannel", "StoreName": "Etsy"}], "ServiceProviderCreditEventList": [{"ProviderTransactionType": "Free", "SellerOrderId": "***************", "MarketplaceId": "1", "MarketplaceCountryCode": "US", "SellerId": "**********", "SellerStoreName": "Etsy", "ProviderId": "1", "ProviderStoreName": "Etsy", "TransactionAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "TransactionCreationDate": "2020-02-05T13:56:00.363Z"}], "RentalTransactionEventList": [{"AmazonOrderId": "444-555-3343433", "RentalEventType": "string", "ExtensionLength": 0, "PostedDate": "2020-02-05T13:56:00.363Z", "RentalChargeList": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "RentalFeeList": [{"FeeType": "FixedClosingFee", "FeeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}], "MarketplaceName": "1", "RentalInitialValue": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "RentalReimbursement": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "RentalTaxWithheldList": [{"TaxCollectionModel": "Free", "TaxesWithheld": [{"ChargeType": "Tax", "ChargeAmount": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}]}]}], "ProductAdsPaymentEventList": [{"postedDate": "2020-02-05T13:56:00.363Z", "transactionType": "Free", "invoiceId": "3454535453", "baseValue": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "taxValue": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}, "transactionValue": {"CurrencyCode": "USD", "CurrencyAmount": 25.37}}]}}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "headers": {"x-amzn-RateLimit-Limit": {"description": "Your rate limit (requests per second) for this operation.", "type": "string"}, "x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}, "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"MaxResultsPerPage": {"value": 2}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Input not valid."}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ListFinancialEventsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}}}, "definitions": {"AdhocDisbursementEvent": {"type": "object", "properties": {"TransactionType": {"type": "string", "description": "The type of transaction. For example, \"Disbursed to Amazon Gift Card balance\"."}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "TransactionId": {"type": "string", "description": "The identifier for the transaction."}, "TransactionAmount": {"description": "The amount of the transaction.", "$ref": "#/definitions/Currency"}}, "description": "An event related to an Adhoc Disbursement."}, "AdhocDisbursementEventList": {"type": "array", "items": {"$ref": "#/definitions/AdhocDisbursementEvent"}, "description": "A list of `AdhocDisbursement` events."}, "AdjustmentEvent": {"type": "object", "properties": {"AdjustmentType": {"type": "string", "description": "The type of adjustment.\n\nPossible values:\n\n* `FBAInventoryReimbursement`: An FBA inventory reimbursement to a seller's account. This occurs if a seller's inventory is damaged.\n* `ReserveEvent`: A reserve event that is generated at the time a settlement period closes. This occurs when some money from a seller's account is held back.\n* `PostageBilling`: The amount paid by a seller for shipping labels.\n* `PostageRefund`: The reimbursement of shipping labels purchased for orders that were canceled or refunded.\n* `LostOrDamagedReimbursement`: An Amazon Easy Ship reimbursement to a seller's account for a package that we lost or damaged.\n* `CanceledButPickedUpReimbursement`: An Amazon Easy Ship reimbursement to a seller's account. This occurs when a package is picked up and the order is subsequently canceled. This value is used only in the India marketplace.\n* `ReimbursementClawback`: An Amazon Easy Ship reimbursement clawback from a seller's account. This occurs when a prior reimbursement is reversed. This value is used only in the India marketplace.\n* `SellerRewards`: An award credited to a seller's account for their participation in an offer in the Seller Rewards program. Applies only to the India marketplace."}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "AdjustmentAmount": {"description": "The amount adjusted as part of this event.", "$ref": "#/definitions/Currency"}, "AdjustmentItemList": {"description": "A list of information about adjustments to an account.", "$ref": "#/definitions/AdjustmentItemList"}}, "description": "An adjustment to the seller's account."}, "AdjustmentEventList": {"type": "array", "description": "A list of adjustment event information for the seller's account.", "items": {"$ref": "#/definitions/AdjustmentEvent"}}, "AdjustmentItem": {"type": "object", "properties": {"Quantity": {"type": "string", "description": "Represents the number of units in the seller's inventory when the `AdjustmentType` is `FBAInventoryReimbursement`."}, "PerUnitAmount": {"description": "The per-unit value of the item.", "$ref": "#/definitions/Currency"}, "TotalAmount": {"description": "The total value of the item.", "$ref": "#/definitions/Currency"}, "SellerSKU": {"type": "string", "description": "The seller SKU of the item. The seller SKU is qualified by the seller's seller ID, which is included with every call to the Selling Partner API."}, "FnSKU": {"type": "string", "description": "A unique identifier assigned to products stored in and fulfilled from a fulfillment center."}, "ProductDescription": {"type": "string", "description": "A short description of the item."}, "ASIN": {"type": "string", "description": "The Amazon Standard Identification Number (ASIN) of the item."}}, "description": "An item in an adjustment to the seller's account."}, "AdjustmentItemList": {"type": "array", "description": "A list of information about items in an adjustment to the seller's account.", "items": {"$ref": "#/definitions/AdjustmentItem"}}, "AffordabilityExpenseEvent": {"type": "object", "required": ["TaxTypeCGST", "TaxTypeIGST", "TaxTypeSGST"], "properties": {"AmazonOrderId": {"type": "string", "description": "An Amazon-defined identifier for an order."}, "PostedDate": {"description": "The date and time when the financial event was created.", "$ref": "#/definitions/Date"}, "MarketplaceId": {"type": "string", "description": "The Amazon-defined marketplace identifier."}, "TransactionType": {"type": "string", "description": "The type of transaction. \n\nPossible values:\n\n* `Charge`: an affordability promotion expense.\n* `Refund`: an affordability promotion expense reversal."}, "BaseExpense": {"description": "The amount charged for clicks incurred under the Sponsored Products program.", "$ref": "#/definitions/Currency"}, "TaxTypeCGST": {"description": "Central Goods and Service Tax, charged and collected by the central government.", "$ref": "#/definitions/Currency"}, "TaxTypeSGST": {"description": "State Goods and Service Tax, charged and collected by the state government.", "$ref": "#/definitions/Currency"}, "TaxTypeIGST": {"description": "Integrated Goods and Service Tax, charged and collected by the central government.", "$ref": "#/definitions/Currency"}, "TotalExpense": {"description": "The total amount charged to the seller. `TotalExpense` = `BaseExpense` + `TaxTypeIGST` + `TaxTypeCGST` + `TaxTypeSGST`.", "$ref": "#/definitions/Currency"}}, "description": "An expense related to an affordability promotion."}, "AffordabilityExpenseEventList": {"type": "array", "description": "A list of expense information related to an affordability promotion.", "items": {"$ref": "#/definitions/AffordabilityExpenseEvent"}}, "BigDecimal": {"type": "number", "description": "A signed decimal number."}, "ChargeComponent": {"type": "object", "properties": {"ChargeType": {"type": "string", "description": "The type of charge."}, "ChargeAmount": {"description": "The amount of the charge.", "$ref": "#/definitions/Currency"}}, "description": "A charge on the seller's account.\n\nPossible values:\n\n* `Principal`: The selling price of the order item, which is equal to the selling price of the item multiplied by the quantity ordered.\n\n* `Tax`: The tax on the principal that is collected by the seller.\n\n* `MarketplaceFacilitatorTax-Principal`: The tax that is withheld on the principal.\n\n* `MarketplaceFacilitatorTax-Shipping`: The tax that is withheld on the `ShippingCharge`.\n\n* `MarketplaceFacilitatorTax-Giftwrap`: The tax that is withheld on the Giftwrap charge.\n\n* `MarketplaceFacilitatorTax-Other`: The tax that is withheld on other miscellaneous charges.\n\n* `Discount`: The promotional discount for an order item.\n\n* `TaxDiscount`: The tax that is deducted for promotional rebates.\n\n* `CODItemCharge`: The COD charge for an order item.\n\n* `CODItemTaxCharge`: The tax that is collected by the seller on a `CODItemCharge`.\n\n* `CODOrderCharge`: The COD charge for an order.\n\n* `CODOrderTaxCharge`: The tax that is collected by the seller on a `CODOrderCharge`.\n\n* `CODShippingCharge`: Shipping charges for a COD order.\n\n* `CODShippingTaxCharge`: The tax that is collected by the seller on a `CODShippingCharge`.\n\n* `ShippingCharge`: The shipping charge.\n\n* `ShippingTax`: The tax that is collected by the seller on a `ShippingCharge`.\n\n* `Goodwill`: The amount of money that is given to a buyer as a gesture of goodwill or to compensate for pain and suffering in the buying experience.\n\n* `Giftwrap`: The gift wrap charge.\n\n* `GiftwrapTax`: The tax that is collected by the seller on a gift wrap charge.\n\n* `RestockingFee`: The charge that is applied to the buyer when returning a product in certain categories.\n\n* `ReturnShipping`: The amount of money that is given to the buyer to compensate for shipping the item back if we are at fault.\n\n* `PointsFee`: The value of Amazon Points deducted from the refund if the buyer does not have enough Amazon Points to cover the deduction.\n\n* `GenericDeduction`: A generic bad debt deduction.\n\n* `FreeReplacementReturnShipping`: The compensation for return shipping when a buyer receives the wrong item, requests a free replacement, and returns the incorrect item.\n\n* `PaymentMethodFee`: The fee that is collected for certain payment methods in certain marketplaces.\n\n* `ExportCharge`: The export duty that is charged when an item is shipped to an international destination as part of the Amazon Global program.\n\n* `SAFE-TReimbursement`: The SAFE-T claim amount for the item.\n\n* `TCS-CGST`: Tax Collected at Source (TCS) for Central Goods and Services Tax (CGST).\n\n* `TCS-SGST`: Tax Collected at Source for State Goods and Services Tax (SGST).\n\n* `TCS-IGST`: Tax Collected at Source for Integrated Goods and Services Tax (IGST).\n\n* `TCS-UTGST`: Tax Collected at Source for Union Territories Goods and Services Tax (UTGST)."}, "ChargeComponentList": {"type": "array", "description": "A list of charge information on the seller's account.", "items": {"$ref": "#/definitions/ChargeComponent"}}, "ChargeInstrument": {"type": "object", "properties": {"Description": {"type": "string", "description": "A short description of the charge instrument."}, "Tail": {"type": "string", "description": "The account tail (trailing digits) of the charge instrument."}, "Amount": {"description": "The amount charged to this charge instrument.", "$ref": "#/definitions/Currency"}}, "description": "A payment instrument."}, "ChargeInstrumentList": {"type": "array", "description": "A list of payment instruments.", "items": {"$ref": "#/definitions/ChargeInstrument"}}, "ChargeRefundEvent": {"type": "object", "properties": {"PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "ReasonCode": {"type": "string", "description": "The reason given for a charge refund. For example, `SubscriptionFeeCorrection`."}, "ReasonCodeDescription": {"type": "string", "description": "A description of the Reason Code. For example, `SubscriptionFeeCorrection`."}, "ChargeRefundTransactions": {"description": "The amount of the charge refund credit.", "$ref": "#/definitions/ChargeRefundTransactions"}}, "description": "An event related to charge refund."}, "ChargeRefundEventList": {"type": "array", "items": {"$ref": "#/definitions/ChargeRefundEvent"}, "description": "A list of charge refund events."}, "ChargeRefundTransaction": {"type": "object", "properties": {"ChargeAmount": {"description": "The amount of the charge refund credit.", "$ref": "#/definitions/Currency"}, "ChargeType": {"type": "string", "description": "The type of charge."}}, "description": "The charge refund transaction."}, "ChargeRefundTransactions": {"type": "array", "description": "A list of `ChargeRefund` transactions", "items": {"$ref": "#/definitions/ChargeRefundTransaction"}}, "CouponPaymentEvent": {"type": "object", "properties": {"PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "CouponId": {"type": "string", "description": "A coupon identifier."}, "SellerCouponDescription": {"type": "string", "description": "The description provided by the seller when they created the coupon."}, "ClipOrRedemptionCount": {"type": "integer", "format": "int64", "description": "The number of coupon clips or redemptions."}, "PaymentEventId": {"type": "string", "description": "A payment event identifier."}, "FeeComponent": {"$ref": "#/definitions/FeeComponent"}, "ChargeComponent": {"$ref": "#/definitions/ChargeComponent"}, "TotalAmount": {"description": "The `FeeComponent` value plus the `ChargeComponent` value.", "$ref": "#/definitions/Currency"}}, "description": "An event related to coupon payments."}, "CouponPaymentEventList": {"type": "array", "description": "A list of coupon payment event information.", "items": {"$ref": "#/definitions/CouponPaymentEvent"}}, "Currency": {"type": "object", "properties": {"CurrencyCode": {"type": "string", "description": "The three-digit currency code in ISO 4217 format."}, "CurrencyAmount": {"description": "The monetary value.", "$ref": "#/definitions/BigDecimal"}}, "description": "A currency type and amount."}, "Date": {"type": "string", "format": "date-time", "description": "A date in [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format."}, "DebtRecoveryEvent": {"type": "object", "properties": {"DebtRecoveryType": {"type": "string", "description": "The debt recovery type.\n\nPossible values:\n\n* `DebtPayment`\n* `DebtPaymentFailure`\n* `DebtAdjustment`"}, "RecoveryAmount": {"description": "The amount applied for recovery.", "$ref": "#/definitions/Currency"}, "OverPaymentCredit": {"description": "The amount returned for overpayment.", "$ref": "#/definitions/Currency"}, "DebtRecoveryItemList": {"$ref": "#/definitions/DebtRecoveryItemList"}, "ChargeInstrumentList": {"$ref": "#/definitions/ChargeInstrumentList"}}, "description": "A debt payment or debt adjustment."}, "DebtRecoveryEventList": {"type": "array", "description": "A list of debt recovery event information.", "items": {"$ref": "#/definitions/DebtRecoveryEvent"}}, "DebtRecoveryItem": {"type": "object", "properties": {"RecoveryAmount": {"description": "The amount applied for the recovery item.", "$ref": "#/definitions/Currency"}, "OriginalAmount": {"description": "The original debt amount.", "$ref": "#/definitions/Currency"}, "GroupBeginDate": {"description": "The start date and time of the financial event group that contains the debt. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format.", "$ref": "#/definitions/Date"}, "GroupEndDate": {"description": "The end date and time of the financial event group that contains the debt. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format.", "$ref": "#/definitions/Date"}}, "description": "An item of a debt payment or debt adjustment."}, "DebtRecoveryItemList": {"type": "array", "description": "A list of debt recovery item information.", "items": {"$ref": "#/definitions/DebtRecoveryItem"}}, "DirectPayment": {"type": "object", "properties": {"DirectPaymentType": {"type": "string", "description": "The type of payment.\n\nPossible values:\n\n* `StoredValueCardRevenue` - The amount that is deducted from the seller's account because the seller received money through a stored value card.\n\n* `StoredValueCardRefund` - The amount that Amazon returns to the seller if the order that is purchased using a stored value card is refunded.\n\n* `PrivateLabelCreditCardRevenue` - The amount that is deducted from the seller's account because the seller received money through a private label credit card offered by Amazon.\n\n* `PrivateLabelCreditCardRefund` - The amount that Amazon returns to the seller if the order that is purchased using a private label credit card offered by Amazon is refunded.\n\n* `CollectOnDeliveryRevenue` - The COD amount that the seller collected directly from the buyer.\n\n* `CollectOnDeliveryRefund` - The amount that Amazon refunds to the buyer if an order paid for by COD is refunded."}, "DirectPaymentAmount": {"description": "The amount of the direct payment.", "$ref": "#/definitions/Currency"}}, "description": "A payment made directly to a seller."}, "DirectPaymentList": {"type": "array", "description": "A list of direct payment information.", "items": {"$ref": "#/definitions/DirectPayment"}}, "FailedAdhocDisbursementEventList": {"type": "array", "items": {"$ref": "#/definitions/FailedAdhocDisbursementEvent"}, "description": "A list of `FailedAdhocDisbursementEvent`."}, "FailedAdhocDisbursementEvent": {"type": "object", "properties": {"FundsTransfersType": {"type": "string", "description": "The type of fund transfer. For example, `Refund`."}, "TransferId": {"type": "string", "description": "The transfer identifier."}, "DisbursementId": {"type": "string", "description": "The disbursement identifier."}, "PaymentDisbursementType": {"type": "string", "description": "The type of payment for disbursement. For example, `CREDIT_CARD`."}, "Status": {"type": "string", "description": "The status of the failed `AdhocDisbursement`. For example, `HARD_DECLINED`."}, "TransferAmount": {"description": "The amount of the Adhoc Disbursement.", "$ref": "#/definitions/Currency"}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}}, "description": "Failed ad hoc disbursement event list."}, "FBALiquidationEvent": {"type": "object", "properties": {"PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "OriginalRemovalOrderId": {"type": "string", "description": "The identifier for the original removal order."}, "LiquidationProceedsAmount": {"description": "The amount paid by the liquidator for the seller's inventory. The seller receives this amount minus `LiquidationFeeAmount`.", "$ref": "#/definitions/Currency"}, "LiquidationFeeAmount": {"description": "The fee charged to the seller by Amazon for liquidating the seller's FBA inventory.", "$ref": "#/definitions/Currency"}}, "description": "A payment event for Fulfillment by Amazon (FBA) inventory liquidation. This event is used only in the US marketplace."}, "FBALiquidationEventList": {"type": "array", "description": "A list of FBA inventory liquidation payment events.", "items": {"$ref": "#/definitions/FBALiquidationEvent"}}, "FeeComponent": {"type": "object", "properties": {"FeeType": {"type": "string", "description": "The type of fee. For more information about Selling on Amazon fees, see [Selling on Amazon Fee Schedule](https://sellercentral.amazon.com/gp/help/200336920) on Seller Central. For more information about Fulfillment by Amazon fees, see [FBA features, services and fees](https://sellercentral.amazon.com/gp/help/201074400) on Seller Central."}, "FeeAmount": {"description": "The amount of the fee.", "$ref": "#/definitions/Currency"}}, "description": "A fee associated with the event."}, "FeeComponentList": {"type": "array", "description": "A list of fee component information.", "items": {"$ref": "#/definitions/FeeComponent"}}, "FinancialEventGroup": {"type": "object", "properties": {"FinancialEventGroupId": {"type": "string", "description": "A unique identifier for the financial event group."}, "ProcessingStatus": {"type": "string", "description": "The processing status of the financial event group indicates whether the balance of the financial event group is settled.\n\nPossible values:\n\n* `Open`\n* `Closed`"}, "FundTransferStatus": {"type": "string", "description": "The status of the fund transfer."}, "OriginalTotal": {"description": "The total amount in the currency of the marketplace in which the transactions occurred. For a closed financial group, this is the total amount of a disbursement or a charge amount. For an open financial event group, this is the current balance.", "$ref": "#/definitions/Currency"}, "ConvertedTotal": {"description": "The total amount in the currency of the marketplace in which the funds were disbursed.", "$ref": "#/definitions/Currency"}, "FundTransferDate": {"description": "The date and time when the disbursement or charge was initiated. Only present for closed settlements. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format.", "$ref": "#/definitions/Date"}, "TraceId": {"type": "string", "description": "The trace identifier used by sellers to look up transactions externally."}, "AccountTail": {"type": "string", "description": "The account tail of the payment instrument."}, "BeginningBalance": {"description": "The balance at the start of the settlement period.", "$ref": "#/definitions/Currency"}, "FinancialEventGroupStart": {"description": "The date and time at which the financial event group is opened. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format.", "$ref": "#/definitions/Date"}, "FinancialEventGroupEnd": {"description": "The date and time at which the financial event group is closed. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format.", "$ref": "#/definitions/Date"}}, "description": "Information related to a financial event group."}, "FinancialEventGroupList": {"type": "array", "description": "A list of financial event group information.", "items": {"$ref": "#/definitions/FinancialEventGroup"}}, "FinancialEvents": {"type": "object", "properties": {"ShipmentEventList": {"description": "A list of shipment events.", "$ref": "#/definitions/ShipmentEventList"}, "ShipmentSettleEventList": {"description": "A list of Shipment Settle events.", "$ref": "#/definitions/ShipmentSettleEventList"}, "RefundEventList": {"description": "A list of refund events.", "$ref": "#/definitions/ShipmentEventList"}, "GuaranteeClaimEventList": {"description": "A list of guarantee claim events.", "$ref": "#/definitions/ShipmentEventList"}, "ChargebackEventList": {"description": "A list of chargeback events.", "$ref": "#/definitions/ShipmentEventList"}, "PayWithAmazonEventList": {"$ref": "#/definitions/PayWithAmazonEventList"}, "ServiceProviderCreditEventList": {"$ref": "#/definitions/SolutionProviderCreditEventList"}, "RetrochargeEventList": {"$ref": "#/definitions/RetrochargeEventList"}, "RentalTransactionEventList": {"$ref": "#/definitions/RentalTransactionEventList"}, "ProductAdsPaymentEventList": {"$ref": "#/definitions/ProductAdsPaymentEventList"}, "ServiceFeeEventList": {"$ref": "#/definitions/ServiceFeeEventList"}, "SellerDealPaymentEventList": {"$ref": "#/definitions/SellerDealPaymentEventList"}, "DebtRecoveryEventList": {"$ref": "#/definitions/DebtRecoveryEventList"}, "LoanServicingEventList": {"$ref": "#/definitions/LoanServicingEventList"}, "AdjustmentEventList": {"$ref": "#/definitions/AdjustmentEventList"}, "SAFETReimbursementEventList": {"$ref": "#/definitions/SAFETReimbursementEventList"}, "SellerReviewEnrollmentPaymentEventList": {"$ref": "#/definitions/SellerReviewEnrollmentPaymentEventList"}, "FBALiquidationEventList": {"$ref": "#/definitions/FBALiquidationEventList"}, "CouponPaymentEventList": {"$ref": "#/definitions/CouponPaymentEventList"}, "ImagingServicesFeeEventList": {"$ref": "#/definitions/ImagingServicesFeeEventList"}, "NetworkComminglingTransactionEventList": {"$ref": "#/definitions/NetworkComminglingTransactionEventList"}, "AffordabilityExpenseEventList": {"$ref": "#/definitions/AffordabilityExpenseEventList"}, "AffordabilityExpenseReversalEventList": {"$ref": "#/definitions/AffordabilityExpenseEventList"}, "RemovalShipmentEventList": {"$ref": "#/definitions/RemovalShipmentEventList"}, "RemovalShipmentAdjustmentEventList": {"$ref": "#/definitions/RemovalShipmentAdjustmentEventList"}, "TrialShipmentEventList": {"$ref": "#/definitions/TrialShipmentEventList"}, "TDSReimbursementEventList": {"$ref": "#/definitions/TDSReimbursementEventList"}, "AdhocDisbursementEventList": {"$ref": "#/definitions/AdhocDisbursementEventList"}, "TaxWithholdingEventList": {"$ref": "#/definitions/TaxWithholdingEventList"}, "ChargeRefundEventList": {"$ref": "#/definitions/ChargeRefundEventList"}, "FailedAdhocDisbursementEventList": {"$ref": "#/definitions/FailedAdhocDisbursementEventList"}, "ValueAddedServiceChargeEventList": {"$ref": "#/definitions/ValueAddedServiceChargeEventList"}, "CapacityReservationBillingEventList": {"$ref": "#/definitions/CapacityReservationBillingEventList"}}, "description": "All the information that is related to a financial event."}, "ImagingServicesFeeEvent": {"type": "object", "properties": {"ImagingRequestBillingItemID": {"type": "string", "description": "The identifier for the imaging services request."}, "ASIN": {"type": "string", "description": "The Amazon Standard Identification Number (ASIN) of the item for which the imaging service was requested."}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "FeeList": {"description": "A list of fees associated with the event.", "$ref": "#/definitions/FeeComponentList"}}, "description": "A fee event related to Amazon Imaging services."}, "ImagingServicesFeeEventList": {"type": "array", "description": "A list of fee events related to Amazon Imaging services.", "items": {"$ref": "#/definitions/ImagingServicesFeeEvent"}}, "ListFinancialEventGroupsPayload": {"type": "object", "properties": {"NextToken": {"type": "string", "description": "When present and not empty, pass this string token in the next request to return the next response page."}, "FinancialEventGroupList": {"$ref": "#/definitions/FinancialEventGroupList"}}, "description": "The payload for the `listFinancialEventGroups` operation."}, "ListFinancialEventGroupsResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `listFinancialEventGroups` operation.", "$ref": "#/definitions/ListFinancialEventGroupsPayload"}, "errors": {"description": "One or more unexpected errors occurred during the `listFinancialEventGroups` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `listFinancialEventGroups` operation."}, "ListFinancialEventsPayload": {"type": "object", "properties": {"NextToken": {"type": "string", "description": "When present and not empty, pass this string token in the next request to return the next response page."}, "FinancialEvents": {"$ref": "#/definitions/FinancialEvents"}}, "description": "The payload for the `listFinancialEvents` operation."}, "ListFinancialEventsResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `listFinancialEvents` operation.", "$ref": "#/definitions/ListFinancialEventsPayload"}, "errors": {"description": "One or more unexpected errors occurred during the `listFinancialEvents` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `listFinancialEvents` operation."}, "LoanServicingEvent": {"type": "object", "properties": {"LoanAmount": {"description": "The amount of the loan.", "$ref": "#/definitions/Currency"}, "SourceBusinessEventType": {"type": "string", "description": "The type of event.\n\nPossible values:\n\n* `LoanAdvance`\n\n* `LoanPayment`\n\n* `LoanRefund`"}}, "description": "A loan advance, loan payment, or loan refund."}, "LoanServicingEventList": {"type": "array", "description": "A list of loan servicing events.", "items": {"$ref": "#/definitions/LoanServicingEvent"}}, "NetworkComminglingTransactionEvent": {"type": "object", "properties": {"TransactionType": {"type": "string", "description": "The type of network item swap.\n\nPossible values:\n\n* `NetCo`: A Fulfillment by Amazon inventory pooling transaction. Available only in the India marketplace.\n\n* `ComminglingVAT`: A commingling VAT transaction. Available only in the Spain, UK, France, Germany, and Italy marketplaces."}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "NetCoTransactionID": {"type": "string", "description": "The identifier for the network item swap."}, "SwapReason": {"type": "string", "description": "The reason for the network item swap."}, "ASIN": {"type": "string", "description": "The Amazon Standard Identification Number (ASIN) of the swapped item."}, "MarketplaceId": {"type": "string", "description": "The marketplace in which the event took place."}, "TaxExclusiveAmount": {"description": "The price of the swapped item minus `TaxAmount`.", "$ref": "#/definitions/Currency"}, "TaxAmount": {"description": "The tax on the network item swap paid by the seller.", "$ref": "#/definitions/Currency"}}, "description": "A network commingling transaction event."}, "NetworkComminglingTransactionEventList": {"type": "array", "description": "A list of network commingling transaction events.", "items": {"$ref": "#/definitions/NetworkComminglingTransactionEvent"}}, "PayWithAmazonEvent": {"type": "object", "properties": {"SellerOrderId": {"type": "string", "description": "An order identifier that is specified by the seller."}, "TransactionPostedDate": {"description": "The date and time when the payment transaction is posted. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format.", "$ref": "#/definitions/Date"}, "BusinessObjectType": {"type": "string", "description": "The type of business object."}, "SalesChannel": {"type": "string", "description": "The sales channel for the transaction."}, "Charge": {"description": "The charge associated with the event.", "$ref": "#/definitions/ChargeComponent"}, "FeeList": {"description": "A list of fees associated with the event.", "$ref": "#/definitions/FeeComponentList"}, "PaymentAmountType": {"type": "string", "description": "The type of payment.\n\nPossible values:\n\n* `Sales`"}, "AmountDescription": {"type": "string", "description": "A short description of this payment event."}, "FulfillmentChannel": {"type": "string", "description": "The fulfillment channel.\n\nPossible values:\n\n* `AFN`: Amazon Fulfillment Network (Fulfillment by Amazon)\n\n* `MFN`: Merchant Fulfillment Network (self-fulfilled)"}, "StoreName": {"type": "string", "description": "The name of the store where the event occurred."}}, "description": "An event related to the seller's Pay with Amazon account."}, "PayWithAmazonEventList": {"type": "array", "description": "A list of events related to the seller's Pay with Amazon account.", "items": {"$ref": "#/definitions/PayWithAmazonEvent"}}, "ProductAdsPaymentEvent": {"type": "object", "properties": {"postedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "transactionType": {"type": "string", "description": "Indicates if the transaction is for a charge or a refund.\n\nPossible values:\n\n* `charge`\n\n* `refund`"}, "invoiceId": {"type": "string", "description": "The identifier for the invoice that includes the transaction."}, "baseValue": {"description": "Base amount of the transaction, before tax.", "$ref": "#/definitions/Currency"}, "taxValue": {"description": "Tax amount of the transaction.", "$ref": "#/definitions/Currency"}, "transactionValue": {"description": "The total amount of the transaction. Equal to `baseValue` + `taxValue`.", "$ref": "#/definitions/Currency"}}, "description": "A Sponsored Products payment event."}, "ProductAdsPaymentEventList": {"type": "array", "description": "A list of sponsored products payment events.", "items": {"$ref": "#/definitions/ProductAdsPaymentEvent"}}, "Promotion": {"type": "object", "properties": {"PromotionType": {"type": "string", "description": "The type of promotion."}, "PromotionId": {"type": "string", "description": "The seller-specified identifier for the promotion."}, "PromotionAmount": {"description": "The amount of promotional discount applied to the item.", "$ref": "#/definitions/Currency"}}, "description": "A promotion applied to an item."}, "PromotionList": {"type": "array", "description": "A list of promotions.", "items": {"$ref": "#/definitions/Promotion"}}, "RemovalShipmentEvent": {"type": "object", "properties": {"PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "MerchantOrderId": {"type": "string", "description": "The merchant removal `orderId`."}, "OrderId": {"type": "string", "description": "The identifier for the removal shipment order."}, "TransactionType": {"type": "string", "description": "The type of removal order.\n\nPossible values:\n\n* `WHOLESALE_LIQUIDATION`"}, "StoreName": {"type": "string", "description": "The name of the store where the event occurred."}, "RemovalShipmentItemList": {"description": "A list of removal shipment items.", "$ref": "#/definitions/RemovalShipmentItemList"}}, "description": "A removal shipment event for a removal order."}, "RemovalShipmentEventList": {"type": "array", "description": "A list of removal shipment event information.", "items": {"$ref": "#/definitions/RemovalShipmentEvent"}}, "RemovalShipmentItem": {"type": "object", "properties": {"RemovalShipmentItemId": {"type": "string", "description": "An identifier for an item in a removal shipment."}, "TaxCollectionModel": {"type": "string", "description": "The tax collection model that is applied to the item.\n\nPossible values:\n\n* `MarketplaceFacilitator`: Tax is withheld and remitted to the taxing authority by Amazon on behalf of the seller.\n* `Standard`: Tax is paid to the seller and not remitted to the taxing authority by Amazon."}, "FulfillmentNetworkSKU": {"type": "string", "description": "The Amazon fulfillment network SKU for the item."}, "Quantity": {"type": "integer", "format": "int32", "description": "The quantity of the item."}, "Revenue": {"description": "The total amount paid to the seller for the removed item.", "$ref": "#/definitions/Currency"}, "FeeAmount": {"description": "The fee that Amazon charged to the seller for the removal of the item. The amount is a negative number.", "$ref": "#/definitions/Currency"}, "TaxAmount": {"description": "Tax collected on the revenue.", "$ref": "#/definitions/Currency"}, "TaxWithheld": {"description": "The tax withheld and remitted to the taxing authority by Amazon on behalf of the seller. If `TaxCollectionModel` = `MarketplaceFacilitator`, then `TaxWithheld` = `TaxAmount` (except when the `TaxWithheld` amount is a negative number). Otherwise, `TaxWithheld` = `0`.", "$ref": "#/definitions/Currency"}}, "description": "Item-level information for a removal shipment."}, "RemovalShipmentItemList": {"type": "array", "description": "A list of `RemovalShipmentItem`.", "items": {"$ref": "#/definitions/RemovalShipmentItem"}}, "RemovalShipmentAdjustmentEvent": {"type": "object", "properties": {"PostedDate": {"description": "The date when the financial event was posted.", "$ref": "#/definitions/Date"}, "AdjustmentEventId": {"type": "string", "description": "The unique identifier for the adjustment event."}, "MerchantOrderId": {"type": "string", "description": "The merchant removal orderId."}, "OrderId": {"type": "string", "description": "The orderId for shipping inventory."}, "TransactionType": {"type": "string", "description": "The type of removal order.\n\nPossible values:\n\n* `WHOLESALE_LIQUIDATION`."}, "RemovalShipmentItemAdjustmentList": {"type": "array", "description": "A comma-delimited list of `RemovalShipmentItemAdjustment` details for FBA inventory.", "items": {"$ref": "#/definitions/RemovalShipmentItemAdjustment"}}}, "description": "A financial adjustment event for FBA liquidated inventory. A positive value indicates money owed to Amazon by the buyer (for example, when the charge was incorrectly calculated as less than it should be). A negative value indicates a full or partial refund owed to the buyer (for example, when the buyer receives damaged items or fewer items than ordered)."}, "RemovalShipmentAdjustmentEventList": {"type": "array", "description": "A comma-delimited list of `RemovalShipmentAdjustment` details for FBA inventory.", "items": {"$ref": "#/definitions/RemovalShipmentAdjustmentEvent"}}, "RemovalShipmentItemAdjustment": {"type": "object", "properties": {"RemovalShipmentItemId": {"type": "string", "description": "An identifier for an item in a removal shipment."}, "TaxCollectionModel": {"type": "string", "description": "The tax collection model that is applied to the item.\n\nPossible values:\n\n* `MarketplaceFacilitator`: Tax is withheld and remitted to the taxing authority by Amazon on behalf of the seller.\n* `Standard`: Tax is paid to the seller and not remitted to the taxing authority by Amazon."}, "FulfillmentNetworkSKU": {"type": "string", "description": "The Amazon fulfillment network SKU for the item."}, "AdjustedQuantity": {"type": "integer", "format": "int32", "description": "Adjusted quantity of `RemovalShipmentItemAdjustment` items."}, "RevenueAdjustment": {"description": "The total amount adjusted for disputed items.", "$ref": "#/definitions/Currency"}, "TaxAmountAdjustment": {"description": "Adjustment on the tax collected amount on the adjusted revenue.", "$ref": "#/definitions/Currency"}, "TaxWithheldAdjustment": {"description": "Adjustment of the tax that is withheld and remitted to the taxing authority by Amazon on behalf of the seller. If `TaxCollectionModel` = `MarketplaceFacilitator`, then `TaxWithheld` = `TaxAmount` (except when the `TaxWithheld` amount is a negative number). Otherwise, `TaxWithheld` = `0`.", "$ref": "#/definitions/Currency"}}, "description": "Item-level information for a removal shipment item adjustment."}, "RentalTransactionEvent": {"type": "object", "properties": {"AmazonOrderId": {"type": "string", "description": "An Amazon-defined identifier for an order."}, "RentalEventType": {"type": "string", "description": "The type of rental event.\n\nPossible values:\n\n* `RentalCustomerPayment-Buyout`: A transaction type that represents when the customer wants to buy out a rented item.\n\n* `RentalCustomerPayment-Extension`: A transaction type that represents when the customer wants to extend the rental period.\n\n* `RentalCustomerRefund-Buyout`: A transaction type that represents when the customer requests a refund for the buyout of the rented item.\n\n* `RentalCustomerRefund-Extension`: A transaction type that represents when the customer requests a refund over the extension on the rented item.\n\n* `RentalHandlingFee`: A transaction type that represents the fee that Amazon charges sellers who rent through Amazon.\n\n* `RentalChargeFailureReimbursement`: A transaction type that represents when Amazon sends money to the seller to compensate for a failed charge.\n\n* `RentalLostItemReimbursement`: A transaction type that represents when Amazon sends money to the seller to compensate for a lost item."}, "ExtensionLength": {"type": "integer", "format": "int32", "description": "The number of days that the buyer extended an already rented item. This value is only returned for `RentalCustomerPayment-Extension` and `RentalCustomerRefund-Extension` events."}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "RentalChargeList": {"description": "A list of charges associated with the rental event.", "$ref": "#/definitions/ChargeComponentList"}, "RentalFeeList": {"description": "A list of fees associated with the rental event.", "$ref": "#/definitions/FeeComponentList"}, "MarketplaceName": {"type": "string", "description": "The name of the marketplace."}, "RentalInitialValue": {"description": "The amount of money that the customer originally paid to rent the item. This value is only returned for `RentalChargeFailureReimbursement` and `RentalLostItemReimbursement` events.", "$ref": "#/definitions/Currency"}, "RentalReimbursement": {"description": "The amount of money that Amazon sends the seller to compensate for a lost item or a failed charge. This value is only returned for `RentalChargeFailureReimbursement` and `RentalLostItemReimbursement` events.", "$ref": "#/definitions/Currency"}, "RentalTaxWithheldList": {"description": "A list of taxes withheld information for a rental item.", "$ref": "#/definitions/TaxWithheldComponentList"}}, "description": "An event related to a rental transaction."}, "RentalTransactionEventList": {"type": "array", "description": "A list of rental transaction event information.", "items": {"$ref": "#/definitions/RentalTransactionEvent"}}, "RetrochargeEvent": {"type": "object", "properties": {"RetrochargeEventType": {"type": "string", "description": "The type of event.\n\nPossible values:\n\n* `Retrocharge`\n\n* `RetrochargeReversal`"}, "AmazonOrderId": {"type": "string", "description": "An Amazon-defined identifier for an order."}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "BaseTax": {"description": "The base tax associated with the retrocharge event.", "$ref": "#/definitions/Currency"}, "ShippingTax": {"description": "The shipping tax associated with the retrocharge event.", "$ref": "#/definitions/Currency"}, "MarketplaceName": {"type": "string", "description": "The name of the marketplace where the retrocharge event occurred."}, "RetrochargeTaxWithheldList": {"description": "A list of information about taxes withheld.", "$ref": "#/definitions/TaxWithheldComponentList"}}, "description": "A retrocharge or retrocharge reversal."}, "RetrochargeEventList": {"type": "array", "description": "A list of information about `Retrocharge` or `RetrochargeReversal` events.", "items": {"$ref": "#/definitions/RetrochargeEvent"}}, "SAFETReimbursementEvent": {"type": "object", "properties": {"PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "SAFETClaimId": {"type": "string", "description": "A SAFE-T claim identifier."}, "ReimbursedAmount": {"description": "The amount of the reimbursement.", "$ref": "#/definitions/Currency"}, "ReasonCode": {"type": "string", "description": "Indicates why the seller was reimbursed."}, "SAFETReimbursementItemList": {"$ref": "#/definitions/SAFETReimbursementItemList"}}, "description": "A SAFE-T claim reimbursement on the seller's account."}, "SAFETReimbursementEventList": {"type": "array", "description": "A list of `SAFETReimbursementEvent`.", "items": {"$ref": "#/definitions/SAFETReimbursementEvent"}}, "SAFETReimbursementItem": {"type": "object", "properties": {"itemChargeList": {"description": "A list of charges associated with the item.", "$ref": "#/definitions/ChargeComponentList"}, "productDescription": {"type": "string", "description": "The description of the item as shown on the product detail page on the retail website."}, "quantity": {"type": "string", "description": "The number of units of the item being reimbursed."}}, "description": "An item from a SAFE-T claim reimbursement."}, "SAFETReimbursementItemList": {"type": "array", "description": "A list of `SAFETReimbursementItem`.", "items": {"$ref": "#/definitions/SAFETReimbursementItem"}}, "SellerDealPaymentEvent": {"type": "object", "properties": {"postedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "dealId": {"type": "string", "description": "The unique identifier of the deal."}, "dealDescription": {"type": "string", "description": "The internal description of the deal."}, "eventType": {"type": "string", "description": "The type of event: `SellerDealComplete`."}, "feeType": {"type": "string", "description": "The type of fee: `RunLightningDealFee`."}, "feeAmount": {"description": "The monetary amount of the fee.", "$ref": "#/definitions/Currency"}, "taxAmount": {"description": "The monetary amount of the tax applied.", "$ref": "#/definitions/Currency"}, "totalAmount": {"description": "The total monetary amount paid.", "$ref": "#/definitions/Currency"}}, "description": "An event linked to the payment of a fee related to the specified deal."}, "SellerDealPaymentEventList": {"type": "array", "description": "A list of payment events for deal-related fees.", "items": {"$ref": "#/definitions/SellerDealPaymentEvent"}}, "SellerReviewEnrollmentPaymentEvent": {"type": "object", "properties": {"PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "EnrollmentId": {"type": "string", "description": "An enrollment identifier."}, "ParentASIN": {"type": "string", "description": "The Amazon Standard Identification Number (ASIN) of the item that was enrolled in the Early Reviewer Program."}, "FeeComponent": {"$ref": "#/definitions/FeeComponent"}, "ChargeComponent": {"$ref": "#/definitions/ChargeComponent"}, "TotalAmount": {"description": "The `FeeComponent` value plus the `ChargeComponent` value.", "$ref": "#/definitions/Currency"}}, "description": "A fee payment event for the Early Reviewer Program."}, "SellerReviewEnrollmentPaymentEventList": {"type": "array", "description": "A list of information about fee events for the Early Reviewer Program.", "items": {"$ref": "#/definitions/SellerReviewEnrollmentPaymentEvent"}}, "ServiceFeeEvent": {"type": "object", "properties": {"AmazonOrderId": {"type": "string", "description": "An Amazon-defined identifier for an order."}, "FeeReason": {"type": "string", "description": "A short description of the service fee reason."}, "FeeList": {"description": "A list of fee components associated with the service fee.", "$ref": "#/definitions/FeeComponentList"}, "SellerSKU": {"type": "string", "description": "The seller SKU of the item. The seller SKU is qualified by the seller's seller ID, which is included with every call to the Selling Partner API."}, "FnSKU": {"type": "string", "description": "A unique identifier assigned by Amazon to products stored in and fulfilled from an Amazon fulfillment center."}, "FeeDescription": {"type": "string", "description": "A short description of the service fee event."}, "ASIN": {"type": "string", "description": "The Amazon Standard Identification Number (ASIN) of the item."}, "StoreName": {"type": "string", "description": "The name of the store where the event occurred."}}, "description": "A service fee on the seller's account."}, "ServiceFeeEventList": {"type": "array", "description": "A list of information about service fee events.", "items": {"$ref": "#/definitions/ServiceFeeEvent"}}, "ShipmentEvent": {"type": "object", "properties": {"AmazonOrderId": {"type": "string", "description": "An Amazon-defined identifier for an order."}, "SellerOrderId": {"type": "string", "description": "A seller-defined identifier for an order."}, "MarketplaceName": {"type": "string", "description": "The name of the marketplace where the event occurred."}, "StoreName": {"type": "string", "description": "The name of the store where the event occurred."}, "OrderChargeList": {"description": "A list of order-level charges. These charges are applicable to Multi-Channel Fulfillment COD orders.", "$ref": "#/definitions/ChargeComponentList"}, "OrderChargeAdjustmentList": {"description": "A list of order-level charge adjustments. These adjustments are applicable to Multi-Channel Fulfillment COD orders.", "$ref": "#/definitions/ChargeComponentList"}, "ShipmentFeeList": {"description": "A list of shipment-level fees.", "$ref": "#/definitions/FeeComponentList"}, "ShipmentFeeAdjustmentList": {"description": "A list of shipment-level fee adjustments.", "$ref": "#/definitions/FeeComponentList"}, "OrderFeeList": {"description": "A list of order-level fees. These charges are applicable to Multi-Channel Fulfillment orders.", "$ref": "#/definitions/FeeComponentList"}, "OrderFeeAdjustmentList": {"description": "A list of order-level fee adjustments. These adjustments are applicable to Multi-Channel Fulfillment orders.", "$ref": "#/definitions/FeeComponentList"}, "DirectPaymentList": {"description": "A list of transactions where buyers pay Amazon through one of the credit cards offered by Amazon or where buyers pay a seller directly through COD.", "$ref": "#/definitions/DirectPaymentList"}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "ShipmentItemList": {"$ref": "#/definitions/ShipmentItemList"}, "ShipmentItemAdjustmentList": {"description": "A list of shipment item adjustments.", "$ref": "#/definitions/ShipmentItemList"}}, "description": "A shipment, refund, guarantee claim, or chargeback."}, "ShipmentEventList": {"type": "array", "description": "A list of shipment event information.", "items": {"$ref": "#/definitions/ShipmentEvent"}}, "ShipmentSettleEventList": {"type": "array", "description": "A list of `ShipmentEvent` items.", "items": {"$ref": "#/definitions/ShipmentEvent"}}, "ShipmentItem": {"type": "object", "properties": {"SellerSKU": {"type": "string", "description": "The seller SKU of the item. The seller SKU is qualified by the seller's seller ID, which is included with every call to the Selling Partner API."}, "OrderItemId": {"type": "string", "description": "An Amazon-defined order item identifier."}, "OrderAdjustmentItemId": {"type": "string", "description": "An Amazon-defined order adjustment identifier defined for refunds, guarantee claims, and chargeback events."}, "QuantityShipped": {"type": "integer", "format": "int32", "description": "The number of items shipped."}, "ItemChargeList": {"description": "A list of charges associated with the shipment item.", "$ref": "#/definitions/ChargeComponentList"}, "ItemChargeAdjustmentList": {"description": "A list of charge adjustments associated with the shipment item. This value is only returned for refunds, guarantee claims, and chargeback events.", "$ref": "#/definitions/ChargeComponentList"}, "ItemFeeList": {"description": "A list of fees associated with the shipment item.", "$ref": "#/definitions/FeeComponentList"}, "ItemFeeAdjustmentList": {"description": "A list of fee adjustments associated with the shipment item. This value is only returned for refunds, guarantee claims, and chargeback events.", "$ref": "#/definitions/FeeComponentList"}, "ItemTaxWithheldList": {"description": "A list of taxes withheld information for a shipment item.", "$ref": "#/definitions/TaxWithheldComponentList"}, "PromotionList": {"$ref": "#/definitions/PromotionList"}, "PromotionAdjustmentList": {"description": "A list of promotion adjustments associated with the shipment item. This value is only returned for refunds, guarantee claims, and chargeback events.", "$ref": "#/definitions/PromotionList"}, "CostOfPointsGranted": {"description": "The cost of Amazon Points granted for a shipment item.", "$ref": "#/definitions/Currency"}, "CostOfPointsReturned": {"description": "The cost of Amazon Points returned for a shipment item. This value is only returned for refunds, guarantee claims, and chargeback events.", "$ref": "#/definitions/Currency"}}, "description": "An item of a shipment, refund, guarantee claim, or chargeback."}, "ShipmentItemList": {"type": "array", "description": "A list of shipment items.", "items": {"$ref": "#/definitions/ShipmentItem"}}, "SolutionProviderCreditEvent": {"type": "object", "properties": {"ProviderTransactionType": {"type": "string", "description": "The transaction type."}, "SellerOrderId": {"type": "string", "description": "A seller-defined identifier for an order."}, "MarketplaceId": {"type": "string", "description": "The identifier of the marketplace where the order was placed."}, "MarketplaceCountryCode": {"type": "string", "description": "The two-letter country code of the country associated with the marketplace where the order was placed."}, "SellerId": {"type": "string", "description": "The Amazon-defined identifier of the seller."}, "SellerStoreName": {"type": "string", "description": "The store name where the payment event occurred."}, "ProviderId": {"type": "string", "description": "The Amazon-defined identifier of the solution provider."}, "ProviderStoreName": {"type": "string", "description": "The store name where the payment event occurred."}, "TransactionAmount": {"description": "The amount of the credit.", "$ref": "#/definitions/Currency"}, "TransactionCreationDate": {"description": "The date and time that the credit transaction was created, in [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) date-time format.", "$ref": "#/definitions/Date"}}, "description": "A credit given to a solution provider."}, "SolutionProviderCreditEventList": {"type": "array", "description": "A list of `SolutionProviderCreditEvent`.", "items": {"$ref": "#/definitions/SolutionProviderCreditEvent"}}, "TaxWithholdingPeriod": {"type": "object", "properties": {"StartDate": {"description": "Start of the time range.", "$ref": "#/definitions/Date"}, "EndDate": {"description": "End of the time range.", "$ref": "#/definitions/Date"}}, "description": "The period during which tax withholding on a seller's account is calculated."}, "TaxWithholdingEvent": {"type": "object", "properties": {"PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "BaseAmount": {"description": "The amount which tax was withheld against.", "$ref": "#/definitions/Currency"}, "WithheldAmount": {"description": "The amount of the tax withholding deducted from seller's account.", "$ref": "#/definitions/Currency"}, "TaxWithholdingPeriod": {"description": "Time period for which tax is withheld.", "$ref": "#/definitions/TaxWithholdingPeriod"}}, "description": "A tax withholding event on a seller's account."}, "TaxWithholdingEventList": {"type": "array", "items": {"$ref": "#/definitions/TaxWithholdingEvent"}, "description": "A list of tax withholding events."}, "TaxWithheldComponent": {"type": "object", "properties": {"TaxCollectionModel": {"type": "string", "description": "The tax collection model applied to the item.\n\nPossible values:\n\n* `MarketplaceFacilitator`: Tax is withheld and remitted to the taxing authority by Amazon on behalf of the seller.\n* `Standard`: Tax is paid to the seller and not remitted to the taxing authority by Amazon."}, "TaxesWithheld": {"description": "A list of charges that represent the types and amounts of taxes withheld.", "$ref": "#/definitions/ChargeComponentList"}}, "description": "Information about the taxes withheld."}, "TaxWithheldComponentList": {"type": "array", "description": "A list of information about taxes withheld.", "items": {"$ref": "#/definitions/TaxWithheldComponent"}}, "TDSReimbursementEvent": {"type": "object", "properties": {"PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "TDSOrderId": {"type": "string", "description": "The Tax-Deducted-at-Source (TDS) identifier."}, "ReimbursedAmount": {"description": "The amount reimbursed.", "$ref": "#/definitions/Currency"}}, "description": "An event related to a Tax-Deducted-at-Source (TDS) reimbursement."}, "TDSReimbursementEventList": {"type": "array", "items": {"$ref": "#/definitions/TDSReimbursementEvent"}, "description": "A list of `TDSReimbursementEvent` items."}, "TrialShipmentEvent": {"type": "object", "properties": {"AmazonOrderId": {"type": "string", "description": "An Amazon-defined identifier for an order."}, "FinancialEventGroupId": {"type": "string", "description": "The identifier of the financial event group."}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "SKU": {"type": "string", "description": "The seller SKU of the item. The seller SKU is qualified by the seller's seller ID, which is included with every call to the Selling Partner API."}, "FeeList": {"description": "A list of fees charged by Amazon for trial shipments.", "$ref": "#/definitions/FeeComponentList"}}, "description": "An event related to a trial shipment."}, "TrialShipmentEventList": {"type": "array", "items": {"$ref": "#/definitions/TrialShipmentEvent"}, "description": "A list of information about trial shipment financial events."}, "ValueAddedServiceChargeEventList": {"type": "array", "items": {"$ref": "#/definitions/ValueAddedServiceChargeEvent"}, "description": "A list of `ValueAddedServiceCharge` events."}, "ValueAddedServiceChargeEvent": {"type": "object", "properties": {"TransactionType": {"type": "string", "description": "The transaction type. For example, 'Other Support Service fees'"}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "Description": {"type": "string", "description": "A short description of the service charge event."}, "TransactionAmount": {"description": "The amount of the service charge event.", "$ref": "#/definitions/Currency"}}, "description": "An event related to a value added service charge."}, "CapacityReservationBillingEvent": {"type": "object", "properties": {"TransactionType": {"type": "string", "description": "The transaction type. For example, FBA Inventory Fee."}, "PostedDate": {"description": "The date and time when the financial event was posted.", "$ref": "#/definitions/Date"}, "Description": {"type": "string", "description": "A short description of the capacity reservation billing event."}, "TransactionAmount": {"description": "The amount of the capacity reservation billing event.", "$ref": "#/definitions/Currency"}}, "description": "An event related to a capacity reservation billing charge."}, "CapacityReservationBillingEventList": {"type": "array", "items": {"$ref": "#/definitions/CapacityReservationBillingEvent"}, "description": "A list of `CapacityReservationBillingEvent` events."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition in a human-readable form."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}}}