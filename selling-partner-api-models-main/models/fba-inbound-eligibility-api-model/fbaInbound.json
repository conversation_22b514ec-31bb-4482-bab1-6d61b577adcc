{"swagger": "2.0", "info": {"description": "With the FBA Inbound Eligibility API, you can build applications that let sellers get eligibility previews for items before shipping them to Amazon's fulfillment centers. With this API you can find out if an item is eligible for inbound shipment to Amazon's fulfillment centers in a specific marketplace. You can also find out if an item is eligible for using the manufacturer barcode for FBA inventory tracking. Sellers can use this information to inform their decisions about which items to ship Amazon's fulfillment centers.", "version": "v1", "title": "Selling Partner API for FBA Inbound Eligibilty", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/fba/inbound/v1/eligibility/itemPreview": {"get": {"tags": ["fbaInbound"], "description": "This operation gets an eligibility preview for an item that you specify. You can specify the type of eligibility preview that you want (INBOUND or COMMINGLING). For INBOUND previews, you can specify the marketplace in which you want to determine the item's eligibility.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 1 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getItemEligibilityPreview", "parameters": [{"name": "marketplaceIds", "in": "query", "description": "The identifier for the marketplace in which you want to determine eligibility. Required only when program=INBOUND.", "required": false, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"name": "asin", "in": "query", "description": "The ASIN of the item for which you want an eligibility preview.", "required": true, "type": "string"}, {"name": "program", "in": "query", "description": "The program that you want to check eligibility against.", "required": true, "type": "string", "enum": ["INBOUND", "COMMINGLING"]}], "responses": {"200": {"description": "Success.", "schema": {"$ref": "#/definitions/GetItemEligibilityPreviewResponse"}, "examples": {"ItemEligibilityPreview": {"payload": {"asin": "TEST_ASIN", "marketplaceId": "TEST_MARKETPLACE_ID", "program": "INBOUND", "isEligibleForProgram": true}}}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"asin": {"value": "TEST_CASE_200"}}}, "response": {"payload": {"asin": "TEST_CASE_200", "marketplaceId": "TEST_CASE_200", "program": "INBOUND", "isEligibleForProgram": true}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetItemEligibilityPreviewResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"asin": {"value": "TEST_CASE_400"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid input."}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/GetItemEligibilityPreviewResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"asin": {"value": "TEST_CASE_401"}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/GetItemEligibilityPreviewResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"asin": {"value": "TEST_CASE_403"}}}, "response": {"errors": [{"code": "Unauthorized", "message": "Access to requested resource is denied."}]}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetItemEligibilityPreviewResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"asin": {"value": "TEST_CASE_404"}}}, "response": {"errors": [{"code": "NotFound", "message": "The requested resource doesn't exist."}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetItemEligibilityPreviewResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"asin": {"value": "TEST_CASE_429"}}}, "response": {"errors": [{"code": "Quo<PERSON>Exceeded", "message": "You exceeded your quota for the requested resource."}]}}]}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetItemEligibilityPreviewResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"asin": {"value": "TEST_CASE_500"}}}, "response": {"errors": [{"code": "InternalFailure", "message": "We encountered an internal error. Please try again."}]}}]}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetItemEligibilityPreviewResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n_Note:_ For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference ID."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"asin": {"value": "TEST_CASE_503"}}}, "response": {"errors": [{"code": "ServiceUnavailable", "message": "Service is temporarily unavailable. Please try again."}]}}]}}}}}}, "definitions": {"GetItemEligibilityPreviewResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the getItemEligibilityPreview operation.", "$ref": "#/definitions/ItemEligibilityPreview"}, "errors": {"description": "An unexpected condition occurred during the GetItemEligibilityPreview operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the getItemEligibilityPreview operation."}, "ItemEligibilityPreview": {"type": "object", "required": ["asin", "isEligibleForProgram", "program"], "properties": {"asin": {"type": "string", "description": "The ASIN for which eligibility was determined."}, "marketplaceId": {"type": "string", "description": "The marketplace for which eligibility was determined."}, "program": {"type": "string", "description": "The program for which eligibility was determined.", "enum": ["INBOUND", "COMMINGLING"], "x-docgen-enum-table-extension": [{"value": "INBOUND", "description": "Inbound shipment."}, {"value": "COMMINGLING", "description": "Using the manufacturer barcode for FBA inventory tracking."}]}, "isEligibleForProgram": {"type": "boolean", "description": "Indicates if the item is eligible for the program."}, "ineligibilityReasonList": {"type": "array", "description": "Potential Ineligibility Reason Codes.", "items": {"type": "string", "description": "Potential Ineligibility Reason Codes.", "enum": ["FBA_INB_0004", "FBA_INB_0006", "FBA_INB_0007", "FBA_INB_0008", "FBA_INB_0009", "FBA_INB_0010", "FBA_INB_0011", "FBA_INB_0012", "FBA_INB_0013", "FBA_INB_0014", "FBA_INB_0015", "FBA_INB_0016", "FBA_INB_0017", "FBA_INB_0018", "FBA_INB_0019", "FBA_INB_0034", "FBA_INB_0035", "FBA_INB_0036", "FBA_INB_0037", "FBA_INB_0038", "FBA_INB_0050", "FBA_INB_0051", "FBA_INB_0053", "FBA_INB_0055", "FBA_INB_0056", "FBA_INB_0059", "FBA_INB_0065", "FBA_INB_0066", "FBA_INB_0067", "FBA_INB_0068", "FBA_INB_0095", "FBA_INB_0097", "FBA_INB_0098", "FBA_INB_0099", "FBA_INB_0100", "FBA_INB_0103", "FBA_INB_0104", "FBA_INB_0197", "UNKNOWN_INB_ERROR_CODE"], "x-docgen-enum-table-extension": [{"value": "FBA_INB_0004", "description": "Missing package dimensions. This product is missing necessary information; dimensions need to be provided in the manufacturer's original packaging."}, {"value": "FBA_INB_0006", "description": "The SKU for this product is unknown or cannot be found."}, {"value": "FBA_INB_0007", "description": "Product Under Dangerous Goods (Hazmat) Review. We do not have enough information to determine what the product is or comes with to enable us to complete our dangerous goods review. Until you provide the necessary information, the products will not be available for sale and you will not be able to send more units to Amazon fulfillment centers. You will need to add more details to the product listings, such as a clear title, bullet points, description, and image. The review process takes 4 business days."}, {"value": "FBA_INB_0008", "description": "Product Under Dangerous Goods (Hazmat) Review. We require detailed battery information to correctly classify the product, and until you provide the necessary information, the products will not be available for sale and you will not be able to send more units to Amazon fulfillment centers. Download an exemption sheet for battery and battery-powered products available in multiple languages in \"Upload dangerous goods documents: safety data sheet (SDS) or exemption sheet\" in Seller Central and follow instructions to submit it through the same page. The review process takes 4 business days."}, {"value": "FBA_INB_0009", "description": "Product Under Dangerous Goods (Hazmat) Review. We do not have enough dangerous goods information to correctly classify the product and until you provide the necessary information, the products will not be available for sale and you will not be able to send more units to Amazon fulfillment centers. Please provide a Safety Data Sheet (SDS) through \"Upload dangerous goods documents: safety data sheet (SDS) or exemption sheet\" in Seller Central, and make sure the SDS complies with all the requirements. The review process takes 4 business days."}, {"value": "FBA_INB_0010", "description": "Product Under Dangerous Goods (Hazmat) Review. The dangerous goods information is mismatched and so the product cannot be correctly classified. Until you provide the necessary information, the products will not be available for sale and you will not be able to send more units to Amazon fulfillment centers. Please provide compliant documents through \"Upload dangerous goods documents: safety data sheet (SDS) or exemption sheet\" in Seller Central, and make sure it complies with all the requirements. The review process takes 4 business days, the product will remain unfulfillable until review process is complete."}, {"value": "FBA_INB_0011", "description": "Product Under Dangerous Goods (Hazmat) Review. We have incomplete, inaccurate or conflicting dangerous goods information and cannot correctly classify the product. Until you provide the necessary information, the products will not be available for sale and you will not be able to send more units to Amazon fulfillment centers. Please provide compliant documents through \"Upload dangerous goods documents: safety data sheet (SDS) or exemption sheet\" in Seller Central, and make sure it complies with all the requirements. The review process takes 4 business days and the product will remain unfulfillable until the review process is complete."}, {"value": "FBA_INB_0012", "description": "Product Under Dangerous Goods (Hazmat) Review. We have determined there is conflicting product information (title, bullet points, images, or product description) within the product detail pages or with other offers for the product. Until the conflicting information is corrected, the products will not be available for sale and you will not be able to send more units to Amazon fulfillment centers. We need you to confirm the information on the product detail page The review process takes 4 business days."}, {"value": "FBA_INB_0013", "description": "Product Under Dangerous Goods (Hazmat) Review. Additional information is required in order to complete the Hazmat review process."}, {"value": "FBA_INB_0014", "description": "Product Under Dangerous Goods (Hazmat) Review. The product has been identified as possible dangerous goods. The review process generally takes 4 - 7 business days and until the review process is complete the product is unfulfillable and cannot be received at Amazon fulfilment centers or ordered by customers. For more information about dangerous goods please see \"Dangerous goods identification guide (hazmat)\"\" help page in Seller Central."}, {"value": "FBA_INB_0015", "description": "Dangerous goods (Hazmat). The product is regulated as unfulfillable and not eligible for sale with Amazon. We ask that you refrain from sending additional units in new shipments. We will need to dispose of your dangerous goods inventory in accordance with the terms of the Amazon Business Services Agreement. If you have questions or concerns, please contact Seller Support within five business days of this notice. For more information about dangerous goods please see “Dangerous goods identification guide (hazmat)” help page in Seller Central."}, {"value": "FBA_INB_0016", "description": "Dangerous goods (Hazmat). The product is regulated as a fulfillable dangerous good (Hazmat). You may need to be in the FBA dangerous good (Hazmat) program to be able to sell your product. For more information on the FBA dangerous good (Hazmat) program please contact Seller Support. For more information about dangerous goods please see the \"Dangerous goods identification guide (hazmat)\" help page in Seller Central."}, {"value": "FBA_INB_0017", "description": "This product does not exist in the destination marketplace catalog. The necessary product information will need to be provided before it can be inbounded."}, {"value": "FBA_INB_0018", "description": "Product missing category. This product must have a category specified before it can be sent to Amazon."}, {"value": "FBA_INB_0019", "description": "This product must have a title before it can be sent to Amazon."}, {"value": "FBA_INB_0034", "description": "Product cannot be stickerless, commingled. This product must be removed. You can send in new inventory by creating a new listing for this product that requires product labels."}, {"value": "FBA_INB_0035", "description": "Expiration-dated/lot-controlled product needs to be labeled. This product requires labeling to be received at our fulfillment centers."}, {"value": "FBA_INB_0036", "description": "Expiration-dated or lot-controlled product needs to be commingled. This product cannot be shipped to Amazon without being commingled. This error condition cannot be corrected from here. This product must be removed."}, {"value": "FBA_INB_0037", "description": "This product is not eligible to be shipped to our fulfillment center. You do not have all the required tax documents. If you have already filed documents please wait up to 48 hours for the data to propagate."}, {"value": "FBA_INB_0038", "description": "Parent ASIN cannot be fulfilled by Amazon. You can send this product by creating a listing against the child ASIN."}, {"value": "FBA_INB_0050", "description": "There is currently no fulfillment center in the destination country capable of receiving this product. Please delete this product from the shipment or contact Seller Support if you believe this is an error."}, {"value": "FBA_INB_0051", "description": "This product has been blocked by FBA and cannot currently be sent to Amazon for fulfillment."}, {"value": "FBA_INB_0053", "description": "Product is not eligible in the destination marketplace. This product is not eligible either because the required shipping option is not available or because the product is too large or too heavy."}, {"value": "FBA_INB_0055", "description": "Product unfulfillable due to media region restrictions. This product has a region code restricted for this marketplace. This product must be removed."}, {"value": "FBA_INB_0056", "description": "Product is ineligible for inbound. Used non-media goods cannot be shipped to Amazon."}, {"value": "FBA_INB_0059", "description": "Unknown Exception. This product must be removed at this time."}, {"value": "FBA_INB_0065", "description": "Product cannot be stickerless, commingled. This product must be removed. You can send in new inventory by creating a new listing for this product that requires product labels."}, {"value": "FBA_INB_0066", "description": "Unknown Exception. This product must be removed at this time."}, {"value": "FBA_INB_0067", "description": "Product ineligible for freight shipping. This item is ineligible for freight shipping with our Global Shipping Service. This item must be removed."}, {"value": "FBA_INB_0068", "description": "Account not configured for expiration-dated or lot-controlled products. Please contact TAM if you would like to configure your account to handle expiration-dated or lot-controlled inventory. Once configured, you will be able to send in this product."}, {"value": "FBA_INB_0095", "description": "The barcode (UPC/EAN/JAN/ISBN) for this product is associated with more than one product in our fulfillment system. This product must be removed. You can send in new inventory by creating a new listing for this product that requires product labels."}, {"value": "FBA_INB_0097", "description": "Fully regulated dangerous good."}, {"value": "FBA_INB_0098", "description": "Merchant is not authorized to send item to destination marketplace."}, {"value": "FBA_INB_0099", "description": "Seller account previously terminated."}, {"value": "FBA_INB_0100", "description": "You do not have the required tax information to send inventory to fulfillment centers in Mexico."}, {"value": "FBA_INB_0103", "description": "This is an expiration-dated/lot-controlled product that cannot be handled at this time."}, {"value": "FBA_INB_0104", "description": "Item Requires Manufacturer Barcode. Only NEW products can be stored in our fulfillment centers without product labels."}, {"value": "FBA_INB_0197", "description": "Item requires safety and compliance documentation. Orders for this product cannot be fulfilled by FBA without required safety and compliance documentation."}, {"value": "UNKNOWN_INB_ERROR_CODE", "description": "Unknown Ineligibility Reason."}]}}}, "description": "The response object which contains the ASIN, marketplaceId if required, eligibility program, the eligibility status (boolean), and a list of ineligibility reason codes."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition in a human-readable form."}, "details": {"type": "string", "description": "Additional information that can help the caller understand or fix the issue."}}, "description": "Error response returned when the request is unsuccessful."}}}