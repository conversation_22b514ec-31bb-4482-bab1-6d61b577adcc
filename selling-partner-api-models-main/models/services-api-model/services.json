{"swagger": "2.0", "info": {"description": "With the Services API, you can build applications that help service providers get and modify their service orders and manage their resources.", "version": "v1", "title": "Selling Partner API for Services", "contact": {"name": "Selling Partner API Developer Support", "url": "https://sellercentral.amazon.com/gp/mws/contactus.html"}, "license": {"name": "Apache License 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}}, "host": "sellingpartnerapi-na.amazon.com", "schemes": ["https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/service/v1/serviceJobs/{serviceJobId}": {"get": {"tags": ["service"], "description": "Gets details of service job indicated by the provided `serviceJobID`.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 20 | 40 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getServiceJobByServiceJobId", "parameters": [{"name": "serviceJobId", "in": "path", "description": "A service job identifier.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/GetServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": {}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "validJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}}}, "response": {"payload": {}}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/GetServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "422": {"description": "Unprocessable Entity. Unable to process the contained instructions.", "schema": {"$ref": "#/definitions/GetServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/serviceJobs/{serviceJobId}/cancellations": {"put": {"tags": ["service"], "description": "Cancels the service job indicated by the service job identifier specified.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "cancelServiceJobByServiceJobId", "parameters": [{"name": "serviceJobId", "in": "path", "description": "An Amazon defined service job identifier.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"name": "cancellationReasonCode", "in": "query", "description": "A cancel reason code that specifies the reason for cancelling a service job.", "required": true, "type": "string", "maxLength": 100, "minLength": 1, "pattern": "^[A-Z0-9_]*$"}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/CancelServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "validJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}, "cancellationReasonCode": {"value": "V1"}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CancelServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "nullJobId"}, "cancellationReasonCode": {"value": "V1"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [serviceJobId]", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}, "cancellationReasonCode": {"value": "NULL"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [cancellationReasonCode]", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "nullJobId"}, "cancellationReasonCode": {"value": "NULL"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [serviceJobId, cancellationReasonCode]", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CancelServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "unauthorizedJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}, "cancellationReasonCode": {"value": "V1"}}}, "response": {"errors": [{"code": "UnauthorizedAction", "message": "Not authorized to access this resource.Please check your input again", "details": ""}]}}, {"request": {"parameters": {}}, "response": {}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CancelServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CancelServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CancelServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "422": {"description": "Unprocessable Entity. Unable to process the contained instructions.", "schema": {"$ref": "#/definitions/CancelServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "completedJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}, "cancellationReasonCode": {"value": "V1"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Job with jobId completedJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut and jobStatus COMPLETED cannot be cancelled", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}, "cancellationReasonCode": {"value": "INV1"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Received invalid input reason code IV1 for jobId validJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "invalidJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}, "cancellationReasonCode": {"value": "V1"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Job not found for jobId invalidJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut", "details": ""}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CancelServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CancelServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CancelServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/serviceJobs/{serviceJobId}/completions": {"put": {"tags": ["service"], "description": "Completes the service job indicated by the service job identifier specified.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "completeServiceJobByServiceJobId", "parameters": [{"name": "serviceJobId", "in": "path", "description": "An Amazon defined service job identifier.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/CompleteServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "validJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CompleteServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "nullJobId"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [serviceJobId]", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CompleteServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "unauthorizedJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}}}, "response": {"errors": [{"code": "UnauthorizedAction", "message": "Not authorized to access this resource.Please check your input again", "details": ""}]}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CompleteServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CompleteServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CompleteServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "422": {"description": "Unprocessable Entity. Unable to process the contained instructions.", "schema": {"$ref": "#/definitions/CompleteServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "cancelledJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Operation not allowed on job with jobId : cancelledJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut and jobState : CANCELLED", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "invalidJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Job not found for jobId invalidJobId-48b6d5a3-b708-dbe9-038d-dd95e8d74iut", "details": ""}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CompleteServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CompleteServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CompleteServiceJobByServiceJobIdResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/serviceJobs": {"get": {"tags": ["service"], "description": "Gets service job details for the specified filter query.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 10 | 40 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getServiceJobs", "parameters": [{"name": "serviceOrderIds", "in": "query", "description": "List of service order ids for the query you want to perform.Max values supported 20.", "required": false, "type": "array", "items": {"type": "string"}, "maxItems": 20, "minItems": 1}, {"name": "serviceJobStatus", "in": "query", "description": "A list of one or more job status by which to filter the list of jobs.", "required": false, "type": "array", "items": {"type": "string", "enum": ["NOT_SERVICED", "CANCELLED", "COMPLETED", "PENDING_SCHEDULE", "NOT_FULFILLABLE", "HOLD", "PAYMENT_DECLINED"], "x-docgen-enum-table-extension": [{"value": "NOT_SERVICED", "description": "Jobs which are not serviced."}, {"value": "CANCELLED", "description": "Jobs which are cancelled."}, {"value": "COMPLETED", "description": "Jobs successfully completed."}, {"value": "PENDING_SCHEDULE", "description": "Jobs which are pending schedule."}, {"value": "NOT_FULFILLABLE", "description": "Jobs which are not fulfillable."}, {"value": "HOLD", "description": "Jobs which are on hold."}, {"value": "PAYMENT_DECLINED", "description": "Jobs for which payment was declined."}]}}, {"name": "pageToken", "in": "query", "description": "String returned in the response of your previous request.", "required": false, "type": "string"}, {"name": "pageSize", "in": "query", "description": "A non-negative integer that indicates the maximum number of jobs to return in the list, Value must be 1 - 20. Default 20.", "required": false, "type": "integer", "default": 20, "maximum": 20, "minimum": 1}, {"name": "sortField", "in": "query", "description": "Sort fields on which you want to sort the output.", "required": false, "type": "string", "enum": ["JOB_DATE", "JOB_STATUS"], "x-docgen-enum-table-extension": [{"value": "JOB_DATE", "description": "Sort on job date."}, {"value": "JOB_STATUS", "description": "Sort on job status."}]}, {"name": "sortOrder", "in": "query", "description": "Sort order for the query you want to perform.", "required": false, "type": "string", "enum": ["ASC", "DESC"], "x-docgen-enum-table-extension": [{"value": "ASC", "description": "Sort in ascending order."}, {"value": "DESC", "description": "Sort in descending order."}]}, {"name": "createdAfter", "in": "query", "description": "A date used for selecting jobs created at or after a specified time. Must be in ISO 8601 format. Required if `LastUpdatedAfter` is not specified. Specifying both `CreatedAfter` and `LastUpdatedAfter` returns an error.", "required": false, "type": "string"}, {"name": "createdBefore", "in": "query", "description": "A date used for selecting jobs created at or before a specified time. Must be in ISO 8601 format.", "required": false, "type": "string"}, {"name": "lastUpdatedAfter", "in": "query", "description": "A date used for selecting jobs updated at or after a specified time. Must be in ISO 8601 format. Required if `createdAfter` is not specified. Specifying both `CreatedAfter` and `LastUpdatedAfter` returns an error.", "required": false, "type": "string"}, {"name": "lastUpdatedBefore", "in": "query", "description": "A date used for selecting jobs updated at or before a specified time. Must be in ISO 8601 format.", "required": false, "type": "string"}, {"name": "scheduleStartDate", "in": "query", "description": "A date used for filtering jobs schedules at or after a specified time. Must be in ISO 8601 format. Schedule end date should not be earlier than schedule start date.", "required": false, "type": "string"}, {"name": "scheduleEndDate", "in": "query", "description": "A date used for filtering jobs schedules at or before a specified time. Must be in ISO 8601 format. Schedule end date should not be earlier than schedule start date.", "required": false, "type": "string"}, {"name": "marketplaceIds", "in": "query", "description": "Used to select jobs that were placed in the specified marketplaces.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"name": "asins", "in": "query", "description": "List of Amazon Standard Identification Numbers (ASIN) of the items. Max values supported is 20.", "required": false, "type": "array", "items": {"type": "string", "minLength": 10, "maxLength": 10}, "maxItems": 20, "minItems": 1}, {"name": "requiredSkills", "in": "query", "description": "A defined set of related knowledge, skills, experience, tools, materials, and work processes common to service delivery for a set of products and/or service scenarios. Max values supported is 20.", "required": false, "type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 50}, "maxItems": 20, "minItems": 1}, {"name": "storeIds", "in": "query", "description": "List of Amazon-defined identifiers for the region scope. Max values supported is 50.", "required": false, "type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 100}, "maxItems": 50, "minItems": 1}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/GetServiceJobsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {}}, "response": {"payload": {"totalResultSize": 1, "nextPageToken": "merchantSklktoreIdbcdcd2ad-5883-4e48-b114-f13328a9e9f", "previousPageToken": "merchantSklktoreIdbcdcd2ad-5883-4e48-b114-f13328a9e9f", "jobs": [{"serviceOrderId": "2345324", "serviceJobId": "34534399990035", "createTime": "2019-12-11T14:49:53.952Z", "serviceJobStatus": "COMPLETED", "buyer": {"name": "name<PERSON><PERSON><PERSON>"}, "appointments": [{"appointmentId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStatus": "COMPLETED", "appointmentTime": {"startTime": "2020-01-31T06:38:56.961Z", "durationInMinutes": 60}, "assignedTechnicians": [{"technicianId": "technician<PERSON>d<PERSON><PERSON><PERSON>", "name": "name<PERSON><PERSON><PERSON>"}]}]}]}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetServiceJobsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"createdAfter": {"value": "TEST_CASE_400"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid Input"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetServiceJobsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetServiceJobsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/GetServiceJobsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetServiceJobsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetServiceJobsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetServiceJobsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetServiceJobsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/serviceJobs/{serviceJobId}/appointments": {"post": {"tags": ["service"], "description": "Adds an appointment to the service job indicated by the service job identifier specified.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "addAppointmentForServiceJobByServiceJobId", "parameters": [{"name": "serviceJobId", "in": "path", "description": "An Amazon defined service job identifier.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"in": "body", "name": "body", "description": "Add appointment operation input details.", "required": true, "schema": {"$ref": "#/definitions/AddAppointmentRequest"}}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z"}}}}}, "response": {"appointmentId": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-2-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z", "durationInMinutes": 60}}}}}, "response": {"appointmentId": "validJobId-2-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "nullJobId"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z", "durationInMinutes": 60}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [serviceJobId]", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "unauthorizedJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z"}}}}}, "response": {"errors": [{"code": "UnauthorizedAction", "message": "Not authorized to access this resource. Please check your input again.", "details": ""}]}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "422": {"description": "Unprocessable Entity. Unable to process the contained instructions.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000+05:30"}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "ISO8601 time 2021-01-01T10:00:00.000+05:30 is not in UTC.", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10-00:00.000Z"}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to parse ISO8601 input: 2021-01-01T10-00:00.000Z", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "invalidJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z"}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "No job exist with jobId : invalidJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "completedJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z"}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Operation not allowed on job with jobId : completedJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468 and jobState : COMPLETED", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "withActiveAppointmentJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z"}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to add appointment for jobId : withActiveAppointmentJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468, reason : Job already has an active appointmentId.", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2019-01-01T10:00:00.000Z"}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to add appointment for jobId : validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468, reason : Start time of appointment should be in the future.", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2022-01-01T10:00:00.000Z"}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to add appointment for jobId : validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468, reason : Start time for appointment is beyond the maximum allowed period of 365 days.", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:15:00.000Z"}}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to add appointment for jobId : validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468, reason : Appointment slot is not available.", "details": ""}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/serviceJobs/{serviceJobId}/appointments/{appointmentId}": {"post": {"tags": ["service"], "description": "Reschedules an appointment for the service job indicated by the service job identifier specified.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "rescheduleAppointmentForServiceJobByServiceJobId", "parameters": [{"name": "serviceJobId", "in": "path", "description": "An Amazon defined service job identifier.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"name": "appointmentId", "in": "path", "description": "An existing appointment identifier for the Service Job.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"in": "body", "name": "body", "description": "Reschedule appointment operation input details.", "required": true, "schema": {"$ref": "#/definitions/RescheduleAppointmentRequest"}}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z"}, "rescheduleReasonCode": "R1"}}}}, "response": {"appointmentId": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468_new_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-2-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-2-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z", "durationInMinutes": 60}, "rescheduleReasonCode": "R1"}}}}, "response": {"appointmentId": "validJobId-2-9cb9bc29-3d7d-5e49-5709-efb693d34468_new_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-3-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-3-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z", "durationInMinutes": 60}, "rescheduleReasonCode": "R1"}}}}, "response": {"appointmentId": "validJobId-3-9cb9bc29-3d7d-5e49-5709-efb693d34468_new_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a", "warnings": [{"code": "RESOURCES_UNASSIGNED", "message": "Unassigned resources : ATechnicianId,BTechnicianId"}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "nullJobId"}, "appointmentId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z", "durationInMinutes": 60}, "rescheduleReasonCode": "R1"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [serviceJobId]", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "nullAppointmentId"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z", "durationInMinutes": 60}, "rescheduleReasonCode": "R1"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [appointmentId]", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "unauthorizedJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "unauthorizedJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z"}, "rescheduleReasonCode": "R1"}}}}, "response": {"errors": [{"code": "UnauthorizedAction", "message": "Not authorized to access this resource. Please check your input again.", "details": ""}]}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "422": {"description": "Unprocessable Entity. Unable to process the contained instructions.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000+05:30"}, "rescheduleReasonCode": "R1"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "ISO8601 time 2021-01-01T10:00:00.000+05:30 is not in UTC.", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10-00:00.000Z"}, "rescheduleReasonCode": "R1"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to parse ISO8601 input: 2021-01-01T10-00:00.000Z", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "invalidJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "invalidJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z"}, "rescheduleReasonCode": "R1"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "No job exist with jobId : invalidJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "completedJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "completedJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z"}, "rescheduleReasonCode": "R1"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Operation not allowed on job with jobId : completedJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468 and jobState : COMPLETED", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2019-01-01T10:00:00.000Z"}, "rescheduleReasonCode": "R1"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to add appointment for jobId : validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468, reason : Start time of appointment should be in the future.", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2022-01-01T10:00:00.000Z"}, "rescheduleReasonCode": "R1"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to add appointment for jobId : validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468, reason : Start time for appointment is beyond the maximum allowed period of 365 days.", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:15:00.000Z"}, "rescheduleReasonCode": "R1"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to add appointment for jobId : validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468, reason : Appointment slot is not available.", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentTime": {"startTime": "2021-01-01T10:00:00.000Z"}, "rescheduleReasonCode": "U1"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to add appointment for jobId : validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468, reason : Appointment reschedule reason code is not valid.", "details": ""}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/SetAppointmentResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/serviceJobs/{serviceJobId}/appointments/{appointmentId}/resources": {"put": {"tags": ["service"], "description": "Assigns new resource(s) or overwrite/update the existing one(s) to a service job appointment.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 1 | 2 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "assignAppointmentResources", "parameters": [{"name": "serviceJobId", "in": "path", "description": "An Amazon-defined service job identifier. Get this value by calling the `getServiceJobs` operation of the Services API.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"name": "appointmentId", "in": "path", "description": "An Amazon-defined identifier of active service job appointment.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"in": "body", "name": "body", "required": true, "schema": {"$ref": "#/definitions/AssignAppointmentResourcesRequest"}}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/AssignAppointmentResourcesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "validResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{}]}}}}, "response": {}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "overBookedResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {"payload": {"warnings": [{"code": "RESOURCES_OVERBOOKED", "message": "Resources overbooked for this time window."}]}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/AssignAppointmentResourcesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "nullJobId"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "validResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [serviceJobId]", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "nullAppointmentId"}, "body": {"value": {"resources": [{"resourceId": "validResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [appointmentId]", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "nullJobId"}, "appointmentId": {"value": "nullAppointmentId"}, "body": {"value": {"resources": [{"resourceId": "validResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [appointmentId, serviceJobId]", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Resources not provided in input JSON payload.", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": []}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Resources must have size greater than or equal to 1.", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/AssignAppointmentResourcesResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "unauthorizedJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "validResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {"errors": [{"code": "UnauthorizedAction", "message": "Not authorized to access this resource. Please check your input again.", "details": ""}]}}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/AssignAppointmentResourcesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/AssignAppointmentResourcesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/AssignAppointmentResourcesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "422": {"description": "Unprocessable Entity. Unable to process the contained instructions.", "schema": {"$ref": "#/definitions/AssignAppointmentResourcesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "invalidJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "validResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "No job exists with jobId : invalidJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "invalidAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "validResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Failed to Update Appointment for jobId : invalidJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687, appointmentId : invalidAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d, reason : No appointment exists with appointmentId: invalidAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "badResourceId"}]}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Invalid resourceId : badResourceId provided in the input.", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "completedJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "completedAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "validResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Operation not allowed on job with jobId : completedJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687 and jobState : COMPLETED", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "cancelledJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "cancelledAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "validResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Operation not allowed on job with jobId : cancelledJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687 and jobState : CANCELLED", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "cancelledAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "validResourceId-A8B3M999LMHF2"}, {"resourceId": "validResourceId-AMIDIAX1H5V"}]}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Appointment metadata update is not allowed for appointmentId cancelledAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d", "details": ""}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"resources": [{"resourceId": "invalidResourceId-A8B3M999LMHF2"}, {"resourceId": "invalidResourceId-AMIDIAX1H5V"}]}}}}, "response": {"errors": [{"code": "RESOURCES_MISMATCHED_SERVICE_LOCATION_TYPE", "message": "Resources do not have same service location type as job. Applicable resources: A8B3M999LMHF2,AMIDIAX1H5V", "details": ""}, {"code": "RESOURCES_MISSING_REQUIRED_SK<PERSON>LS", "message": "Resources Missing required XYZ skills. Applicable resources: A8B3M999LMHF2,AMIDIAX1H5V", "details": ""}, {"code": "RESOURCES_NOT_AVAILABLE_IN_LOCATION", "message": "Resources are not available in the store. Applicable resources: A8B3M999LMHF2,AMIDIAX1H5V", "details": ""}, {"code": "RESOURCES_NOT_REGISTERED_UNDER_MERCHANT", "message": "Resources are not registered under this Merchant. Applicable resources: A8B3M999LMHF2,AMIDIAX1H5V", "details": ""}, {"code": "RESOURCES_BACKGROUND_CHECK_INCOMPLETE", "message": "Resources background check is incomplete. Applicable resources: A8B3M999LMHF2,AMIDIAX1H5V", "details": ""}, {"code": "RESOURCES_NOT_AVAILABLE_IN_TIME_WINDOW", "message": "Resources do not have sufficient available capacity. Applicable resources: A8B3M999LMHF2,AMIDIAX1H5V", "details": ""}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/AssignAppointmentResourcesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/AssignAppointmentResourcesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/AssignAppointmentResourcesResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/serviceJobs/{serviceJobId}/appointments/{appointmentId}/fulfillment": {"put": {"tags": ["service"], "description": "Updates the appointment fulfillment data related to a given `jobID` and `appointmentID`.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "setAppointmentFulfillmentData", "parameters": [{"name": "serviceJobId", "in": "path", "description": "An Amazon-defined service job identifier. Get this value by calling the `getServiceJobs` operation of the Services API.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"name": "appointmentId", "in": "path", "description": "An Amazon-defined identifier of active service job appointment.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"in": "body", "name": "body", "description": "Appointment fulfillment data collection details.", "required": true, "schema": {"$ref": "#/definitions/SetAppointmentFulfillmentDataRequest"}}], "responses": {"204": {"description": "Success response.", "schema": {"type": "string"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "validUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": ""}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}}}}}, "response": ""}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}]}}}}, "response": ""}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"fulfillmentDocuments": [{"uploadDestinationId": "348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": ""}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}]}}}}, "response": ""}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "fulfillmentDocuments": [{"uploadDestinationId": "348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": ""}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687"}, "appointmentId": {"value": "validAppointmentId-1-9cb9bc29-3d7d-5e49-5709-efb693t25687_87b9d5f2-839d-y13e-sd4d-dae1c3996s3d"}, "body": {"value": {"appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": ""}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "nullJobId"}, "appointmentId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "validUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [serviceJobId]"}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "nullAppointmentId"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "validUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [appointmentId]"}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "nullAppointmentId"}, "body": {"value": {}}}}, "response": [{"code": "InvalidInput", "message": "No fulfillment artifacts provided in JSON payload.", "details": ""}]}]}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "unauthorizedJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "unauthorizedJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "UnauthorizedAction", "message": "Not authorized to access this resource. Please check your input again.", "details": ""}]}]}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "415": {"description": "The entity of the request is in a format not supported by the requested resource.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "422": {"description": "Unprocessable Entity. Unable to process the contained instructions.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "invalidJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "invalidAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "validUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "No job exist with jobId : invalidJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "InvalidStatusJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "invalidStatusAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "validUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Job with id InvalidStatusJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468 can not be updated. Please check if the job is in valid status.", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "invalidAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "validUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Appointment with id invalidAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468 is not present in the job. Please check the input again.", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "InvalidStatusCancelledAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-02T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "validUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Appointment with appointment id InvalidStatusCancelledAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468 is not valid to update. Please check the input again", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"endTime": "2021-01-03T13:18:10.668Z"}}}}}, "response": [{"code": "InvalidInput", "message": "Appointment start time is required. Please check the input again.", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2021-01-01T10:00:00.000+05:30"}}}}}, "response": [{"code": "InvalidInput", "message": "Appointment end time is required. Please check the input again.", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2021-01-01T10:00:00.000+05:30", "endTime": "2021-01-03T13:18:10.668Z"}}}}}, "response": [{"code": "InvalidInput", "message": "ISO8601 time 2021-01-01T10:00:00.000+05:30 is not in UTC format. Please provide time in UTC", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2021-01-01T10-00:00.000Z", "endTime": "2021-01-03T13:18:10.668Z"}}}}}, "response": [{"code": "InvalidInput", "message": "Could not parse given time input 2021-01-01T10-00:00.000Z. Please provide time in ISO8601 format", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "9999-01-01T10:00:00.000Z", "endTime": "2021-01-03T13:18:10.668Z"}}}}}, "response": [{"code": "InvalidInput", "message": "Appointment start time should not be in future. Please check the input again", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-01T10:00:00.000+05:30", "endTime": "9999-01-03T13:18:10.668Z"}}}}}, "response": [{"code": "InvalidInput", "message": "Appointment end time should not be in future. Please check the input again", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2020-01-04T10:00:00.000+05:30", "endTime": "2020-01-03T13:18:10.668Z"}}}}}, "response": [{"code": "InvalidInput", "message": "Appointment end time should be after start time. Please check the input again.", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentResources": [{"resourceId": "InValidResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}]}}}}, "response": [{"code": "InvalidInput", "message": "One or more resources provided is invalid. Please check the input again.", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"appointmentResources": [{"resourceId": "ResourceIdNotExist-20334421900"}, {"resourceId": "validResourceId-82309484378"}]}}}}, "response": [{"code": "InvalidInput", "message": "One or more resources provided does not exist or is deleted. Please check the input again.", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentDocuments": [{"uploadDestinationId": "InvalidUploadDestinationId-9cb9bc29-3d7d-5e49-5709-efb693d34468", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Input document id does not exist. Please check the input", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentDocuments": [{"uploadDestinationId": "UploadDestinationIdExpired-9cb9bc29-3d7d-5e49-5709-efb693d34468", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Input document parameters are invalid. Please check the input.", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentDocuments": [{"uploadDestinationId": "InvalidDocumentTypeUploadDestinationId-9cb9bc29-3d7d-5e49-5709-efb693d34468", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Document failed to meet content type restrictions. Please review document uploaded", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentDocuments": [{"uploadDestinationId": "InvalidDocumentLengthUploadDestinationId-9cb9bc29-3d7d-5e49-5709-efb693d34468", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Failed to retrieve document content length. Please review document uploaded", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentDocuments": [{"uploadDestinationId": "ExceededDcoumentLengthUploadDestinationId-9cb9bc29-3d7d-5e49-5709-efb693d34468", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Document failed to meet content length restrictions. Please review document uploaded", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentDocuments": [{"uploadDestinationId": "DocumentNotUploadedUploadDestinationId-9cb9bc29-3d7d-5e49-5709-efb693d34468", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Document encrypted not found or exist. Please review document uploaded", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentDocuments": [{"uploadDestinationId": "IncorrectEncryptedDocumentUploadDestinationId-9cb9bc29-3d7d-5e49-5709-efb693d34468", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Document failed to decrypt or decipher. Please review the uploaded document", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentDocuments": [{"uploadDestinationId": "validUploadDestinationId-9cb9bc29-3d7d-5e49-5709-efb693d34468", "contentSha256": "InvalidSHA256GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Document failed to meet sanity check. Could not get a Sha256 Message Digest instance. Please review document uploaded", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentDocuments": [{"uploadDestinationId": "UploadedFileDetectedMalwareUploadeDestinationId-348293-2384982-239847982379", "contentSha256": "validSHA256GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Document failed to meet malware check. Please review document uploaded", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "InValidResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "InValidUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "ne or more resources provided is invalid. Please check the input again.", "details": ""}, {"code": "InvalidInput", "message": "Appointment start time is required. Please check the input again.", "details": ""}, {"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Input document id does not exist. Please check the input", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "InValidUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Appointment start time is required. Please check the input again.", "details": ""}, {"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Input document id does not exist. Please check the input", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "validUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Appointment start time is required. Please check the input again.", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2022-01-03T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "ResourceIdNotExist-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "validUploadDesitnationID348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "One or more resources provided does not exist or is deleted. Please check the input again.", "details": ""}]}, {"request": {"parameters": {"serviceJobId": {"value": "validJobId-1-9cb9bc29-3d7d-5e49-5709-efb693d34468"}, "appointmentId": {"value": "validAppointmentId-9cb9bc29-3d7d-5e49-5709-efb693d34468_00b9d5f2-839d-c13e-b8cd-dae1c3995b2a"}, "body": {"value": {"fulfillmentTime": {"startTime": "2022-01-03T13:18:10.668Z", "endTime": "2022-01-03T13:18:10.668Z"}, "appointmentResources": [{"resourceId": "validResourceId-20334421900"}, {"resourceId": "validResourceId-82309484378"}], "fulfillmentDocuments": [{"uploadDestinationId": "UploadedFileDetectedMalwareUploadeDestinationId-348293-2384982-239847982379", "contentSha256": "z06EuBzgzc7GiDNVqcxMqYEr7n0BCS9EtNN7szHe0RT="}]}}}}, "response": [{"code": "InvalidInput", "message": "Failed to process proof of appointment input. Reason: Document failed to meet malware check. Please review document uploaded", "details": ""}]}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/ErrorList"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}}}, "parameters": []}, "/service/v1/serviceResources/{resourceId}/capacity/range": {"post": {"tags": ["service"], "description": "Provides capacity slots in a format similar to availability records.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getRangeSlotCapacity", "parameters": [{"name": "resourceId", "description": "Resource Identifier.", "type": "string", "in": "path", "required": true, "maxLength": 100, "minLength": 1}, {"name": "body", "description": "Request body.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RangeSlotCapacityQuery"}}, {"name": "marketplaceIds", "description": "An identifier for the marketplace in which the resource operates.", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"name": "nextPageToken", "description": "Next page token returned in the response of your previous request.", "in": "query", "required": false, "type": "string"}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/RangeSlotCapacity"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"resourceId": {"value": "validResourceId-9d267d55-9426-5bfp-cc47-f167gb969f48"}, "body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY", "AVAILABLE_CAPACITY"], "startDateTime": "2021-04-04T00:00:00Z", "endDateTime": "2021-04-04T02:00:00Z"}}}}, "response": {"resourceId": "validResourceId-9d267d55-9426-5bfp-cc47-f167gb969f48", "capacities": [{"capacityType": "SCHEDULED_CAPACITY", "slots": [{"startDateTime": "2021-04-04T00:00:00Z", "endDateTime": "2021-04-04T02:00:00Z", "capacity": 1}]}, {"capacityType": "AVAILABLE_CAPACITY", "slots": [{"startDateTime": "2021-04-04T00:00:00Z", "endDateTime": "2021-04-04T01:00:00Z", "capacity": 0}, {"startDateTime": "2021-04-04T01:00:00Z", "endDateTime": "2021-04-04T02:00:00Z", "capacity": 1}]}]}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9d267d55-9426-5bfp-cc47-f167gb969f29"}, "body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY", "AVAILABLE_CAPACITY"], "startDateTime": "2022-03-01T00:00:00Z", "endDateTime": "2022-05-30T00:00:00Z"}}}}, "response": {"resourceId": "validResourceId-9d267d55-9426-5bfp-cc47-f167gb969f48", "nextPageToken": "MjAyMi0wNC0wNVQwMDowMDowMFo%3D", "capacities": [{"capacityType": "SCHEDULED_CAPACITY", "slots": [{"startDateTime": "2022-03-01T00:00:00Z", "endDateTime": "2022-04-05T00:00:00Z", "capacity": 1}]}, {"capacityType": "AVAILABLE_CAPACITY", "slots": [{"startDateTime": "2022-03-01T00:00:00Z", "endDateTime": "2022-03-01T10:00:00Z", "capacity": 0}, {"startDateTime": "2022-03-01T10:00:00Z", "endDateTime": "2022-04-05T00:00:00Z", "capacity": 1}]}]}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9d267d55-9426-5bfp-cc47-f167gb969f29"}, "nextPageToken": {"value": "MjAyMi0wNC0wNVQwMDowMDowMFo%3D"}, "body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY", "AVAILABLE_CAPACITY"], "startDateTime": "2022-03-01T00:00:00Z", "endDateTime": "2022-05-30T00:00:00Z"}}}}, "response": {"resourceId": "validResourceId-9d267d55-9426-5bfp-cc47-f167gb969f48", "capacities": [{"capacityType": "SCHEDULED_CAPACITY", "slots": [{"startDateTime": "2022-04-05T00:00:00Z", "endDateTime": "2022-05-30T00:00:00Z", "capacity": 1}]}, {"capacityType": "AVAILABLE_CAPACITY", "slots": [{"startDateTime": "2022-04-05T00:00:00Z", "endDateTime": "2022-05-30T10:00:00Z", "capacity": 1}]}]}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9d267d55-9426-5bfp-cc47-f167gbadfhak32"}, "body": {"value": {"capacityTypes": ["RESERVED_CAPACITY", "AVAILABLE_CAPACITY"], "startDateTime": "2021-04-04T00:00:00Z", "endDateTime": "2021-04-04T02:00:00Z"}}}}, "response": {"resourceId": "validResourceId-9d267d55-9426-5bfp-cc47-f167gbadfhak32", "capacities": [{"capacityType": "AVAILABLE_CAPACITY", "slots": [{"startDateTime": "2021-04-04T00:00:00Z", "endDateTime": "2021-04-04T01:00:00Z", "capacity": 0}, {"startDateTime": "2021-04-04T01:00:00Z", "endDateTime": "2021-04-04T02:00:00Z", "capacity": 1}]}, {"capacityType": "RESERVED_CAPACITY", "slots": [{"startDateTime": "2021-04-04T00:00:00Z", "endDateTime": "2021-04-04T01:00:00Z", "capacity": 1}, {"startDateTime": "2021-04-04T01:00:00Z", "endDateTime": "2021-04-04T02:00:00Z", "capacity": 0}]}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/RangeSlotCapacityErrors"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY"], "startDateTime": "2021-04-04T00:00:00Z", "endDateTime": "2021-04-04T02:00:00Z"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "This is invalid input", "details": "Received the following errors: [Missing or invalid request parameters: [resourceId]]"}]}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9d267d55-9426-5bfp-cc47-f167gb969f48a"}, "body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY"], "startDateTime": "2021-04-04T00:00:00Z"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "This is invalid input", "details": "Received the following errors: [startDateTime or endDateTime are not present]"}]}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9d267d55-9426-5bfp-cc47-f167gb969f48b"}, "body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY"], "startDateTime": "2021-04-04T02:00:00Z", "endDateTime": "2021-04-04T00:00:00Z"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "This is invalid input", "details": "Received the following errors: [endDateTime should be after Start Time]"}]}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9d267d55-9426-5bfp-cc47-f167gb969f48c"}, "body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY"], "startDateTime": "2021-0", "endDateTime": "2021-04-04T00:00:00Z"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "This is invalid input", "details": "Received the following errors: [startDateTime is not a valid ISO date/time object]"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/RangeSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/RangeSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/RangeSlotCapacityErrors"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/RangeSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/RangeSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/RangeSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/RangeSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/RangeSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/serviceResources/{resourceId}/capacity/fixed": {"post": {"tags": ["service"], "description": "Provides capacity in fixed-size slots. \n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getFixedSlotCapacity", "parameters": [{"name": "resourceId", "in": "path", "description": "Resource Identifier.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"in": "body", "name": "body", "description": "Request body.", "required": true, "schema": {"$ref": "#/definitions/FixedSlotCapacityQuery"}}, {"name": "marketplaceIds", "in": "query", "description": "An identifier for the marketplace in which the resource operates.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"name": "nextPageToken", "in": "query", "description": "Next page token returned in the response of your previous request.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/FixedSlotCapacity"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"resourceId": {"value": "validResourceId-9e378g66-9537-6ggq-dd48-f167gb969f48"}, "body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY", "RESERVED_CAPACITY"], "startDateTime": "2021-04-04T00:00:00Z", "endDateTime": "2021-04-04T02:00:00Z", "slotDuration": 30}}}}, "response": {"resourceId": "validResourceId-9e378g66-9537-6ggq-dd48-f167gb969f48", "slotDuration": 30, "capacities": [{"startDateTime": "2021-04-04T00:00:00Z", "scheduledCapacity": 1, "reservedCapacity": 1}, {"startDateTime": "2021-04-04T00:30:00Z", "scheduledCapacity": 1, "reservedCapacity": 0}, {"startDateTime": "2021-04-04T01:00:00Z", "scheduledCapacity": 0, "reservedCapacity": 0}, {"startDateTime": "2021-04-04T01:30:00Z", "scheduledCapacity": 0, "reservedCapacity": 0}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/FixedSlotCapacityErrors"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY"], "startDateTime": "2021-04-04T00:00:00Z", "endDateTime": "2021-04-04T02:00:00Z"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "This is invalid input", "details": "Received the following errors: [Missing or invalid request parameters: [resourceId]]"}]}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9d267d55-9dfa-5bfp-cc47-f167gb969f48a"}, "body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY"], "startDateTime": "2021-04-04T00:00:00Z", "endDateTime": "2021-04-04T02:00:00Z", "slotDuration": 400}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "This is invalid input", "details": "Received the following errors: [Slot duration is not valid, it should be a multiple of 5 and within allowed range.]"}]}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9e378g66-9537-6ggq-dd48-f1klmb969f48"}, "body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY"], "startDateTime": "2021-04-04T02:00:00Z", "endDateTime": "2021-04-04T00:00:00Z"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "This is invalid input", "details": "Received the following errors: [endDateTime should be after Start Time]"}]}}, {"request": {"parameters": {"body": {"resourceId": {"value": "validResourceId-9e378g66-9537-6ggq-dd48-f1klmb969f482"}, "value": {"capacityTypes": ["SCHEDULED_CAPACITY"], "startDateTime": "2021-04-04T00:00:00Z"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "This is invalid input", "details": "Received the following errors: [startDateTime or endDateTime are not present]"}]}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9e378g66-9537-6ggq-dd48-f1klmb969f483"}, "body": {"value": {"capacityTypes": ["SCHEDULED_CAPACITY"], "startDateTime": "2021-0", "endDateTime": "2021-04-04T00:00:00Z"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "This is invalid input", "details": "Received the following errors: [startDateTime is not a valid ISO date/time object]"}]}}]}}, "401": {"description": "The request's Authorization header is not formatted correctly or does not contain a valid token.", "schema": {"$ref": "#/definitions/FixedSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/FixedSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/FixedSlotCapacityErrors"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/FixedSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"description": "Unique request reference identifier.", "type": "string"}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/FixedSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/FixedSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/FixedSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/FixedSlotCapacityErrors"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/serviceResources/{resourceId}/schedules": {"put": {"tags": ["service"], "description": "Update the schedule of the given resource.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "updateSchedule", "parameters": [{"name": "resourceId", "in": "path", "description": "Resource (store) Identifier", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"in": "body", "name": "body", "description": "Schedule details", "required": true, "schema": {"$ref": "#/definitions/UpdateScheduleRequest"}}, {"name": "marketplaceIds", "in": "query", "description": "An identifier for the marketplace in which the resource operates.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/UpdateScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"resourceId": {"value": "validResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36"}, "body": {"value": {"schedules": [{"startTime": "2020-01-01T00:00:00.00-07", "endTime": "2020-01-01T23:59:00.00-07", "recurrence": {"endTime": "2020-01-06T23:59:00.00-07", "daysOfWeek": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"]}}, {"startTime": "2020-01-11T00:00:00.00-07", "endTime": "2020-01-11T23:59:00.00-07", "recurrence": {"endTime": "2020-01-16T23:59:00.00-07", "daysOfWeek": ["MONDAY", "TUESDAY", "WEDNESDAY"]}}]}}}}, "response": {}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36"}, "body": {"value": {"schedules": [{"startTime": "2020-01-01T12:00:00.00-07", "endTime": "2020-01-01T23:59:00.00-07", "recurrence": {"endTime": "2020-01-06T23:59:00.00-07", "daysOfWeek": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"]}}, {"startTime": "2020-01-11T00:00:00.00-07", "endTime": "2020-01-11T23:59:00.00-07", "recurrence": {"endTime": "2020-01-16T23:59:00.00-07", "daysOfWeek": ["MONDAY", "TUESDAY", "WEDNESDAY"]}}]}}}}, "response": {"payload": [{"availability": {"startTime": "2020-01-01T12:00:00.00-07", "endTime": "2020-01-01T23:59:00.00-07", "recurrence": {"endTime": "2020-01-06T23:59:00.00-07", "daysOfWeek": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"]}}, "warnings": [{"code": "ScheduleOverride", "message": "This AvailabilityRecord will override the current schedule as the time-ranges overlap"}]}]}}, {"request": {"parameters": {"resourceId": {"value": "invalidResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36"}, "body": {"value": {"schedules": [{"startTime": "2020-01-01T00:00:00.00-07", "endTime": "2020-01-01T23:59:00.00-07", "recurrence": {"endTime": "2020-01-06T23:59:00.00-07", "daysOfWeek": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"]}}, {"startTime": "2020-01-11T00:00:00.00-07", "endTime": "2020-01-11T23:59:00.00-07", "recurrence": {"endTime": "2020-01-16T23:59:00.00-07", "daysOfWeek": ["MONDAY", "TUESDAY", "WEDNESDAY"]}}]}}}}, "response": {"errors": [{"message": "Invalid request parameters: [resourceId]", "code": "InvalidInput"}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/UpdateScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"resourceId": {"value": "null"}, "body": {"value": {"schedules": []}}}}, "response": {"errors": [{"message": "Missing or invalid request parameters: [resourceId]", "code": "InvalidInput"}]}}, {"request": {"parameters": {"resourceId": {"value": "validResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36"}, "body": {"value": {"schedules": []}}}}, "response": {"errors": [{"message": "Missing or invalid request parameters: [schedule]", "code": "InvalidInput"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/UpdateScheduleResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/UpdateScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/UpdateScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/UpdateScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/UpdateScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/UpdateScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/UpdateScheduleResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/reservation": {"post": {"tags": ["service"], "description": "Create a reservation.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createReservation", "parameters": [{"in": "body", "name": "body", "description": "Reservation details", "required": true, "schema": {"$ref": "#/definitions/CreateReservationRequest"}}, {"name": "marketplaceIds", "in": "query", "description": "An identifier for the marketplace in which the resource operates.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/CreateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"resourceId": "validResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36", "reservation": {"availability": {"startTime": "2020-04-01T10:00:00.00-07", "endTime": "2020-04-01T11:00:00.00-07"}, "type": "BREAK"}}}}}, "response": {"payload": {"reservation": {"reservationId": "457"}}}}, {"request": {"parameters": {"body": {"value": {"resourceId": "invalidResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36", "reservation": {"availability": {"startTime": "2020-04-01T10:00:00.00-07", "endTime": "2020-04-01T11:00:00.00-07"}, "type": "BREAK"}}}}}, "response": {"errors": [{"message": "Invalid request parameters: [resourceId]", "code": "InvalidInput"}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"resourceId": "null", "reservation": {}}}}}, "response": {"errors": [{"message": "Missing or invalid request parameters: [resourceId]", "code": "InvalidInput"}]}}, {"request": {"parameters": {"body": {"value": {"resourceId": "validResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36", "reservation": {}}}}}, "response": {"errors": [{"message": "Missing or invalid request parameters: [reservation]", "code": "InvalidInput"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateReservationResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/CreateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/reservation/{reservationId}": {"put": {"tags": ["service"], "description": "Update a reservation.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "updateReservation", "parameters": [{"name": "reservationId", "in": "path", "description": "Reservation Identifier", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"in": "body", "name": "body", "description": "Reservation details", "required": true, "schema": {"$ref": "#/definitions/UpdateReservationRequest"}}, {"name": "marketplaceIds", "in": "query", "description": "An identifier for the marketplace in which the resource operates.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/UpdateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reservationId": {"value": "456"}, "body": {"value": {"resourceId": "validResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36", "reservation": {"availability": {"startTime": "2020-04-01T10:00:00.00-07", "endTime": "2020-04-01T11:00:00.00-07"}, "type": "BREAK"}}}}}, "response": {"payload": {"reservation": {"reservationId": "457"}}}}, {"request": {"parameters": {"reservationId": {"value": "456"}, "body": {"value": {"resourceId": "invalidResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36", "reservation": {"availability": {"startTime": "2020-04-01T10:00:00.00-07", "endTime": "2020-04-01T11:00:00.00-07"}, "type": "BREAK"}}}}}, "response": {"errors": [{"message": "Invalid request parameters: [resourceId]", "code": "InvalidInput"}]}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/UpdateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"resourceId": "null", "reservation": {}}}}}, "response": {"errors": [{"message": "Missing or invalid request parameters: [resourceId]", "code": "InvalidInput"}]}}, {"request": {"parameters": {"body": {"value": {"resourceId": "validResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36", "reservation": {}}}}}, "response": {"errors": [{"message": "Missing or invalid request parameters: [reservation]", "code": "InvalidInput"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/UpdateReservationResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The reservation specified does not exist.", "schema": {"$ref": "#/definitions/UpdateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reservationId": {"value": "not-existent-456"}, "body": {"value": {"resourceId": "invalidResourceId-9c156c44-8315-4aeb-bb36-e056fa827e36", "reservation": {"availability": {"startTime": "2020-04-01T10:00:00.00-07", "endTime": "2020-04-01T11:00:00.00-07"}, "type": "BREAK"}}}}}, "response": {"errors": [{"message": "Could not find reservation with ID: [not-existent-456]", "code": "InvalidInput"}]}}]}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/UpdateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/UpdateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/UpdateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/UpdateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/UpdateReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "delete": {"tags": ["service"], "description": "Cancel a reservation. \n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "cancelReservation", "parameters": [{"name": "reservationId", "in": "path", "description": "Reservation Identifier", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"name": "marketplaceIds", "in": "query", "description": "An identifier for the marketplace in which the resource operates.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}], "responses": {"204": {"description": "Success response.", "schema": {"$ref": "#/definitions/CancelReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reservationId": {"value": "validReservationId-9c156c44-8315-4aeb-bb36-e056fa827e36"}}}, "response": {}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CancelReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reservationId": {"value": "invalidReservationId-a654baa-8315-4aeb-bb36-e056fa827e36"}}}, "response": {"errors": [{"message": "Missing or invalid request parameters: [reservationId]", "code": "InvalidInput"}]}}]}}, "403": {"description": "403 can be caused for reasons like Access Denied, Unauthorized, Expired Token, Invalid Signature or Resource Not Found.", "schema": {"$ref": "#/definitions/CancelReservationResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "404": {"description": "The reservation specified does not exist.", "schema": {"$ref": "#/definitions/CancelReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"reservationId": {"value": "nonExistingReservationId-a3726c44-8315-4aeb-bb36-e056fa827e36"}}}, "response": {"errors": [{"message": "Reservation does not exist", "code": "InvalidInput"}]}}]}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CancelReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "415": {"description": "The entity of the request is in a format not supported by the requested resource.", "schema": {"$ref": "#/definitions/CancelReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CancelReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "500": {"description": "Encountered an unexpected condition which prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CancelReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CancelReservationResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}}}, "parameters": []}, "/service/v1/serviceJobs/{serviceJobId}/appointmentSlots": {"get": {"tags": ["service"], "description": "Gets appointment slots for the service associated with the service job id specified.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getAppointmmentSlotsByJobId", "parameters": [{"name": "serviceJobId", "in": "path", "description": "A service job identifier to retrive appointment slots for associated service.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"name": "marketplaceIds", "in": "query", "description": "An identifier for the marketplace in which the resource operates.", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"name": "startTime", "in": "query", "description": "A time from which the appointment slots will be retrieved. The specified time must be in ISO 8601 format. If `startTime` is provided, `endTime` should also be provided. Default value is as per business configuration.", "required": false, "type": "string"}, {"name": "endTime", "in": "query", "description": "A time up to which the appointment slots will be retrieved. The specified time must be in ISO 8601 format. If `endTime` is provided, `startTime` should also be provided. Default value is as per business configuration. Maximum range of appointment slots can be 90 days.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "validJob-9c156c44-8315-4aeb-bb36-e056fa827e36"}}}, "response": {"payload": {"startTime": "2021-04-04T00:00:00Z", "endTime": "2021-04-04T02:00:00Z", "schedulingType": "REAL_TIME_SCHEDULING", "appointmentSlots": [{"startTime": "2021-04-04T00:00:00Z", "endTime": "2021-04-04T01:0:00Z", "capacity": 20}, {"startTime": "2021-04-04T01:00:00Z", "endTime": "2021-04-04T02:0:00Z", "capacity": 0}]}}}, {"request": {"parameters": {"serviceJobId": {"value": "validJob-9c156c44-8315-4aeb-bb36-e056fa827e36"}, "startTime": {"value": "2021-04-04T00:00:00Z"}, "endTime": {"value": "2021-04-04T02:00:00Z"}}}, "response": {"payload": {"startTime": "2021-04-04T00:00:00Z", "endTime": "2021-04-04T02:00:00Z", "schedulingType": "REAL_TIME_SCHEDULING", "appointmentSlots": [{"startTime": "2021-04-04T00:00:00Z", "endTime": "2021-04-04T01:0:00Z", "capacity": 20}, {"startTime": "2021-04-04T01:00:00Z", "endTime": "2021-04-04T02:0:00Z", "capacity": 0}]}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"serviceJobId": {"value": "nullJobId"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [serviceJobId]"}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJob-9c156c44-8315-4aeb-bb36-e056fa827e36"}, "endTime": {"value": "20-21-04-04T01:00:00Z"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "End Time is not a valid ISO date/time object"}]}}, {"request": {"parameters": {"serviceJobId": {"value": "validJob-9c156c44-8315-4aeb-bb36-e056fa827e36"}, "startTime": {"value": "2021-04-04T05:00:00Z"}, "endTime": {"value": "2021-04-04T02:00:00Z"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "End Time should be after Start Time"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "422": {"description": "Unprocessable Entity. Unable to process the contained instructions.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/appointmentSlots": {"get": {"tags": ["service"], "description": "Gets appointment slots as per the service context specified.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 20 | 40 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "getAppointmentSlots", "parameters": [{"name": "asin", "in": "query", "description": "ASIN associated with the service.", "required": true, "type": "string"}, {"name": "storeId", "in": "query", "description": "Store identifier defining the region scope to retrive appointment slots.", "required": true, "type": "string", "maxLength": 100, "minLength": 1}, {"name": "marketplaceIds", "in": "query", "description": "An identifier for the marketplace for which appointment slots are queried", "required": true, "type": "array", "items": {"type": "string"}, "maxItems": 1}, {"name": "startTime", "in": "query", "description": "A time from which the appointment slots will be retrieved. The specified time must be in ISO 8601 format. If `startTime` is provided, `endTime` should also be provided. Default value is as per business configuration.", "required": false, "type": "string"}, {"name": "endTime", "in": "query", "description": "A time up to which the appointment slots will be retrieved. The specified time must be in ISO 8601 format. If `endTime` is provided, `startTime` should also be provided. Default value is as per business configuration. Maximum range of appointment slots can be 90 days.", "required": false, "type": "string"}], "responses": {"200": {"description": "Success response.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"asin": {"value": "B07BB1RGT5"}, "storeId": {"value": "53694163-6dc8-4f80-b6b1-ec47b7b9747e"}}}, "response": {"payload": {"startTime": "2021-04-04T00:00:00Z", "endTime": "2021-04-04T02:00:00Z", "schedulingType": "REAL_TIME_SCHEDULING", "appointmentSlots": [{"startTime": "2021-04-04T00:00:00Z", "endTime": "2021-04-04T01:0:00Z", "capacity": 20}, {"startTime": "2021-04-04T01:00:00Z", "endTime": "2021-04-04T02:0:00Z", "capacity": 0}]}}}, {"request": {"parameters": {"asin": {"value": "B07BB1RGT6"}, "storeId": {"value": "53694163-6dc8-4f80-b6b1-ec47b7b9747f"}, "startTime": {"value": "2021-04-04T00:00:00Z"}, "endTime": {"value": "2021-04-04T02:00:00Z"}}}, "response": {"payload": {"startTime": "2021-04-04T00:00:00Z", "endTime": "2021-04-04T02:00:00Z", "schedulingType": "NON_REAL_TIME_SCHEDULING", "appointmentSlots": [{"startTime": "2021-04-04T00:00:00Z", "endTime": "2021-04-04T01:0:00Z", "capacity": 20}, {"startTime": "2021-04-04T01:00:00Z", "endTime": "2021-04-04T02:0:00Z", "capacity": 0}]}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"asin": {"value": "nullValue"}, "storeId": {"value": "53694163-6dc8-4f80-b6b1-ec47b7b9747e"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [asin]"}]}}, {"request": {"parameters": {"asin": {"value": "B07BB1RGT5"}, "storeId": {"value": "53694163-6dc8-4f80-b6b1-ec47b7b9747e"}, "endTime": {"value": "20-21-04-04T01:00:00Z"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Range End Time is not a valid ISO date/time object"}]}}, {"request": {"parameters": {"asin": {"value": "B07BB1RGT5"}, "storeId": {"value": "53694163-6dc8-4f80-b6b1-ec47b7b9747e"}, "startTime": {"value": "2021-04-04T05:00:00Z"}, "endTime": {"value": "2021-04-04T02:00:00Z"}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Range End Time should be after Range Start Time"}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "415": {"description": "The request payload is in an unsupported format.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "422": {"description": "Unprocessable Entity. Unable to process the contained instructions.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/GetAppointmentSlotsResponse"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}}}, "parameters": []}, "/service/v1/documents": {"post": {"tags": ["service"], "description": "Creates an upload destination.\n\n**Usage Plan:**\n\n| Rate (requests per second) | Burst |\n| ---- | ---- |\n| 5 | 20 |\n\nThe `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).", "operationId": "createServiceDocumentUploadDestination", "parameters": [{"in": "body", "name": "body", "description": "Upload document operation input details.", "required": true, "schema": {"$ref": "#/definitions/ServiceUploadDocument"}}], "responses": {"200": {"description": "Successfully created an upload destination for the given resource.", "schema": {"$ref": "#/definitions/CreateServiceDocumentUploadDestination"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-requestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"contentType": "PNG", "contentLength": 1386437, "contentMD5": "97WrSKv9ffHkDopCdB32mw=="}}}}, "response": {"payload": {"encryptionDetails": {"standard": "AES", "initializationVector": "paPlpo1iBBLmyOhU0mIo5g==", "key": "PDuDJm2l+0ydObrRpS48tB+t2qbtOmWhSEOiFWKnH2k="}, "uploadDestinationId": "amzn1.tortuga.3.15ba627d-8e24-42ad-89d1-5eb01f5ba0af.T15MXQRST78UTC<->amzn1.tortuga.1.token.DizquVc+EoX/lWAV/7WTlw==", "url": "https://tortuga-devo.s3-us-west-2.amazonaws.com/%2FThirtyDays/amzn1.tortuga.3.15ba627d-8e24-42ad-89d1-5eb01f5ba0af.T15MXQRST78UTC?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20210108T103450Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Expires=300&X-Amz-Credential=AKIAUR3X5C6O5CADVWED%2F20210108%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Signature=dd2fefe6c6102aba14bab1481b33cf07dcc0385bd49f7eb5796d77b082ea5ba3"}}}]}}, "400": {"description": "Request has missing or invalid parameters and cannot be parsed.", "schema": {"$ref": "#/definitions/CreateServiceDocumentUploadDestination"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [contentType, contentLength].", "details": ""}]}}, {"request": {"parameters": {"body": {"value": {"contentType": "PNG"}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [contentLength].", "details": ""}]}}, {"request": {"parameters": {"body": {"value": {"contentLength": 123}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "Missing or invalid request parameters: [contentType].", "details": ""}]}}]}}, "403": {"description": "Indicates that access to the resource is forbidden. Possible reasons include Access Denied, Unauthorized, Expired Token, or Invalid Signature.", "schema": {"$ref": "#/definitions/CreateServiceDocumentUploadDestination"}, "headers": {"x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "404": {"description": "The resource specified does not exist.", "schema": {"$ref": "#/definitions/CreateServiceDocumentUploadDestination"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "413": {"description": "The request size exceeded the maximum accepted size.", "schema": {"$ref": "#/definitions/CreateServiceDocumentUploadDestination"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}, "415": {"description": "The request's Content-Type header is invalid.", "schema": {"$ref": "#/definitions/CreateServiceDocumentUploadDestination"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-requestid": {"type": "string", "description": "Unique request reference identifier."}}}, "422": {"description": "Unprocessable Entity. Unable to process the contained instructions.", "schema": {"$ref": "#/definitions/CreateServiceDocumentUploadDestination"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}, "x-amzn-api-sandbox": {"static": [{"request": {"parameters": {"body": {"value": {"contentType": "PNGr", "contentLength": 1386437, "contentMD5": "97WrSKv9ffHkDopCdB32mw=="}}}}, "response": {"errors": [{"code": "InvalidInput", "message": "contentType parameter is invalid. Use one of ['TIFF', 'JPG', 'PNG', 'JPEG', 'GIF', 'PDF']", "details": ""}]}}]}}, "429": {"description": "The frequency of requests was greater than allowed.", "schema": {"$ref": "#/definitions/CreateServiceDocumentUploadDestination"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-requestid": {"type": "string", "description": "Unique request reference ID."}}}, "500": {"description": "An unexpected condition occurred that prevented the server from fulfilling the request.", "schema": {"$ref": "#/definitions/CreateServiceDocumentUploadDestination"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference identifier."}}}, "503": {"description": "Temporary overloading or maintenance of the server.", "schema": {"$ref": "#/definitions/CreateServiceDocumentUploadDestination"}, "headers": {"x-amzn-RateLimit-Limit": {"type": "string", "description": "Your rate limit (requests per second) for this operation.\n**Note:** For this status code, the rate limit header is deprecated and no longer returned."}, "x-amzn-RequestId": {"type": "string", "description": "Unique request reference id."}}}}}, "parameters": []}}, "definitions": {"GetServiceJobByServiceJobIdResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `getServiceJobByServiceJobId` operation.", "$ref": "#/definitions/ServiceJob"}, "errors": {"description": "An unexpected condition occurred during the `getServiceJobByServiceJobId` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `getServiceJobByServiceJobId` operation."}, "CancelServiceJobByServiceJobIdResponse": {"type": "object", "properties": {"errors": {"description": "Encountered errors for the `cancelServiceJobByServiceJobId` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "Response schema for the `cancelServiceJobByServiceJobId` operation."}, "CompleteServiceJobByServiceJobIdResponse": {"type": "object", "properties": {"errors": {"description": "Encountered errors for the `completeServiceJobByServiceJobId` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "Response schema for the `completeServiceJobByServiceJobId` operation."}, "GetServiceJobsResponse": {"type": "object", "properties": {"payload": {"description": "The payload for the `getServiceJobs` operation.", "$ref": "#/definitions/JobListing"}, "errors": {"description": "An unexpected condition occurred during the `getServiceJobs` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "Response schema for the `getServiceJobs` operation."}, "SetAppointmentResponse": {"type": "object", "properties": {"appointmentId": {"description": "New appointment identifier generated during the `addAppointmentForServiceJobByServiceJobId` or `rescheduleAppointmentForServiceJobByServiceJobId` operations.", "$ref": "#/definitions/AppointmentId"}, "warnings": {"description": "Warnings generated during the `addAppointmentForServiceJobByServiceJobId` or `rescheduleAppointmentForServiceJobByServiceJobId` operations.", "$ref": "#/definitions/WarningList"}, "errors": {"description": "Errors occurred during during the `addAppointmentForServiceJobByServiceJobId` or `rescheduleAppointmentForServiceJobByServiceJobId` operations.", "$ref": "#/definitions/ErrorList"}}, "description": "Response schema for the `addAppointmentForServiceJobByServiceJobId` and `rescheduleAppointmentForServiceJobByServiceJobId` operations."}, "AssignAppointmentResourcesResponse": {"type": "object", "properties": {"payload": {"type": "object", "description": "The payload for the `assignAppointmentResource` operation.", "properties": {"warnings": {"description": "Warnings generated during the `assignAppointmentResources` operation.", "$ref": "#/definitions/WarningList"}}}, "errors": {"description": "Errors occurred during during the `assignAppointmentResources` operation.", "$ref": "#/definitions/ErrorList"}}, "description": "Response schema for the `assignAppointmentResources` operation."}, "AssignAppointmentResourcesRequest": {"type": "object", "required": ["resources"], "properties": {"resources": {"description": "List of resource objects to be assigned.", "$ref": "#/definitions/AppointmentResources"}}, "description": "Request schema for the `assignAppointmentResources` operation."}, "JobListing": {"type": "object", "properties": {"totalResultSize": {"type": "integer", "description": "Total result size of the query result."}, "nextPageToken": {"type": "string", "description": "A generated string used to pass information to your next request. If `nextPageToken` is returned, pass the value of `nextPageToken` to the `pageToken` to get next results."}, "previousPageToken": {"type": "string", "description": "A generated string used to pass information to your next request. If `previousPageToken` is returned, pass the value of `previousPageToken` to the `pageToken` to get previous page results."}, "jobs": {"type": "array", "description": "List of job details for the given input.", "items": {"$ref": "#/definitions/ServiceJob"}}}, "description": "The payload for the `getServiceJobs` operation."}, "ServiceJob": {"type": "object", "properties": {"createTime": {"type": "string", "format": "date-time", "description": "The date and time of the creation of the job in ISO 8601 format."}, "serviceJobId": {"description": "The service job identifier.", "$ref": "#/definitions/ServiceJobId"}, "serviceJobStatus": {"type": "string", "description": "The status of the service job.", "enum": ["NOT_SERVICED", "CANCELLED", "COMPLETED", "PENDING_SCHEDULE", "NOT_FULFILLABLE", "HOLD", "PAYMENT_DECLINED"], "x-docgen-enum-table-extension": [{"value": "NOT_SERVICED", "description": "Indicates that the service for the service job is not complete."}, {"value": "CANCELLED", "description": "Indicates that the service job is cancelled."}, {"value": "COMPLETED", "description": "Indicates that the service is performed and the service job is closed successfully."}, {"value": "PENDING_SCHEDULE", "description": "Indicates that an appointment for the service job has not been scheduled."}, {"value": "NOT_FULFILLABLE", "description": "Indicates that the service job is not actionable due to an unexpected exception."}, {"value": "HOLD", "description": "Indicates that the appointment time preference given by customer cannot be serviced by the service provider."}, {"value": "PAYMENT_DECLINED", "description": "Indicates that the customer payment has been declined."}]}, "scopeOfWork": {"description": "The scope of work for the order.", "$ref": "#/definitions/ScopeOfWork"}, "seller": {"description": "Information about the seller of the service job.", "$ref": "#/definitions/Seller"}, "serviceJobProvider": {"description": "Information about the service job provider.", "$ref": "#/definitions/ServiceJobProvider"}, "preferredAppointmentTimes": {"type": "array", "description": "A list of appointment windows preferred by the buyer. Included only if the buyer selected appointment windows when creating the order.", "items": {"$ref": "#/definitions/AppointmentTime"}}, "appointments": {"type": "array", "description": "A list of appointments.", "items": {"$ref": "#/definitions/Appointment"}}, "serviceOrderId": {"description": "The Amazon-defined identifier for an order placed by the buyer in 3-7-7 format.", "$ref": "#/definitions/OrderId"}, "marketplaceId": {"type": "string", "description": "The marketplace identifier.", "pattern": "^[A-Z0-9]*$"}, "storeId": {"type": "string", "description": "The Amazon-defined identifier for the region scope.", "minLength": 1, "maxLength": 100}, "buyer": {"description": "Information about the buyer.", "$ref": "#/definitions/Buyer"}, "associatedItems": {"type": "array", "description": "A list of items associated with the service job.", "items": {"$ref": "#/definitions/AssociatedItem"}}, "serviceLocation": {"description": "Information about the location of the service job.", "$ref": "#/definitions/ServiceLocation"}}, "description": "The job details of a service."}, "ServiceJobId": {"type": "string", "description": "Amazon identifier for the service job.", "minLength": 1, "maxLength": 100}, "OrderId": {"type": "string", "description": "The Amazon-defined identifier for an order placed by the buyer, in 3-7-7 format.", "minLength": 5, "maxLength": 20}, "ScopeOfWork": {"type": "object", "properties": {"asin": {"type": "string", "description": "The Amazon Standard Identification Number (ASIN) of the service job."}, "title": {"type": "string", "description": "The title of the service job."}, "quantity": {"type": "integer", "description": "The number of service jobs."}, "requiredSkills": {"type": "array", "description": "A list of skills required to perform the job.", "items": {"type": "string"}}}, "description": "The scope of work for the order."}, "Seller": {"type": "object", "properties": {"sellerId": {"type": "string", "description": "The identifier of the seller of the service job.", "pattern": "^[A-Z0-9]*$"}}, "description": "Information about the seller of the service job."}, "ServiceJobProvider": {"type": "object", "properties": {"serviceJobProviderId": {"type": "string", "description": "The identifier of the service job provider.", "pattern": "^[A-Z0-9]*$"}}, "description": "Information about the service job provider."}, "Buyer": {"type": "object", "properties": {"buyerId": {"type": "string", "description": "The identifier of the buyer.", "pattern": "^[A-Z0-9]*$"}, "name": {"type": "string", "description": "The name of the buyer."}, "phone": {"type": "string", "description": "The phone number of the buyer."}, "isPrimeMember": {"type": "boolean", "description": "When true, the service is for an Amazon Prime buyer."}}, "description": "Information about the buyer."}, "AppointmentTime": {"type": "object", "required": ["durationInMinutes", "startTime"], "properties": {"startTime": {"type": "string", "format": "date-time", "description": "The date and time of the start of the appointment window in ISO 8601 format."}, "durationInMinutes": {"type": "integer", "description": "The duration of the appointment window, in minutes.", "minimum": 1}}, "description": "The time of the appointment window."}, "AppointmentId": {"type": "string", "description": "The appointment identifier.", "minLength": 5, "maxLength": 100}, "Appointment": {"type": "object", "properties": {"appointmentId": {"description": "The appointment identifier.", "$ref": "#/definitions/AppointmentId"}, "appointmentStatus": {"type": "string", "description": "The status of the appointment.", "enum": ["ACTIVE", "CANCELLED", "COMPLETED"], "x-docgen-enum-table-extension": [{"value": "ACTIVE", "description": "Indicates that an appointment is scheduled."}, {"value": "CANCELLED", "description": "Indicates that the appointment is cancelled."}, {"value": "COMPLETED", "description": "Indicates that the appointment is completed."}]}, "appointmentTime": {"description": "The time of the appointment window.", "$ref": "#/definitions/AppointmentTime"}, "assignedTechnicians": {"type": "array", "description": "A list of technicians assigned to the service job.", "items": {"$ref": "#/definitions/Technician"}, "minItems": 1}, "rescheduledAppointmentId": {"description": "The identifier of a rescheduled appointment.", "$ref": "#/definitions/AppointmentId"}, "poa": {"description": "Proof of Appointment (POA) details.", "$ref": "#/definitions/Poa"}}, "description": "The details of an appointment."}, "Technician": {"type": "object", "properties": {"technicianId": {"type": "string", "description": "The technician identifier.", "minLength": 1, "maxLength": 50}, "name": {"type": "string", "description": "The name of the technician."}}, "description": "A technician who is assigned to perform the service job in part or in full."}, "Poa": {"type": "object", "properties": {"appointmentTime": {"description": "The time of the appointment window.", "$ref": "#/definitions/AppointmentTime"}, "technicians": {"type": "array", "description": "A list of technicians.", "items": {"$ref": "#/definitions/Technician"}, "minItems": 1}, "uploadingTechnician": {"type": "string", "description": "The identifier of the technician who uploaded the POA.", "pattern": "^[A-Z0-9]*$"}, "uploadTime": {"type": "string", "format": "date-time", "description": "The date and time when the POA was uploaded in ISO 8601 format."}, "poaType": {"type": "string", "description": "The type of POA uploaded.", "enum": ["NO_SIGNATURE_DUMMY_POS", "CUSTOMER_SIGNATURE", "DUMMY_RECEIPT", "POA_RECEIPT"], "x-docgen-enum-table-extension": [{"value": "NO_SIGNATURE_DUMMY_POS", "description": "Indicates that the type of proof of appointment uploaded is a dummy signature."}, {"value": "CUSTOMER_SIGNATURE", "description": "Indicates that the type of proof of appointment uploaded is a customer signature."}, {"value": "DUMMY_RECEIPT", "description": "Indicates that the type of proof of appointment uploaded is a dummy receipt."}, {"value": "POA_RECEIPT", "description": "Indicates that the type of proof of appointment is a receipt."}]}}, "description": "Proof of Appointment (POA) details."}, "AssociatedItem": {"type": "object", "properties": {"asin": {"type": "string", "description": "The Amazon Standard Identification Number (ASIN) of the item."}, "title": {"type": "string", "description": "The title of the item."}, "quantity": {"type": "integer", "description": "The total number of items included in the order."}, "orderId": {"description": "The Amazon-defined identifier for an order placed by the buyer in 3-7-7 format.", "$ref": "#/definitions/OrderId"}, "itemStatus": {"type": "string", "description": "The status of the item.", "enum": ["ACTIVE", "CANCELLED", "SHIPPED", "DELIVERED"], "x-docgen-enum-table-extension": [{"value": "ACTIVE", "description": "Indicates the item is yet to be shipped."}, {"value": "CANCELLED", "description": "Indicates the item has been cancelled."}, {"value": "SHIPPED", "description": "Indicates the item is shipped but not delivered."}, {"value": "DELIVERED", "description": "Indicates the item is delivered."}]}, "brandName": {"type": "string", "description": "The brand name of the item."}, "itemDelivery": {"description": "Delivery information for the item.", "$ref": "#/definitions/ItemDelivery"}}, "description": "Information about an item associated with the service job."}, "ItemDelivery": {"type": "object", "properties": {"estimatedDeliveryDate": {"type": "string", "format": "date-time", "description": "The date and time of the latest Estimated Delivery Date (EDD) of all the items with an EDD. In ISO 8601 format."}, "itemDeliveryPromise": {"description": "Promised delivery information for the item.", "$ref": "#/definitions/ItemDeliveryPromise"}}, "description": "Delivery information for the item."}, "ItemDeliveryPromise": {"type": "object", "properties": {"startTime": {"type": "string", "format": "date-time", "description": "The date and time of the start of the promised delivery window in ISO 8601 format."}, "endTime": {"type": "string", "format": "date-time", "description": "The date and time of the end of the promised delivery window in ISO 8601 format."}}, "description": "Promised delivery information for the item."}, "ServiceLocation": {"type": "object", "properties": {"serviceLocationType": {"type": "string", "description": "The location of the service job.", "enum": ["IN_HOME", "IN_STORE", "ONLINE"], "x-docgen-enum-table-extension": [{"value": "IN_HOME", "description": "Indicates the service for the service job is performed at the customers home address."}, {"value": "IN_STORE", "description": "Indicates the service for the service job is performed at the service providers store."}, {"value": "ONLINE", "description": "Indicates the service for the service job is performed remotely."}]}, "address": {"description": "The shipping address for the service job.", "$ref": "#/definitions/Address"}}, "description": "Information about the location of the service job."}, "Address": {"type": "object", "required": ["addressLine1", "name"], "properties": {"name": {"type": "string", "description": "The name of the person, business, or institution."}, "addressLine1": {"type": "string", "description": "The first line of the address."}, "addressLine2": {"type": "string", "description": "Additional address information, if required."}, "addressLine3": {"type": "string", "description": "Additional address information, if required."}, "city": {"type": "string", "description": "The city."}, "county": {"type": "string", "description": "The county."}, "district": {"type": "string", "description": "The district."}, "stateOrRegion": {"type": "string", "description": "The state or region."}, "postalCode": {"type": "string", "description": "The postal code. This can contain letters, digits, spaces, and/or punctuation."}, "countryCode": {"type": "string", "description": "The two digit country code, in ISO 3166-1 alpha-2 format."}, "phone": {"type": "string", "description": "The phone number."}}, "description": "The shipping address for the service job."}, "AddAppointmentRequest": {"type": "object", "required": ["appointmentTime"], "properties": {"appointmentTime": {"description": "Input appointment time details.", "$ref": "#/definitions/AppointmentTimeInput"}}, "description": "Input for add appointment operation."}, "RescheduleAppointmentRequest": {"type": "object", "required": ["appointmentTime", "rescheduleReasonCode"], "properties": {"appointmentTime": {"description": "Input appointment time details.", "$ref": "#/definitions/AppointmentTimeInput"}, "rescheduleReasonCode": {"description": "Input appointment reschedule reason.", "$ref": "#/definitions/RescheduleReasonCode"}}, "description": "Input for rescheduled appointment operation."}, "AppointmentTimeInput": {"type": "object", "required": ["startTime"], "properties": {"startTime": {"type": "string", "format": "date-time", "description": "The date, time in UTC for the start time of an appointment in ISO 8601 format."}, "durationInMinutes": {"type": "integer", "description": "The duration of an appointment in minutes."}}, "description": "The input appointment time details."}, "RescheduleReasonCode": {"type": "string", "description": "The appointment reschedule reason code."}, "ErrorList": {"type": "array", "description": "A list of error responses returned when a request is unsuccessful.", "items": {"$ref": "#/definitions/Error"}}, "Error": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An error code that identifies the type of error that occurred."}, "message": {"type": "string", "description": "A message that describes the error condition in a human-readable form."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or fix the issue."}, "errorLevel": {"type": "string", "description": "The type of error.", "enum": ["ERROR", "WARNING"], "x-docgen-enum-table-extension": [{"value": "ERROR", "description": "Error"}, {"value": "WARNING", "description": "Warning"}]}}, "description": "Error response returned when the request is unsuccessful."}, "WarningList": {"type": "array", "description": "A list of warnings returned in the sucessful execution response of an API request.", "items": {"$ref": "#/definitions/Warning"}}, "Warning": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string", "description": "An warning code that identifies the type of warning that occurred."}, "message": {"type": "string", "description": "A message that describes the warning condition in a human-readable form."}, "details": {"type": "string", "description": "Additional details that can help the caller understand or address the warning."}}, "description": "Warning returned when the request is successful, but there are important callouts based on which API clients should take defined actions."}, "RangeSlotCapacityErrors": {"description": "The error response schema for the `getRangeSlotCapacity` operation.", "type": "object", "properties": {"errors": {"description": "Errors encountered during the `getRangeSlotCapacity` operation.", "$ref": "#/definitions/ErrorList"}}}, "RangeSlotCapacity": {"description": "Response schema for the `getRangeSlotCapacity` operation.", "type": "object", "properties": {"resourceId": {"description": "Resource Identifier.", "type": "string"}, "capacities": {"description": "Array of range capacities where each entry is for a specific capacity type.", "type": "array", "items": {"$ref": "#/definitions/RangeCapacity"}}, "nextPageToken": {"description": "Next page token, if there are more pages.", "type": "string"}}}, "RangeCapacity": {"description": "Range capacity entity where each entry has a capacity type and corresponding slots.", "type": "object", "properties": {"capacityType": {"description": "Capacity type corresponding to the slots.", "$ref": "#/definitions/CapacityType"}, "slots": {"description": "Array of capacity slots in range slot format.", "type": "array", "items": {"$ref": "#/definitions/RangeSlot"}}}}, "RangeSlot": {"description": "Capacity slots represented in a format similar to availability rules.", "type": "object", "properties": {"startDateTime": {"description": "Start date time of slot in ISO 8601 format with precision of seconds.", "type": "string", "format": "date-time"}, "endDateTime": {"description": "End date time of slot in ISO 8601 format with precision of seconds.", "type": "string", "format": "date-time"}, "capacity": {"description": "Capacity of the slot.", "type": "integer", "format": "int32"}}}, "FixedSlotCapacityErrors": {"description": "The error response schema for the `getFixedSlotCapacity` operation.", "type": "object", "properties": {"errors": {"description": "Errors encountered during the `getFixedSlotCapacity` operation.", "$ref": "#/definitions/ErrorList"}}}, "FixedSlotCapacity": {"description": "Response schema for the `getFixedSlotCapacity` operation.", "type": "object", "properties": {"resourceId": {"description": "Resource Identifier.", "type": "string"}, "slotDuration": {"description": "The duration of each slot which is returned. This value will be a multiple of 5 and fall in the following range: 5 <= `slotDuration` <= 360.", "type": "number", "format": "int32", "multipleOf": 5}, "capacities": {"description": "Array of capacity slots in fixed slot format.", "type": "array", "items": {"$ref": "#/definitions/FixedSlot"}}, "nextPageToken": {"description": "Next page token, if there are more pages.", "type": "string"}}}, "FixedSlot": {"description": "In this slot format each slot only has the requested capacity types. This slot size is as specified by slot duration.", "type": "object", "properties": {"startDateTime": {"description": "Start date time of slot in ISO 8601 format with precision of seconds.", "type": "string", "format": "date-time"}, "scheduledCapacity": {"description": "Scheduled capacity corresponding to the slot. This capacity represents the originally allocated capacity as per resource schedule.", "type": "integer", "format": "int32"}, "availableCapacity": {"description": "Available capacity corresponding to the slot. This capacity represents the capacity available for allocation to reservations.", "type": "integer", "format": "int32"}, "encumberedCapacity": {"description": "Encumbered capacity corresponding to the slot. This capacity represents the capacity allocated for Amazon Jobs/Appointments/Orders.", "type": "integer", "format": "int32"}, "reservedCapacity": {"description": "Reserved capacity corresponding to the slot. This capacity represents the capacity made unavailable due to events like Breaks/Leaves/Lunch.", "type": "integer", "format": "int32"}}}, "UpdateScheduleResponse": {"type": "object", "properties": {"payload": {"type": "array", "description": "Contains the `UpdateScheduleRecords` for which the error/warning has occurred.", "items": {"$ref": "#/definitions/UpdateScheduleRecord"}}, "errors": {"description": "Errors encountered, if any.", "$ref": "#/definitions/ErrorList"}}, "description": "Response schema for the `updateSchedule` operation."}, "SetAppointmentFulfillmentDataRequest": {"type": "object", "properties": {"fulfillmentTime": {"description": "Input appointment time details.", "$ref": "#/definitions/FulfillmentTime"}, "appointmentResources": {"description": "Resources involved in appointment fulfillment.", "$ref": "#/definitions/AppointmentResources"}, "fulfillmentDocuments": {"description": "Documents specific to appointment fulfillment.", "$ref": "#/definitions/FulfillmentDocuments"}}, "description": "Input for set appointment fulfillment data operation."}, "FulfillmentTime": {"type": "object", "properties": {"startTime": {"type": "string", "format": "date-time", "description": "The date, time in UTC of the fulfillment start time in ISO 8601 format."}, "endTime": {"type": "string", "format": "date-time", "description": "The date, time in UTC of the fulfillment end time in ISO 8601 format."}}, "description": "Input for fulfillment time details"}, "FulfillmentDocuments": {"type": "array", "description": "List of documents captured during service appointment fulfillment.", "items": {"$ref": "#/definitions/FulfillmentDocument"}}, "FulfillmentDocument": {"type": "object", "properties": {"uploadDestinationId": {"type": "string", "description": "The identifier of the upload destination. Get this value by calling the `createServiceDocumentUploadDestination` operation of the Services API."}, "contentSha256": {"type": "string", "description": "Sha256 hash of the file content. This value is used to determine if the file has been corrupted or tampered with during transit."}}, "description": "Document that captured during service appointment fulfillment that portrays proof of completion"}, "AppointmentResources": {"type": "array", "description": "List of resources that performs or performed job appointment fulfillment.", "items": {"$ref": "#/definitions/AppointmentResource"}}, "AppointmentResource": {"type": "object", "properties": {"resourceId": {"type": "string", "description": "The resource identifier."}}, "description": "The resource that performs or performed appointment fulfillment."}, "CreateReservationResponse": {"type": "object", "properties": {"payload": {"description": "`CreateReservationRecord` contains only the new `reservationId` if the operation was successful. Otherwise it will contain the reservation entity with warnings/errors.", "$ref": "#/definitions/CreateReservationRecord"}, "errors": {"description": "Errors encountered, if any.", "$ref": "#/definitions/ErrorList"}}, "description": "Response schema for the `createReservation` operation."}, "UpdateReservationResponse": {"type": "object", "properties": {"payload": {"description": "`UpdateReservationRecord` contains only the new `reservationId` if the operation was successful. Otherwise it will contain the reservation entity with warnings/errors.", "$ref": "#/definitions/UpdateReservationRecord"}, "errors": {"description": "Errors encountered, if any.", "$ref": "#/definitions/ErrorList"}}, "description": "Response schema for the `updateReservation` operation."}, "CancelReservationResponse": {"type": "object", "properties": {"errors": {"description": "Errors encountered, if any", "$ref": "#/definitions/ErrorList"}}, "description": "Response schema for the `cancelReservation` operation."}, "DayOfWeek": {"type": "string", "description": "The day of the week.", "enum": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "x-docgen-enum-table-extension": [{"value": "MONDAY", "description": "Monday."}, {"value": "TUESDAY", "description": "Tuesday."}, {"value": "WEDNESDAY", "description": "Wednesday."}, {"value": "THURSDAY", "description": "Thursday."}, {"value": "FRIDAY", "description": "Friday."}, {"value": "SATURDAY", "description": "Saturday."}, {"value": "SUNDAY", "description": "Sunday."}]}, "Recurrence": {"type": "object", "required": ["endTime"], "properties": {"endTime": {"type": "string", "format": "date-time", "description": "End time of the recurrence."}, "daysOfWeek": {"type": "array", "description": "Days of the week when recurrence is valid. If the schedule is valid every Monday, input will only contain `MONDAY` in the list.", "items": {"$ref": "#/definitions/DayOfWeek"}}, "daysOfMonth": {"type": "array", "description": "Days of the month when recurrence is valid.", "items": {"type": "integer", "minimum": 1, "maximum": 31}}}, "description": "Repeated occurrence of an event in a time range."}, "AvailabilityRecord": {"type": "object", "required": ["endTime", "startTime"], "properties": {"startTime": {"type": "string", "format": "date-time", "description": "Denotes the time from when the resource is available in a day in ISO-8601 format."}, "endTime": {"type": "string", "format": "date-time", "description": "Denotes the time till when the resource is available in a day in ISO-8601 format."}, "recurrence": {"description": "Recurrence object containing the recurrence pattern of schedule.", "$ref": "#/definitions/Recurrence"}, "capacity": {"type": "integer", "description": "Signifies the capacity of a resource which is available.", "minimum": 1}}, "description": "`AvailabilityRecord` to represent the capacity of a resource over a time range."}, "AvailabilityRecords": {"type": "array", "description": "List of `AvailabilityRecord`s to represent the capacity of a resource over a time range.", "items": {"$ref": "#/definitions/AvailabilityRecord"}}, "Reservation": {"type": "object", "required": ["availability", "type"], "properties": {"reservationId": {"type": "string", "description": "Unique identifier for a reservation. If present, it is treated as an update reservation request and will update the corresponding reservation. Otherwise, it is treated as a new create reservation request."}, "type": {"type": "string", "description": "Type of reservation.", "enum": ["APPOINTMENT", "TRAVEL", "VACATION", "BREAK", "TRAINING"], "x-docgen-enum-table-extension": [{"value": "APPOINTMENT", "description": "Reduce resource (store) capacity because of an appointment."}, {"value": "TRAVEL", "description": "Reduce resource (store) capacity because technician(s) are travelling."}, {"value": "VACATION", "description": "Reduce resource (store) capacity because technician(s) are on vacation."}, {"value": "BREAK", "description": "Reduce resource (store) capacity because technician(s) are on break."}, {"value": "TRAINING", "description": "Reduce resource (store) capacity because technician(s) are in training."}]}, "availability": {"description": "`AvailabilityRecord` to represent the capacity of a resource over a time range.", "$ref": "#/definitions/AvailabilityRecord"}}, "description": "Reservation object reduces the capacity of a resource."}, "UpdateScheduleRecord": {"type": "object", "properties": {"availability": {"description": "Availability record if the operation failed.", "$ref": "#/definitions/AvailabilityRecord"}, "warnings": {"description": "Warnings encountered, if any.", "$ref": "#/definitions/WarningList"}, "errors": {"description": "Errors encountered, if any.", "$ref": "#/definitions/ErrorList"}}, "description": "`UpdateScheduleRecord` entity contains the `AvailabilityRecord` if there is an error/warning while performing the requested operation on it."}, "CreateReservationRecord": {"type": "object", "properties": {"reservation": {"description": "Reservation record if the operation failed. It will only contain the new `reservationId` if the operation is successful.", "$ref": "#/definitions/Reservation"}, "warnings": {"description": "Warnings encountered, if any.", "$ref": "#/definitions/WarningList"}, "errors": {"description": "Errors encountered, if any.", "$ref": "#/definitions/ErrorList"}}, "description": "`CreateReservationRecord` entity contains the `Reservation` if there is an error/warning while performing the requested operation on it, otherwise it will contain the new `reservationId`."}, "UpdateReservationRecord": {"type": "object", "properties": {"reservation": {"description": "Reservation record if the operation failed. It will only contain the new `reservationId` if the operation is successful.", "$ref": "#/definitions/Reservation"}, "warnings": {"description": "Warnings encountered, if any.", "$ref": "#/definitions/WarningList"}, "errors": {"description": "Errors encountered, if any.", "$ref": "#/definitions/ErrorList"}}, "description": "`UpdateReservationRecord` entity contains the `Reservation` if there is an error/warning while performing the requested operation on it, otherwise it will contain the new `reservationId`."}, "RangeSlotCapacityQuery": {"type": "object", "description": "Request schema for the `getRangeSlotCapacity` operation. This schema is used to define the time range and capacity types that are being queried.", "required": ["startDateTime", "endDateTime"], "properties": {"capacityTypes": {"description": "An array of capacity types which are being requested. Default value is `[SCHEDULED_CAPACITY]`.", "type": "array", "items": {"$ref": "#/definitions/CapacityType"}}, "startDateTime": {"description": "Start date time from which the capacity slots are being requested in ISO 8601 format.", "type": "string", "format": "date-time"}, "endDateTime": {"description": "End date time up to which the capacity slots are being requested in ISO 8601 format.", "type": "string", "format": "date-time"}}}, "FixedSlotCapacityQuery": {"type": "object", "description": "Request schema for the `getFixedSlotCapacity` operation. This schema is used to define the time range, capacity types and slot duration which are being queried.", "required": ["startDateTime", "endDateTime"], "properties": {"capacityTypes": {"description": "An array of capacity types which are being requested. Default value is `[SCHEDULED_CAPACITY]`.", "type": "array", "items": {"$ref": "#/definitions/CapacityType"}}, "slotDuration": {"description": "Size in which slots are being requested. This value should be a multiple of 5 and fall in the range: 5 <= `slotDuration` <= 360.", "type": "number", "format": "int32", "multipleOf": 5}, "startDateTime": {"description": "Start date time from which the capacity slots are being requested in ISO 8601 format.", "type": "string", "format": "date-time"}, "endDateTime": {"description": "End date time up to which the capacity slots are being requested in ISO 8601 format.", "type": "string", "format": "date-time"}}}, "UpdateScheduleRequest": {"type": "object", "required": ["schedules"], "properties": {"schedules": {"description": "List of schedule objects to define the normal working hours of a resource.", "$ref": "#/definitions/AvailabilityRecords"}}, "description": "Request schema for the `updateSchedule` operation."}, "CapacityType": {"description": "Type of capacity", "type": "string", "enum": ["SCHEDULED_CAPACITY", "AVAILABLE_CAPACITY", "ENCUMBERED_CAPACITY", "RESERVED_CAPACITY"], "x-docgen-enum-table-extension": [{"description": "This capacity represents the originally allocated capacity as per resource schedule.", "value": "SCHEDULED_CAPACITY"}, {"description": "This capacity represents the capacity available for allocation to reservations.", "value": "AVAILABLE_CAPACITY"}, {"description": "This capacity represents the capacity allocated for Amazon Jobs/Appointments/Orders.", "value": "ENCUMBERED_CAPACITY"}, {"description": "This capacity represents the capacity made unavailable due to events like Breaks/Leaves/Lunch.", "value": "RESERVED_CAPACITY"}]}, "CreateReservationRequest": {"type": "object", "required": ["reservation", "resourceId"], "properties": {"resourceId": {"type": "string", "description": "Resource (store) identifier."}, "reservation": {"description": "`Reservation` object to reduce the capacity of a resource.", "$ref": "#/definitions/Reservation"}}, "description": "Request schema for the `createReservation` operation."}, "UpdateReservationRequest": {"type": "object", "required": ["reservation", "resourceId"], "properties": {"resourceId": {"type": "string", "description": "Resource (store) identifier."}, "reservation": {"description": "`Reservation` object to reduce the capacity of a resource.", "$ref": "#/definitions/Reservation"}}, "description": "Request schema for the `updateReservation` operation."}, "GetAppointmentSlotsResponse": {"type": "object", "description": "The response of fetching appointment slots based on service context.", "properties": {"payload": {"description": "The appointment slots fetched based on service context.", "$ref": "#/definitions/AppointmentSlotReport"}, "errors": {"description": "Errors occurred in getting schedule.", "$ref": "#/definitions/ErrorList"}}}, "AppointmentSlotReport": {"description": "Availability information as per the service context queried.", "type": "object", "properties": {"schedulingType": {"type": "string", "description": "Defines the type of slots.", "enum": ["REAL_TIME_SCHEDULING", "NON_REAL_TIME_SCHEDULING"], "x-docgen-enum-table-extension": [{"value": "REAL_TIME_SCHEDULING", "description": "The slots provided are backed by inventory in inventory management system."}, {"value": "NON_REAL_TIME_SCHEDULING", "description": "The slots provided are based on working hours defined in seller management system."}]}, "startTime": {"type": "string", "format": "date-time", "description": "Start Time from which the appointment slots are generated in ISO 8601 format."}, "endTime": {"type": "string", "format": "date-time", "description": "End Time up to which the appointment slots are generated in ISO 8601 format."}, "appointmentSlots": {"type": "array", "description": "A list of time windows along with associated capacity in which the service can be performed.", "items": {"$ref": "#/definitions/AppointmentSlot"}}}}, "AppointmentSlot": {"type": "object", "description": "A time window along with associated capacity in which the service can be performed.", "properties": {"startTime": {"type": "string", "format": "date-time", "description": "Time window start time in ISO 8601 format."}, "endTime": {"type": "string", "format": "date-time", "description": "Time window end time in ISO 8601 format."}, "capacity": {"type": "integer", "description": "Number of resources for which a slot can be reserved.", "minimum": 0}}}, "ServiceUploadDocument": {"type": "object", "required": ["contentLength", "contentType"], "properties": {"contentType": {"type": "string", "description": "The content type of the to-be-uploaded file", "enum": ["TIFF", "JPG", "PNG", "JPEG", "GIF", "PDF"], "x-docgen-enum-table-extension": [{"value": "TIFF", "description": "To be uploaded P<PERSON> is of type image/tiff."}, {"value": "JPG", "description": "To be uploaded POA is of type image/jpg."}, {"value": "PNG", "description": "To be uploaded POA is of type image/png."}, {"value": "JPEG", "description": "To be uploaded POA is of type image/jpeg."}, {"value": "GIF", "description": "To be uploaded POA is of type image/gif."}, {"value": "PDF", "description": "To be uploaded POA is of type application/pdf."}]}, "contentLength": {"type": "number", "format": "int64", "description": "The content length of the to-be-uploaded file", "minimum": 1, "maximum": 5242880}, "contentMD5": {"type": "string", "description": "An MD5 hash of the content to be submitted to the upload destination. This value is used to determine if the data has been corrupted or tampered with during transit.", "pattern": "^[A-Za-z0-9\\\\+/]{22}={2}$"}}, "description": "Input for to be uploaded document."}, "CreateServiceDocumentUploadDestination": {"type": "object", "properties": {"payload": {"$ref": "#/definitions/ServiceDocumentUploadDestination"}, "errors": {"$ref": "#/definitions/ErrorList"}}, "description": "The response schema for the `createServiceDocumentUploadDestination` operation."}, "ServiceDocumentUploadDestination": {"type": "object", "required": ["encryptionDetails", "uploadDestinationId", "url"], "properties": {"uploadDestinationId": {"type": "string", "description": "The unique identifier to be used by APIs that reference the upload destination."}, "url": {"type": "string", "description": "The URL to which to upload the file."}, "encryptionDetails": {"$ref": "#/definitions/EncryptionDetails"}, "headers": {"type": "object", "description": "The headers to include in the upload request.", "properties": {}}}, "description": "Information about an upload destination."}, "EncryptionDetails": {"type": "object", "required": ["initializationVector", "key", "standard"], "properties": {"standard": {"type": "string", "description": "The encryption standard required to encrypt or decrypt the document contents.", "enum": ["AES"], "x-docgen-enum-table-extension": [{"value": "AES", "description": "The Advanced Encryption Standard (AES)."}]}, "initializationVector": {"type": "string", "description": "The vector to encrypt or decrypt the document contents using Cipher Block Chaining (CBC)."}, "key": {"type": "string", "description": "The encryption key used to encrypt or decrypt the document contents."}}, "description": "Encryption details for required client-side encryption and decryption of document contents."}}}