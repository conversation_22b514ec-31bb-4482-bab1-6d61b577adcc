{"$schema": "http://json-schema.org/draft-07/schema", "type": "object", "description": "Forecast customer demand of your products for production and inventory planning purposes.", "examples": [{"reportSpecification": {"reportType": "GET_VENDOR_FORECASTING_REPORT", "reportOptions": {"sellingProgram": "RETAIL"}, "lastUpdatedDate": "2021-06-20", "marketplaceIds": ["ATVPDKIKX0DER"]}, "forecastByAsin": [{"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-06-06", "endDate": "2021-06-12", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-06-13", "endDate": "2021-06-19", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-06-20", "endDate": "2021-06-26", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-06-27", "endDate": "2021-07-03", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-07-04", "endDate": "2021-07-10", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-07-11", "endDate": "2021-07-17", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-07-18", "endDate": "2021-07-24", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-07-25", "endDate": "2021-07-31", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-08-01", "endDate": "2021-08-07", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-08-08", "endDate": "2021-08-14", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-08-15", "endDate": "2021-08-21", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-08-22", "endDate": "2021-08-28", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-08-29", "endDate": "2021-09-04", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-09-05", "endDate": "2021-09-11", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-09-12", "endDate": "2021-09-18", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-09-19", "endDate": "2021-09-25", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-09-26", "endDate": "2021-10-02", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-10-03", "endDate": "2021-10-09", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-10-10", "endDate": "2021-10-16", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-10-17", "endDate": "2021-10-23", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-10-24", "endDate": "2021-10-30", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-10-31", "endDate": "2021-11-06", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-11-07", "endDate": "2021-11-13", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-11-14", "endDate": "2021-11-20", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-11-21", "endDate": "2021-11-27", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-11-28", "endDate": "2021-12-04", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}, {"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-12-05", "endDate": "2021-12-11", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.9, "p80ForecastUnits": 30.3, "p90ForecastUnits": 300.7}]}], "required": ["reportSpecification", "forecastByAsin"], "properties": {"reportSpecification": {"type": "object", "description": "The report input summary which includes the marketplace, selling program, and last updated date.", "examples": [{"reportType": "GET_VENDOR_FORECASTING_REPORT", "reportOptions": {"sellingProgram": "RETAIL"}, "lastUpdatedDate": "2021-06-20", "marketplaceIds": ["ATVPDKIKX0DER"]}], "required": ["reportType", "reportOptions", "lastUpdatedDate", "marketplaceIds"], "properties": {"reportType": {"type": "string", "description": "The report type.", "enum": ["GET_VENDOR_FORECASTING_REPORT"]}, "reportOptions": {"type": "object", "description": "Report options specifying parameters such as sellingProgram.", "examples": [{"sellingProgram": "RETAIL"}], "required": ["sellingProgram"], "properties": {"sellingProgram": {"type": "string", "description": "The selling program.", "enum": ["RETAIL", "FRESH"]}}}, "lastUpdatedDate": {"type": "string", "format": "date", "description": "The date when the report was last updated. Follows the <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> Date format of YYYY-MM-DD.", "examples": ["2021-06-20"]}, "marketplaceIds": {"type": "array", "description": "This parameter must match the marketplaceId of the selling partner account. Each selling partner account belongs to only one marketplaceId.", "examples": [["ATVPDKIKX0DER"]], "items": {"type": "string"}}}}, "forecastByAsin": {"type": "array", "items": {"$ref": "#/definitions/ForecastByAsin"}}}, "definitions": {"ForecastByAsin": {"type": "object", "description": "Describes forecast units for a particular ASIN for a specific period of time in the future.", "examples": [{"forecastGenerationDate": "2021-06-06", "asin": "B123456789", "startDate": "2021-06-06", "endDate": "2021-06-12", "meanForecastUnits": 3.1, "p70ForecastUnits": 3.7, "p80ForecastUnits": 30.2, "p90ForecastUnits": 300.8}], "required": ["forecastGenerationDate", "asin", "startDate", "endDate", "meanForecastUnits", "p70ForecastUnits", "p80ForecastUnits", "p90ForecastUnits"], "properties": {"forecastGenerationDate": {"type": "string", "format": "date", "description": "The date that the forecast was generated.", "examples": ["2021-06-06"]}, "asin": {"description": "The Amazon Standard Identification Number.", "type": "string", "examples": ["B123456789"]}, "startDate": {"type": "string", "format": "date", "description": "The start date of the time period being forecasted. The forecasted time period includes this date.", "examples": ["2021-06-06"]}, "endDate": {"type": "string", "format": "date", "description": "The end date of the time period being forecasted. The forecasted time period includes this date.", "examples": ["2021-06-12"]}, "meanForecastUnits": {"type": "number", "minimum": 0, "description": "The average amount of forecast units that Amazon has predicted for this date range. This means that, on average, Amazon predicts that this many stock units will be purchased during this date range.", "examples": [1.25]}, "p70ForecastUnits": {"type": "number", "minimum": 0, "description": "The 70th percentile of forecast units that Amazon has predicted for this date range. This means that Amazon has forecasted a 70% probability that the ASIN will not run out of stock during the date range if this many units of inventory are present at the start of the date range.", "examples": [2.25]}, "p80ForecastUnits": {"type": "number", "minimum": 0, "description": "The 80th percentile of forecast units that Amazon has predicted for this date range. This means that Amazon has forecasted an 80% probability that the ASIN will not run out of stock during the date range if this many units of inventory are present at the start of the date range.", "examples": [3.25]}, "p90ForecastUnits": {"type": "number", "minimum": 0, "description": "The 90th percentile of forecast units that Amazon has predicted for this date range. This means that Amazon has forecasted a 90% probability that the ASIN will not run out of stock during the date range if this many units of inventory are present at the start of the date range.", "examples": [4.25]}}}}}