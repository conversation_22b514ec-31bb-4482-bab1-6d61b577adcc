{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://example.com/example.json", "type": "object", "title": "The root schema", "description": "The notification response schema that comprises the entire JSON document for the notification.", "examples": [{"NotificationVersion": "1.0", "NotificationType": "TRANSACTION_UPDATE", "PayloadVersion": "1.0", "EventTime": "2023-07-14T03:35:13.214Z", "NotificationMetadata": {"ApplicationId": "amzn1.sellerapps.app.aacccfff-4455-4b7c-4422-664ecacdd336", "SubscriptionId": "subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "PublishTime": "2023-07-14T03:35:13.214Z", "NotificationId": "d0e9e693-c3ad-4373-979f-ed4ec98dd746"}, "Payload": {"TransactionUpdateNotification": {"SellingPartnerMetadata": {"SellingPartnerId": "A3TH9S8BH6GOGM", "MarketplaceId": "ATIV93840DER", "AccountType": "PAYABLE"}, "RelatedIdentifiers": [{"RelatedIdentifierName": "OrderId", "RelatedIdentifierValue": "*************"}], "TransactionType": "Shipment", "PostedDate": "2023-07-14T03:35:13.214Z", "TotalAmount": {"CurrencyAmount": 10, "CurrencyCode": "USD"}}}}], "required": ["NotificationVersion", "NotificationType", "PayloadVersion", "EventTime", "Payload", "NotificationMetadata"], "properties": {"NotificationVersion": {"$id": "#/properties/NotificationVersion", "type": "string", "title": "The NotificationVersion schema", "description": "The notification version.", "examples": ["1.0"]}, "NotificationType": {"$id": "#/properties/NotificationType", "type": "string", "title": "The NotificationType schema", "description": "The type of this notification, used to differentiate different notifications. Combined with payload version controls the structure of payload object.", "examples": ["TRANSACTION_UPDATE"]}, "PayloadVersion": {"$id": "#/properties/PayloadVersion", "type": "string", "title": "The PayloadVersion schema", "description": "The payload version of the notification.", "examples": ["1.0"]}, "EventTime": {"$id": "#/properties/EventTime", "type": "string", "title": "The EventTime schema", "description": "The time when this notification was published, presented in ISO-8601 date/time format.", "examples": ["2023-07-14T03:35:13.214Z"]}, "Payload": {"$id": "#/properties/Payload", "type": "object", "title": "The Payload schema", "description": "The payload for this TRANSACTION_UPDATE notification. It's unique for different event type and will provide more in-depth information about this notification.", "examples": [{"TransactionUpdateNotification": {"SellingPartnerMetadata": {"SellingPartnerId": "A3TH9S8BH6GOGM", "MarketplaceId": "ATIV93840DER", "AccountType": "PAYABLE"}, "RelatedIdentifiers": [{"RelatedIdentifierName": "OrderId", "RelatedIdentifierValue": "*************"}], "TransactionType": "Shipment", "PostedDate": "2023-07-14T03:35:13.214Z", "TotalAmount": {"CurrencyAmount": 10, "CurrencyCode": "USD"}}}], "required": ["TransactionUpdateNotification"], "properties": {"TransactionUpdateNotification": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification", "type": "object", "title": "The TransactionUpdateNotification schema", "description": "An explanation about the TRANSACTION_UPDATE notification.", "examples": [{"SellingPartnerMetadata": {"SellingPartnerId": "A3TH9S8BH6GOGM", "MarketplaceId": "ATIV93840DER", "AccountType": "PAYABLE"}, "RelatedIdentifiers": [{"RelatedIdentifierName": "OrderId", "RelatedIdentifierValue": "*************"}], "TransactionType": "Shipment", "PostedDate": "2023-07-14T03:35:13.214Z", "TotalAmount": {"CurrencyAmount": 10, "CurrencyCode": "USD"}}], "required": ["SellingPartnerMetadata", "TransactionType", "PostedDate", "TotalAmount"], "properties": {"SellingPartnerMetadata": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/SellingPartnerMetadata", "type": "object", "description": "Metadata describing the seller.", "examples": [{"SellingPartnerId": "A3TH9S8BH6GOGM", "MarketplaceId": "ATIV93840DER", "AccountType": "PAYABLE"}], "required": ["<PERSON><PERSON><PERSON><PERSON>nerId", "MarketplaceId", "AccountType"], "properties": {"SellingPartnerId": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/SellingPartnerMetadata/properties/SellingPartnerId", "type": "string", "description": "Unique seller identifier.", "examples": ["A3TH9S8BH6GOGM"]}, "MarketplaceId": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/SellingPartnerMetadata/properties/MarketplaceId", "type": "string", "description": "Marketplace identifier of transaction.", "examples": ["ATIV93840DER"]}, "AccountType": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/SellingPartnerMetadata/properties/AccountType", "type": "string", "description": "Account type of transaction.", "examples": ["PAYABLE"]}}}, "RelatedIdentifiers": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/RelatedIdentifiers", "type": "array", "description": "Related business identifiers of the transaction.", "examples": [[{"RelatedIdentifierName": "OrderId", "RelatedIdentifierValue": "*************"}]], "items": {"type": "object", "description": "Related business identifier of the transaction.", "examples": [{"RelatedIdentifierName": "OrderId", "RelatedIdentifierValue": "*************"}], "required": ["RelatedIdentifierName", "RelatedIdentifierValue"], "properties": {"RelatedIdentifierName": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/RelatedIdentifiers/RelatedIdentifierName", "type": "string", "description": "Enumerated set of related business identifier names.", "examples": ["OrderId"]}, "RelatedIdentifierValue": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/RelatedIdentifiers/RelatedIdentifierValue", "type": "string", "description": "Corresponding value of RelatedIdentifierName", "examples": ["*************"]}}}}, "TransactionType": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/TransactionType", "type": "string", "description": "Type of transaction.", "examples": ["Shipment"]}, "PostedDate": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/PostedDate", "type": "string", "description": "The date and time when the transaction was posted.", "examples": ["2023-07-14T03:35:13.214Z"]}, "TotalAmount": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/TotalAmount", "type": "object", "description": "Total amount of transaction.", "examples": [{"CurrencyAmount": 10, "CurrencyCode": "USD"}], "required": ["CurrencyAmount", "CurrencyCode"], "properties": {"CurrencyAmount": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/TotalAmount/properties/CurrencyAmount", "type": "number", "description": "Amount value of transaction.", "examples": [10]}, "CurrencyCode": {"$id": "#/properties/Payload/properties/TransactionUpdateNotification/properties/TotalAmount/properties/CurrencyCode", "type": "string", "description": "Currency code of transaction.", "examples": ["USD"]}}}}}}}, "NotificationMetadata": {"$id": "#/properties/NotificationMetadata", "type": "object", "title": "The NotificationMetadata schema", "description": "The notification metadata.", "examples": [{"ApplicationId": "app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "SubscriptionId": "subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "PublishTime": "2023-07-13T19:42:04.284Z", "NotificationId": "d0e9e693-c3ad-4373-979f-ed4ec98dd746"}], "required": ["ApplicationId", "SubscriptionId", "PublishTime", "NotificationId"], "properties": {"ApplicationId": {"$id": "#/properties/NotificationMetadata/properties/ApplicationId", "type": "string", "title": "The ApplicationId schema", "description": "The identifier for the application that uses the notifications.", "examples": ["app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}, "SubscriptionId": {"$id": "#/properties/NotificationMetadata/properties/SubscriptionId", "type": "string", "title": "The SubscriptionId schema", "description": "A unique identifier for the subscription which resulted in this notification.", "examples": ["subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}, "PublishTime": {"$id": "#/properties/NotificationMetadata/properties/PublishTime", "type": "string", "title": "The PublishTime schema", "description": "The date and time (in UTC) that the notification was sent, presented in ISO-8601 date/time format.", "examples": ["2023-07-14T19:42:04.284Z"]}, "NotificationId": {"$id": "#/properties/NotificationMetadata/properties/NotificationId", "type": "string", "title": "The NotificationId schema", "description": "A unique identifier for this notification instance.", "examples": ["d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}}}}}