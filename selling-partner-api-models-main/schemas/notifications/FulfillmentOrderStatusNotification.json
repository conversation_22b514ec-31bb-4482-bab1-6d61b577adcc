{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "title": "The root schema", "description": "The root schema comprises the entire JSON document.", "default": {}, "examples": [{"NotificationVersion": "1.0", "NotificationType": "FULFILLMENT_ORDER_STATUS", "PayloadVersion": "1.0", "EventTime": "2020-07-13T19:42:04.284Z", "Payload": {"FulfillmentOrderStatusNotification": {"SellerId": "A3TH9S8BH6GOGM", "EventType": "TYPE3945", "StatusUpdatedDateTime": "2020-07-13T19:42:04.284Z", "SellerFulfillmentOrderId": "SFOID2345", "FulfillmentOrderStatus": "PROCESSED", "FulfillmentShipment": {"FulfillmentShipmentStatus": "PROCESSED", "AmazonShipmentId": "ASID49535", "EstimatedArrivalDateTime": "2020-07-13T19:42:04.284Z", "info": {"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}, "FulfillmentShipmentPackages": [{"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}, {"PackageNumber": 2, "CarrierCode": "1-930434", "TrackingNumber": "1Z84456456573405"}, {"PackageNumber": 3, "CarrierCode": "3-930434", "TrackingNumber": "1Z885647654573405"}]}, "FulfillmentReturnItem": {"ReceivedDateTime": "2020-07-13T19:42:04.284Z", "ReturnedQuantity": 12, "SellerSKU": "SELLERSKU9345"}}}, "NotificationMetadata": {"ApplicationId": "app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "SubscriptionId": "subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "PublishTime": "2020-07-13T19:42:04.284Z", "NotificationId": "d0e9e693-c3ad-4373-979f-ed4ec98dd746"}}], "required": ["NotificationVersion", "NotificationType", "PayloadVersion", "EventTime", "Payload", "NotificationMetadata"], "properties": {"NotificationVersion": {"$id": "#/properties/NotificationVersion", "type": "string", "title": "The NotificationVersion schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["1.0"]}, "NotificationType": {"$id": "#/properties/NotificationType", "type": "string", "title": "The NotificationType schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["FULFILLMENT_ORDER_STATUS"]}, "PayloadVersion": {"$id": "#/properties/PayloadVersion", "type": "string", "title": "The PayloadVersion schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["1.0"]}, "EventTime": {"$id": "#/properties/EventTime", "type": "string", "title": "The EventTime schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2020-07-13T19:42:04.284Z"]}, "Payload": {"$id": "#/properties/Payload", "type": "object", "title": "The Payload schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"FulfillmentOrderStatusNotification": {"SellerId": "A3TH9S8BH6GOGM", "EventType": "TYPE3945", "StatusUpdatedDateTime": "2020-07-13T19:42:04.284Z", "SellerFulfillmentOrderId": "SFOID2345", "FulfillmentOrderStatus": "PROCESSED", "FulfillmentShipment": {"FulfillmentShipmentStatus": "PROCESSED", "AmazonShipmentId": "ASID49535", "EstimatedArrivalDateTime": "2020-07-13T19:42:04.284Z", "info": {"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}, "FulfillmentShipmentPackages": [{"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}, {"PackageNumber": 2, "CarrierCode": "1-930434", "TrackingNumber": "1Z84456456573405"}, {"PackageNumber": 3, "CarrierCode": "3-930434", "TrackingNumber": "1Z885647654573405"}]}, "FulfillmentReturnItem": {"ReceivedDateTime": "2020-07-13T19:42:04.284Z", "ReturnedQuantity": 12, "SellerSKU": "SELLERSKU9345"}}}], "required": ["FulfillmentOrderStatusNotification"], "properties": {"FulfillmentOrderStatusNotification": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification", "type": "object", "title": "The FulfillmentOrderStatusNotification schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"SellerId": "A3TH9S8BH6GOGM", "EventType": "TYPE3945", "StatusUpdatedDateTime": "2020-07-13T19:42:04.284Z", "SellerFulfillmentOrderId": "SFOID2345", "FulfillmentOrderStatus": "PROCESSED", "FulfillmentShipment": {"FulfillmentShipmentStatus": "PROCESSED", "AmazonShipmentId": "ASID49535", "EstimatedArrivalDateTime": "2020-07-13T19:42:04.284Z", "info": {"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}, "FulfillmentShipmentPackages": [{"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}, {"PackageNumber": 2, "CarrierCode": "1-930434", "TrackingNumber": "1Z84456456573405"}, {"PackageNumber": 3, "CarrierCode": "3-930434", "TrackingNumber": "1Z885647654573405"}]}, "FulfillmentReturnItem": {"ReceivedDateTime": "2020-07-13T19:42:04.284Z", "ReturnedQuantity": 12, "SellerSKU": "SELLERSKU9345"}}], "required": ["SellerId", "EventType", "StatusUpdatedDateTime", "SellerFulfillmentOrderId", "FulfillmentOrderStatus", "FulfillmentShipment", "FulfillmentReturnItem"], "properties": {"SellerId": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/SellerId", "type": "string", "title": "The SellerId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["A3TH9S8BH6GOGM"]}, "EventType": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/EventType", "type": "string", "title": "The EventType schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["TYPE3945"]}, "StatusUpdatedDateTime": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/StatusUpdatedDateTime", "type": "string", "title": "The StatusUpdatedDateTime schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2020-07-13T19:42:04.284Z"]}, "SellerFulfillmentOrderId": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/SellerFulfillmentOrderId", "type": "string", "title": "The SellerFulfillmentOrderId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["SFOID2345"]}, "FulfillmentOrderStatus": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentOrderStatus", "type": "string", "title": "The FulfillmentOrderStatus schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["PROCESSED"]}, "FulfillmentShipment": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment", "type": "object", "title": "The FulfillmentShipment schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"FulfillmentShipmentStatus": "PROCESSED", "AmazonShipmentId": "ASID49535", "EstimatedArrivalDateTime": "2020-07-13T19:42:04.284Z", "info": {"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}, "FulfillmentShipmentPackages": [{"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}, {"PackageNumber": 2, "CarrierCode": "1-930434", "TrackingNumber": "1Z84456456573405"}, {"PackageNumber": 3, "CarrierCode": "3-930434", "TrackingNumber": "1Z885647654573405"}]}], "required": ["FulfillmentShipmentStatus", "AmazonShipmentId", "EstimatedArrivalDateTime", "info", "FulfillmentShipmentPackages"], "properties": {"FulfillmentShipmentStatus": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/FulfillmentShipmentStatus", "type": "string", "title": "The FulfillmentShipmentStatus schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["PROCESSED"]}, "AmazonShipmentId": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/AmazonShipmentId", "type": "string", "title": "The AmazonShipmentId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["ASID49535"]}, "EstimatedArrivalDateTime": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/EstimatedArrivalDateTime", "type": "string", "title": "The EstimatedArrivalDateTime schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2020-07-13T19:42:04.284Z"]}, "FulfillmentShipmentPackages": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/FulfillmentShipmentPackages", "type": "array", "title": "The FulfillmentShipmentPackages schema", "description": "An explanation about the purpose of this instance.", "default": [], "examples": [[{"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}, {"PackageNumber": 2, "CarrierCode": "1-930434", "TrackingNumber": "1Z84456456573405"}]], "properties": {"FulfillmentShipmentPackageItem": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/FulfillmentShipmentPackages/properties/FulfillmentShipmentPackageItem", "type": "object", "title": "The info schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}], "required": ["PackageNumber", "CarrierCode", "TrackingNumber"], "properties": {"PackageNumber": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/info/properties/PackageNumber", "type": "integer", "title": "The PackageNumber schema", "description": "An explanation about the purpose of this instance.", "default": 0, "examples": [1]}, "CarrierCode": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/info/properties/CarrierCode", "type": "string", "title": "The CarrierCode schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2-930434"]}, "TrackingNumber": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/info/properties/TrackingNumber", "type": "string", "title": "The TrackingNumber schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["1Z84456456573405"]}}, "additionalProperties": true}}, "additionalItems": true, "items": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/FulfillmentShipmentPackages/properties/FulfillmentShipmentPackageItem", "anyOf": [{"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/FulfillmentShipmentPackages/items/anyOf/0", "type": "object", "title": "The first anyOf schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"PackageNumber": 1, "CarrierCode": "2-930434", "TrackingNumber": "1Z84456456573405"}], "required": ["PackageNumber", "CarrierCode", "TrackingNumber"], "properties": {"PackageNumber": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/FulfillmentShipmentPackages/items/anyOf/0/properties/PackageNumber", "type": "integer", "title": "The PackageNumber schema", "description": "An explanation about the purpose of this instance.", "default": 0, "examples": [1]}, "CarrierCode": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/FulfillmentShipmentPackages/items/anyOf/0/properties/CarrierCode", "type": "string", "title": "The CarrierCode schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2-930434"]}, "TrackingNumber": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentShipment/properties/FulfillmentShipmentPackages/items/anyOf/0/properties/TrackingNumber", "type": "string", "title": "The TrackingNumber schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["1Z84456456573405"]}}, "additionalProperties": true}]}}}, "additionalProperties": true}, "FulfillmentReturnItem": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentReturnItem", "type": "object", "title": "The FulfillmentReturnItem schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"ReceivedDateTime": "2020-07-13T19:42:04.284Z", "ReturnedQuantity": 12, "SellerSKU": "SELLERSKU9345"}], "required": ["ReceivedDateTime", "ReturnedQuantity", "SellerSKU"], "properties": {"ReceivedDateTime": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentReturnItem/properties/ReceivedDateTime", "type": "string", "title": "The ReceivedDateTime schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2020-07-13T19:42:04.284Z"]}, "ReturnedQuantity": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentReturnItem/properties/ReturnedQuantity", "type": "integer", "title": "The ReturnedQuantity schema", "description": "An explanation about the purpose of this instance.", "default": 0, "examples": [12]}, "SellerSKU": {"$id": "#/properties/Payload/properties/FulfillmentOrderStatusNotification/properties/FulfillmentReturnItem/properties/SellerSKU", "type": "string", "title": "The SellerSKU schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["SELLERSKU9345"]}}, "additionalProperties": true}}, "additionalProperties": true}}, "additionalProperties": true}, "NotificationMetadata": {"$id": "#/properties/NotificationMetadata", "type": "object", "title": "The NotificationMetadata schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"ApplicationId": "app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "SubscriptionId": "subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "PublishTime": "2020-07-13T19:42:04.284Z", "NotificationId": "d0e9e693-c3ad-4373-979f-ed4ec98dd746"}], "required": ["ApplicationId", "SubscriptionId", "PublishTime", "NotificationId"], "properties": {"ApplicationId": {"$id": "#/properties/NotificationMetadata/properties/ApplicationId", "type": "string", "title": "The ApplicationId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}, "SubscriptionId": {"$id": "#/properties/NotificationMetadata/properties/SubscriptionId", "type": "string", "title": "The SubscriptionId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}, "PublishTime": {"$id": "#/properties/NotificationMetadata/properties/PublishTime", "type": "string", "title": "The PublishTime schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2020-07-13T19:42:04.284Z"]}, "NotificationId": {"$id": "#/properties/NotificationMetadata/properties/NotificationId", "type": "string", "title": "The NotificationId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}}, "additionalProperties": true}}, "additionalProperties": true}