{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "title": "The root schema", "description": "The root schema comprises the entire JSON document.", "default": {}, "examples": [{"NotificationVersion": "1.0", "NotificationType": "ITEM_PRODUCT_TYPE_CHANGE", "PayloadVersion": "1.0", "EventTime": "2019-03-20T18:59:30.194Z", "Payload": {"MarketplaceId": "ATVPDKIKX0DER", "Asin": "B1234567", "PreviousProductType": "PET_HEALTH_CARE", "CurrentProductType": "PET_APPAREL"}, "NotificationMetadata": {"ApplicationId": "amzn1.sellerapps.app.f1234566-aaec-55a6-b123-bcb752069ec5", "SubscriptionId": "93b098e1-c42-2f45-93a1-78910a6a8369", "PublishTime": "2019-03-20T18:59:48.768Z", "NotificationId": "0e999936-da2c-4f9c-9fc2-02b67bae5f49"}}], "required": ["NotificationVersion", "NotificationType", "PayloadVersion", "EventTime", "Payload", "NotificationMetadata"], "properties": {"NotificationVersion": {"$id": "#/properties/NotificationVersion", "type": "string", "title": "The NotificationVersion schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["1.0"]}, "NotificationType": {"$id": "#/properties/NotificationType", "type": "string", "title": "The NotificationType schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["ITEM_PRODUCT_TYPE_CHANGE"]}, "PayloadVersion": {"$id": "#/properties/PayloadVersion", "type": "string", "title": "The PayloadVersion schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["1.0"]}, "EventTime": {"$id": "#/properties/EventTime", "type": "string", "title": "The EventTime schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2019-03-20T18:59:30.194Z"]}, "Payload": {"$id": "#/properties/Payload", "type": "object", "title": "The Payload schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"MarketplaceId": "ATVPDKIKX0DER", "Asin": "B1234567", "PreviousProductType": "PET_HEALTH_CARE", "CurrentProductType": "PET_APPAREL"}], "required": ["MarketplaceId", "<PERSON><PERSON>", "PreviousProductType", "CurrentProductType"], "properties": {"MarketplaceId": {"$id": "#/properties/Payload/properties/MarketplaceId", "type": "string", "title": "The MarketplaceId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["ATVPDKIKX0DER"]}, "Asin": {"$id": "#/properties/Payload/properties/Asin", "type": "string", "title": "The Asin schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["B1234567"]}, "PreviousProductType": {"$id": "#/properties/Payload/properties/PreviousProductType", "type": "string", "title": "The PreviousProductType schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["PET_HEALTH_CARE"]}, "CurrentProductType": {"$id": "#/properties/Payload/properties/CurrentProductType", "type": "string", "title": "The CurrentProductType schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["PET_APPAREL"]}}, "additionalProperties": true}, "NotificationMetadata": {"$id": "#/properties/NotificationMetadata", "type": "object", "title": "The NotificationMetadata schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"ApplicationId": "amzn1.sellerapps.app.f1234566-aaec-55a6-b123-bcb752069ec5", "SubscriptionId": "93b098e1-c42-2f45-93a1-78910a6a8369", "PublishTime": "2019-03-20T18:59:48.768Z", "NotificationId": "0e999936-da2c-4f9c-9fc2-02b67bae5f49"}], "required": ["ApplicationId", "SubscriptionId", "PublishTime", "NotificationId"], "properties": {"ApplicationId": {"$id": "#/properties/NotificationMetadata/properties/ApplicationId", "type": "string", "title": "The ApplicationId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["amzn1.sellerapps.app.f1234566-aaec-55a6-b123-bcb752069ec5"]}, "SubscriptionId": {"$id": "#/properties/NotificationMetadata/properties/SubscriptionId", "type": "string", "title": "The SubscriptionId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["93b098e1-c42-2f45-93a1-78910a6a8369"]}, "PublishTime": {"$id": "#/properties/NotificationMetadata/properties/PublishTime", "type": "string", "title": "The PublishTime schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2019-03-20T18:59:48.768Z"]}, "NotificationId": {"$id": "#/properties/NotificationMetadata/properties/NotificationId", "type": "string", "title": "The NotificationId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["0e999936-da2c-4f9c-9fc2-02b67bae5f49"]}}, "additionalProperties": true}}, "additionalProperties": true}