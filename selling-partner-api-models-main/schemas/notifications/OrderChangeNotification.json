{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "title": "The root schema", "description": "The notification response schema that comprises the entire JSON document for ORDER_CHANGE notification.", "examples": [{"NotificationVersion": "1.0", "NotificationType": "ORDER_CHANGE", "PayloadVersion": "1.0", "EventTime": "2020-01-11T00:09:53.109Z", "Payload": {"OrderChangeNotification": {"NotificationLevel": "OrderLevel", "SellerId": "A3TH9S8BH6GOGM", "AmazonOrderId": "903-8868176-2219830", "OrderChangeType": "BuyerRequestedChange", "OrderChangeTrigger": {"TimeOfOrderChange": "2022-11-29T19:42:04.284Z", "ChangeReason": "Buyer Requested Cancel"}, "Summary": {"MarketplaceId": "ATVPDKIKX0DER", "OrderStatus": "Unshipped", "PurchaseDate": "2022-07-13T19:42:04.284Z", "DestinationPostalCode": "48110", "FulfillmentType": "MFN", "OrderType": "StandardOrder", "NumberOfItemsShipped": 0, "NumberOfItemsUnshipped": 10, "EarliestDeliveryDate": "2022-11-07T19:42:04.284Z", "LatestDeliveryDate": "2022-12-07T19:42:04.284Z", "EarliestShipDate": "2022-11-07T19:42:04.284Z", "LatestShipDate": "2022-12-07T19:42:04.284Z", "CancelNotifyDate": "2022-12-07T19:42:04.284Z", "OrderPrograms": ["Business"], "ShippingPrograms": ["EasyShip"], "EasyShipShipmentStatus": "Delivered", "ElectronicInvoiceStatus": "NotFound", "OrderItems": [{"OrderItemId": "OIID34853450", "SellerSKU": "SellerSKUID1", "SupplySourceId": "d7679e14-031b-4ab3-a81b-ec4fc7a460b3", "OrderItemStatus": "Unshipped", "Quantity": 10, "QuantityShipped": 0, "IsBuyerRequestedCancel": true, "ItemEarliestDeliveryDate": "2022-11-07T19:42:04.284Z", "ItemLatestDeliveryDate": "2022-12-07T19:42:04.284Z"}]}}}, "NotificationMetadata": {"ApplicationId": "app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "SubscriptionId": "subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "PublishTime": "2020-07-13T19:42:04.284Z", "NotificationId": "d0e9e693-c3ad-4373-979f-ed4ec98dd746"}}], "required": ["NotificationVersion", "NotificationType", "PayloadVersion", "EventTime", "Payload", "NotificationMetadata"], "properties": {"NotificationVersion": {"$id": "#/properties/NotificationVersion", "type": "string", "title": "The NotificationVersion schema", "description": "The notification version.", "examples": ["1.0"]}, "NotificationType": {"$id": "#/properties/NotificationType", "type": "string", "title": "The NotificationType schema", "description": "The type of this notification, used to differentiate different notifications. Combined with payload version, this controls the structure of the payload object.", "examples": ["ORDER_CHANGE"]}, "PayloadVersion": {"$id": "#/properties/PayloadVersion", "type": "string", "title": "The PayloadVersion schema", "description": "The payload version of the notification.", "examples": ["1.0"]}, "EventTime": {"$id": "#/properties/EventTime", "type": "string", "title": "The EventTime schema", "description": "The time when this notification was published, in ISO-8601 date/time format.", "examples": ["2020-01-11T00:09:53.109Z"]}, "Payload": {"$id": "#/properties/Payload", "type": "object", "title": "The Payload schema", "description": "The payload for the ORDER_CHANGE notification.", "examples": [{"OrderChangeNotification": {"NotificationLevel": "OrderLevel", "SellerId": "A3TH9S8BH6GOGM", "AmazonOrderId": "903-8868176-2219830", "OrderChangeType": "BuyerRequestedChange", "OrderChangeTrigger": {"TimeOfOrderChange": "2022-11-29T19:42:04.284Z", "ChangeReason": "Buyer Requested Cancel"}, "Summary": {"MarketplaceId": "ATVPDKIKX0DER", "OrderStatus": "Unshipped", "PurchaseDate": "2022-07-13T19:42:04.284Z", "DestinationPostalCode": "48110", "FulfillmentType": "MFN", "OrderType": "StandardOrder", "NumberOfItemsShipped": 0, "NumberOfItemsUnshipped": 10, "EarliestDeliveryDate": "2022-11-07T19:42:04.284Z", "LatestDeliveryDate": "2022-12-07T19:42:04.284Z", "EarliestShipDate": "2022-11-07T19:42:04.284Z", "LatestShipDate": "2022-12-07T19:42:04.284Z", "CancelNotifyDate": "2022-12-07T19:42:04.284Z", "OrderPrograms": ["Business"], "ShippingPrograms": ["EasyShip"], "EasyShipShipmentStatus": "Delivered", "ElectronicInvoiceStatus": "NotFound", "OrderItems": [{"OrderItemId": "OIID34853450", "SellerSKU": "SellerSKUID1", "SupplySourceId": "d7679e14-031b-4ab3-a81b-ec4fc7a460b3", "OrderItemStatus": "Unshipped", "Quantity": 10, "QuantityShipped": 0, "IsBuyerRequestedCancel": true, "ItemEarliestDeliveryDate": "2022-11-07T19:42:04.284Z", "ItemLatestDeliveryDate": "2022-12-07T19:42:04.284Z"}]}}}], "required": ["OrderChangeNotification"], "properties": {"OrderChangeNotification": {"$id": "#/properties/Payload/properties/OrderChangeNotification", "type": "object", "title": "The OrderChangeNotification schema", "description": "An explanation about the ORDER_CHANGE notification.", "examples": [{"NotificationLevel": "OrderLevel", "SellerId": "A3TH9S8BH6GOGM", "AmazonOrderId": "903-8868176-2219830", "OrderChangeType": "BuyerRequestedChange", "OrderChangeTrigger": {"TimeOfOrderChange": "2022-11-29T19:42:04.284Z", "ChangeReason": "Buyer Requested Cancel"}, "Summary": {"MarketplaceId": "ATVPDKIKX0DER", "OrderStatus": "Unshipped", "PurchaseDate": "2022-07-13T19:42:04.284Z", "DestinationPostalCode": "48110", "FulfillmentType": "MFN", "OrderType": "StandardOrder", "NumberOfItemsShipped": 0, "NumberOfItemsUnshipped": 10, "EarliestDeliveryDate": "2022-11-07T19:42:04.284Z", "LatestDeliveryDate": "2022-12-07T19:42:04.284Z", "EarliestShipDate": "2022-11-07T19:42:04.284Z", "LatestShipDate": "2022-12-07T19:42:04.284Z", "CancelNotifyDate": "2022-12-07T19:42:04.284Z", "OrderPrograms": ["Business"], "ShippingPrograms": ["EasyShip"], "EasyShipShipmentStatus": "Delivered", "ElectronicInvoiceStatus": "NotFound", "OrderItems": [{"OrderItemId": "OIID34853450", "SellerSKU": "SellerSKUID1", "SupplySourceId": "d7679e14-031b-4ab3-a81b-ec4fc7a460b3", "OrderItemStatus": "Unshipped", "Quantity": 10, "QuantityShipped": 0, "IsBuyerRequestedCancel": true, "ItemEarliestDeliveryDate": "2022-11-07T19:42:04.284Z", "ItemLatestDeliveryDate": "2022-12-07T19:42:04.284Z"}]}}], "required": ["NotificationLevel", "SellerId", "AmazonOrderId", "OrderChangeType", "OrderChangeTrigger", "Summary"], "properties": {"NotificationLevel": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/NotificationLevel", "type": "string", "enum": ["OrderItemLevel", "OrderLevel"], "title": "The NotificationLevel schema", "description": "The notification level of current notification.", "examples": ["OrderLevel"]}, "SellerId": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/SellerId", "type": "string", "title": "The SellerId schema", "description": "The selling partner identifier.", "examples": ["AXXXXXXXXXXXXX"]}, "AmazonOrderId": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/AmazonOrderId", "type": "string", "title": "The AmazonOrderId schema", "description": "The Amazon order identifier, in 3-7-7 format.", "examples": ["903-8868176-2219830"]}, "OrderChangeType": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/OrderChangeType", "type": "string", "enum": ["BuyerRequestedChange", "OrderStatusChange"], "title": "The OrderChangeType schema", "description": "The type of order change that caused the notification to be sent. Possible values are BuyerRequestedChange and OrderStatusChange.", "examples": ["BuyerRequestedChange"]}, "OrderChangeTrigger": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/OrderChangeTrigger", "type": "object", "title": "The OrderChangeTrigger schema", "description": "Details about what caused this ORDER_CHANGE notification to be sent.", "examples": [{"TimeOfOrderChange": "2022-11-29T19:42:04.284Z", "ChangeReason": "Buyer Requested Cancel"}], "required": ["TimeOfOrderChange", "ChangeReason"], "properties": {"TimeOfOrderChange": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/OrderChangeTrigger/properties/TimeOfOrderChange", "type": ["string", "null"], "title": "The TimeOfOrderChange schema", "description": "The time of the change that caused this notification, in ISO-8601 date/time format. Will be null when there is no related timestamp.", "examples": ["2022-11-29T19:42:04.284Z"]}, "ChangeReason": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/OrderChangeTrigger/properties/ChangeReason", "type": "string", "title": "The ChangeReason schema", "description": "The reason this ORDER_CHANGE notification was sent.", "examples": ["Buyer Requested Cancel"]}}}, "Summary": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary", "type": "object", "title": "The Summary schema", "description": "Information about the order and order items that had the change.", "examples": [{"MarketplaceId": "ATVPDKIKX0DER", "OrderStatus": "Unshipped", "PurchaseDate": "2022-07-13T19:42:04.284Z", "DestinationPostalCode": "48110", "FulfillmentType": "MFN", "OrderType": "StandardOrder", "NumberOfItemsShipped": 0, "NumberOfItemsUnshipped": 10, "EarliestDeliveryDate": "2022-11-07T19:42:04.284Z", "LatestDeliveryDate": "2022-12-07T19:42:04.284Z", "EarliestShipDate": "2022-11-07T19:42:04.284Z", "LatestShipDate": "2022-12-07T19:42:04.284Z", "CancelNotifyDate": "2022-12-07T19:42:04.284Z", "OrderPrograms": ["Business"], "ShippingPrograms": ["EasyShip"], "EasyShipShipmentStatus": "Delivered", "ElectronicInvoiceStatus": "NotFound", "OrderItems": [{"OrderItemId": "OIID34853450", "SellerSKU": "SellerSKUID1", "OrderItemStatus": "Unshipped", "Quantity": 10, "QuantityShipped": 0, "IsBuyerRequestedCancel": true, "ItemEarliestDeliveryDate": "2022-11-07T19:42:04.284Z", "ItemLatestDeliveryDate": "2022-12-07T19:42:04.284Z"}]}], "required": ["MarketplaceId", "OrderStatus", "PurchaseDate", "DestinationPostalCode", "FulfillmentType", "OrderType", "OrderItems"], "properties": {"MarketplaceId": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/MarketplaceId", "type": "string", "title": "The MarketplaceId schema", "description": "The Amazon marketplace identifier of the order.", "examples": ["ATVPDKIKX0DER"]}, "OrderStatus": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderStatus", "type": "string", "enum": ["Pending", "Unshipped", "PartiallyShipped", "Shipped", "Canceled", "Unfulfillable", "InvoiceUnconfirmed", "PendingAvailability"], "title": "The OrderStatus schema", "description": "The current order status.", "examples": ["Unshipped"]}, "PurchaseDate": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/PurchaseDate", "type": ["string", "null"], "title": "The PurchaseDate schema", "description": "The purchase date of the order, in ISO-8601 date/time format. It will be null when there is no related information.", "examples": ["2022-07-13T19:42:04.284Z"]}, "DestinationPostalCode": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/DestinationPostalCode", "type": ["string", "null"], "title": "The DestinationPostalCode schema", "description": "The destination postal code. It will be null when there is no related information.", "examples": ["48110"]}, "FulfillmentType": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/FulfillmentType", "type": "string", "enum": ["AFN", "MFN"], "title": "The FulfillmentType schema", "description": "Fulfillment type of the affected order, MFN or AFN.", "examples": ["MFN"]}, "OrderType": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderType", "type": "string", "enum": ["StandardOrder", "LongLeadTimeOrder", "Preorder", "BackOrder", "SourcingOnDemandOrder"], "title": "The OrderType schema", "description": "The type of the order.", "examples": ["StandardOrder"]}, "NumberOfItemsShipped": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/NumberOfItemsShipped", "type": "integer", "title": "The NumberOfItemsShipped schema", "description": "The number of items shipped.", "examples": [0]}, "NumberOfItemsUnshipped": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/NumberOfItemsUnshipped", "type": "integer", "title": "The NumberOfItemsUnshipped schema", "description": "The number of items unshipped.", "examples": [10]}, "EarliestDeliveryDate": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/EarliestDeliveryDate", "type": "string", "title": "The EarliestDeliveryDate schema", "description": "The start of the time period during which you committed to fulfill the order, in ISO-8601 date/time format. Returned only for seller-fulfilled orders.", "examples": ["2022-11-07T19:42:04.284Z"]}, "LatestDeliveryDate": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/LatestDeliveryDate", "type": "string", "title": "The LatestDeliveryDate schema", "description": "The end of the time period during which you committed to fulfill the order, in ISO-8601 date/time format. Returned only for seller-fulfilled orders that do not have a PendingAvailability, Pending, or Canceled status.", "examples": ["2022-12-07T19:42:04.284Z"]}, "EarliestShipDate": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/EarliestShipDate", "type": "string", "title": "The EarliestShipDate schema", "description": "The start of the time period during which you committed to ship the order, in ISO-8601 date/time format.", "examples": ["2022-11-07T19:42:04.284Z"]}, "LatestShipDate": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/LatestShipDate", "type": "string", "title": "The LatestShipDate schema", "description": "The end of the time period during which you committed to ship the order, in ISO-8601 date/time format.", "examples": ["2022-12-07T19:42:04.284Z"]}, "CancelNotifyDate": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/CancelNotifyDate", "type": "string", "title": "The CancelNotifyDate schema", "description": "The end of the time period during which you must notify the seller of possible cancellation, in ISO-8601 date/time format.", "examples": ["2022-12-07T19:42:04.284Z"]}, "OrderPrograms": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderPrograms", "type": "array", "title": "The OrderPrograms schema", "description": "The order programs in which this order participates.", "items": {"type": "string", "enum": ["Business", "Prime", "Premium", "IBA", "Replacement"]}, "examples": [["Business"]]}, "ShippingPrograms": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/ShippingPrograms", "type": "array", "title": "The ShippingPrograms schema", "description": "The shipping programs in which this order participates.", "items": {"type": "string", "enum": ["ShipDateSet", "GlobalExpress", "ISPU", "AccessPoint", "TFM", "EasyShip"]}, "examples": [["EasyShip"]]}, "EasyShipShipmentStatus": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/EasyShipShipmentStatus", "type": "string", "enum": ["PendingSchedule", "PendingPickUp", "PendingDropOff", "LabelCanceled", "PickedUp", "DroppedOff", "AtOriginFC", "AtDestinationFC", "Delivered", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Undeliverable", "ReturningToSeller", "ReturnedToSeller", "Lost", "OutForDelivery", "Damaged"], "title": "The EasyShipShipmentStatus schema", "description": "The status of the Amazon Easy Ship order. This property is included only for Amazon Easy Ship orders.", "examples": ["Delivered"]}, "ElectronicInvoiceStatus": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/ElectronicInvoiceStatus", "type": "string", "enum": ["NotRequired", "NotFound", "Processing", "Errored", "Accepted"], "title": "The ElectronicInvoiceStatus schema", "description": "The status of the electronic invoice.", "examples": ["NotFound"]}, "OrderItems": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderItems", "type": "array", "title": "The OrderItems schema", "description": "Information about order items included in this order. For OrderItemLevel notifications, one payload includes one item, while for OrderLevel notifications, one payload includes all items.", "examples": [[{"OrderItemId": "OIID34853450", "SellerSKU": "SellerSKUID1", "SupplySourceId": "d7679e14-031b-4ab3-a81b-ec4fc7a460b3", "OrderItemStatus": "Unshipped", "Quantity": 10, "QuantityShipped": 0, "IsBuyerRequestedCancel": true, "ItemEarliestDeliveryDate": "2022-11-07T19:42:04.284Z", "ItemLatestDeliveryDate": "2022-12-07T19:42:04.284Z"}]], "items": {"type": "object", "title": "The OrderItem schema", "description": "Information about every order item.", "examples": [{"OrderItemId": "OIID34853450", "SellerSKU": "SellerSKUID1", "SupplySourceId": "d7679e14-031b-4ab3-a81b-ec4fc7a460b3", "OrderItemStatus": "Unshipped", "Quantity": 10, "QuantityShipped": 0, "IsBuyerRequestedCancel": true, "ItemEarliestDeliveryDate": "2022-11-07T19:42:04.284Z", "ItemLatestDeliveryDate": "2022-12-07T19:42:04.284Z"}], "required": ["OrderItemId", "SellerSKU", "SupplySourceId", "Quantity"], "properties": {"OrderItemId": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderItems/OrderItemId", "type": "string", "title": "The OrderItemId schema", "description": "The Amazon-defined order item identifier.", "examples": ["OIID34853450"]}, "SellerSKU": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderItems/SellerSKU", "type": "string", "title": "The SellerSKU schema", "description": "The seller-specific SKU for an item.", "examples": ["SellerSKUID1"]}, "SupplySourceId": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderItems/SupplySourceId", "type": ["string", "null"], "title": "The SupplySourceId schema", "description": "The unique identifier of the supply source. Will be null when there is no related information.", "examples": ["d7679e14-031b-4ab3-a81b-ec4fc7a460b3"]}, "OrderItemStatus": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderItems/OrderItemStatus", "type": "string", "enum": ["Unshipped", "Shipped"], "title": "The OrderItemStatus schema", "description": "The current status of the order item. This field Will have a non-null value when items' statuses are different.", "examples": ["Unshipped"]}, "Quantity": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderItems/Quantity", "type": "integer", "title": "The Quantity schema", "description": "The number of items in the order.", "examples": [10]}, "QuantityShipped": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderItems/QuantityShipped", "type": "integer", "title": "The QuantityShipped schema", "description": "The number of items shipped.", "examples": [0]}, "IsBuyerRequestedCancel": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderItems/IsBuyerRequestedCancel", "type": "boolean", "title": "The IsBuyerRequestedCancel schema", "description": "True when the buyer has requested cancelation.", "examples": [true]}, "ItemEarliestDeliveryDate": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderItems/ItemEarliestDeliveryDate", "type": "string", "title": "The ItemEarliestDeliveryDate schema", "description": "The start of the time period during which you have committed to fulfill the order.", "examples": ["2022-11-07T19:42:04.284Z"]}, "ItemLatestDeliveryDate": {"$id": "#/properties/Payload/properties/OrderChangeNotification/properties/Summary/properties/OrderItems/ItemLatestDeliveryDate", "type": "string", "title": "The ItemLatestDeliveryDate schema", "description": "The end of the time period during which you have committed to fulfill the order.", "examples": ["2022-12-07T19:42:04.284Z"]}}}}}}}}}}, "NotificationMetadata": {"$id": "#/properties/NotificationMetadata", "type": "object", "title": "The NotificationMetadata schema", "description": "The notification metadata.", "examples": [{"ApplicationId": "app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "SubscriptionId": "subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "PublishTime": "2020-07-13T19:42:04.284Z", "NotificationId": "d0e9e693-c3ad-4373-979f-ed4ec98dd746"}], "required": ["ApplicationId", "SubscriptionId", "PublishTime", "NotificationId"], "properties": {"ApplicationId": {"$id": "#/properties/NotificationMetadata/properties/ApplicationId", "type": "string", "title": "The ApplicationId schema", "description": "The identifier for the application that uses the notifications.", "examples": ["app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}, "SubscriptionId": {"$id": "#/properties/NotificationMetadata/properties/SubscriptionId", "type": "string", "title": "The SubscriptionId schema", "description": "A unique identifier for the subscription which resulted in this notification.", "examples": ["subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}, "PublishTime": {"$id": "#/properties/NotificationMetadata/properties/PublishTime", "type": "string", "title": "The PublishTime schema", "description": "The date and time (in UTC) that the notification was sent, in ISO-8601 date/time format.", "examples": ["2020-07-13T19:42:04.284Z"]}, "NotificationId": {"$id": "#/properties/NotificationMetadata/properties/NotificationId", "type": "string", "title": "The NotificationId schema", "description": "A unique identifier for this notification instance.", "examples": ["d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}}}}}