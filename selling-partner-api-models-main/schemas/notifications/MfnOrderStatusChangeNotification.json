{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "title": "The root schema", "description": "The root schema comprises the entire JSON document.", "default": {}, "examples": [{"NotificationVersion": "1.0", "NotificationType": "MFN_ORDER_STATUS_CHANGE", "PayloadVersion": "1.0", "EventTime": "2020-07-13T19:42:04.284Z", "Payload": {"MFNOrderStatusChangeNotification": {"SellerId": "AXXXXXXXXXXXXX", "MarketplaceId": "ATVPDKIKX0DER", "AmazonOrderId": "333-7777777-7777777", "PurchaseDate": 1595882000633, "OrderStatus": "SHIPPING", "DestinationPostalCode": "48110", "SupplySourceId": "55448834-0d79-5155-75c4-8529543a7c31", "OrderItemId": "OIID34853450", "SellerSKU": "SellerSKUID1", "Quantity": 45}}, "NotificationMetadata": {"ApplicationId": "app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "SubscriptionId": "subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "PublishTime": "2020-07-13T19:42:04.284Z", "NotificationId": "d0e9e693-c3ad-4373-979f-ed4ec98dd746"}}], "required": ["NotificationVersion", "NotificationType", "PayloadVersion", "EventTime", "Payload", "NotificationMetadata"], "properties": {"NotificationVersion": {"$id": "#/properties/NotificationVersion", "type": "string", "title": "The NotificationVersion schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["1.0"]}, "NotificationType": {"$id": "#/properties/NotificationType", "type": "string", "title": "The NotificationType schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["MFN_ORDER_STATUS_CHANGE"]}, "PayloadVersion": {"$id": "#/properties/PayloadVersion", "type": "string", "title": "The PayloadVersion schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["1.0"]}, "EventTime": {"$id": "#/properties/EventTime", "type": "string", "title": "The EventTime schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2020-07-13T19:42:04.284Z"]}, "Payload": {"$id": "#/properties/Payload", "type": "object", "title": "The Payload schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"MFNOrderStatusChangeNotification": {"SellerId": "AXXXXXXXXXXXXX", "MarketplaceId": "ATVPDKIKX0DER", "AmazonOrderId": "333-7777777-7777777", "PurchaseDate": 1595882000633, "OrderStatus": "SHIPPING", "DestinationPostalCode": "48110", "SupplySourceId": "55448834-0d79-5155-75c4-8529543a7c31", "OrderItemId": "OIID34853450", "SellerSKU": "SellerSKUID1", "Quantity": 45}}], "required": ["MFNOrderStatusChangeNotification"], "properties": {"MFNOrderStatusChangeNotification": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification", "type": "object", "title": "The MFNOrderStatusChangeNotification schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"SellerId": "AXXXXXXXXXXXXX", "MarketplaceId": "ATVPDKIKX0DER", "AmazonOrderId": "333-7777777-7777777", "PurchaseDate": 1595882000633, "OrderStatus": "SHIPPING", "DestinationPostalCode": "48110", "SupplySourceId": "55448834-0d79-5155-75c4-8529543a7c31", "OrderItemId": "OIID34853450", "SellerSKU": "SellerSKUID1", "Quantity": 45}], "required": ["SellerId", "MarketplaceId", "AmazonOrderId", "PurchaseDate", "OrderStatus", "DestinationPostalCode", "SupplySourceId", "OrderItemId", "SellerSKU", "Quantity"], "properties": {"SellerId": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification/properties/SellerId", "type": "string", "title": "The SellerId schema", "description": "The selling partner identifier.", "default": "", "examples": ["AXXXXXXXXXXXXX"]}, "MarketplaceId": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification/properties/MarketplaceId", "type": "string", "title": "The MarketplaceId schema", "description": "Amazon marketplace identifier of the affected order.", "examples": ["ATVPDKIKX0DER"]}, "AmazonOrderId": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification/properties/AmazonOrderId", "type": "string", "title": "The AmazonOrderId schema", "description": "An Amazon-defined order identifier in 3-7-7 format.", "default": "", "examples": ["333-7777777-7777777"]}, "PurchaseDate": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification/properties/PurchaseDate", "type": ["integer", "null"], "title": "The PurchaseDate schema", "description": "The purchase date of the order in Epoch time.", "default": 0, "examples": [1595882000633]}, "OrderStatus": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification/properties/OrderStatus", "type": "string", "title": "The OrderStatus schema", "description": "The current order status, its possible values are UPCOMINGORDER, PENDING, SHIPPING, WAITING, CANCELED, INVOICEUNCONFIRMED, SHIPPED and UNFULF<PERSON>LABLE.", "default": "", "examples": ["SHIPPING"]}, "DestinationPostalCode": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification/properties/DestinationPostalCode", "type": ["string", "null"], "title": "The DestinationPostalCode schema", "description": "The destination postal code.", "default": "", "examples": ["48110"]}, "SupplySourceId": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification/properties/SupplySourceId", "type": ["string", "null"], "title": "The SupplySourceId schema", "description": "The unique identifier of the supply source.", "default": "", "examples": ["55448834-0d79-5155-75c4-8529543a7c31"]}, "OrderItemId": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification/properties/OrderItemId", "type": "string", "title": "The OrderItemId schema", "description": "The Amazon-defined order item identifier.", "default": "", "examples": ["OIID34853450"]}, "SellerSKU": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification/properties/SellerSKU", "type": "string", "title": "The SellerSKU schema", "description": "The seller-specific SKU identifier for an item.", "default": "", "examples": ["SellerSKUID1"]}, "Quantity": {"$id": "#/properties/Payload/properties/MFNOrderStatusChangeNotification/properties/Quantity", "type": "integer", "title": "The Quantity schema", "description": "The number of items in the order.", "default": 0, "examples": [45]}}, "additionalProperties": true}}, "additionalProperties": true}, "NotificationMetadata": {"$id": "#/properties/NotificationMetadata", "type": "object", "title": "The NotificationMetadata schema", "description": "An explanation about the purpose of this instance.", "default": {}, "examples": [{"ApplicationId": "app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "SubscriptionId": "subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746", "PublishTime": "2020-07-13T19:42:04.284Z", "NotificationId": "d0e9e693-c3ad-4373-979f-ed4ec98dd746"}], "required": ["ApplicationId", "SubscriptionId", "PublishTime", "NotificationId"], "properties": {"ApplicationId": {"$id": "#/properties/NotificationMetadata/properties/ApplicationId", "type": "string", "title": "The ApplicationId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["app-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}, "SubscriptionId": {"$id": "#/properties/NotificationMetadata/properties/SubscriptionId", "type": "string", "title": "The SubscriptionId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["subscription-id-d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}, "PublishTime": {"$id": "#/properties/NotificationMetadata/properties/PublishTime", "type": "string", "title": "The PublishTime schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["2020-07-13T19:42:04.284Z"]}, "NotificationId": {"$id": "#/properties/NotificationMetadata/properties/NotificationId", "type": "string", "title": "The NotificationId schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["d0e9e693-c3ad-4373-979f-ed4ec98dd746"]}}, "additionalProperties": true}}, "additionalProperties": true}