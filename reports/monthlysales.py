import logging
import random
import csv
import io
import re
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render

from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import site_query, get_user_id_fn
from .report import ReportManager, parse_url_key, download_report_data, generate_start_and_end_date, \
    convert_to_beijing_time
from SPAPI_Python_SDK.login_user_id import session_decorator, session_middleware, login_required_if_no_user_id

from datetime import datetime

from SPAPI_Python_SDK.retry_utils import api_retry_decorator, handle_api_errors, logger, HTTPError

SITE_LOOP = {1, 4, 6}

api_retry = api_retry_decorator(max_retries=3)

from .report import (
    ReportManager, parse_url_key, download_report_data, get_first_user_id,
    safe_create_report, safe_check_report, safe_get_report
)


def addslashes(s):
    return re.sub(r"(['\"\\])", r"\\\1", s)


def check_duplicate_order(db, amazon_order_id, user_id):
    """
    检查订单是否已存在，防止重复插入
    :param db: 数据库连接对象
    :param amazon_order_id: 亚马逊订单ID
    :param user_id: 用户ID
    :return: True表示已存在，False表示不存在
    """
    try:
        check_sql = "SELECT COUNT(*) as count FROM fba_complete_order WHERE `amazon-order-id` = %s AND user_id = %s"
        result = db.fetchone(check_sql, (amazon_order_id, user_id))
        return result['count'] > 0 if result else False
    except Exception as e:
        logger.error(f"检查重复订单时发生错误: {e}")
        return False


def process_report_data_in_batches(db, uncompressed_data, user_id, zom_report_id):
    """
    分批处理报告数据，参考amazon_collect.py的批处理机制
    :param db: 数据库连接对象
    :param uncompressed_data: 解压后的数据
    :param user_id: 用户ID
    :param zom_report_id: 报告ID
    :return: 成功处理的记录数
    """
    try:
        data_file_like = io.StringIO(uncompressed_data)
        csv_reader = csv.DictReader(data_file_like, delimiter='\t')

        # 批处理配置
        batch_size = 1000  # 减小批次大小以避免内存问题
        total_processed = 0
        duplicate_count = 0
        error_count = 0

        # SQL语句
        sql_insert = """
            INSERT INTO fba_complete_order (
                user_id, Shipment_date, SKU, Fulfillment_center_id, Quantity,
                `amazon-order-id`, Currency, Item_price_per_unit, Shipping_price,
                city, state, Postal_code
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
        """

        data_batch = []

        for row_num, cell in enumerate(csv_reader, 1):
            try:
                # 提取数据
                shipment_date = convert_to_beijing_time(cell.get('shipment-date', ''))
                sku = cell.get('sku', '')
                fulfillment_center_id = cell.get('fulfillment-center-id', '')
                quantity = cell.get('quantity', '0')
                amazon_order_id = cell.get('amazon-order-id', '')
                currency = cell.get('currency', '')

                # 安全转换数值
                try:
                    item_price_per_unit = float(cell.get('item-price-per-unit', 0))
                    shipping_price = float(cell.get('shipping-price', 0))
                except (ValueError, TypeError):
                    item_price_per_unit = 0.0
                    shipping_price = 0.0

                city = cell.get('ship-city', '')
                state = cell.get('ship-state', '')
                postal_code = cell.get('ship-postal-code', '')

                # 检查重复订单
                if amazon_order_id and check_duplicate_order(db, amazon_order_id, user_id):
                    duplicate_count += 1
                    logger.warning(f"跳过重复订单: {amazon_order_id}")
                    continue

                # 添加到批次
                data_batch.append((
                    user_id, shipment_date, sku, fulfillment_center_id, quantity,
                    amazon_order_id, currency, item_price_per_unit, shipping_price,
                    city, state, postal_code
                ))

                # 当批次达到指定大小时执行插入
                if len(data_batch) >= batch_size:
                    success_count = execute_batch_insert(db, sql_insert, data_batch, total_processed)
                    total_processed += success_count
                    data_batch = []  # 清空批次

            except Exception as e:
                error_count += 1
                logger.error(f"处理第 {row_num} 行数据时发生错误: {e}")
                continue

        # 处理剩余的数据
        if data_batch:
            success_count = execute_batch_insert(db, sql_insert, data_batch, total_processed)
            total_processed += success_count

        logger.info(f"数据处理完成 - 总处理: {total_processed}, 重复跳过: {duplicate_count}, 错误: {error_count}")
        return total_processed

    except Exception as e:
        logger.error(f"分批处理数据时发生错误: {e}")
        raise


def execute_batch_insert(db, sql_insert, data_batch, current_total):
    """
    执行批量插入操作
    :param db: 数据库连接对象
    :param sql_insert: 插入SQL语句
    :param data_batch: 数据批次
    :param current_total: 当前已处理总数
    :return: 成功插入的记录数
    """
    try:
        # 使用事务确保数据一致性
        db.bulk_insert(sql_insert, data_batch)
        batch_count = len(data_batch)
        logger.info(f"成功插入批次数据: {batch_count} 条 (总计: {current_total + batch_count})")
        return batch_count

    except Exception as e:
        logger.error(f"批量插入失败: {e}")
        # 如果批量插入失败，尝试逐条插入以找出问题数据
        return handle_batch_insert_failure(db, sql_insert, data_batch)


def handle_batch_insert_failure(db, sql_insert, data_batch):
    """
    处理批量插入失败的情况，逐条尝试插入
    :param db: 数据库连接对象
    :param sql_insert: 插入SQL语句
    :param data_batch: 数据批次
    :return: 成功插入的记录数
    """
    success_count = 0
    for i, data_row in enumerate(data_batch):
        try:
            db.insert(sql_insert, data_row)
            success_count += 1
        except Exception as row_e:
            logger.error(f"插入单条数据失败 (批次索引 {i}): {row_e}")
            logger.error(f"问题数据: {data_row}")

    logger.info(f"逐条插入完成: {success_count}/{len(data_batch)} 条成功")
    return success_count


@session_middleware
@login_required_if_no_user_id
def generate_reports(request):
    """
    为所有用户生成报告
    :param request:
    :return:
    """
    page_title = "每月销量-创建报告"
    return_url = request.GET.get('self', "1")
    site_id = int(request.GET.get('site', "1").strip())
    user_id = get_user_id_fn(request, site_id)
    url_key = request.GET.get('key', "")
    get_start_date = request.GET.get('StartDate', "")
    get_end_date = request.GET.get('EndDate', "")
    result = generate_start_and_end_date(get_start_date, get_end_date)
    start_date = result.get("StartDate")
    end_date = result.get("EndDate")

    if not user_id:
        return HttpResponse("没有用户")

    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    try:
        # 使用上下文管理器来管理数据库连接
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:

            api_client = ReportManager(user_id, db)  # 报告API
            # 创建报告
            report_type = "GET_FBA_FULFILLMENT_CUSTOMER_SHIPMENT_SALES_DATA"
            # body = {
            # 	"reportType": report_type,
            # 	"dataStartTime": start_date,
            # 	"dataEndTime": end_date,
            # 	"marketplaceIds": ["ATVPDKIKX0DER"]
            # }

            # 2025年3月8日15:05:02
            # create_report_response = api_client.createReport(report_type, start_date, end_date)
            create_report_response = safe_create_report(
                api_client,
                report_type=report_type,
                start_date=start_date,
                end_date=end_date
            )

            report_id = create_report_response.report_id

            # 保存report_id
            new_id = api_client.save_report_id(report_id, 'MonthSales')

            # 下一个user_id
            new_user_id = api_client.next_user()

            # 拼接url_key
            url_key = f"{url_key}-{user_id}_{new_id}" if url_key else f"{user_id}_{new_id}"

            # MPR(1)、郑州(4)、武汉(6)
            site_ids_loop = [1, 4, 6]
            if site_id in site_ids_loop:
                if new_user_id:  # 有下一个用户
                    show_context = f"开始创建用户{new_user_id}的报告"
                    go_url = f"/reports/generate_reports_sale_month/?user_id={new_user_id}&site={site_id}&key={url_key}&StartDate={get_start_date}&EndDate={get_end_date}"
                    context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                    return render(request, "SPAPI_Python_SDK/goto.html", context)

            # 没有下一个用户或不是MPR，郑州，武汉表示全部建仓成功
            show_context = "全部创建成功, 开始获取报告"
            user_id = url_key.split('-')[0].split('_')[0]
            go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={get_start_date}&EndDate={get_end_date}"
            context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
            return render(request, "SPAPI_Python_SDK/goto.html", context)
    except Exception as e:
        logging.error(f"生成报告出错: {e}")
        return HttpResponse(f"生成报告出错: {e}")


@login_required_if_no_user_id
def check_report(request):
    """
    检查报告状态
    :param request:
    :return:
    """
    page_title = "检查报告状态"
    site_id = request.GET.get('site', "").strip()
    user_id = request.GET.get('user_id')
    StartDate = request.GET.get('StartDate')
    EndDate = request.GET.get('EndDate')
    url_key = request.GET.get('key')
    array_ZR_id = parse_url_key(url_key) if url_key else {}

    if not user_id:
        return HttpResponse("没有user")
    site_id = int(site_id)
    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    # 使用上下文管理器来管理数据库连接
    try:
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
            sql = f"SELECT id,report_id FROM fba_report WHERE user_id={user_id} AND id={array_ZR_id[user_id]} AND report_id !='' AND report_document_id IS NULL AND f=0 AND report_type='MonthSales' ORDER BY id DESC LIMIT 1"

            row = db.fetchone(sql)

            if not row:  # 判断有没有下一个用户
                site_ids_loop = [1, 4, 6]
                if site_id in site_ids_loop:
                    sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
                    user_rs = db.fetchone(sql)
                    if user_rs:
                        new_user_id = user_rs["id"]
                        show_context = f"开始获取下一用户的报告"
                        # user_id = url_key.split('-')[0].split('_')[0]
                        go_url = f"/reports/check_report_sale_month/?user_id={new_user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                        context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                        return render(request, "SPAPI_Python_SDK/goto.html", context)
                    else:
                        show_context = "进入最后一步-统计销量"
                        go_url = f"/reports/count_sku_sales_month/?site={site_id}&StartDate={StartDate}&EndDate={EndDate}"
                        context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                        return render(request, "SPAPI_Python_SDK/goto.html", context)
                else:
                    return HttpResponse("创建完成")

            report_id = row['report_id']
            api_client = ReportManager(user_id, db)
            # 检查报告状态
            # 2025年3月8日15:06:58
            # response = api_client.checkReport(report_id)

            response = safe_check_report(api_client, report_id)
            response_dict = response.to_dict()
            status = response_dict.get("processing_status")
            created_time = response_dict.get("created_time")
            report_document_id = response_dict.get("report_document_id")

            # 根据报告状态判断下一步
            if status == "DONE":
                update_sqls = f"UPDATE fba_report SET report_document_id='{report_document_id}', create_date='{created_time}' WHERE report_id='{report_id}'"
                db.update(update_sqls)

                show_context = "查询成功，即将抓取"
                go_url = f"/reports/get_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
            else:
                if status == "FATAL":
                    sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
                    db.delete(sql)
                    go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                    second = random.randint(5, 10)
                    show_context = f"{user_id} 报告因致命错误而停止"
                elif status == "CANCELLED":
                    sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
                    db.delete(sql)
                    go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                    second = random.randint(5, 10)
                    show_context = f"{user_id} 亚马逊数据取消"
                elif status == "IN_QUEUE":
                    go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                    second = random.randint(10, 50)
                    show_context = f"{user_id} 报告正在处理"
                else:
                    go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                    second = random.randint(100, 200)
                    show_context = "报告尚未开始处理"

                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": second}
                return render(request, "SPAPI_Python_SDK/goto.html", context)


    except ValueError as e:
        logger.error(f"参数错误: {str(e)}")
        raise  # 触发重试机制
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        raise HTTPError(str(e)) from e


@login_required_if_no_user_id
def get_report(request):
    """
    获取报告数据
    """
    page_title = "每日销量-月获取报告"
    site_id = int(request.GET.get('site'))
    user_id = request.GET.get('user_id')
    StartDate = request.GET.get('StartDate')
    EndDate = request.GET.get('EndDate')
    url_key = request.GET.get('key')
    array_ZR_id = parse_url_key(url_key)
    if not user_id:
        return HttpResponse("没有user")

    dbdata = DB_DATA.get(site_id)
    if not dbdata:
        return HttpResponse("无效的 site_id")
    try:
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
            sql = f"SELECT id, user_id,report_id, report_document_id FROM fba_report WHERE f=0 and user_id={user_id} AND id={array_ZR_id[user_id]} ORDER BY id desc LIMIT 1"
            rs = db.fetchone(sql)

            if not rs:
                show_context = "处理完成"
                if site_id in [1, 4, 6]:
                    go_url = f"/reports/update_report_table_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                else:
                    go_url = f"/reports/update_report_table_sale_month/?user_id={user_id}&site={site_id}&StartDate={StartDate}&EndDate={EndDate}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
            else:
                user_id = rs["user_id"]
                zom_report_id = rs["report_id"]
                report_document_id = rs["report_document_id"]
                api_client = ReportManager(user_id, db)

                # 获取报销详情url
                # 2025年3月8日15:14:45
                # DocementResponses = api_client.getReportUrl(report_document_id)
                # 获取报告下载URL和压缩方式
                try:
                    DocementResponses = safe_get_report(api_client, report_document_id)
                    document_data = DocementResponses.to_dict()

                    report_document_id = document_data['report_document_id']
                    compression_algorithm = document_data['compression_algorithm']

                    report_url = document_data['url']

                    # 下载并解压报告数据
                    uncompressed_data = download_report_data(report_url, compression_algorithm)
                except Exception as e:
                    # 处理异常参数解包，兼容单参数和双参数情况
                    if len(e.args) >= 2:
                        error_message, response_text = e.args[0], e.args[1]
                    elif len(e.args) == 1:
                        error_message = str(e.args[0])
                        response_text = ""
                    else:
                        error_message = str(e)
                        response_text = ""

                    if "403" in error_message and "已过期" in response_text:
                        document_data = api_client.getReportUrl(report_document_id)
                        report_url = document_data['url']
                        uncompressed_data = download_report_data(report_url, compression_algorithm)
                    else:
                        raise  # 重新抛出异常
                if uncompressed_data:
                    # 使用分批处理机制来处理大量数据
                    success_count = process_report_data_in_batches(
                        db, uncompressed_data, user_id, zom_report_id
                    )

                    get_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    # 更新 fba_report 表
                    try:
                        sql = f"UPDATE fba_report SET f=1, get_date='{get_date}' WHERE report_id={zom_report_id}"
                        db.update(sql)
                        logger.info(f"用户 {user_id} 报告处理完成，成功处理 {success_count} 条记录")
                    except Exception as e:
                        logger.error(f"更新 fba_report 时发生错误: {e}")

                show_context = f"{user_id}报告数据抓取完成"
                go_url = f"/reports/update_report_table_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)


    except Exception as e:
        logging.error(f"流程执行失败: {str(e)}")
        raise


@login_required_if_no_user_id
def update_report_table(request):
    page_title = "每月销量-更新报告状态"
    site_id = request.GET.get('site')
    user_id = request.GET.get('user_id')
    StartDate = request.GET.get('StartDate')
    EndDate = request.GET.get('EndDate')
    url_key = request.GET.get('key')
    array_ZR_id = parse_url_key(url_key) if url_key else {}
    if not user_id:
        return HttpResponse("没有user")
    site_id = int(site_id)
    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
        sql = f"SELECT report_document_id FROM fba_report WHERE user_id={user_id} and f=1 AND id={array_ZR_id[user_id]} ORDER BY id ASC LIMIT 100"

        rs = db.fetchall(sql)
        #
        for row in rs:
            report_document_id = row['report_document_id']
            sql = f"DELETE FROM fba_report WHERE report_document_id = '{report_document_id}'"
        # db.delete(sql)

        # 检查是否有下一个用户
        site_ids_loop = [1, 4, 6]
        if site_id in site_ids_loop:
            sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
            user_rs = db.fetchone(sql)
            if user_rs:
                user_id = user_rs["id"]
                show_context = "即将查询下一个用户"
                go_url = f"/reports/check_report_sale_month/?user_id={user_id}&site={site_id}&key={url_key}&StartDate={StartDate}&EndDate={EndDate}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
            else:
                show_context = "进入最后一步"
                go_url = f"/reports/count_sku_sales_month/?site={site_id}&StartDate={StartDate}&EndDate={EndDate}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
        else:
            show_context = "每日销量更新完成"
            go_url = f"/reports/count_sku_sales_month/?site={site_id}&StartDate={StartDate}&EndDate={EndDate}"
            context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
            return render(request, "SPAPI_Python_SDK/goto.html", context)


@login_required_if_no_user_id
def count_sku_sales(request):
    page_title = "每月销量-更新报告状态"
    site_id = request.GET.get('site')
    start_date = request.GET.get('StartDate')
    end_date = request.GET.get('EndDate')
    if not all([site_id, start_date, end_date]):
        return HttpResponse("缺少必要参数")
    site_id = int(site_id)
    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("未知平台")

    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:

        user_sql = "SELECT id FROM user where switch=1 AND MWS_Switch=1"
        users = db.fetchall(user_sql)

        # 插入统计标题
        title = f"{start_date}到{end_date}统计"
        up_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        sql = f"INSERT INTO fba_count_sku_title(Title, count_date, s_date, e_date)VALUES('{title}', '{up_date}', '{start_date}', '{end_date}');"
        new_id = db.insert(sql)

        for user in users:
            sales_sql = f"SELECT SUM(Quantity) as sales, SKU FROM fba_complete_order WHERE user_id = {user['id']} AND Shipment_date >= '{start_date} 00:00:00' AND Shipment_date <= '{end_date} 23:23:59' GROUP BY SKU"

            sku_rs = db.fetchall(sales_sql)
            for sku in sku_rs:
                insert_sql = f"INSERT INTO fba_count_sku_list(user_id, Title_id, SKU, sales)VALUES({user['id']}, {new_id}, '{sku['SKU']}', {sku['sales']});"
                db.insert(insert_sql)
        show_context = "全部更新完成"
        context = {"show_context": show_context, "page_title": page_title}
        return render(request, "SPAPI_Python_SDK/show.html", context)
