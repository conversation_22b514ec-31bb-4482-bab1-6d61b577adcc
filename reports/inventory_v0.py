import logging
import random
import csv
import io

from datetime import datetime
from django.http import HttpResponse
from django.shortcuts import render

from SPAPI_Python_SDK.Database import Database, DB_DATA
from .report import ReportManager, parse_url_key, download_report_data
from SPAPI_Python_SDK.login_user_id import session_middleware, login_required_if_no_user_id
from SPAPI_Python_SDK.retry_utils import api_retry_decorator, handle_api_errors, logger, HTTPError

api_retry = api_retry_decorator(max_retries=3)


@session_middleware
@login_required_if_no_user_id
@handle_api_errors
@api_retry
def safe_create_report(api_client, report_type, **kwargs):
	return api_client.createReport(report_type, **kwargs)


def generate_reports(request):
	"""
	为所有用户生成报告
	:param request:
	:return:
	"""
	page_title = "FBA库存-创建报告"
	user_id = request.GET.get('user_id')
	site_id = int(request.GET.get('site_id'))
	url_key = request.GET.get('key')
	if not user_id:
		return HttpResponse("没有用户")

	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")

	try:
		# 使用上下文管理器来管理数据库连接
		with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
			api_client = ReportManager(user_id, db)  # 报告API
			# 创建报告
			report_type = "GET_FBA_MYI_UNSUPPRESSED_INVENTORY_DATA"
			# create_report_response = api_client.createReport(report_type)

			create_report_response = safe_create_report(api_client, report_type=report_type)
			report_id = create_report_response.report_id

			# 保存report_id
			new_id = api_client.save_report_id(report_id, 'Inventory')

			# 下一个user_id
			new_user_id = api_client.next_user()

			# 拼接url_key
			url_key = f"{url_key}-{user_id}_{new_id}" if url_key else f"{user_id}_{new_id}"

			# MPR(1)、郑州(4)、武汉(6)
			site_ids_loop = [1, 4, 6]
			if site_id in site_ids_loop:
				if new_user_id:  # 有下一个用户
					show_context = f"开始创建用户{new_user_id}的报告"
					go_url = f"/reports/generate_reports_inventory/?user_id={new_user_id}&site_id={site_id}&key={url_key}"
					context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
					return render(request, "SPAPI_Python_SDK/goto.html", context)

			# 没有下一个用户或不是MPR，郑州，武汉表示全部建仓成功
			show_context = "全部创建成功, 开始获取报告"
			user_id = url_key.split('-')[0].split('_')[0]
			go_url = f"/reports/check_report_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}"
			context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
			return render(request, "SPAPI_Python_SDK/goto.html", context)
	except Exception as e:
		logging.error(f"生成报告出错: {e}")
		return HttpResponse(f"生成报告出错: {e}")


@handle_api_errors
@api_retry
def _check_report_core(api_client, report_id):
	response = api_client.checkReport(report_id)
	if response.status_code != 200:
		raise HTTPError(f"API异常: {response.status_code}")
	return response


@login_required_if_no_user_id
def check_report(request):
	"""
	检查报告状态
	:param request:
	:return:
	"""
	page_title = "FBA库存-检查报告状态"
	user_id = request.GET.get('user_id')
	site_id = int(request.GET.get('site_id'))
	url_key = request.GET.get('key')
	array_ZR_id = parse_url_key(url_key) if url_key else {}

	if not user_id:
		return HttpResponse("没有user")

	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")

	# 使用上下文管理器来管理数据库连接
	try:
		with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
			sql = f"SELECT id,report_id FROM fba_report WHERE user_id={user_id} AND id={array_ZR_id[user_id]} AND report_id!='' AND report_document_id IS NULL AND f=0 AND report_type='Inventory' ORDER BY id DESC LIMIT 1"
			row = db.fetchone(sql)

			if not row:  # 如果当前用户没有报告id
				site_ids_loop = [1, 4, 6]
				if site_id in site_ids_loop:
					sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
					user_rs = db.fetchone(sql);
					if user_rs:
						new_user_id = user_rs["id"]
						show_context = f"开始获取下一用户的报告"
						go_url = f"/reports/check_report_inventory/?user_id={new_user_id}&site_id={site_id}&key={url_key}"
						context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
						return render(request, "SPAPI_Python_SDK/goto.html", context)
					else:
						return HttpResponse("全部创建完成")
				else:
					return HttpResponse("创建完成")

			report_id = row['report_id']
			api_client = ReportManager(user_id, db)

			# 检查报告状态 2025年3月7日17:19:46
			# response = api_client.checkReport(report_id)
			response = _check_report_core(api_client, report_id)
			response_dict = response.to_dict()
			status = response_dict.get("processing_status")
			created_time = response_dict.get("created_time")
			report_document_id = response_dict.get("report_document_id")
			# 根据报告状态判断下一步
			if status == "DONE":
				update_sqls = f"UPDATE fba_report SET report_document_id='{report_document_id}', create_date='{created_time}' WHERE report_id='{report_id}'"
				db.update(update_sqls)

				show_context = "查询成功，即将抓取"
				go_url = f"/reports/get_report_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}";
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)
			else:
				if status == "FATAL":
					sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
					db.delete(sql)
					go_url = f"/reports/check_report_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}"
					second = random.randint(5, 10)
					show_context = f"{user_id} 报告因致命错误而停止"
				elif status == "CANCELLED":
					sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
					db.delete(sql)
					go_url = f"/reports/check_report_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}"
					second = random.randint(5, 10)
					show_context = f"{user_id} 亚马逊数据取消"
				elif status == "IN_QUEUE":
					go_url = f"/reports/check_report_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}"
					second = random.randint(10, 50)
					show_context = f"{user_id} 报告正在处理"
				else:
					go_url = f"/reports/check_report_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}"
					second = random.randint(100, 200)
					show_context = "报告尚未开始处理"

				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": second}
				return render(request, "SPAPI_Python_SDK/goto.html", context)

	except Exception as e:
		logging.error(f"查询报告状态-参数错误: {e}")
		return HttpResponse(f"查询报告状态-参数错误: {e}")
	except Exception as e:
		logger.error(f"查询报告状态-: {str(e)}")
		raise HTTPError(str(e)) from e


@handle_api_errors
@api_retry
def _get_report_core(api_client, report_document_id):
	response = api_client.getReport(report_document_id)

	if response.status_code != 200:
		raise HTTPError(f"API异常: {response.status_code}")
	return response


@login_required_if_no_user_id
def get_report(request):
	"""
	获取报告数据
	"""
	page_title = "FBA库存-获取报告"
	user_id = request.GET.get('user_id')
	site_id = int(request.GET.get('site_id'))
	url_key = request.GET.get('key')
	array_ZR_id = parse_url_key(url_key)
	if not user_id:
		return HttpResponse("没有user")

	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")

	with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
		sql = f"SELECT id, user_id,report_id, report_document_id FROM fba_report WHERE f=0 and user_id={user_id} AND id={array_ZR_id[user_id]} ORDER BY id desc LIMIT 1"
		rs = db.fetchone(sql)

		if not rs:
			show_context = "处理完成"
			if site_id in [1, 4, 6]:
				go_url = f"/reports/update_report_table_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}"
			else:
				go_url = f"/reports/update_report_table_inventory/?user_id={user_id}&site_id={site_id}"
			context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
			return render(request, "SPAPI_Python_SDK/goto.html", context)
		else:
			db.start_transaction()
			try:
				report_document_id = rs["report_document_id"]
				zom_report_id = rs["report_id"]
				user_id = rs["user_id"]
				api_client = ReportManager(user_id, db)

				# 获取报告数据
				# DocementResponses = api_client.getReportUrl(report_document_id)

				# 2025年3月7日17:22:58
				DocementResponses = _get_report_core(api_client, report_document_id)
				document_data = DocementResponses.to_dict()

				report_document_id = document_data['report_document_id']

				if not report_document_id:
					logger.error(f"错误：获取报告 {report_document_id} 元数据失败，缺少 reportDocumentId。")
					# 根据你的逻辑决定是返回错误还是抛出异常
					# return HttpResponseServerError("获取报告元数据失败") # Django 示例
					raise ValueError(f"获取报告 {report_document_id} 元数据失败，缺少 reportDocumentId。")

				compression_algorithm = document_data['compression_algorithm']
				report_url = document_data['url']
				if not report_url:
					print(f"错误：获取报告 {report_document_id} 元数据失败，缺少 URL。")
					# return HttpResponseServerError("获取报告元数据失败") # Django 示例
					raise ValueError(f"获取报告 {report_document_id} 元数据失败，缺少 URL。")

				try:
					uncompressed_data = download_report_data(report_url, compression_algorithm)
				except Exception as e:
					# 处理异常参数解包，兼容单参数和双参数情况
					if len(e.args) >= 2:
						error_message, response_text = e.args[0], e.args[1]
					elif len(e.args) == 1:
						error_message = str(e.args[0])
						response_text = ""
					else:
						error_message = str(e)
						response_text = ""

					if "403" in error_message and "已过期" in response_text:
						document_data = api_client.getReportUrl(report_document_id)
						report_url = document_data['url']
						uncompressed_data = download_report_data(report_url, compression_algorithm)
					else:
						raise  # 重新抛出异常

				update_params = []
				if uncompressed_data:
					data_file_like = io.StringIO(uncompressed_data)
					csv_reader = csv.DictReader(data_file_like, delimiter='\t')

					# 遍历每个报告数据
					for cell in csv_reader:
						sku = cell['sku']
						total_quantity = cell['afn-total-quantity']  # AFN 总库存
						sellable_quantity = cell['afn-fulfillable-quantity']  # 可售库存
						unsellable_quantity = cell['afn-unsellable-quantity']  # 不可售库存
						in_bound_quantity = cell['afn-inbound-shipped-quantity']  # 已发货但未到仓库
						reserved_quantity = cell['afn-reserved-quantity']  # 预留
						get_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # 抓取时间
						receiving_quantity = cell['afn-inbound-receiving-quantity']  # 亚马逊仓库正在接收中
						future_quantity = cell['afn-future-supply-buyable']  # 未来可销售库存

						# 查询当前 SKU 是否已存在
						sql = f"SELECT id FROM fba_sku_status WHERE fba_sku_status.sku='{sku}'"
						existing_sku = db.fetchone(sql)

						if existing_sku:
							update_params.append((total_quantity, sellable_quantity, unsellable_quantity,
												  in_bound_quantity, reserved_quantity, get_date, receiving_quantity,
												  future_quantity, sku))
						else:
							if site_id in [1, 4, 6]:
								sql = f"SELECT id, user_id FROM sku_pro WHERE sku='{sku}'"
								user_rs = db.fetchone(sql)
								if user_rs:
									user_id_to_insert = user_rs["user_id"]
									# 插入新记录
									sql_insert = """
										INSERT INTO fba_sku_status (
											user_id, sku, total_quantity, sellable_quantity, unsellable_quantity, 
											shipped_7, shipped_30, shipped_90, shipped_180, shipped_365, in_bound_quantity, Reserved_quantity, Stock_up_date, Receiving_quantity, Future_quantity
										) VALUES (%s, %s, %s, %s, %s, 0, 0, 0, 0, 0, %s, %s, %s, %s, %s)
									"""
									db.insert(sql_insert, (
										user_id_to_insert, sku, total_quantity, sellable_quantity, unsellable_quantity,
										in_bound_quantity, reserved_quantity, get_date, receiving_quantity,
										future_quantity))
					# 批量更新已存在的 SKU
					if update_params:
						sql_update = """
							UPDATE fba_sku_status SET total_quantity = %s, sellable_quantity = %s,unsellable_quantity = %s, in_bound_quantity = %s, Reserved_quantity = %s, 
							Stock_up_date = %s,Receiving_quantity = %s,Future_quantity = %s WHERE sku = %s
						"""
						db.bulk_update(sql_update, update_params)

					# 更新 INVENTORY_FBA_ReportId
					db.update(f"UPDATE fba_report SET f=1, get_date='{get_date}' WHERE id={zom_report_id}")

					db.commit()
			except Exception as e:
				db.rollback()
				raise  # 触发重试

			show_context = f"{user_id}报告数据抓取完成"
			go_url = f"/reports/update_report_table_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}"
			context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
			return render(request, "SPAPI_Python_SDK/goto.html", context)


@login_required_if_no_user_id
def update_report_table(request):
	page_title = "FBA库存-更新报告表"
	user_id = request.GET.get('user_id')
	site_id = int(request.GET.get('site_id'))
	url_key = request.GET.get('key')
	array_ZR_id = parse_url_key(url_key) if url_key else {}
	if not user_id:
		return HttpResponse("没有user")

	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")

	with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
		sql = f"SELECT report_document_id FROM fba_report WHERE user_id={user_id} and f=1 AND id={array_ZR_id[user_id]} ORDER BY id ASC LIMIT 100"

		rs = db.fetchall(sql)
		for row in rs:
			report_document_id = row['report_document_id']
			sql = f"DELETE FROM fba_report WHERE report_document_id = '{report_document_id}'"
		# db.delete(sql)

		# 检查是否有下一个用户
		site_ids_loop = [1, 4, 6]
		if site_id in site_ids_loop:
			sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
			user_rs = db.fetchone(sql)
			if not user_rs:
				show_context = "全部获取完成"
				return HttpResponse(show_context)
			if user_rs:
				user_id = user_rs["id"]
				show_context = "即将查询下一个用户"
				go_url = f"/reports/check_report_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}"
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)
			else:
				show_context = "全部更新完成"
				context = {"show_context": show_context, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/show.html", context)
		else:
			show_context = "全部更新完成"
			context = {"show_context": show_context, "page_title": page_title}
			return render(request, "SPAPI_Python_SDK/show.html", context)
