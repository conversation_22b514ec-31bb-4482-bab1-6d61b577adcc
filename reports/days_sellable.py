import random
import logging
import csv
import io
import requests

from datetime import datetime
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render

from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.login_user_id import session_middleware, login_required_if_no_user_id
from SPAPI_Python_SDK.retry_utils import logger
from SPAPI_Python_SDK.config import common_config, get_user_info
from spapi.spapiclient import SPAPIClient


def parse_user_ids(url_key: str) -> dict:
    """
    解析url_key字符串，将其转换为字典。

    示例输入: "1_1-2_2-3_3"
    Args:
            url_key: 要解析的字符串

    Returns:
            解析后的字典，键和值都转为整数
    """
    result = {}
    if not url_key:
        return result

    try:
        pairs = url_key.split('-')
        for pair in pairs:
            if not pair:
                continue
            user_id, id_num = pair.split('_')
            result[user_id] = int(id_num)
        return result
    except (ValueError, AttributeError) as e:
        logging.error(f"解析user_ids出错: {str(e)}")
        return {}


def download_report_data(url, compression_algorithm=None):
    headers = {
        "User-Agent": "MySPAPIClient/1.0 (Language=Python)"
    }
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        if not compression_algorithm:
            # encodings = ['latin-1', 'ISO-8859-1']  # 常见编码列表
            encodings = ['utf-8', 'latin-1', 'ISO-8859-1', 'GB18030']
            for encoding in encodings:
                try:
                    return response.content.decode(encoding)
                except UnicodeDecodeError:
                    continue
            raise UnicodeDecodeError("无法用已知编码解码响应内容")
        if compression_algorithm == 'GZIP':
            compressed_file = io.BytesIO(response.content)
            with gzip.GzipFile(fileobj=compressed_file, mode='rb') as file:
                data = file.read()
                encodings = ['utf-8', 'latin-1',
                             'ISO-8859-1', 'GB18030']  # 添加更多编码
                for encoding in encodings:
                    try:
                        return data.decode(encoding)
                    except UnicodeDecodeError:
                        continue
                raise UnicodeDecodeError("无法用已知编码解码 GZIP 压缩的数据")
        else:
            encodings = ['utf-8', 'latin-1', 'ISO-8859-1', 'GB18030']  # 添加更多编码
            for encoding in encodings:
                try:
                    return response.content.decode(encoding)
                except UnicodeDecodeError:
                    continue
            raise UnicodeDecodeError("无法用已知编码解码响应内容")
    else:
        raise Exception(
            f"下载报告失败，状态码: {response.status_code}，返回：{response.text}")


@session_middleware
@login_required_if_no_user_id
def create_report(request):
    page_title = "FBA库存健康度-创建报告"
    user_id = request.GET.get('user_id')
    site_id = int(request.GET.get('site_id'))
    url_key = request.GET.get('key')
    if not user_id:
        return HttpResponse("没有用户")
    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    try:
        # 使用上下文管理器来管理数据库连接
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
            user_data = get_user_info(db, user_id)
            refresh_token = user_data['refresh_token']
            config = common_config(refresh_token)
            api_client = SPAPIClient(config, "swagger_client_reports")
            reports_api = api_client.get_api_client('ReportsApi')
            report_type = "GET_FBA_INVENTORY_PLANNING_DATA"
            start_date = None
            end_date = None
            body = {
                "reportType": report_type,
                "dataStartTime": start_date,
                "dataEndTime": end_date,
                "marketplaceIds": ["ATVPDKIKX0DER"]
            }
            createReport = reports_api.create_report(body)
            report_id = createReport.report_id

            logger.info(f"用户{user_id}创建报告成功, report_id: {report_id}")

            create_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            sql = f"INSERT INTO fba_report (user_id,create_date, f, report_id, report_type) VALUES({user_id},'{create_date}', 0, '{report_id}', 'SellableDays')"
            new_id = db.insert(sql)

            sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
            user_rs = db.fetchone(sql)
            if not user_rs:
                next_user_id = None
            else:
                next_user_id = user_rs['id']

            # 拼接url_key
            url_key = f"{url_key}-{user_id}_{new_id}" if url_key else f"{user_id}_{new_id}"

            # MPR(1)、郑州(4)、武汉(6)
            site_ids_loop = [1, 4, 6]
            if site_id in site_ids_loop:
                if next_user_id:
                    second = random.randint(62, 68)
                    show_context = f"开始创建用户{next_user_id}的报告"
                    go_url = f"/reports/create_report_sellable_day/?user_id={next_user_id}&site_id={site_id}&key={url_key}"
                    context = {"show_context": show_context, "go_url": go_url,
                               "page_title": page_title, "second": second}
                    return render(request, "SPAPI_Python_SDK/goto.html", context)

            show_context = "全部创建成功, 开始获取报告"
            user_id = url_key.split('-')[0].split('_')[0]
            go_url = f"/reports/check_report_sellable_day/?user_id={user_id}&site_id={site_id}&key={url_key}"
            context = {"show_context": show_context,
                       "go_url": go_url, "page_title": page_title}
            return render(request, "SPAPI_Python_SDK/goto.html", context)
    except Exception as e:
        logging.error(f"生成报告出错: {e}")
        return HttpResponse(f"生成报告出错: {e}")


@login_required_if_no_user_id
def check_report(request):
    page_title = "FBA库存健康度-检查报告状态"
    user_id = request.GET.get('user_id')
    site_id = int(request.GET.get('site_id'))
    url_key = request.GET.get('key')
    array_ZR_id = parse_user_ids(url_key)

    if not user_id:
        return HttpResponse("没有user")

    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    # 使用上下文管理器来管理数据库连接
    try:
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
            sql = f"SELECT id,report_id FROM fba_report WHERE user_id={user_id} AND id={array_ZR_id[user_id]} AND report_id !='' AND report_document_id IS NULL AND f=0 AND report_type='SellableDays' ORDER BY id DESC LIMIT 1"
            row = db.fetchone(sql)
            if not row:
                site_ids_loop = [1, 4, 6]
                if site_id in site_ids_loop:
                    sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
                    user_rs = db.fetchone(sql)
                    if user_rs:
                        new_user_id = user_rs["id"]
                        show_context = f"开始获取下一用户的报告"
                        go_url = f"/reports/check_report_sellable_day/?user_id={new_user_id}&site_id={site_id}&key={url_key}"
                        context = {"show_context": show_context,
                                   "go_url": go_url, "page_title": page_title}
                        return render(request, "SPAPI_Python_SDK/goto.html", context)
                    else:
                        show_context = "全部创建完成"
                        context = {"show_context": show_context,
                                   "page_title": page_title}
                        return render(request, "SPAPI_Python_SDK/show.html", context)
                else:
                    show_context = "创建完成"
                    context = {"show_context": show_context,
                               "page_title": page_title}
                    return render(request, "SPAPI_Python_SDK/show.html", context)

            report_id = row['report_id']
            user_data = get_user_info(db, user_id)
            refresh_token = user_data['refresh_token']
            config = common_config(refresh_token)
            api_client = SPAPIClient(config, "swagger_client_reports")
            reports_api = api_client.get_api_client('ReportsApi')
            getreport = reports_api.get_report(report_id)
            response_dict = getreport.to_dict()

            status = response_dict.get("processing_status")
            report_document_id = response_dict.get("report_document_id")
            # 根据报告状态判断下一步
            if status == "DONE":
                update_sqls = f"UPDATE fba_report SET report_document_id='{report_document_id}' WHERE report_id='{report_id}'"
                db.update(update_sqls)

                show_context = "报告文档id更新完成，即将写入报告数据"
                go_url = f"/reports/get_report_sellable_day/?user_id={user_id}&site_id={site_id}&key={url_key}"
                context = {"show_context": show_context,
                           "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
            else:
                if status == "FATAL":
                    sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
                    db.delete(sql)

                    # 最好是做成直接跳转到下一个用户
                    go_url = f"/reports/check_report_sellable_day/?user_id={user_id}&site_id={site_id}&key={url_key}"
                    second = random.randint(5, 10)
                    show_context = f"报告因致命错误而停止,请等待4个小时后重测"
                    logger.info(
                        f"用户{user_id}{show_context}, report_id: {report_id}")

                elif status == "CANCELLED":
                    sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
                    db.delete(sql)

                    # 最好是做成直接跳转到下一个用户
                    go_url = f"/reports/check_report_sellable_day/?user_id={user_id}&site_id={site_id}&key={url_key}"
                    second = random.randint(5, 10)
                    show_context = f"亚马逊数据取消,请半个小时后重测"
                    logger.info(
                        f"用户{user_id}{show_context}, report_id: {report_id}")
                elif status == "IN_QUEUE":
                    go_url = f"/reports/check_report_sellable_day/?user_id={user_id}&site_id={site_id}&key={url_key}"
                    second = random.randint(20, 50)
                    show_context = f"{user_id} 报告正在处理"
                else:
                    go_url = f"/reports/check_report_sellable_day/?user_id={user_id}&site_id={site_id}&key={url_key}"
                    second = random.randint(100, 150)
                    show_context = "报告尚未开始处理"

                context = {"show_context": show_context, "go_url": go_url,
                           "page_title": page_title, "second": second}
                return render(request, "SPAPI_Python_SDK/goto.html", context)

    except Exception as e:
        logger.error(f"查询报告状态-参数错误: {str(e)}")
        return HttpResponse(f"查询报告状态-参数错误: {e}")


@login_required_if_no_user_id
def get_report(request):
    """
    获取报告数据
    """
    page_title = "FBA库存健康度-获取报告"
    user_id = request.GET.get('user_id')
    site_id = int(request.GET.get('site_id'))
    url_key = request.GET.get('key')
    array_ZR_id = parse_user_ids(url_key)
    if not user_id:
        return HttpResponse("没有user")

    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    # try:
    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
        sql = f"SELECT id, user_id,report_id, report_document_id FROM fba_report WHERE f=0 and user_id={user_id} AND id={array_ZR_id[user_id]} ORDER BY id desc LIMIT 1"
        rs = db.fetchone(sql)

        if not rs:
            show_context = "处理完成"
            if site_id in [1, 4, 6]:
                go_url = f"/reports/update_report_table_sellable_day/?user_id={user_id}&site_id={site_id}&key={url_key}"
            else:
                go_url = f"/reports/update_report_table_sellable_day/?user_id={user_id}&site_id={site_id}"
            context = {"show_context": show_context,
                       "go_url": go_url, "page_title": page_title}
            return render(request, "SPAPI_Python_SDK/goto.html", context)

        else:
            report_document_id = rs["report_document_id"]
            zom_report_id = rs["report_id"]
            user_id = rs["user_id"]
            user_data = get_user_info(db, user_id)
            refresh_token = user_data['refresh_token']
            config = common_config(refresh_token)
            api_client = SPAPIClient(config, "swagger_client_reports")
            reports_api = api_client.get_api_client('ReportsApi')
            getreportdocument = reports_api.get_report_document(
                report_document_id)

            document_data = getreportdocument.to_dict()
            report_document_id = document_data['report_document_id']
            compression_algorithm = document_data['compression_algorithm']

            report_url = document_data['url']

            try:
                uncompressed_data = download_report_data(
                    report_url, compression_algorithm)
            except Exception as e:
                error_message, response_text = e.args
                if "403" in error_message and "已过期" in response_text:
                    document_data = reports_api.get_report_document(
                        report_document_id)
                    report_url = document_data['url']
                    uncompressed_data = download_report_data(report_url)

            if uncompressed_data:
                data_file = io.StringIO(uncompressed_data)
                csv_reader = list(csv.DictReader(data_file, delimiter='\t'))
                total_rows = len(csv_reader)

            # sql_update = """UPDATE fba_sku_status SET days_supply = %s, Total_days_supply = %s WHERE sku = %s"""
            sql_update = """UPDATE fba_sku_status SET snapshot_date = %s, total_quantity = %s, sellable_quantity = %s, unsellable_quantity=%s, shipped_7=%s, shipped_30=%s, shipped_90=%s, in_bound_quantity=%s, Reserved_quantity=%s, Receiving_quantity=%s, inbound_working=%s, inbound_shipped=%s, days_supply=%s, Total_days_supply=%s, estimated_excess=%s, Recommended_quantity=%s WHERE sku = %s"""

            sql_insert_record = "INSERT INTO fba_sales_records (snapshot_date, sku, shipped_7, shipped_30, shipped_90) VALUES (%s, %s, %s, %s, %s)"

            if total_rows <= 100:
                # 单条模式（适合小数据量）
                for cell in csv_reader:
                    if valid_sku_data(cell):  # 数据校验函数
                        params = prepare_params(cell)
                        db.execute(sql_update, params)

                        db.execute(
                            """INSERT INTO fba_sales_records
                                     (snapshot_date, sku, shipped_7, shipped_30, shipped_90)
                               VALUES (%s, %s, %s, %s, %s)
                            ON DUPLICATE KEY UPDATE               -- 已有就覆盖，防止脚本重跑冲突
                                shipped_7  = VALUES(shipped_7),
                                shipped_30 = VALUES(shipped_30),
                                shipped_90 = VALUES(shipped_90)""",
                            (params[0], params[16],
                             params[4], params[5], params[6])
                        )

            else:
                # 批量模式（带动态提交）
                batch_size = 50 if 100 < total_rows <= 1000 else 100
                params_buffer = []
                record_buf = []
                for idx, cell in enumerate(csv_reader, 1):
                    params_status = prepare_params(cell)

                    if valid_sku_data(cell):
                        params_buffer.append(params_status)

                    record_buf.append(
                        (params_status[0], params_status[16], params_status[4], params_status[5], params_status[6]))

                    # 提交条件：达到批次或最后一条
                    if idx % batch_size == 0 or idx == total_rows:
                        db.bulk_update(sql_update, params_buffer)
                        params_buffer = []  # 清空缓冲区

                        db.bulk_insert(sql_insert_record, record_buf)
                        record_buf = []
            get_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            sql = f"UPDATE fba_report SET f=1, get_date='{get_date}' WHERE report_id={zom_report_id}"
            db.update(sql)
        show_context = f"{user_id}报告数据抓取完成"
        go_url = f"/reports/update_report_table_sellable_day/?user_id={user_id}&site_id={site_id}&key={url_key}"
        context = {"show_context": show_context,
                   "go_url": go_url, "page_title": "获取报告"}
        return render(request, "SPAPI_Python_SDK/goto.html", context)


def valid_sku_data(cell):
    """数据有效性校验"""
    return all([
        cell.get('sku'),
        cell.get('days-of-supply', '') not in ['', '0'],
        cell.get(
            'Total Days of Supply (including units from open shipments)', '') != ''
    ])


def prepare_params(cell):
    """参数预处理"""
    return (
        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        int(float(cell.get('Inventory Supply at FBA', '0') or '0')),
        int(float(cell.get('available', '0') or '0')),
        int(float(cell.get('unfulfillable-quantity', '0') or '0')),
        int(float(cell.get('units-shipped-t7', '0') or '0')),
        int(float(cell.get('units-shipped-t30', '0') or '0')),  # 双重保险处理空值
        int(float(cell.get('units-shipped-t90', '0') or '0')),  # 双重保险处理空值
        int(float(cell.get('inbound-quantity', 0) or '0')),
        int(float(cell.get('Total Reserved Quantity', 0) or '0')),
        int(float(cell.get('inbound-received', 0) or '0')),
        int(float(cell.get('inbound-working', 0) or '0')),
        int(float(cell.get('inbound-shipped', 0) or '0')),
        int(float(cell.get('days-of-supply', 0) or '0')),
        int(float(cell.get(
            'Total Days of Supply (including units from open shipments)', 0) or '0')),
        int(float(cell.get('estimated-excess-quantity', 0) or '0')),
        int(float(cell.get('Recommended ship-in quantity', 0) or '0')),
        cell.get('sku')  # 保持必要字段不设默认值
    )


@login_required_if_no_user_id
def update_report_table(request):
    page_title = "FBA库存健康度-更新报告表"
    user_id = request.GET.get('user_id')
    site_id = int(request.GET.get('site_id'))
    url_key = request.GET.get('key')
    array_ZR_id = parse_user_ids(url_key) if url_key else {}
    if not user_id:
        return HttpResponse("没有user")

    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
        sql = f"SELECT report_document_id FROM fba_report WHERE user_id={user_id} and f=1 AND id={array_ZR_id[user_id]} ORDER BY id ASC"
        rs = db.fetchall(sql)
        for row in rs:
            report_document_id = row['report_document_id']
            sql = f"DELETE FROM fba_report WHERE report_document_id = '{report_document_id}'"
        # db.delete(sql)

        site_ids_loop = [1, 4, 6]
        if site_id in site_ids_loop:
            sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
            user_rs = db.fetchone(sql)
            if not user_rs:
                show_context = "FAB库存健康度报告 - 全部获取完成"
                context = {"show_context": show_context, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/show.html", context)

            if user_rs:
                user_id = user_rs["id"]
                show_context = "即将查询下一个用户"
                go_url = f"/reports/check_report_sellable_day/?user_id={user_id}&site_id={site_id}&key={url_key}"
                context = {"show_context": show_context,
                           "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
            else:
                show_context = "FAB库存健康度报告 - 全部更新完成"
                context = {"show_context": show_context,
                           "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/show.html", context)
        else:
            show_context = "FAB库存健康度报告 - 全部更新完成"
            context = {"show_context": show_context, "page_title": page_title}
            return render(request, "SPAPI_Python_SDK/show.html", context)
