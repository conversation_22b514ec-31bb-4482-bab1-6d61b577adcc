import logging
import random
import csv
import io

from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from datetime import datetime
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.login_user_id import session_middleware, login_required_if_no_user_id
from .report import (
    ReportManager, parse_url_key, download_report_data, get_first_user_id,
    safe_create_report, _check_report_core, _get_report_core
)

SITE_LOOP = {1, 4, 6}


@session_middleware
@login_required_if_no_user_id
def generate_reports(request):  # 所有用户生成报告
    page_title = "FBA库存-创建报告"
    user_id = request.GET.get("user_id")
    site_id = int(request.GET["site_id"])
    url_key = request.GET.get("key", "")
    if not user_id:
        return HttpResponse("没有用户")

    db = Database(**DB_DATA[site_id])
    with db:
        api = ReportManager(user_id, db)
        rid = safe_create_report(api, report_type="GET_FBA_MYI_UNSUPPRESSED_INVENTORY_DATA").report_id
        new_id = api.save_report_id(rid, "Inventory")
        next_uid = api.next_user()

    url_key = f"{url_key}-{user_id}_{new_id}" if url_key else f"{user_id}_{new_id}"
    if next_uid and site_id in SITE_LOOP:
        return render(request, "goto.html", {
            "show_context": f"开始创建用户{next_uid}的报告",
            "go_url": f"/reports/generate_reports_inventory/?user_id={next_uid}&site_id={site_id}&key={url_key}",
            "page_title": page_title
        })
    user_id = int(get_first_user_id(url_key))
    return render(request, "goto.html", {
        "show_context": "全部创建成功, 开始获取报告",
        "go_url": f"/reports/check_report_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}"
    })


def check_report(request):  # 检查报告状态
    page_title = "FBA库存-检查报告状态"
    user_id = request.GET.get("user_id")  # 当前要检查的 user
    site_id = int(request.GET["site_id"])
    url_key = request.GET.get("key", "")
    array_ZR_id = parse_url_key(url_key) if url_key else {}

    if not user_id:
        return HttpResponse("没有 user")

    dbdata = DB_DATA.get(site_id)
    if not dbdata:
        return HttpResponse("无效的 site_id")

    try:
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
            sql = (
                "SELECT id, report_id FROM fba_report "
                "WHERE user_id=%s AND id=%s AND report_id!='' AND f=1 AND report_type='Inventory' "
                "ORDER BY id DESC LIMIT 1"
            )
            row = db.fetchone(sql, (user_id, array_ZR_id.get(user_id)))

            # ---------- 没有待查询的 report ----------
            if not row:
                if site_id in SITE_LOOP:
                    next_row = db.fetchone(
                        "SELECT id FROM user WHERE MWS_Switch=1 AND id>%s AND Father_id=0 ORDER BY id ASC LIMIT 1",
                        (user_id,),
                    )
                    if next_row:
                        new_uid = next_row["id"]
                        return render(
                            request,
                            "SPAPI_Python_SDK/goto.html",
                            {
                                "show_context": "开始获取下一用户的报告",
                                "go_url": f"/reports/check_report_inventory/?user_id={new_uid}&site_id={site_id}&key={url_key}",
                                "page_title": page_title,
                            },
                        )
                return HttpResponse("全部创建完成")

            # ---------- 有 report，检查状态 ----------
            report_id = row["report_id"]
            api_client = ReportManager(user_id, db)

            rsp_dict = _check_report_core(api_client, report_id).to_dict()
            status = rsp_dict.get("processing_status")
            created_time = rsp_dict.get("created_time")
            report_document_id = rsp_dict.get("report_document_id")

            if status == "DONE":
                db.update(
                    "UPDATE fba_report SET report_document_id=%s, create_date=%s "
                    "WHERE report_id=%s", (report_document_id, created_time, report_id),
                )
                return render(
                    request,
                    "SPAPI_Python_SDK/goto.html",
                    {
                        "show_context": "查询成功，即将抓取",
                        "go_url": f"/reports/get_report_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}",
                        "page_title": page_title,
                    },
                )

            # ---------- 其余状态 ----------
            if status in ("FATAL", "CANCELLED"):
                db.delete("DELETE FROM fba_report WHERE report_id=%s", (report_id,))
                second = random.randint(5, 10)
                show_context = f"{user_id} 报告{status.lower()}，已删除重新排队"
            elif status == "IN_QUEUE":
                second = random.randint(10, 50)
                show_context = f"{user_id} 报告正在处理"
            else:
                second = random.randint(100, 200)
                show_context = "报告尚未开始处理"

            return render(
                request,
                "SPAPI_Python_SDK/goto.html",
                {
                    "show_context": show_context,
                    "second": second,
                    "go_url": f"/reports/check_report_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}",
                    "page_title": page_title,
                },
            )

    except Exception as exc:
        logging.error(f"查询报告状态异常: {exc}")
        return HttpResponse(f"查询报告状态异常: {exc}")


# ---------------- 获取报告数据 ----------------
def get_report(request):
    page_title = "FBA库存-获取报告"
    user_id = request.GET.get("user_id")
    site_id = int(request.GET["site_id"])
    url_key = request.GET.get("key", "")
    array_ZR_id = parse_url_key(url_key)

    if not user_id:
        return HttpResponse("没有 user")

    dbdata = DB_DATA.get(site_id)
    if not dbdata:
        return HttpResponse("无效的 site_id")

    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
        rs = db.fetchone(
            "SELECT id, user_id, report_id, report_document_id FROM fba_report WHERE f=1 AND user_id=%s AND id=%s ORDER BY id DESC LIMIT 1",
            (user_id, array_ZR_id.get(user_id)),
        )

        # ---------- 该 user 没有待抓取文档 ----------
        if not rs:
            next_url = f"/reports/update_report_table_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}"
            return render(
                request,
                "SPAPI_Python_SDK/goto.html",
                {"show_context": "处理完成", "go_url": next_url, "page_title": page_title, },
            )

        try:
            rpt_doc_id = rs["report_document_id"]
            rpt_id = rs["report_id"]
            api_client = ReportManager(user_id, db)

            doc_meta = _get_report_core(api_client, rpt_doc_id).to_dict()
            report_url = doc_meta["url"]
            algorithm = doc_meta.get("compression_algorithm")

            try:
                raw = download_report_data(report_url, algorithm)
            except Exception as e:
                err, txt = e.args
                if "403" in err and "已过期" in txt:
                    doc_meta = api_client.getReportUrl(rpt_doc_id)
                    raw = download_report_data(doc_meta["url"], algorithm)
                else:
                    raise
            # ---------- 写入 / 更新库存表 ----------
            update_params, get_date = [], datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            csv_reader = csv.DictReader(io.StringIO(raw), delimiter="\t")

            for row in csv_reader:
                sku = row["sku"]
                vals = (
                    row["afn-total-quantity"],
                    row["afn-fulfillable-quantity"],
                    row["afn-unsellable-quantity"],
                    row["afn-inbound-shipped-quantity"],
                    row["afn-reserved-quantity"],
                    get_date,
                    row["afn-inbound-receiving-quantity"],
                    row["afn-future-supply-buyable"],
                    sku,
                )
                exists = db.fetchone("SELECT id FROM fba_sku_status WHERE sku=%s", (sku,))
                if exists:
                    update_params.append(vals)
                elif site_id in SITE_LOOP:
                    u_row = db.fetchone("SELECT user_id FROM sku_pro WHERE sku=%s LIMIT 1", (sku,))
                    if u_row:
                        db.insert(
                            "INSERT INTO fba_sku_status "
                            "(user_id, sku, total_quantity, sellable_quantity, unsellable_quantity, shipped_7, shipped_30, shipped_90, shipped_180, shipped_365, in_bound_quantity, Reserved_quantity, Stock_up_date, Receiving_quantity, Future_quantity) "
                            "VALUES (%s,%s,%s,%s,%s,0,0,0,0,0,%s,%s,%s,%s,%s)",
                            (
                                u_row["user_id"],
                                sku,
                                row["afn-total-quantity"],
                                row["afn-fulfillable-quantity"],
                                row["afn-unsellable-quantity"],
                                row["afn-inbound-shipped-quantity"],
                                row["afn-reserved-quantity"],
                                get_date,
                                row["afn-inbound-receiving-quantity"],
                                row["afn-future-supply-buyable"],
                            ),
                        )

            if update_params:
                db.bulk_update(
                    "UPDATE fba_sku_status SET total_quantity=%s, sellable_quantity=%s, unsellable_quantity=%s, in_bound_quantity=%s, Reserved_quantity=%s, Stock_up_date=%s, Receiving_quantity=%s, Future_quantity=%s "
                    "WHERE sku=%s", update_params,
                )

            # 标记 f=1
            db.update("UPDATE fba_report SET f=1, get_date=%s WHERE report_id=%s", (get_date, rpt_id))

        except Exception:
            raise  # 触发 retry 机制（外层装饰器）

        return render(
            request,
            "SPAPI_Python_SDK/goto.html",
            {
                "show_context": f"{user_id} 报告数据抓取完成",
                "go_url": f"/reports/update_report_table_inventory/?user_id={user_id}&site_id={site_id}&key={url_key}",
                "page_title": page_title,
            },
        )


# ---------------- 更新报告表 & 继续循环 ----------------

def update_report_table(request):
    page_title = "FBA库存-更新报告表"

    user_id = request.GET.get("user_id")
    site_id = int(request.GET["site_id"])
    url_key = request.GET.get("key", "")
    array_ZR_id = parse_url_key(url_key) if url_key else {}

    if not user_id:
        return HttpResponse("没有 user")

    dbdata = DB_DATA.get(site_id)
    if not dbdata:
        return HttpResponse("无效的 site_id")

    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
        docs = db.fetchall(
            "SELECT report_document_id FROM fba_report WHERE user_id=%s AND f=1 AND id=%s ORDER BY id ASC LIMIT 100",
            (user_id, array_ZR_id.get(user_id)),
        )

        # 清理已处理的文档
        # for d in docs:
        # db.delete("DELETE FROM fba_report WHERE report_document_id=%s", (d["report_document_id"],))

        # 是否还有下一个 user
        if site_id in SITE_LOOP:
            nxt = db.fetchone(
                "SELECT id FROM user WHERE MWS_Switch=1 AND id>%s AND Father_id=0 ORDER BY id ASC LIMIT 1",
                (user_id,),
            )
            if nxt:
                return render(
                    request,
                    "SPAPI_Python_SDK/goto.html",
                    {
                        "show_context": "即将查询下一个用户",
                        "go_url": f"/reports/check_report_inventory/?user_id={nxt['id']}&site_id={site_id}&key={url_key}",
                        "page_title": page_title,
                    },
                )

        # 完成
        return render(
            request,
            "SPAPI_Python_SDK/show.html",
            {"show_context": "全部更新完成", "page_title": page_title},
        )
