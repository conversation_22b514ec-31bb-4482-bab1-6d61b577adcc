from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import site_query, get_user_id
from .report import ReportManager, parse_url_key, download_report_data, get_time_range
from datetime import datetime
import logging
import random
import csv
import io


async def generate_reports(request):
	page_title = "创建报告"
	user_id = request.GET.get('user_id')

	return_url = request.GET.get('self', "")
	url_key = request.GET.get('key')
	if not return_url:
		return HttpResponse("没有来源网址")

	site_id = site_query(request, return_url)  # 获取网站ID
	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")
	if user_id is None:
		user_id = get_user_id(request, site_id)

	time_range = get_time_range()
	start_date = time_range['s']
	end_date = time_range['e']

	# 使用上下文管理器来管理数据库连接
	with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
		api_client = ReportManager(user_id, db)  # 报告API
		# 创建报告
		report_type = "GET_FBA_ESTIMATED_FBA_FEES_TXT_DATA"

		create_report_response = api_client.createReport(report_type, start_date, end_date)
		report_id = create_report_response.report_id
		new_id = api_client.save_report_id(report_id, 'fee')
		new_user_id = api_client.next_user()

		# 拼接url_key
		url_key = f"{url_key}-{user_id}_{new_id}" if url_key else f"{user_id}_{new_id}"

		# MPR(1)、郑州(4)、武汉(6)
		site_ids_loop = [1, 4, 6]
		if site_id in site_ids_loop:
			if new_user_id:  # 有下一个用户
				show_context = f"开始创建用户{new_user_id}的报告"
				go_url = f"/reports/generate_reports_fee_all/?user_id={new_user_id}&self={return_url}&key={url_key}"
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)

		# 没有下一个用户或不是MPR，郑州，武汉表示全部建仓成功
		show_context = "全部创建成功, 开始获取报告"
		user_id = url_key.split('-')[0].split('_')[0]
		go_url = f"/reports/check_report_fee_all/?user_id={user_id}&self={return_url}&key={url_key}"
		context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
		return render(request, "SPAPI_Python_SDK/goto.html", context)


def check_report(request):
	"""
	检查报告状态
	:param request:
	:return:
	"""
	page_title = "检查报告状态"
	user_id = request.GET.get('user_id')
	return_url = request.GET.get('self', "")
	url_key = request.GET.get('key')
	array_ZR_id = parse_url_key(url_key) if url_key else {}

	if not user_id:
		return HttpResponse("没有user")
	if not return_url:
		return HttpResponse("没有来源网址")

	site_id = site_query(request, return_url)  # 网站ID
	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")

	# 使用上下文管理器来管理数据库连接
	try:
		with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
			sql = f"SELECT id,report_id FROM fba_report WHERE user_id={user_id} AND id={array_ZR_id[user_id]} AND report_id!='' AND report_document_id IS NULL AND f=0 AND report_type='fee' ORDER BY id DESC LIMIT 1"
			row = db.fetchone(sql)

			# 判断有没有下一个用户
			if not row:
				site_ids_loop = [1, 4, 6]
				if site_id in site_ids_loop:
					sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
					user_rs = db.fetchone(sql);
					if user_rs:
						new_user_id = user_rs["id"]
						show_context = f"开始获取下一用户的报告"
						go_url = f"/reports/check_report_fee_all/?user_id={new_user_id}&self={return_url}&key={url_key}"
						context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
						return render(request, "SPAPI_Python_SDK/goto.html", context)
					else:
						return HttpResponse("更新完成")
				else:
					return HttpResponse("更新完成")

			report_id = row['report_id']
			api_client = ReportManager(user_id, db)
			# 检查报告状态
			response = api_client.checkReport(report_id)
			response_dict = response.to_dict()

			status = response_dict.get("processing_status")
			created_time = response_dict.get("created_time")
			report_document_id = response_dict.get("report_document_id")

			# 根据报告状态判断下一步
			if status == "DONE":
				update_sqls = f"UPDATE fba_report SET report_document_id='{report_document_id}', create_date='{created_time}' WHERE report_id='{report_id}'";
				db.update(update_sqls)

				show_context = "报告文档id更新完成，即将写入报告数据"
				go_url = f"/reports/get_report_fee_all/?user_id={user_id}&self={return_url}&key={url_key}";
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)
			else:
				if status == "FATAL":
					sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
					db.delete(sql)
					return HttpResponse("报告因致命错误而停止,请等待4个小时后重测")
				elif status == "CANCELLED":
					sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
					db.delete(sql)
					return HttpResponse("亚马逊数据取消,请半个小时后重测")
				elif status == "IN_QUEUE":
					go_url = f"/reports/check_report_fee_all/?user_id={user_id}&self={return_url}&key={url_key}"
					second = random.randint(10, 50)
					show_context = f"{user_id} 报告正在处理"
				else:
					go_url = f"/reports/check_report_fee_all/?user_id={user_id}&self={return_url}&key={url_key}"
					second = random.randint(100, 500)
					show_context = "报告尚未开始处理"

				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": second}
				return render(request, "SPAPI_Python_SDK/goto.html", context)

	except Exception as e:
		logging.error(f"错误检查报告: {e}")
		return HttpResponse(f"错误检查报告: {e}")


def get_report(request):
	"""
	获取报告数据
	"""
	page_title = "获取报告"
	user_id = request.GET.get('user_id')
	return_url = request.GET.get('self', "")
	url_key = request.GET.get('key')
	array_ZR_id = parse_url_key(url_key)
	if not user_id:
		return HttpResponse("没有user")
	if not return_url:
		return HttpResponse("没有来源网址")

	site_id = site_query(request, return_url)  # 网站ID
	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")
	try:
		with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
			sql = f"SELECT id, user_id,report_id, report_document_id FROM fba_report WHERE f=0 and user_id={user_id} AND id={array_ZR_id[user_id]} ORDER BY id desc LIMIT 1"
			rs = db.fetchone(sql)

			if not rs:
				show_context = "处理完成"
				if site_id in [1, 4, 6]:
					go_url = f"/reports/update_report_table_fee_all/?user_id={user_id}&self={return_url}&key={url_key}"
				else:
					go_url = f"/reports/update_report_table_fee_all/?user_id={user_id}&self={return_url}&key={url_key}"
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)
			else:
				report_document_id = rs["report_document_id"]
				zom_report_id = rs["report_id"]
				user_id = rs["user_id"]
				api_client = ReportManager(user_id, db)

				DocementResponses = api_client.getReportUrl(report_document_id)
				document_data = DocementResponses.to_dict()

				report_document_id = document_data['report_document_id']
				compression_algorithm = document_data.get('compression_algorithm', None)

				try:
					report_url = document_data['url']
					uncompressed_data = download_report_data(report_url, compression_algorithm)
				except Exception as e:
					# 处理异常参数解包，兼容单参数和双参数情况
					if len(e.args) >= 2:
						error_message, response_text = e.args[0], e.args[1]
					elif len(e.args) == 1:
						error_message = str(e.args[0])
						response_text = ""
					else:
						error_message = str(e)
						response_text = ""

					if "403" in error_message and "已过期" in response_text:
						document_data = api_client.getReportUrl(report_document_id)
						report_url = document_data['url']
						uncompressed_data = download_report_data(report_url)

				if uncompressed_data:
					data_file_like = io.StringIO(uncompressed_data)
					csv_reader = csv.DictReader(data_file_like, delimiter='\t')

					get_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
					update_params = []
					sql_update = None
					# 遍历每个报告数据
					for cell in csv_reader:
						amazon_store = cell['amazon-store']
						if amazon_store == 'US':
							sku = cell['sku']
							fba_money = cell['sales-price']
							Amazon_charge = cell['estimated-fee-total']
							FBA_fee = cell['expected-fulfillment-fee-per-unit']

							update_params.append((fba_money, Amazon_charge, FBA_fee, sku))
							if sql_update is None:
								sql_update = "UPDATE sku_pro SET fba_money = %s, Amazon_charge = %s, FBA_fee = %s WHERE sku_pro.sku = %s"

					# 这里如果 sql_update 还是 None 说明根本没有 US 行，就可以跳过执行
					if sql_update and update_params:
						try:
							db.bulk_update(sql_update, update_params)
						except Exception as e:
							return HttpResponse(f"更新失败，原因：{str(e)}")

					# 		constructed_sql = sql_update
					# 		for p in param_list:
					# 			constructed_sql = constructed_sql.replace('%s', repr(p), 1)
					# 		result_queries.append(constructed_sql)
					#
					# final_sql_list = "<br>".join(result_queries)
					# return HttpResponse(final_sql_list)

					# 更新报告状态
					sql = f"UPDATE fba_report SET f=1, get_date='{get_date}' WHERE report_id={zom_report_id}"
					db.update(sql)

				show_context = f"{user_id}报告数据抓取完成"
				go_url = f"/reports/update_report_table_fee_all/?user_id={user_id}&self={return_url}&key={url_key}&t=1"
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)

	except Exception as e:
		logging.error(f"Error getting report: {e}")
		return HttpResponse(f"Error getting report: {e}")


def update_report_table(request):
	page_title = "更新报告表"
	user_id = request.GET.get('user_id')
	return_url = request.GET.get('self', "")
	url_key = request.GET.get('key')
	array_ZR_id = parse_url_key(url_key) if url_key else {}
	if not user_id:
		return HttpResponse("没有user")
	if not return_url:
		return HttpResponse("没有来源网址")

	site_id = site_query(request, return_url)  # 网站ID
	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")

	with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
		sql = f"SELECT report_document_id FROM fba_report WHERE user_id={user_id} and f=1 AND id={array_ZR_id[user_id]} ORDER BY id ASC"

		rs = db.fetchall(sql)
		for row in rs:
			report_document_id = row['report_document_id']

		# 检查是否有下一个用户
		site_ids_loop = [1, 4, 6]
		if site_id in site_ids_loop:
			sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
			user_rs = db.fetchone(sql)
			if not user_rs:
				sql = f"DELETE FROM fba_report WHERE report_document_id = '{report_document_id}'"
				db.delete(sql)
				show_context = "全部获取完成"
				return HttpResponse(show_context)
			if user_rs:
				user_id = user_rs["id"]
				show_context = "即将查询下一个用户"
				go_url = f"/reports/check_report_fee_all/?user_id={user_id}&self={return_url}&key={url_key}"
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)
			else:
				return HttpResponse("全部更新完成")
		else:
			return HttpResponse("全部更新完成")
