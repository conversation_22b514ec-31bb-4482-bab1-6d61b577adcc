import logging
import json
import csv
import io
import random
from datetime import datetime
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render

from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import site_query
from .report import ReportManager, parse_url_key, download_report_data


def create_report(request):
    page_title = "创建报告"
    user_id = request.GET.get('user_id')
    if not user_id:
        return HttpResponse("请返回选择用户")
    return_url = request.GET.get('self', "")
    if not return_url:
        return HttpResponse("没有来源网址")
    site_id = site_query(request, return_url)  # 获取网站ID

    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    try:
        # 使用上下文管理器来管理数据库连接
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
            api_client = ReportManager(user_id, db)  # 报告API
            # 创建报告
            report_type = "GET_FBA_INVENTORY_PLANNING_DATA"
            create_report_response = api_client.createReport(report_type)
            report_id = create_report_response.report_id
            new_id = api_client.save_report_id(report_id, 'SellableDays')

            # url_key = f"{user_id}_{new_id}"
            show_context = "创建成功, 开始查询报告状态"
            go_url = f"/reports/check_report_sellable_day/?user_id={user_id}&self={return_url}&id={new_id}"
            second = 45
            context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": second}
            return render(request, "SPAPI_Python_SDK/goto.html", context)

    except Exception as e:
        logging.error(f"生成报告出错: {e}")
        return HttpResponse(f"生成报告出错: {e}")


def check_report(request):
    page_title = "检查报告状态"
    user_id = request.GET.get('user_id')
    return_url = request.GET.get('self', "")
    id = request.GET.get('id')
    # array_ZR_id = parse_url_key(url_key) if url_key else {}

    if not user_id:
        return HttpResponse("没有user")
    if not return_url:
        return HttpResponse("没有来源网址")

    site_id = site_query(request, return_url)  # 网站ID
    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    # 使用上下文管理器来管理数据库连接
    try:
        with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
            sql = f"SELECT id,report_id FROM fba_report WHERE user_id={user_id} AND id={id} AND report_id!='' AND report_document_id IS NULL AND f=0 AND report_type='SellableDays' ORDER BY id DESC LIMIT 1"
            row = db.fetchone(sql)
            if not row:
                return HttpResponse("创建完成")

            report_id = row['report_id']
            api_client = ReportManager(user_id, db)
            # 检查报告状态
            response = api_client.checkReport(report_id)
            response_dict = response.to_dict()

            status = response_dict.get("processing_status")
            created_time = response_dict.get("created_time")
            report_document_id = response_dict.get("report_document_id")
            # 根据报告状态判断下一步
            if status == "DONE":
                update_sqls = f"UPDATE fba_report SET report_document_id='{report_document_id}', create_date='{created_time}' WHERE report_id='{report_id}'"
                db.update(update_sqls)

                show_context = "报告文档id更新完成，即将写入报告数据"
                go_url = f"/reports/get_report_sellable_day/?user_id={user_id}&self={return_url}&id={id}"
                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
                return render(request, "SPAPI_Python_SDK/goto.html", context)
            else:
                if status == "FATAL":
                    sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
                    db.delete(sql)
                    return HttpResponse("报告因致命错误而停止,请等待4个小时后重测")
                elif status == "CANCELLED":
                    sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
                    db.delete(sql)
                    return HttpResponse("亚马逊数据取消,请半个小时后重测")
                elif status == "IN_QUEUE":
                    go_url = f"/reports/check_report_sellable_day/?user_id={user_id}&self={return_url}&id={id}"
                    second = random.randint(20, 50)
                    show_context = f"{user_id} 报告正在处理"
                else:
                    go_url = f"/reports/check_report_sellable_day/?user_id={user_id}&self={return_url}&id={id}"
                    second = random.randint(100, 150)
                    show_context = "报告尚未开始处理"

                context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": second}
                return render(request, "SPAPI_Python_SDK/goto.html", context)

    except Exception as e:
        logging.error(f"Error checking report: {e}")
        return HttpResponse(f"Error checking report: {e}")


def get_report(request):
    """
    获取报告数据
    """
    user_id = request.GET.get('user_id')
    return_url = request.GET.get('self', "")
    url_key = request.GET.get('key')
    id = request.GET.get('id')
    # array_ZR_id = parse_url_key(url_key)
    if not user_id:
        return HttpResponse("没有user")
    if not return_url:
        return HttpResponse("没有来源网址")

    site_id = site_query(request, return_url)  # 网站ID

    dbdata = DB_DATA[site_id]

    # try:
    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
        sql = f"SELECT id, user_id,report_id, report_document_id FROM fba_report WHERE f=0 and user_id={user_id} AND id={id} ORDER BY id desc LIMIT 1"
        rs = db.fetchone(sql)

        if not rs:
            show_context = "处理完成"
            go_url = f"/reports/update_report_table_sellable_day/?user_id={user_id}&self={return_url}&id={id}"
            context = {"show_context": show_context, "go_url": go_url, "page_title": "获取报告"}
            return render(request, "SPAPI_Python_SDK/goto.html", context)

        report_document_id = rs["report_document_id"]
        zom_report_id = rs["report_id"]
        user_id = rs["user_id"]

        api_client = ReportManager(user_id, db)
        document_data = api_client.getReportUrl(report_document_id).to_dict()
        compression_algorithm = document_data.get('compression_algorithm', None)
        report_url = document_data['url']

        try:
            uncompressed_data = download_report_data(report_url, compression_algorithm)
        except Exception as e:
            error_message, response_text = e.args
            if "403" in error_message and "已过期" in response_text:
                document_data = api_client.getReportUrl(report_document_id)
                report_url = document_data['url']
                uncompressed_data = download_report_data(report_url)

        if uncompressed_data:
            data_file = io.StringIO(uncompressed_data)
            csv_reader = list(csv.DictReader(data_file, delimiter='\t'))
            total_rows = len(csv_reader)

        sql_update = """UPDATE fba_sku_status SET days_supply = %s, Total_days_supply = %s WHERE sku = %s"""

        if total_rows <= 100:
            # 单条模式（适合小数据量）
            for cell in csv_reader:
                if valid_sku_data(cell):  # 数据校验函数
                    params = prepare_params(cell)
                    db.execute(sql_update, params)
        else:
            # 批量模式（带动态提交）
            batch_size = 50 if 100 < total_rows <= 1000 else 300
            params_buffer = []

            for idx, cell in enumerate(csv_reader, 1):
                if valid_sku_data(cell):
                    params_buffer.append(prepare_params(cell))

                # 提交条件：达到批次或最后一条
                if idx % batch_size == 0 or idx == total_rows:
                    db.bulk_update(sql_update, params_buffer)
                    params_buffer.clear()

    show_context = f"{user_id}报告数据抓取完成"
    go_url = f"/reports/update_report_table_sellable_day/?user_id={user_id}&self={return_url}&id={id}"
    context = {"show_context": show_context, "go_url": go_url, "page_title": "获取报告"}
    return render(request, "SPAPI_Python_SDK/goto.html", context)


def valid_sku_data(cell):
    """数据有效性校验"""
    return all([
        cell.get('sku'),
        cell.get('days-of-supply', '') not in ['', '0'],
        cell.get('Total Days of Supply (including units from open shipments)', '') != ''
    ])


def prepare_params(cell):
    """参数预处理"""
    return (
        int(float(cell['days-of-supply'])),
        int(float(cell['Total Days of Supply (including units from open shipments)'])),
        cell['sku']
    )


def update_report_table(request):
    id = request.GET.get('id')
    user_id = request.GET.get('user_id')
    return_url = request.GET.get('self', "")
    if not user_id:
        return HttpResponse("没有user")
    if not return_url:
        return HttpResponse("没有来源网址")

    site_id = site_query(request, return_url)  # 网站ID
    dbdata = DB_DATA[site_id]
    if not dbdata:
        return HttpResponse("无效的 site_id")

    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
        sql = f"SELECT report_document_id FROM fba_report WHERE user_id={user_id} and f=1 AND id={id} ORDER BY id ASC"
        rs = db.fetchall(sql)
        if not rs:
            return HttpResponse("无需要清理的报告记录")
        report_document_id = rs[0]['report_document_id']

        # sql = f"DELETE FROM fba_report WHERE report_document_id = '{report_document_id}'"
        # db.delete(sql)
        return HttpResponse("更新完成")
