import json
import logging
import random

from datetime import datetime
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render

from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import site_query
from .report import ReportManager, parse_url_key, download_report_data, convert_to_beijing_time, convert_to_utc_time
from SPAPI_Python_SDK.login_user_id import get_ip_address, GLOBAL_IP_AUTO
from django.views import View
from time import sleep
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
import csv
import io
import re
import pytz


def addslashes(s):
	return re.sub(r"(['\"\\])", r"\\\1", s)


async def generate_reports(request):
	ip = get_ip_address(request)
	if ip != GLOBAL_IP_AUTO:
		return HttpResponse("未知的IP")

	"""
	为所有用户生成报告
	:param request:
	:return:
	"""
	page_title = "创建报告"
	user_id = request.GET.get('user_id')
	return_url = request.GET.get('self', "")
	url_key = request.GET.get('key')

	if not user_id:
		return HttpResponse("没有用户")
	if not return_url:
		return HttpResponse("没有来源网址")

	site_id = site_query(request, return_url)  # 获取网站ID

	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")

	try:
		# 使用上下文管理器来管理数据库连接
		with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
			sql = f"SELECT id, Shipment_date FROM Sales_FBA_Complete_order WHERE user_id = {user_id} ORDER BY Shipment_date DESC LIMIT 1"
			shipment_data = db.fetchone(sql)  # 最后一次插入数据的时间

			if not shipment_data:
				get_start_date = (datetime.now() - timedelta(days=10)).strftime("%Y-%m-%dT%H:%M:%S")
			else:
				# 将 datetime 对象转换为 ISO 格式字符串
				get_start_date = (shipment_data['Shipment_date'] + timedelta(seconds=1)).isoformat()

			get_end_date = (datetime.now() - timedelta(minutes=2)).strftime("%Y-%m-%dT%H:%M:%S")

			start_date = convert_to_utc_time(get_start_date)
			end_date = convert_to_utc_time(get_end_date)

			api_client = ReportManager(user_id, db)  # 报告API
			# 创建报告
			report_type = "GET_FBA_FULFILLMENT_CUSTOMER_SHIPMENT_SALES_DATA"
			create_report_response = api_client.createReport(report_type, start_date, end_date)
			report_id = create_report_response.report_id

			# 保存report_id
			new_id = api_client.save_report_id(report_id, 'DayiySales')

			# 下一个user_id
			new_user_id = api_client.next_user()

			# 拼接url_key
			url_key = f"{url_key}-{user_id}_{new_id}" if url_key else f"{user_id}_{new_id}"

			# MPR(1)、郑州(4)、武汉(6)
			site_ids_loop = [1, 4, 6]
			if site_id in site_ids_loop:
				if new_user_id:  # 有下一个用户
					show_context = f"开始创建用户{new_user_id}的报告"
					go_url = f"/reports/generate_reports_sale_day/?user_id={new_user_id}&self={return_url}&key={url_key}"
					context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
					return render(request, "SPAPI_Python_SDK/goto.html", context)

			# 没有下一个用户或不是MPR，郑州，武汉表示全部建仓成功
			show_context = "全部创建成功, 开始获取报告"
			user_id = url_key.split('-')[0].split('_')[0]
			go_url = f"/reports/check_report_sale_day/?user_id={user_id}&self={return_url}&key={url_key}"
			context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
			return render(request, "SPAPI_Python_SDK/goto.html", context)
	except Exception as e:
		logging.error(f"生成报告出错: {e}")
		return HttpResponse(f"生成报告出错: {e}")


from tenacity import retry, wait_exponential, stop_after_attempt, retry_if_exception_type
from requests.exceptions import HTTPError, ConnectionError, Timeout


@retry(
	wait=wait_exponential(multiplier=1, min=2, max=60),
	stop=stop_after_attempt(3),
	retry=retry_if_exception_type((ConnectionError, Timeout, HTTPError))
)
def safe_call_report_api(api_method, *args, **kwargs):
	try:
		response = api_method(*args, **kwargs)
		response.raise_for_status()  # 自动检查HTTP状态码
		return response
	except Exception as e:
		if not isinstance(e, (HTTPError, ConnectionError, Timeout)):
			raise HTTPError(f"API请求底层错误: {str(e)}") from e
		else:
			raise


def check_report(request):
	"""
	检查报告状态
	:param request:
	:return:
	"""
	page_title = "检查报告状态"
	user_id = request.GET.get('user_id')
	return_url = request.GET.get('self', "")
	url_key = request.GET.get('key')
	array_ZR_id = parse_url_key(url_key) if url_key else {}

	if not user_id:
		return HttpResponse("没有user")
	if not return_url:
		return HttpResponse("没有来源网址")

	site_id = site_query(request, return_url)  # 网站ID
	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")

	# 使用上下文管理器来管理数据库连接
	try:
		with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
			sql = f"SELECT id,report_id FROM fba_report WHERE user_id={user_id} AND id={array_ZR_id[user_id]} AND report_id!='' AND report_document_id IS NULL AND f=0 AND report_type='DayiySales' ORDER BY id DESC LIMIT 1"
			row = db.fetchone(sql)

			# 判断有没有下一个用户
			if not row:
				site_ids_loop = [1, 4, 6]
				if site_id in site_ids_loop:
					sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
					user_rs = db.fetchone(sql)
					if user_rs:
						new_user_id = user_rs["id"]
						show_context = f"开始获取下一用户的报告"
						go_url = f"/reports/check_report_inventory/?user_id={new_user_id}&self={return_url}&key={url_key}"
						context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
						return render(request, "SPAPI_Python_SDK/goto.html", context)
					else:
						show_context = f"开始统计数量"
						go_url = f"/reports/count_sku_sales_day/?self={return_url}&key={url_key}"
						context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
						return render(request, "SPAPI_Python_SDK/goto.html", context)

			report_id = row['report_id']
			api_client = ReportManager(user_id, db)

			# 检查报告状态
			# response = api_client.checkReport(report_id)

			# 2025年3月5日10:00:32
			try:
				response = safe_call_report_api(api_client.checkReport, report_id, _request_timeout=(15, 30))
			except Exception as e:
				return render(request, "SPAPI_Python_SDK/retry.html", status=500)

			response_dict = response.to_dict()
			status = response_dict.get("processing_status")
			created_time = response_dict.get("created_time")
			report_document_id = response_dict.get("report_document_id")

			# 根据报告状态判断下一步
			if status == "DONE":
				update_sqls = f"UPDATE fba_report SET report_document_id='{report_document_id}', create_date='{created_time}' WHERE report_id='{report_id}'";
				db.update(update_sqls)

				show_context = "查询成功，即将抓取"
				go_url = f"/reports/get_report_sale_day/?user_id={user_id}&self={return_url}&key={url_key}";
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)
			else:
				if status == "FATAL":
					sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
					db.delete(sql)
					go_url = f"/reports/check_report_sale_day/?user_id={user_id}&self={return_url}&key={url_key}"
					second = random.randint(5, 10)
					show_context = f"{user_id} 报告因致命错误而停止"
				elif status == "CANCELLED":
					sql = f"DELETE FROM fba_report WHERE report_id='{report_id}'"
					db.delete(sql)
					go_url = f"/reports/check_report_sale_day/?user_id={user_id}&self={return_url}&key={url_key}"
					second = random.randint(5, 10)
					show_context = f"{user_id} 亚马逊数据取消"
				elif status == "IN_QUEUE":
					go_url = f"/reports/check_report_sale_day/?user_id={user_id}&self={return_url}&key={url_key}"
					second = random.randint(10, 50)
					show_context = f"{user_id} 报告正在处理"
				else:
					go_url = f"/reports/check_report_sale_day/?user_id={user_id}&self={return_url}&key={url_key}"
					second = random.randint(100, 200)
					show_context = "报告尚未开始处理"

				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": second}
				return render(request, "SPAPI_Python_SDK/goto.html", context)

	except Exception as e:
		logging.error(f"错误检查报告: {e}")
		return HttpResponse(f"错误检查报告: {e}")


def get_report(request):
	"""
	获取报告数据
	"""
	page_title = "获取报告"
	user_id = request.GET.get('user_id')
	return_url = request.GET.get('self', "")
	url_key = request.GET.get('key')
	array_ZR_id = parse_url_key(url_key)
	if not user_id:
		return HttpResponse("没有user")
	if not return_url:
		return HttpResponse("没有来源网址")

	site_id = site_query(request, return_url)  # 网站ID
	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")
	try:
		with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
			sql = f"SELECT id, user_id,report_id, report_document_id FROM fba_report WHERE f=0 and user_id={user_id} AND id={array_ZR_id[user_id]} ORDER BY id desc LIMIT 1"
			rs = db.fetchone(sql)

			if not rs:
				show_context = "处理完成"
				if site_id in [1, 4, 6]:
					go_url = f"/reports/update_report_table_sale_day/?user_id={user_id}&self={return_url}&key={url_key}"
				else:
					go_url = f"/reports/update_report_table_sale_day/?user_id={user_id}&self={return_url}"
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)
			else:
				report_document_id = rs["report_document_id"]
				zom_report_id = rs["report_id"]
				user_id = rs["user_id"]
				api_client = ReportManager(user_id, db)

				# DocementResponses = api_client.getReportUrl(report_document_id)
				# 2025年3月5日10:03:33
				try:
					DocementResponses = safe_call_report_api(api_client.getReportUrl, report_document_id,
															 _request_timeout=(15, 30))
				except Exception as e:
					return render(request, "SPAPI_Python_SDK/retry.html", status=500)

				document_data = DocementResponses.to_dict()

				report_document_id = document_data['report_document_id']
				compression_algorithm = document_data['compression_algorithm']

				try:
					report_url = document_data['url']
					uncompressed_data = download_report_data(report_url, compression_algorithm)
				except Exception as e:
					error_message, response_text = e.args
					if "403" in error_message and "已过期" in response_text:
						document_data = api_client.getReportUrl(report_document_id)
						report_url = document_data['url']
						uncompressed_data = download_report_data(report_url, compression_algorithm)

				if uncompressed_data:
					data_file_like = io.StringIO(uncompressed_data)
					csv_reader = csv.DictReader(data_file_like, delimiter='\t')

					get_data = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
					data_list = []
					# 遍历每个报告数据
					for cell in csv_reader:
						shipment_date = convert_to_beijing_time(cell['shipment-date'])
						sku = cell['sku']
						fulfillment_center_id = cell['fulfillment-center-id']
						quantity = int(cell['quantity'])
						amazon_order_id = cell['amazon-order-id']
						currency = cell['currency']
						item_price_per_unit = float(cell['item-price-per-unit'])
						shipping_price = float(cell['shipping-price'])
						city = addslashes(cell['ship-city'])
						state = addslashes(cell['ship-state'])
						postal_code = cell['ship-postal-code']

						data_list.append((
							user_id, shipment_date, sku, fulfillment_center_id, quantity,
							amazon_order_id, currency, item_price_per_unit, shipping_price,
							city, state, postal_code
						))
					if data_list:
						sql = """
							INSERT INTO Sales_FBA_Complete_order(
								user_id, Shipment_date, SKU, Fulfillment_center_id, Quantity, 
								`amazon-order-id`, Currency, Item_price_per_unit, Shipping_price, 
								city, state, Postal_code
							) VALUES (
								%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
							)
						"""

						db.bulk_insert(sql, data_list)

					# 更新 fba_report 表
					try:
						sql = f"UPDATE fba_report SET f=1, get_date='{get_data}' WHERE report_id={zom_report_id}"
						db.update(sql)
					except Exception as e:
						print(f"更新 fba_report 时发生错误: {e}")

				show_context = f"{user_id}报告数据抓取完成"
				go_url = f"/reports/update_report_table_sale_day/?user_id={user_id}&self={return_url}&key={url_key}"
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)

	except Exception as e:
		logging.error(f"获取报告出错: {e}")
		return HttpResponse(f"获取报告出错: {e}")


def update_report_table(request):
	page_title = "更新报告表"
	user_id = request.GET.get('user_id')
	return_url = request.GET.get('self', "")
	url_key = request.GET.get('key')
	array_ZR_id = parse_url_key(url_key) if url_key else {}
	if not user_id:
		return HttpResponse("没有user")
	if not return_url:
		return HttpResponse("没有来源网址")

	site_id = site_query(request, return_url)  # 网站ID
	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")

	with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
		sql = f"SELECT report_document_id FROM fba_report WHERE user_id={user_id} and f=1 AND id={array_ZR_id[user_id]} ORDER BY id ASC LIMIT 100"

		rs = db.fetchall(sql)
		for row in rs:
			report_document_id = row['report_document_id']
			sql = f"DELETE FROM fba_report WHERE report_document_id = '{report_document_id}'"
			db.delete(sql)

		# 检查是否有下一个用户
		site_ids_loop = [1, 4, 6]
		if site_id in site_ids_loop:
			sql = f"SELECT id FROM user where MWS_Switch=1 and id>{user_id} and Father_id=0 ORDER BY id ASC limit 1"
			user_rs = db.fetchone(sql)
			if user_rs:
				user_id = user_rs["id"]
				show_context = "即将查询下一个用户"
				go_url = f"/reports/check_report_sale_day/?user_id={user_id}&self={return_url}&key={url_key}"
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)
			else:
				show_context = "进入最后一步"
				go_url = f"/reports/count_sku_sales_day/?self={return_url}"
				context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
				return render(request, "SPAPI_Python_SDK/goto.html", context)
		else:
			return HttpResponse("全部更新完成")


def count_sku_sales(request):
	return_url = request.GET.get('self', "")

	tz = ZoneInfo('Asia/Shanghai')
	up_date = datetime.now(tz)
	up_date_str = up_date.strftime("%Y-%m-%d %H:%M:%S")

	EndDate = (up_date - timedelta(minutes=1)).strftime("%Y-%m-%d %H:%M:%S")
	date_7 = (up_date - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S")
	date_30 = (up_date - timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S")
	date_90 = (up_date - timedelta(days=90)).strftime("%Y-%m-%d %H:%M:%S")

	day_ranges = [7, 30, 90]
	date_map = {
		7: date_7,
		30: date_30,
		90: date_90
	}

	if not return_url:
		return HttpResponse("没有来源网址")

	site_id = site_query(request, return_url)
	dbdata = DB_DATA[site_id]
	if not dbdata:
		return HttpResponse("无效的 site_id")

	with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
		if site_id in [1, 4, 6]:
			# 查询满足条件的用户
			sql = "SELECT id FROM user WHERE switch=1 AND MWS_Switch=1"
			user_data = db.fetchall(sql)

			for user_rs in user_data:
				user_id = user_rs['id']
				sku_array = {}

				# 针对每个时间周期进行查询
				for i in day_ranges:
					start_time = date_map[i]
					# 构建查询语句
					sql = f"SELECT SUM(Quantity) as sales, SKU FROM Sales_FBA_Complete_order WHERE user_id = {user_id} AND Shipment_date >= '{start_time}' AND Shipment_date <= '{EndDate}' GROUP BY SKU"
					sku_data = db.fetchall(sql)

					for sku_rs in sku_data:
						sku = sku_rs['SKU']
						sales = sku_rs['sales'] if sku_rs['sales'] else 0
						if sku not in sku_array:
							sku_array[sku] = {}
						sku_array[sku][i] = sales

				if sku_array:
					for item, value in sku_array.items():
						shipped_data = {
							'shipped_7': value.get(7, 0),
							'shipped_30': value.get(30, 0),
							'shipped_90': value.get(90, 0)
						}

						# 更新 fba_sku_status
						update_sql = f"UPDATE fba_sku_status SET snapshot_date='{up_date_str}', shipped_7='{shipped_data['shipped_7']}', shipped_30='{shipped_data['shipped_30']}', shipped_90='{shipped_data['shipped_90']}' WHERE sku='{item}'"
						db.update(update_sql)

						# 插入 fba_sales_records
						insert_sql = f"INSERT INTO fba_sales_records(snapshot_date, sku, shipped_7, shipped_30, shipped_90) VALUES('{up_date_str}', '{item}', '{shipped_data['shipped_7']}', '{shipped_data['shipped_30']}', '{shipped_data['shipped_90']}')"
						db.insert(insert_sql)

		elif site_id in [2, 3]:
			sku_array = {}
			for i in day_ranges:
				start_time = date_map[i]
				sql = f"SELECT SUM(Sales_FBA_Complete_order.Quantity) as sales, Sales_FBA_Complete_order.SKU FROM Sales_FBA_Complete_order WHERE Sales_FBA_Complete_order.Shipment_date >= '{start_time}' AND Sales_FBA_Complete_order.Shipment_date <= '{EndDate}' GROUP BY Sales_FBA_Complete_order.SKU"
				sku_data = db.fetchall(sql)

				for sku_rs in sku_data:
					sku = sku_rs['SKU']
					sales = sku_rs['sales'] if sku_rs['sales'] else 0
					if sku not in sku_array:
						sku_array[sku] = {}
					sku_array[sku][i] = sales

			if sku_array:
				for item, value in sku_array.items():
					shipped_data = {
						'shipped_7': value.get(7, 0),
						'shipped_30': value.get(30, 0),
						'shipped_90': value.get(90, 0)
					}

					# 更新 fba_sku_status
					update_sql = f"UPDATE fba_sku_status SET snapshot_date='{up_date_str}', shipped_7='{shipped_data['shipped_7']}', shipped_30='{shipped_data['shipped_30']}', shipped_90='{shipped_data['shipped_90']}' WHERE sku='{item}'"

					db.update(update_sql)

					# 插入 fba_sales_records
					insert_sql = f"INSERT INTO fba_sales_records(snapshot_date, sku, shipped_7, shipped_30, shipped_90) VALUES('{up_date_str}', '{item}', '{shipped_data['shipped_7']}', '{shipped_data['shipped_30']}', '{shipped_data['shipped_90']}')"
					db.insert(insert_sql)

	return HttpResponse("全部更新完成")
