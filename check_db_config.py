#!/usr/bin/env python3
"""
检查当前数据库配置的脚本
"""

import sys
import os

# 添加项目路径到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from SPAPI_Python_SDK.Database import DB_DATA
    
    print("当前 DB_DATA 配置:")
    print("=" * 50)
    
    for site_id, config in DB_DATA.items():
        print(f"Site ID {site_id}:")
        print(f"  DB_HOST: {config['DB_HOST']}")
        print(f"  DB_USER: {config['DB_USER']}")
        print(f"  DB_NAME: {config['DB_NAME']}")
        print(f"  DB_PWD: {config['DB_PWD']}")
        print()
    
    print("=" * 50)
    print("检查完成")
    
except ImportError as e:
    print(f"导入错误: {e}")
except Exception as e:
    print(f"其他错误: {e}")
