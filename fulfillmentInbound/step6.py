from urllib.parse import urlencode

from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import get_user_key, site_query
from SPAPI_Python_SDK.if_user_id import login_required_if_user_id
from spapi.spapiclient import SPAPIClient
from datetime import datetime, timedelta, timezone


@login_required_if_user_id
def step6(request):
	page_title = "箱签第六步"
	return_url = request.GET.get('self', "1")
	fj_id = request.GET.get('fj_id', "")
	page = int(request.GET.get('page', 1))
	site_id = site_query(request, return_url)  # 网站ID
	dbdata = DB_DATA[site_id]
	db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
	sql = f"SELECT fba_job.title, fba_job.user_id, inboundPlanId FROM fba_job WHERE id= {fj_id}"
	job_rs = db.fetchone(sql)
	if not job_rs:
		return JsonResponse({"error": "没有此任务"}, status=404)
	user_id = job_rs["user_id"]
	inboundPlanId = job_rs["inboundPlanId"]
	config = get_user_key(db, user_id)
	api_client = SPAPIClient(config, "swagger_client_fulfillmentInbound")
	fba_api = api_client.get_api_client('FbaInboundApi')
	sql = f"SELECT count(id) as n FROM  fba_shipment_placement WHERE job_id={fj_id}"
	page_count = db.fetchone(sql)
	if page > page_count["n"]:
		page = page_count["n"]
	
	sql = f"SELECT id, placement_option_id, shipment_id FROM fba_shipment_placement WHERE job_id={fj_id} ORDER BY id ASC limit {page - 1}, 1"
	data = db.fetchall(sql)
	for rs in data:
		shipment_id = rs["shipment_id"]
		generateDeliveryWindowOptions = fba_api.generate_delivery_window_options(inboundPlanId, shipment_id.strip())
	
	# return HttpResponse(generateDeliveryWindowOptions, content_type='application/json')
	if page == page_count["n"]:
		sql = f"UPDATE fba_job SET FBA_label_status_2='6' WHERE id={fj_id} "
		db.update(sql)
		
		operation_id = generateDeliveryWindowOptions.operation_id
		query_params = {
			'page_title': '检测第六步结果',
			'fj_id': fj_id,
			'self': return_url,
			'operationId': operation_id,
			'go_url': '/fulfillmentInbound/step7/'
			
		}
		encoded_query_params = urlencode(query_params)
		show_context = "第六步完成"
		go_url = f"/fulfillmentInbound/step1-1/?{encoded_query_params}"
		context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
		return render(request, "SPAPI_Python_SDK/goto.html", context)
	
	else:
		show_context = "第六步，正在更新"
		go_url = f"/fulfillmentInbound/step6/?fj_id={fj_id}&self={return_url}&page={page + 1}"
		context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": 2}
		return render(request, "SPAPI_Python_SDK/goto.html", context)
