import json
from urllib.parse import urlencode

from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import get_user_key, site_query
from SPAPI_Python_SDK.if_user_id import login_required_if_user_id
from spapi.spapiclient import SPAPIClient
from datetime import datetime, timedelta, timezone


@login_required_if_user_id
def step5(request):
	page_title = "箱签第五步"
	return_url = request.GET.get('self', "1")
	fj_id = request.GET.get('fj_id', "")
	# 获取当前时间
	current_datetime = datetime.now(timezone.utc).replace(microsecond=0)
	# 当前时间加1天
	new_datetime = current_datetime + timedelta(days=1)
	# 将新时间转换为ISO 8601格式的字符串
	iso8601_string_new = new_datetime.isoformat().replace("+00:00", "Z")
	site_id = site_query(request, return_url)  # 网站ID
	
	dbdata = DB_DATA[site_id]
	db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
	sql = f"SELECT fba_job.title, fba_job.user_id, inboundPlanId, packingGroupId FROM fba_job WHERE id= {fj_id}"
	job_rs = db.fetchone(sql)
	if not job_rs:
		return JsonResponse({"error": "没有此任务"}, status=404)
	user_id = job_rs["user_id"]
	inboundPlanId = job_rs["inboundPlanId"]
	packing_group_id = job_rs["packingGroupId"]
	config = get_user_key(db, user_id)
	sql = f"SELECT id, placement_option_id, shipment_id FROM fba_shipment_placement WHERE job_id={fj_id} ORDER BY id ASC "
	data = db.fetchall(sql)
	api_client = SPAPIClient(config, "swagger_client_fulfillmentInbound")
	fba_api = api_client.get_api_client('FbaInboundApi')
	strshipment = []
	for rs in data:
		shipment_id = rs["shipment_id"]
		strshipment.append({
			"shipmentId": shipment_id.strip(),
			"readyToShipWindow": {
				"start": iso8601_string_new
			},
		})
	boy = {
		"placementOptionId": rs["placement_option_id"],
		"shipmentTransportationConfigurations": strshipment
	}
	# return JsonResponse(boy, json_dumps_params={'indent': 4}, safe=False)
	
	generateTransportationOptions = fba_api.generate_transportation_options(inboundPlanId, boy)
	sql = f"UPDATE fba_job SET FBA_label_status_2='5' WHERE id={fj_id} "
	db.update(sql)
	
	operation_id = generateTransportationOptions.operation_id
	query_params = {
		'page_title': '检测第五步结果',
		'fj_id': fj_id,
		'self': return_url,
		'operationId': operation_id,
		'go_url': '/fulfillmentInbound/step6/'
		
	}
	encoded_query_params = urlencode(query_params)
	show_context = "第五步完成"
	go_url = f"/fulfillmentInbound/step1-1/?{encoded_query_params}"
	context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
	return render(request, "SPAPI_Python_SDK/goto.html", context)
