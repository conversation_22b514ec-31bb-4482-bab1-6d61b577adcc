import json
from urllib.parse import urlencode

from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import get_user_key, site_query
from SPAPI_Python_SDK.login_user_id import login_required_if_no_user_id
from spapi.spapiclient import SPAPIClient


@login_required_if_no_user_id
def step3(request):
	page_title = "箱签第三步"
	return_url = request.GET.get('self', "1")
	fj_id = request.GET.get('fj_id', "")
	site_id = site_query(request, return_url)  # 网站ID
	
	dbdata = DB_DATA[site_id]
	db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
	sql = f"SELECT fba_job.title, fba_job.user_id, inboundPlanId, packingGroupId FROM fba_job WHERE id= {fj_id}"
	job_rs = db.fetchone(sql)
	if not job_rs:
		return JsonResponse({"error": "没有此任务"}, status=404)
	user_id = job_rs["user_id"]
	inboundPlanId = job_rs["inboundPlanId"]
	packing_group_id = job_rs["packingGroupId"]
	config = get_user_key(db, user_id)
	sql = f"SELECT fba_box_manual.id, fba_box_manual.box_number, fba_box_manual.box_size, fba_box_manual.Box_weight FROM fba_box_manual WHERE fba_job_id={fj_id}"
	box_data = db.fetchall(sql)
	if not box_data:
		return JsonResponse({"error": "此任务没有装箱"}, status=404)
	boxes = []
	for box in box_data:
		sql = f"SELECT fba_box_manual_info.sku_num, sku_pro.sku, sku_pro.prepOwner FROM fba_box_manual_info INNER JOIN sku_pro ON fba_box_manual_info.Sku_fnsku = sku_pro.fnsku WHERE fba_box_manual_info.Fbm_id={box['id']}"
		sku_data = db.fetchall(sql)
		Item_Input_List = []
		
		for sku_rs in sku_data:
			prepOwner = sku_rs["prepOwner"] if sku_rs["prepOwner"] else "SELLER"
			t_item = {
				"labelOwner": "SELLER",
				"msku": sku_rs["sku"],
				"prepOwner": prepOwner,
				"quantity": sku_rs["sku_num"]
			}
			Item_Input_List.append(t_item)
		box_size = box["box_size"].split("*")
		t_box = {
			"contentInformationSource": "BOX_CONTENT_PROVIDED",
			"boxId": f"MM-{box['id']}",
			"dimensions": {
				"height": box_size[1],
				"length": box_size[0],
				"unitOfMeasurement": "CM",
				"width": box_size[2],
			},
			"items": Item_Input_List,
			"quantity": 1,
			"weight": {
				"unit": "KG",
				"value": box["Box_weight"]
			}
		}
		boxes.append(t_box)
	# return JsonResponse(boxes, json_dumps_params={'indent': 4}, safe=False)
	
	boy = {
		"packageGroupings": [
			{
				"boxes": boxes,
				"packingGroupId": packing_group_id,
			}
		]
	}
	# return JsonResponse(boy, json_dumps_params={'indent': 4}, safe=False)
	api_client = SPAPIClient(config, "swagger_client_fulfillmentInbound")
	fba_api = api_client.get_api_client('FbaInboundApi')
	
	setPackingInformation = fba_api.set_packing_information(inboundPlanId, boy)
	sql = f"UPDATE fba_job SET FBA_label_status_2='3' WHERE id={fj_id} "
	db.update(sql)
	operation_id = setPackingInformation.operation_id
	query_params = {
		'page_title': '检测第三步结果',
		'fj_id': fj_id,
		'self': return_url,
		'operationId': operation_id,
		'go_url': '/fulfillmentInbound/step4/'
	}
	encoded_query_params = urlencode(query_params)
	show_context = "第三步完成"
	go_url = f"/fulfillmentInbound/step1-1/?{encoded_query_params}"
	context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": 10}
	return render(request, "SPAPI_Python_SDK/goto2.html", context)


@login_required_if_no_user_id
def step3_1(request):
	page_title = "箱签第三步"
	return_url = request.GET.get('self', "1")
	fj_id = request.GET.get('fj_id', "")
	site_id = site_query(request, return_url)  # 网站ID
	
	dbdata = DB_DATA[site_id]
	db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
	sql = f"SELECT fba_job.title, fba_job.user_id, inboundPlanId, packingGroupId FROM fba_job WHERE id= {fj_id}"
	job_rs = db.fetchone(sql)
	if not job_rs:
		return JsonResponse({"error": "没有此任务"}, status=404)
	user_id = job_rs["user_id"]
	inboundPlanId = job_rs["inboundPlanId"]
	packing_group_id = job_rs["packingGroupId"]
	config = get_user_key(db, user_id)
	api_client = SPAPIClient(config, "swagger_client_fulfillmentInbound")
	fba_api = api_client.get_api_client('FbaInboundApi')
	listInboundPlanBoxes = fba_api.list_inbound_plan_boxes(inboundPlanId)
	
	go_url = f"/fulfillmentInbound/step4/?fj_id={fj_id}&self={return_url}"
	context = {"show_context": listInboundPlanBoxes, "go_url": go_url, "page_title": page_title}
	return render(request, "SPAPI_Python_SDK/goto2.html", context)
