import json

from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import get_user_key, site_query, SITE_PDF_PATH
from SPAPI_Python_SDK.login_user_id import session_decorator
from spapi.spapiclient import SPAPIClient
from datetime import datetime, timedelta, timezone
import requests


@session_decorator
def cancel(request):
	page_title = "取消计划任务"
	return_url = request.GET.get('self', "1")
	fj_id = request.GET.get('fj_id', "")
	site_id = site_query(request, return_url)  # 网站ID
	
	dbdata = DB_DATA[site_id]
	db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
	sql = f"SELECT title, user_id, inboundPlanId  FROM fba_job WHERE id= {fj_id}"
	job_rs = db.fetchone(sql)
	if not job_rs:
		return JsonResponse({"error": "没有此任务"}, status=404)
	user_id = job_rs["user_id"]
	inboundPlanId = job_rs["inboundPlanId"]
	config = get_user_key(db, user_id)
	api_client = SPAPIClient(config, "swagger_client_fulfillmentInbound")
	fba_api = api_client.get_api_client('FbaInboundApi')
	
	cancelInboundPlan = fba_api.cancel_inbound_plan(inbound_plan_id=inboundPlanId)
	sql = f"UPDATE fba_job SET fba_box_label=null, inboundPlanId= null, FBA_label_status=null, packingGroupId=null, ShipmentId=null, FBA_label_status_2=null WHERE id= {fj_id}"
	db.update(sql)
	sql = f"DELETE FROM fba_shipment_placement WHERE job_id={fj_id}"
	db.delete(sql)
	
	# return HttpResponse(cancelInboundPlan, content_type='application/json')
	
	show_context = "取消完成"
	go_url = return_url
	context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
	return render(request, "SPAPI_Python_SDK/goto.html", context)
