from urllib.parse import urlencode

from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import get_user_key, site_query, ARRAY_STATUS_DES
from SPAPI_Python_SDK.if_user_id import login_required_if_user_id
from spapi.spapiclient import SPAPIClient
from datetime import datetime, timezone


@login_required_if_user_id
def step8(request):
	page_title = "箱签第八步"
	return_url = request.GET.get('self', "1")
	fj_id = request.GET.get('fj_id', "")
	site_id = site_query(request, return_url)  # 网站ID
	dbdata = DB_DATA[site_id]
	db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
	sql = f"SELECT fba_job.title, fba_job.user_id, fba_job.channel_id, inboundPlanId FROM fba_job WHERE id= {fj_id}"
	job_rs = db.fetchone(sql)
	if not job_rs:
		return JsonResponse({"error": "没有此任务"}, status=404)
	user_id = job_rs["user_id"]
	inboundPlanId = job_rs["inboundPlanId"]
	config = get_user_key(db, user_id)
	api_client = SPAPIClient(config, "swagger_client_fulfillmentInbound")
	fba_api = api_client.get_api_client('FbaInboundApi')
	
	sql = f"SELECT id, placement_option_id, shipment_id FROM fba_shipment_placement WHERE job_id={fj_id} ORDER BY id ASC limit 1"
	
	data = db.fetchall(sql)
	for rs in data:
		confirmPlacementOption = fba_api.confirm_placement_option(inbound_plan_id=inboundPlanId,
		                                                          placement_option_id=rs["placement_option_id"], )
	
	sql = f"UPDATE fba_job SET FBA_label_status_2='8' WHERE id={fj_id} "
	db.update(sql)
	
	operation_id = confirmPlacementOption.operation_id
	query_params = {
		'page_title': '检测第八步结果',
		'fj_id': fj_id,
		'self': return_url,
		'operationId': operation_id,
		'go_url': '/fulfillmentInbound/step8-1/'
		
	}
	encoded_query_params = urlencode(query_params)
	show_context = "第八步完成"
	go_url = f"/fulfillmentInbound/step1-1/?{encoded_query_params}"
	context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
	return render(request, "SPAPI_Python_SDK/goto.html", context)


@login_required_if_user_id
def step8_1(request):
	page_title = "箱签第八步"
	return_url = request.GET.get('self', "1")
	fj_id = request.GET.get('fj_id', "")
	page = int(request.GET.get('page', 1))
	site_id = site_query(request, return_url)  # 网站ID
	up_date = datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
	dbdata = DB_DATA[site_id]
	db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
	sql = f"SELECT fba_job.title, fba_job.user_id, fba_job.channel_id, inboundPlanId, packingGroupId FROM fba_job WHERE id= {fj_id}"
	job_rs = db.fetchone(sql)
	if not job_rs:
		return JsonResponse({"error": "没有此任务"}, status=404)
	user_id = job_rs["user_id"]
	inboundPlanId = job_rs["inboundPlanId"]
	packing_group_id = job_rs["packingGroupId"]
	config = get_user_key(db, user_id)
	api_client = SPAPIClient(config, "swagger_client_fulfillmentInbound")
	fba_api = api_client.get_api_client('FbaInboundApi')
	sql = f"SELECT count(id) as n FROM  fba_shipment_placement WHERE job_id={fj_id}"
	page_count = db.fetchone(sql)
	if page > page_count["n"]:
		page = page_count["n"]
	
	sql = f"SELECT id, placement_option_id, shipment_id FROM fba_shipment_placement WHERE job_id={fj_id} ORDER BY id ASC limit {page - 1}, 1"
	data = db.fetchall(sql)
	for rs in data:
		shipment_id = rs["shipment_id"]
		listShipmentBoxes = fba_api.list_shipment_boxes(inboundPlanId, shipment_id)
		# return HttpResponse(listShipmentBoxes, content_type='application/json')
		if page_count["n"] > 1:  # 如果大于1说明有分仓，把分仓结果直接做成子任务
			listShipmentBoxes_doc = listShipmentBoxes.to_dict()
			boxes = listShipmentBoxes_doc["boxes"]
			box_id = []
			box_num = []
			for box in boxes:
				strbox_num = remove_letters(box["template_name"].split("-")[1])
				box_num.append(int(strbox_num))
				box_id.append(box["box_id"])
				ShipmentId = box["box_id"][:12]
			title_box = ', '.join(str(item) for item in box_num)
			strbox_id = ', '.join(str(item) for item in box_id)
			title = f"{job_rs["title"]}-c[{title_box}]"
			numberboxes = len(box_num)
			
			# 生成子任务
			sql = f"SELECT SUM(fba_box_manual.Box_weight) as sum_box_weight FROM fba_box_manual WHERE fba_job_id = {fj_id} AND box_number IN ({title_box})"
			weight_rs = db.fetchone(sql)
			
			sql = f"INSERT INTO fba_job(title, up_date, user_id, draft, channel_id, weight, Box_number, ShipmentId, shipping_status, FBA_label_status,Father_id)VALUES('{title}', '{up_date}', '{user_id}', 0, '{job_rs["channel_id"]}',{weight_rs['sum_box_weight']}, {numberboxes},'{ShipmentId}', 4, 1, {fj_id});"
			new_id = db.insert(sql)
			
			# 更新box_id
			sql = f"UPDATE fba_shipment_placement SET box_id='{strbox_id}', sub_id={new_id} WHERE id={rs['id']}"
			db.update(sql)
			
			# 子任务日志
			sql = f"INSERT INTO fba_job_log (fj_id,fj_status,data_log,des)VALUES({new_id}, 2, '{up_date}','{ARRAY_STATUS_DES[1]}');"
			db.insert(sql)
			sql = f"INSERT INTO fba_job_log (fj_id,fj_status,data_log,des)VALUES({new_id}, 4, '{up_date}','{ARRAY_STATUS_DES[1]}');"
			db.insert(sql)
			
			# sql = f"INSERT INTO fba_job_log (fj_id,fj_status,data_log,des)VALUES(%s, %s, %s,%s);"
			# param_log = [[new_id, fj_status, up_date, ARRAY_STATUS_DES[1]] for fj_status in [2, 4]]
			# return HttpResponse(param_log, content_type="application/json")
			# db.insert(sql, param_log)
			# 子任务数据
			
			for box in box_num:
				# 子任务装箱
				sql = f"SELECT fba_box_manual.id, fba_box_manual.fba_job_id, fba_box_manual.box_number, fba_box_manual.box_size, 	fba_box_manual.Box_weight FROM fba_Box_Manual WHERE fba_job_id={fj_id} and box_number={box}"
				box_manuals = db.fetchone(sql)
				if box_manuals:
					sql = f"INSERT INTO fba_box_manual(fba_job_id, box_number, box_size, Box_weight)VALUES({new_id},{box_manuals["box_number"]}, {box_manuals["box_size"]}, {box_manuals["Box_weight"]}); "
					fbm_id = db.insert(sql)
					sql = f"SELECT fba_box_manual_info.id, fba_box_manual_info.Fbm_id, fba_box_manual_info.Sku_fnsku, fba_box_manual_info.sku_num FROM fba_box_manual_info WHERE Fbm_id={box_manuals['id']}"
					old_box_manuals = db.fetchall(sql)
					sql = "INSERT INTO fba_box_manual_info(Fbm_id, Sku_fnsku, sku_num)VALUES(%s, %s, %s);"
					param = [[fbm_id, info["Sku_fnsku"], info["sku_num"]] for info in old_box_manuals]
					db.bulk_insert(sql, param)
				
				sql = f"SELECT sku_pro.id AS sku_id, sku_pro.sku, fba_box_manual_info.sku_num FROM fba_box_manual_info INNER JOIN sku_pro ON fba_box_manual_info.Sku_fnsku = sku_pro.fnsku INNER JOIN fba_box_manual ON fba_box_manual_info.Fbm_id = fba_box_manual.id WHERE fba_box_manual.fba_job_id = {fj_id} AND fba_box_manual.box_number = ({box}) "
				sku_list = db.fetchall(sql)
				for sku_item in sku_list:
					# 子任务SKU对应
					sql = f"SELECT id, num FROM fba_job_sku WHERE fj_id={new_id} AND sku_id={sku_item['sku_id']} "
					t_rs = db.fetchone(sql)
					if t_rs is None:
						sql = f"INSERT INTO fba_job_sku(fj_id, sku_id, num)VALUES({new_id}, {sku_item["sku_id"]}, {sku_item["sku_num"]});"
						db.insert(sql)
					else:
						num = t_rs["num"] + sku_item["sku_num"]
						sql = f"UPDATE  fba_job_sku set num={num} where fj_id={new_id} and sku_id={sku_item["sku_id"]}"
						db.update(sql)
	
	# return HttpResponse(box_id, content_type='application/json')
	# return HttpResponse(listShipmentBoxes, content_type='application/json')
	
	if page == page_count["n"]:
		# return HttpResponse(listShipmentBoxes, content_type='application/json')
		sql = f"UPDATE fba_job SET FBA_label_status_2='8-1' WHERE id={fj_id} "
		db.update(sql)
		show_context = "第八-1步完成"
		go_url = f"/fulfillmentInbound/step8-2/?fj_id={fj_id}&self={return_url}"
		context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
		return render(request, "SPAPI_Python_SDK/goto.html", context)
	else:
		show_context = "第八-1,写入分仓"
		go_url = f"/fulfillmentInbound/step8-1/?fj_id={fj_id}&self={return_url}&page={page + 1}"
		context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
		return render(request, "SPAPI_Python_SDK/goto.html", context)


def remove_letters(s):
	import string
	# 创建一个翻译表，将所有字母映射为None
	remove_letters_table = str.maketrans('', '', string.ascii_letters)
	return s.translate(remove_letters_table)


@login_required_if_user_id
def step8_2(request):
	page_title = "箱签第八-1步"
	return_url = request.GET.get('self', "1")
	fj_id = request.GET.get('fj_id', "")
	page = int(request.GET.get('page', 1))
	site_id = site_query(request, return_url)  # 网站ID
	dbdata = DB_DATA[site_id]
	db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
	sql = f"SELECT fba_job.title, fba_job.user_id, inboundPlanId, packingGroupId FROM fba_job WHERE id= {fj_id}"
	job_rs = db.fetchone(sql)
	if not job_rs:
		return JsonResponse({"error": "没有此任务"}, status=404)
	user_id = job_rs["user_id"]
	inboundPlanId = job_rs["inboundPlanId"]
	packing_group_id = job_rs["packingGroupId"]
	config = get_user_key(db, user_id)
	api_client = SPAPIClient(config, "swagger_client_fulfillmentInbound")
	fba_api = api_client.get_api_client('FbaInboundApi')
	sql = f"SELECT count(id) as n FROM  fba_shipment_placement WHERE job_id={fj_id}"
	page_count = db.fetchone(sql)
	if page > page_count["n"]:
		page = page_count["n"]
	
	sql = f"SELECT id, placement_option_id, shipment_id FROM fba_shipment_placement WHERE job_id={fj_id} ORDER BY id ASC limit {page - 1}, 1"
	rs = db.fetchone(sql)
	
	shipment_id = rs["shipment_id"]
	getShipment = fba_api.get_shipment(inboundPlanId, shipment_id)
	
	shipment_confirmation_id = getShipment.shipment_confirmation_id
	address = getShipment.destination.address
	address_line1 = address.address_line1
	address_line2 = address.address_line2
	if address_line2 == None:
		address_line2 = ""
	city = address.city
	company_name = address.company_name
	country_code = address.country_code
	add_name = address.name
	postal_code = address.postal_code
	state_or_province_code = address.state_or_province_code
	warehouse_id = getShipment.destination.warehouse_id
	address_str = f"{add_name}\n{address_line1}\n{address_line2}\n{city}\n{state_or_province_code}\n{postal_code}\n{country_code}({warehouse_id})"
	if page_count["n"] > 1:  # 如果大于1说明有分仓，把分仓结果直接做成子任务
		sql = f"UPDATE fba_job SET Invoice_address='{address_str}', ZIP_code='{postal_code}', Center_Id='{warehouse_id}' WHERE ShipmentId='{shipment_confirmation_id}'"
		db.update(sql)
		sql = f"select id from fba_address where ZIP_code='{postal_code}'"
		t_rs = db.fetchone(sql)
		if t_rs is None:
			sql = f"INSERT INTO fba_address (address, ZIP_code, add_name, address1, address2, city, state, CountryCode)VALUES('{address_str}', '{postal_code}', '{add_name}', '{address_line1}', '{address_line2}', '{city}', '{state_or_province_code}', '{country_code}')"
			db.insert(sql)
	else:
		sql = f"UPDATE fba_job SET Invoice_address='{address_str}', ZIP_code='{postal_code}', ShipmentId='{shipment_confirmation_id}', Center_Id='{warehouse_id}' WHERE id={fj_id}"
		db.update(sql)
		sql = f"select id from fba_address where ZIP_code='{postal_code}'"
		t_rs = db.fetchone(sql)
		if t_rs is None:
			sql = f"INSERT INTO fba_address (address, ZIP_code, add_name, address1, address2, city, state, CountryCode)VALUES('{address_str}', '{postal_code}', '{add_name}', '{address_line1}', '{address_line2}', '{city}', '{state_or_province_code}', '{country_code}')"
			db.insert(sql)
	# return HttpResponse(getShipment, content_type='application/json')
	if page == page_count["n"]:
		sql = f"UPDATE fba_job SET FBA_label_status_2='8-2' WHERE id={fj_id} "
		db.update(sql)
		show_context = "第八-2步完成"
		go_url = f"/fulfillmentInbound/step9/?fj_id={fj_id}&self={return_url}"
		context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
		return render(request, "SPAPI_Python_SDK/goto.html", context)
	else:
		show_context = f"第八-2-{page}步,正在写入分仓"
		go_url = f"/fulfillmentInbound/step8-2/?fj_id={fj_id}&self={return_url}&page={page + 1}"
		context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": 60}
		return render(request, "SPAPI_Python_SDK/goto.html", context)

# sql = f"SELECT id, placement_option_id, shipment_id FROM fba_shipment_placement WHERE job_id={fj_id} ORDER BY id ASC limit {page - 1}, 1"
# data = db.fetchall(sql)
# i = 0
# for rs in data:
# 	i = i + 1
# 	shipment_id = rs["shipment_id"]
# 	getShipment = fba_api.get_shipment(inboundPlanId, shipment_id)
#
# 	shipment_confirmation_id = getShipment.shipment_confirmation_id
# 	address = getShipment.destination.address
# 	address_line1 = address.address_line1
# 	address_line2 = address.address_line2
# 	if address_line2 == None:
# 		address_line2 = ""
# 	city = address.city
# 	company_name = address.company_name
# 	country_code = address.country_code
# 	add_name = address.name
# 	postal_code = address.postal_code
# 	state_or_province_code = address.state_or_province_code
# 	warehouse_id = getShipment.destination.warehouse_id
# 	address_str = f"{add_name}\n{address_line1}\n{address_line2}\n{city}\n{state_or_province_code}\n{postal_code}\n{country_code}({warehouse_id})"
# 	if page_count["n"] > 1:  # 如果大于1说明有分仓，把分仓结果直接做成子任务
# 		sql = f"UPDATE fba_job SET Invoice_address='{address_str}', ZIP_code='{postal_code}', Center_Id='{warehouse_id}' WHERE ShipmentId='{shipment_confirmation_id}'"
# 		db.update(sql)
# 		sql = f"select id from fba_address where ZIP_code='{postal_code}'"
# 		t_rs = db.fetchone(sql)
# 		if t_rs is None:
# 			sql = f"INSERT INTO fba_address (address, ZIP_code, add_name, address1, address2, city, state, CountryCode)VALUES('{address_str}', '{postal_code}', '{add_name}', '{address_line1}', '{address_line2}', '{city}', '{state_or_province_code}', '{country_code}')"
# 			db.insert(sql)
# 	else:
# 		sql = f"UPDATE fba_job SET Invoice_address='{address_str}', ZIP_code='{postal_code}', ShipmentId='{shipment_confirmation_id}', Center_Id='{warehouse_id}' WHERE id={fj_id}"
# 		db.update(sql)
# 		sql = f"select id from fba_address where ZIP_code='{postal_code}'"
# 		t_rs = db.fetchone(sql)
# 		if t_rs is None:
# 			sql = f"INSERT INTO fba_address (address, ZIP_code, add_name, address1, address2, city, state, CountryCode)VALUES('{address_str}', '{postal_code}', '{add_name}', '{address_line1}', '{address_line2}', '{city}', '{state_or_province_code}', '{country_code}')"
# 			db.insert(sql)
# # return HttpResponse(getShipment, content_type='application/json')
# if page == page_count["n"]:
# 	sql = f"UPDATE fba_job SET FBA_label_status_2='8-2' WHERE id={fj_id} "
# 	db.update(sql)
# 	show_context = "第八-2步完成"
# 	go_url = f"/fulfillmentInbound/step9/?fj_id={fj_id}&self={return_url}"
# 	context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
# 	return render(request, "SPAPI_Python_SDK/goto.html", context)
# else:
# 	show_context = f"{i}第八-2-{page}步,正在写入分仓"
# 	go_url = f"/fulfillmentInbound/step8-2/?fj_id={fj_id}&self={return_url}&page={page + 1}"
# 	context = {"show_context": show_context, "go_url": go_url, "page_title": page_title, "second": 60}
# 	return render(request, "SPAPI_Python_SDK/goto.html", context)
