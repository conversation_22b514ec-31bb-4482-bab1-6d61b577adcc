from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import get_user_key, site_query, common_config
from SPAPI_Python_SDK.if_user_id import login_required_if_user_id
from spapi.spapiclient import SPAPIClient
from urllib.parse import urlencode
from requests.exceptions import HTTPError


@login_required_if_user_id
def step2(request):
	page_title = "箱签第二步"
	return_url = request.GET.get('self', "1")
	fj_id = request.GET.get('fj_id', "")
	site_id = site_query(request, return_url)  # 网站ID

	dbdata = DB_DATA[site_id]
	db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
	sql = f"SELECT fba_job.title, fba_job.user_id, inboundPlanId FROM fba_job WHERE id= {fj_id}"
	job_rs = db.fetchone(sql)
	if not job_rs:
		return JsonResponse({"error": "没有此任务"}, status=404)
	user_id = job_rs["user_id"]
	inboundPlanId = job_rs["inboundPlanId"]
	config = get_user_key(db, user_id)
	api_client = SPAPIClient(config, "swagger_client_fulfillmentInbound")
	fba_api = api_client.get_api_client('FbaInboundApi')
	generatepackingoptions = fba_api.generate_packing_options(inboundPlanId)

	operation_id = generatepackingoptions.operation_id
	query_params = {
		'page_title': '检测第二步-1结果',
		'fj_id': fj_id,
		'self': return_url,
		'operationId': operation_id,
		'go_url': '/fulfillmentInbound/step2-1/'
	}
	encoded_query_params = urlencode(query_params)
	show_context = "第二步完成"
	go_url = f"/fulfillmentInbound/step1-1/?{encoded_query_params}"
	context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
	return render(request, "SPAPI_Python_SDK/goto.html", context)


# return HttpResponse(confirmPackingOption, content_type="application/json")
def step2_1(request):
	page_title = "箱签第二步"
	return_url = request.GET.get('self', "1")
	fj_id = request.GET.get('fj_id', "")
	site_id = site_query(request, return_url)  # 网站ID

	dbdata = DB_DATA[site_id]
	db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
	sql = f"SELECT fba_job.title, fba_job.user_id, inboundPlanId FROM fba_job WHERE id= {fj_id}"
	job_rs = db.fetchone(sql)
	if not job_rs:
		return JsonResponse({"error": "没有此任务"}, status=404)
	user_id = job_rs["user_id"]
	inboundPlanId = job_rs["inboundPlanId"]
	config = get_user_key(db, user_id)
	api_client = SPAPIClient(config, "swagger_client_fulfillmentInbound")
	fba_api = api_client.get_api_client('FbaInboundApi')
	try:
		listPackingOptions = fba_api.list_packing_options(inboundPlanId)
	except Exception as err:
		error_str = str(err)
		# Check if the error is due to quota being exceeded
		if 'QuotaExceeded' in error_str or 'TooManyRequestsException' in error_str or 'status 429' in error_str:
			html = '''
			<html>
			<head>
			    <title>Retrying...</title>
			    <script type="text/javascript">
			        var countdown = 3;
			        function updateCountdown() {
			            if (countdown > 0) {
			                document.getElementById('countdown').innerHTML = countdown;
			                countdown--;
			                setTimeout(updateCountdown, 1000);
			            } else {
			                window.location.reload();
			            }
			        }
			        window.onload = updateCountdown;
			    </script>
			</head>
			<body>
			    <p>请求超频 <span id="countdown">3</span>后自动刷新</p>
			</body>
			</html>
			'''
			return HttpResponse(html)

	options_docment = listPackingOptions.to_dict()
	packing_options = options_docment.get("packing_options")
	packagingoptionsNumber = len(packing_options)
	for option in packing_options:
		packing_option_id = option.get("packing_option_id")
		packing_groups = option.get("packing_groups")
		for packing_group_id in packing_groups:
			listPackingGroupItems = fba_api.list_packing_group_items(inboundPlanId, packing_group_id)
			GroupItems = listPackingGroupItems.to_dict().get("items")

			if packagingoptionsNumber < 1:
				return HttpResponse(listPackingGroupItems, content_type="application/json")
			else:
				sql = f"UPDATE fba_job SET packingGroupId='{packing_group_id}' WHERE id={fj_id}"
				db.update(sql)
		confirmPackingOption = fba_api.confirm_packing_option(inboundPlanId, packing_option_id)
	# return HttpResponse(confirmPackingOption, content_type="application/json")
	sql = f"UPDATE fba_job SET FBA_label_status_2='2' WHERE id={fj_id} "
	db.update(sql)
	show_context = "第二步完成"
	go_url = f"/fulfillmentInbound/step3/?fj_id={fj_id}&self={return_url}"
	context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
	return render(request, "SPAPI_Python_SDK/goto.html", context)

	operation_id = confirmPackingOption.operation_id
	query_params = {
		'page_title': '检测第二步结果',
		'fj_id': fj_id,
		'self': return_url,
		'operationId': operation_id,
		'go_url': '/fulfillmentInbound/step3/'
	}
	encoded_query_params = urlencode(query_params)
	show_context = "第二步完成"
	go_url = f"/fulfillmentInbound/step1-1/?{encoded_query_params}"
	context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
	return render(request, "SPAPI_Python_SDK/goto.html", context)
