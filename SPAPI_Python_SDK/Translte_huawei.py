import requests
from django.http import HttpResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import get_user_key, site_query, get_user_info, common_config
from SPAPI_Python_SDK.login_user_id import session_middleware, login_required_if_no_user_id
from SPAPI_Python_SDK.if_user_id import login_required_if_user_id
from datetime import datetime

import json
import ast

api_key = 'WlxMolZuHjDOXWtmCWKbGZPVInGF7Oxn7B-TRVZD3OP7HzItGFfR5je3B6cl9yfQZYqJ4Za0bsutfclYbcuCNA'  # key
model = 'DeepSeek-V3'
url = "https://infer-modelarts-cn-southwest-2.modelarts-infer.com/v1/infers/fd53915b-8935-48fe-be70-449d76c0fc87/v1/chat/completions"


@login_required_if_no_user_id
def translate_to_cn(request):
	# 用华为的deepseek英译中
	page_title = "英译中"
	dbdata = DB_DATA[1]  # 只有主平台有，其它平台调用主平台的数据
	with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
		sql = "SELECT id,title_en, Highlights_en, des_en FROM collect where collect_status=2 ORDER BY id asc LIMIT 1 "
		rs = db.fetchone(sql)
		if not rs:
			return HttpResponse("翻译完成，请关闭。", status=400)
		
		collect_id = rs["id"]
		
		# model = 'deepseek-chat'
		
		payload = {
			"messages": [
				{
					"content": f"你是一个有15年亚马逊操作经验的运营人员，负责将英文翻译成中文。在翻译过程中要删除品牌词，但保留应用品牌，如：应用于***，适用于***，for***等。保持产品描述清晰，遵守亚马逊政策，优化SEO，文化适应性，专业术语准确，用户友好",
					"role": "system"
				},
				{
					"role": "user",
					"content": f"请将以下Python字典中的值从英文翻译成中文，并返回一个结构与原字典相同的新字典，仅翻译值部分，请确保返回结果仅包含Python字典，不要任何其他文本或代码块：\n\n{rs}"
				}
			],
			"model": model,
			"max_tokens": 4096,
			"temperature": 0.2,
			"stream": False,
			"response_format": {
				"type": "json_schema"
			}
		}
		
		headers = {
			'Content-Type': 'application/json',
			'Accept': 'application/json',
			'Authorization': f'Bearer {api_key}'
		}
		
		# try:
		response = requests.post(url, headers=headers, json=payload)
		response.raise_for_status()
		
		data = response.json()
		translated_text = data['choices'][0]['message']['content']
		translated_json = ast.literal_eval(translated_text)
		title = translated_json.get("title_en", "")
		highlights = translated_json.get("Highlights_en", "")
		des = translated_json.get("des_en", "")
		
		parameters = (title, highlights, des, collect_id)
		sql = "UPDATE collect SET title_cn = %s, Highlights_cn = %s, des_cn = %s, collect_status = 4 WHERE id = %s"
		db.update(sql, parameters)
		
		show_context = "正在翻译，"
		go_url = request.get_full_path()
		context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
		return render(request, "SPAPI_Python_SDK/goto2.html", context)


def translate_to_en(request):
	# 用华为的deepseek中译英
	page_title = "中译英"
	return_url = request.GET.get('self', "")
	pro_id = request.GET.get('pro_id', "")
	site_id = site_query(request, return_url)  # 网站ID
	site_id = 4
	# return HttpResponse(site_id)
	dbdata = DB_DATA[site_id]
	with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
		sql = f"SELECT library_sku.id, library_sku.Pro_id, library_sku.sku_title_cn, library_sku.sku_description, library_sku.sku_des FROM library_sku WHERE Pro_id ={pro_id} ORDER BY id ASC"
		data = db.fetchall(sql)
		if not data:
			return HttpResponse("记录不存在。", status=400)
		
		payload = {
			"messages": [
				{
					"content": f"你是一个有15年亚马逊操作经验的运营人员，负责将中文翻译成英文。在翻译过程中要删除品牌词，但保留应用品牌，如：应用于***，适用于***，for***等。保持产品描述清晰，遵守亚马逊政策，优化SEO，文化适应性，专业术语准确，用户友好",
					"role": "system"
				},
				{
					"role": "user",
					"content": f"请将以下Python字典中的值从中文翻译成英文，并返回一个结构与原字典相同的新字典，仅翻译值部分,请确保返回结果仅包含Python字典，不要任何其他文本或代码块：\n\n{data}"
				}
			],
			"model": model,
			"max_tokens": 4096,
			"temperature": 1.1,
			"stream": False,
			"response_format": {
				"type": "json_schema"
			}
		}
		
		headers = {
			'Content-Type': 'application/json',
			'Accept': 'application/json',
			'Authorization': f'Bearer {api_key}'
		}
		# return HttpResponse(json.dumps(payload), content_type="application/json")
		# try:
		response = requests.post(url, headers=headers, json=payload)
		
		response.raise_for_status()
		
		data_json = response.json()
		translated_text = data_json['choices'][0]['message']['content']
		try:
			translated_json = ast.literal_eval(translated_text)
		except (ValueError, SyntaxError) as e:
			# return HttpResponse("此记录有错误。", content_type="application/json")
			return HttpResponse(json.dumps(translated_text), content_type="application/json")
		# return HttpResponse(json.dumps(translated_json), content_type="application/json")
		for translated in translated_json:
			sku_id = translated.get("id", "")  # 要翻译的SKU ID
			sku_title_en = translated.get("sku_title_cn", "")  # 翻译过的英文标题
			sku_des_en = translated.get("sku_des", "")  # 翻译后的英文亮点
			sku_description_en = translated.get("sku_description", "")  # 翻译后的英文描述
			
			parameters = (sku_title_en, sku_description_en, sku_des_en, sku_id)
			sql = "UPDATE library_sku SET sku_title_en = %s, sku_description_en = %s, sku_des_en = %s WHERE id = %s"
			print(parameters)
			print(sql)
			db.update(sql, parameters)
			
			now_date = datetime.now()
			sql = f"UPDATE library_produc SET status = 4,Translation_date='{now_date}' WHERE id = {pro_id}"
			db.update(sql)
			
			sql = f"INSERT INTO library_produc_log(LP_id, LP_status, data_log, des)VALUES({pro_id}, 4, '{now_date}', '已翻译')"
			db.insert(sql)
		
		show_context = "翻译完成，请关闭。"
		return HttpResponse(show_context, status=200)

# go_url = request.get_full_path()
# context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
# return render(request, "SPAPI_Python_SDK/goto2.html", context)
