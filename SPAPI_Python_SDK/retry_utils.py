# retry_utils.py
import logging
from django.shortcuts import render
import os
import re
from collections import deque
from django.http import HttpResponse, HttpResponseForbidden
from django.conf import settings

from tenacity import (
	retry,
	wait_exponential,
	stop_after_attempt,
	retry_if_exception_type,
	before_sleep_log
)
from requests.exceptions import HTTPError, ConnectionError, Timeout

logger = logging.getLogger('debug_logger')


def api_retry_decorator(max_retries=3):
	"""
	创建通用的API重试装饰器
	:param max_retries: 最大重试次数
	"""
	return retry(
		wait=wait_exponential(multiplier=2, min=5, max=60),
		stop=stop_after_attempt(max_retries),
		retry=retry_if_exception_type((ConnectionError, Timeout, HTTPError)),
		before_sleep=before_sleep_log(logger, logging.WARNING),
		reraise=True
	)


def handle_api_errors(func):
	"""
	统一处理API错误的装饰器
	"""
	
	def wrapper(*args, **kwargs):
		try:
			return func(*args, **kwargs)
		except Exception as e:
			if not isinstance(e, (HTTPError, ConnectionError, Timeout)):
				# 将非预期异常转换为 HTTPError
				raise HTTPError(f"API请求底层错误: {str(e)}") from e
			raise
	
	return wrapper


def log_view(request):
	log_file = os.path.join(settings.BASE_DIR, 'logs', 'debug.log')  # 更可靠的路径写法
	if not os.path.exists(log_file):
		return HttpResponse(f"日志文件路径: {log_file} 未找到", status=404)
	
	# 检查读取权限
	if not os.access(log_file, os.R_OK):
		return HttpResponseForbidden("无权限查看日志。")
	
	# 读取最后100行日志
	log_entries = []
	current_entry = []
	entry_pattern = re.compile(r'^(INFO|ERROR|WARNING|DEBUG)\s+')
	with open(log_file, 'r') as f:
		for line in deque(f, maxlen=1000):
			line = line.rstrip('\n')  # 保留换行符前的所有内容
			if entry_pattern.match(line):
				if current_entry:
					log_entries.append(current_entry)
				current_entry = [line]
			else:
				if current_entry:
					current_entry.append(line)
		
		# 添加最后一个条目
		if current_entry:
			log_entries.append(current_entry)
	return render(request, 'SPAPI_Python_SDK/view_logs.html', {'log_entries': log_entries[::-1]})  # 倒序显示最新日志在前
