import requests
from django.http import HttpResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import get_user_key, site_query, get_user_info, common_config
from SPAPI_Python_SDK.login_user_id import session_middleware, login_required_if_no_user_id
from SPAPI_Python_SDK.if_user_id import login_required_if_user_id
from datetime import datetime
import json
import ast

api_key = 'WlxMolZuHjDOXWtmCWKbGZPVInGF7Oxn7B-TRVZD3OP7HzItGFfR5je3B6cl9yfQZYqJ4Za0bsutfclYbcuCNA'  # key
model = 'DeepSeek-R1'
url = "https://infer-modelarts-cn-southwest-2.modelarts-infer.com/v1/infers/952e4f88-ef93-4398-ae8d-af37f63f0d8e/v1/chat/completions"


def Search_Term(request):
	# 用华为的deepseek英译中
	page_title = "生成亚马逊Search Term"
	return_url = request.GET.get('self', "")
	pro_id = request.GET.get('pro_id', "")
	site_id = site_query(request, return_url)  # 网站ID
	site_id = 4
	# return HttpResponse(site_id)
	dbdata = DB_DATA[site_id]
	with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
		sql = f"SELECT library_sku.id, library_sku.Pro_id, library_sku.sku_title_en, library_sku.sku_description_en, library_sku.sku_des_en FROM library_sku WHERE Pro_id ={pro_id} ORDER BY id ASC"
		rs = db.fetchone(sql)
		if not rs:
			return HttpResponse("记录不存在。", status=400)

		pro_info = {
			"产品名称": rs["sku_title_en"],
			"产品亮点": rs["sku_des_en"],
			"产品描述": rs["sku_description_en"],
		}
		payload = {
			"messages": [
				{
					"content": """ 亚马逊Search Term生成协议
									目标：
									通过数据驱动的关键词工程，实现算法友好、跨文化兼容的搜索词组合，确保250字节内最大化曝光效率
									
									输入规范
									▢ 必填字段
									标题：完整产品标题（含核心参数）
									核心参数：尺寸/材质/认证标准（如44mm/PVC/CE）
									场景清单：至少3个使用场景（如home theater/office/gaming）
									
									▢ 推荐工具
									• 关键词挖掘：Helium10 Magnet + Cerebro
									• 竞争分析：Jungle Scout Keyword Scout
									• 趋势验证：MerchantWords + Google Trends
									
									生成引擎七步法
									1. 核心词锚定
									• 从标题提取2-3个核心名词（如cable hider）
									• 验证标准：Helium10搜索量＞1500 & BSR前50竞品覆盖率＞60%
									
									2. 参数强化
									• 提取尺寸/颜色/材质（44mm/white/PVC）
									• 验证标准：SellerApp搜索量＞800 & 竞争度＜0.5
									
									3. 场景嫁接
									• 植入"for [场景]"结构（如for home theater）
									• 数据要求：MerchantWords趋势指数＞75
									
									4. 欧洲合规改造
									• 公制单位优先（28cm/11inch）
									• 强制添加认证标注（certified CE/RoHS）
									
									5. 痛点解决方案词
									• 生成问题解决型词汇（cord concealer/wire organizer）
									• 来源：Cerebro竞品长尾词（使用率＞40%）
									
									6. 竞品反制策略
									• 逆向TOP10竞品关键词（排除垄断词：占有率＞75%）
									• 工具：Jungle Scout Competitive Intelligence
									
									7. 字节级压缩
									• 删除重复词 & 停用词（the/and）
									• 编码优化：优先使用短拼写（organizer＞organisation）""",
					"role": "system"
				},
				{
					"role": "user",
					"content": f"要求只输出结果并删除开始结尾符，不要其它，产品信息如下\n\n{pro_info}"
				}
			],
			"model": model,
			"max_tokens": 4096,
			"temperature": 0.5,
			"stream": False,
			"response_format": {
				"type": "json_schema"
			}
		}

		headers = {
			'Content-Type': 'application/json',
			'Accept': 'application/json',
			'Authorization': f'Bearer {api_key}'
		}
		# return HttpResponse(json.dumps(payload), content_type="application/json")
		# try:
		response = requests.post(url, headers=headers, json=payload)

		response.raise_for_status()

		data_json = response.json()
		Core_Long_word = data_json['choices'][0]['message']['content']
		# return HttpResponse(Core_Long_word)

		# Core_Long_words = ast.literal_eval(Core_Long_word)

		try:
			parameters = (12, Core_Long_word, pro_id)
			sql = "UPDATE library_produc SET status=%s, pro_Keyword=%s WHERE id=%s"
			db.update(sql, parameters)
			up_date = datetime.now()
			sql = f"INSERT INTO library_produc_log(LP_id, LP_status, data_log, des)VALUES({pro_id}, 12, '{up_date}', '关键词已添加')"
			db.insert(sql)
		# translated_json = ast.literal_eval(Core_Long_words)
		except (ValueError, SyntaxError) as e:
			# return HttpResponse("此记录有错误。", content_type="application/json")
			return HttpResponse(Core_Long_word, content_type="application/json")
		# return HttpResponse(json.dumps(Core_Long_words), content_type="application/json")

		show_context = "关键词生成完成，请关闭"
		return HttpResponse(show_context, status=200)

# go_url = request.get_full_path()
# context = {"show_context": show_context, "go_url": go_url, "page_title": page_title}
# return render(request, "SPAPI_Python_SDK/goto2.html", context)
