import os
import re
import requests
import json
from django.http import HttpResponse, JsonResponse
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.login_user_id import session_middleware, login_required_if_no_user_id
from django.shortcuts import render
from datetime import datetime
from pprint import pprint
from SPAPI_Python_SDK.config import get_user_key, get_user_id, site_query, get_user_info, common_config
from spapi.spapiclient import SPAPIClient


# 函数：发送POST请求,oxylabs 版本
def collect_data(url):
    """
    发送POST请求到API，并返回结果
    :param url: URL
    :return: 返回字典品牌、亮点、描述、图片、价格、标题
    """
    payload = {
        'source': 'amazon',
        'url': url,
        'parse': True
    }

    # 贾乐申请
    oxylabs_user = "dulijia_Ejlff"
    oxylabs_password = "Rm2wpBC5YZDK9Z5_"
    # 李瑞斌申请
    oxylabs_user = "binbin695_hfpPY"
    oxylabs_password = "Rm2wpBC5YZDK9Z5_"

    response = requests.request(
        'POST',
        'https://realtime.oxylabs.io/v1/queries',
        auth=(oxylabs_user, oxylabs_password),  # 用户密码
        json=payload,
    )
    # 提取目标数据
    if response.status_code == 200:
        json_response = response.json()
        pprint(json_response)
        result = json_response['results'][0]['content']
        try:
            array_category = result['category'][0]['ladder']
            category = ">".join([item['name'] for item in array_category])
        except (KeyError, IndexError, TypeError):
            category = ""
        # pprint(result)
        asin = result.get('asin', "")
        anazom_url = f"https://www.amazon.com/dp/{asin}"
        brand = result.get('brand', "")
        bullet_points = result.get('bullet_points')  # 亮点
        description = result.get('description')
        images = result['images'][0]
        price = result['price']
        product_name = result['product_name']

        bullet_points = remove_brand_from_text(bullet_points, brand)
        description = remove_brand_from_text(description, brand)
        product_name = remove_brand_from_text(product_name, brand)
        # pprint(result)
        image_path = download_and_save_image(images)
        outcome = {
            "code": 200,
            "anazom_url": anazom_url,
            "brand": brand,
            "bullet_points": bullet_points,
            "category": category,
            "description": description,
            "images": image_path,
            "price": price,
            "product_name": product_name
        }
    # pprint(outcome)
    else:
        # 错误返回代码就可以了
        outcome = {
            "code": response.status_code,
        }

    return outcome


def collect_data_amazon_api(url):
    """
    发送POST请求到亚马逊API，并返回结果
    :param url: URL
    :return: 返回字典品牌、亮点、描述、图片、价格、标题

    这个采集只用RL平台的RL账号来采集
    """
    asin = extractAmazonAsin(url)
    anazom_url = f"https://www.amazon.com/dp/{asin}"
    dbdata = DB_DATA[2]
    db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"],
                  dbdata["DB_USER"], dbdata["DB_PWD"])
    user_id = 3
    user_data = get_user_info(db, user_id)

    refresh_token = user_data['refresh_token']
    config = common_config(refresh_token)

    marketplace_id = ["ATVPDKIKX0DER"]
    includedData = ["attributes", "classifications", "images", "summaries"]
    locale = "en_US"

    api_client = SPAPIClient(config, "swagger_client_catalogItems")

    catalog_api = api_client.get_api_client('CatalogApi')

    CatalogItem = catalog_api.get_catalog_item(
        asin, marketplace_id, included_data=includedData, locale=locale)
    CatalogItem_dict = CatalogItem.to_dict()
    # return JsonResponse(CatalogItem_dict, json_dumps_params={'indent': 2}, status=200)
    attributes = CatalogItem_dict.get('attributes', {})

    if attributes:
        bullet_points = attributes.get("bullet_point", [])
        bullet_point = '\n'.join(
            [item.get("value", "") for item in bullet_points]) if bullet_points else None

        product_description = attributes.get("product_description", [])
        product_description = '\n'.join([item.get(
            "value", "") for item in product_description]) if product_description else None

        brand = attributes.get("brand", [{}])[0].get("value", None)
        item_name = attributes.get("item_name", [{}])[0].get("value", None)
    classifications = CatalogItem_dict.get('classifications', {})
    if classifications:
        display_names = []
        for market_class in classifications:
            for classification in market_class.get('classifications', []):
                current = classification
                while current:
                    display_names.append(current['displayName'])
                    current = current.get('parent')
        # 先倒序再连接
        display_names.reverse()
        category = " > ".join(display_names)
    else:
        category = ""

    images = CatalogItem_dict.get('images', {})
    if images:
        # 获取第一个marketplace中的第一个image的link
        img = images[0]["images"][0]["link"]
        img_path = download_and_save_image(img)
    else:
        img = ""
    outcome = {
        "code": 200,
        "anazom_url": anazom_url,
        "brand": brand,
        "bullet_points": remove_brand_from_text(bullet_point, brand),
        "category": category,
        "description": remove_brand_from_text(product_description, brand),
        "images": img_path,
        "price": 0.00,
        "product_name": remove_brand_from_text(item_name, brand)
    }
    db.close()
    return outcome


def remove_brand_from_text(text, brand):
    """
    移除文本中的品牌名称（若参数无效直接返回原文本）

    参数:
                    text (str): 原始文本，接受 None 或空字符串
                    brand (str): 需要移除的品牌名称，接受 None 或空字符串

    返回:
                    str: 处理后的文本（当 text 或 brand 为空时返回原 text）

    示例:
                    >>> remove_brand_from_text("苹果手机iPhone14", "苹果")
                    '手机iPhone14'
                    >>> remove_brand_from_text("测试文本", "")
                    '测试文本'
                    >>> remove_brand_from_text(None, "苹果")
                    None
    """
    # 联合验证：确保 text 和 brand 均为非空字符串
    if not isinstance(text, str) or not text.strip():
        return text
    if not isinstance(brand, str) or not brand.strip():
        return text

    # 执行替换操作（自动处理多个匹配项）
    return re.sub(re.escape(brand), "", text, flags=re.IGNORECASE)


def extractAmazonAsin(url):
    # 预处理：移除URL锚点和多余参数
    clean_url = re.sub(r'#.*$', '', url)

    # 主匹配流程
    patterns = [
        (r'/(?:dp|gp/product|exec/obidos/asin|gp/aw/d)/([A-Z0-9]{10})', 1),
        (r'^https?://[^/]+/([A-Z0-9]{10})', 1),  # 短格式
        (r'[?&](?:asin|productID)=([A-Z0-9]{10})', 1)  # 参数形式
    ]

    for pattern, group in patterns:
        match = re.search(pattern, clean_url, re.IGNORECASE)
        if match:
            potential_asin = match.group(group).upper()
            if re.match(r'^[A-Z0-9]{10}$', potential_asin):
                return potential_asin

    return False


def download_and_save_image(image_url):
    import random

    # home_dir = "X:\\web\\site"  # 本地测试目录
    home_dir = "C:\\web\\MPR"  # 服务器地址
    img_dir = "upimage\\images"
    current_date = datetime.now().strftime('%Y%m')
    image_dir = os.path.join(home_dir, img_dir, current_date)
    if not os.path.exists(image_dir):
        os.makedirs(image_dir)

    # 图片的本地路径
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    random_number = f"{random.randint(0, 9999):04d}"
    image_name = f"{timestamp}{random_number}.jpg"
    image_path = os.path.join(image_dir, image_name)

    # 下载图片
    response = requests.get(image_url)
    if response.status_code == 200:
        with open(image_path, 'wb') as f:
            f.write(response.content)

        local_path = image_path.replace(home_dir, "").replace('\\', '/')
        return local_path  # 返回本地图片路径
    else:
        # 如果下载失败，可以记录日志或抛出异常
        # log_error(f"Failed to download image from {image_url}")
        return None


@session_middleware
@login_required_if_no_user_id
def collect(request):
    page_title = "亚马逊采集"
    dbdata = DB_DATA[1]  # 只有主平台有，其它平台调用主平台的数据
    Assistant_id = request.GET.get('Assistant_id')
    return_url = request.GET.get('self', "1")
    c_id = request.GET.get("c_id", "")
    # try:
    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
        if c_id:
            sql = "SELECT collect.id, collect.anazom_url FROM collect WHERE id=%s and Assistant_id=%s ORDER BY id desc LIMIT 1"
            rs = db.fetchone(sql, (c_id, Assistant_id))
        else:
            sql = "SELECT collect.id, collect.anazom_url FROM collect WHERE collect_status=%s and Assistant_id=%s ORDER BY id desc LIMIT 1"
            rs = db.fetchone(sql, (-1, Assistant_id))

            # 检查有没有需要更新的价格
            sql = "SELECT id, anazom_url FROM collect WHERE collect_status=6 ORDER BY id DESC LIMIT 20"
            asin_data = db.fetchall(sql)

            # 如果没有新的采集任务，并且没有需要更新的价格，等待60秒继续采集
            if not asin_data and not rs:
                show_context = "采集完成,没有新的采集任务等待中……"
                go_url = request.get_full_path()
                second = 60*5
                context = {"show_context": show_context,
                           "go_url": go_url, "page_title": page_title, "second": second}
                return render(request, "SPAPI_Python_SDK/goto.html", context)

        if not rs:
            # show_context = "正在采集。"
            # go_url = "http://mpr.hkzhenshang.com/collection/up_url.php"
            # go_url = request.get_full_path()
            show_context = "开始采集价格，正在跳转..."
            go_url = f"https://py.hkzhenshang.com/getItemOffersBatch/?self={return_url}"
            context = {"show_context": show_context,
                       "go_url": go_url, "page_title": page_title}
            return render(request, "SPAPI_Python_SDK/goto.html", context)

        url = rs['anazom_url']
        # result = collect_data(url)
        result = collect_data_amazon_api(url)
        # return HttpResponse(json.dumps(result), content_type='application/json')
        code = result["code"]
        if code == 200:
            param = ["6", result["anazom_url"], result["category"], result["product_name"], result["price"],
                     result["brand"], result["images"], result["bullet_points"], result["description"], rs["id"]]

            # return HttpResponse(json.dumps(param), content_type="application/json")
            sql = "UPDATE collect SET collect_status=%s, anazom_url=%s, category=%s, title_en=%s, price=%s, brand=%s, images=%s, highlights_en=%s, des_en=%s WHERE id=%s"
            db.update(sql, param)
            if c_id:
                # show_context = "采集完成。"
                # go_url = "http://mpr.hkzhenshang.com/collection/up_url.php"
                show_context = "开始采集价格，正在跳转..."
                go_url = f"https://py.hkzhenshang.com/getItemOffersBatch/?self={return_url}"
            # go_url = request.get_full_path()
            else:

                show_context = "正在采集……"
                # go_url = "http://mpr.hkzhenshang.com/collection/up_url.php"
                go_url = request.get_full_path()
            context = {"show_context": show_context,
                       "go_url": go_url, "page_title": page_title}
            return render(request, "SPAPI_Python_SDK/goto.html", context)

        else:
            return HttpResponse(json.dumps(result), content_type='application/json')
