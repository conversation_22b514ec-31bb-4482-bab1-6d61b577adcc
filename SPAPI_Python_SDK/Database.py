import time
from mysql.connector import Error, OperationalError

MPR_DB_HOST = "************"
MPR_DB_USER = "ad_site"
MPR_DB_PWD = "ad_site0161"
MPR_DB_NAME = "zom_ems"

RL_DB_HOST = "************"
RL_DB_USER = "ad_site"
RL_DB_PWD = "ad_site0161"
RL_DB_NAME = "super_site"

EO_DB_HOST = "************"
EO_DB_USER = "ad_site"
EO_DB_PWD = "ad_site0161"
EO_DB_NAME = "eo_site"

ZZ_DB_HOST = "************"
ZZ_DB_USER = "ad_site"
ZZ_DB_PWD = "ad_site0161"
ZZ_DB_NAME = "zz_site"

WH_DB_HOST = "************"
WH_DB_USER = "ad_site"
WH_DB_PWD = "ad_site0161"
WH_DB_NAME = "wh_site"

DB_DATA = {
    1: {
        "DB_PWD": MPR_DB_PWD,
        "DB_HOST": MPR_DB_HOST,
        "DB_USER": MPR_DB_USER,
        "DB_NAME": MPR_DB_NAME
    },
    2: {
        "DB_PWD": RL_DB_PWD,
        "DB_HOST": RL_DB_HOST,
        "DB_USER": RL_DB_USER,
        "DB_NAME": RL_DB_NAME
    },
    3: {
        "DB_PWD": EO_DB_PWD,
        "DB_HOST": EO_DB_HOST,
        "DB_USER": EO_DB_USER,
        "DB_NAME": EO_DB_NAME
    },
    4: {
        "DB_PWD": ZZ_DB_PWD,
        "DB_HOST": ZZ_DB_HOST,
        "DB_USER": ZZ_DB_USER,
        "DB_NAME": ZZ_DB_NAME

    },
    6: {
        "DB_PWD": WH_DB_PWD,
        "DB_HOST": WH_DB_HOST,
        "DB_USER": WH_DB_USER,
        "DB_NAME": WH_DB_NAME
    }
}

CLIENT_DATA = {
    "client_id": "amzn1.application-oa2-client.7b951f0bb86b429aad2ee59d88748854",
    "client_secret": "amzn1.oa2-cs.v1.d6e336c2bac2510df69e5763ca08a179a985e8afbebea1e087b950a1186f36f6"
}

import mysql.connector
from mysql.connector import Error


class Database:
    def __init__(self, host=MPR_DB_HOST, database=MPR_DB_NAME, user=MPR_DB_USER, password=MPR_DB_PWD):
        self.host = host
        self.database = database
        self.user = user
        self.password = password
        self._connect()

    def check_connection(self):
        """连接健康检查"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            return True
        except (OperationalError, AttributeError):
            self.reconnect()
            return False

    def reconnect(self):
        """带指数退避的智能重连"""
        reconnect_attempts = [
            (1, 2),  # 第1次重试：等待2秒
            (2, 5),  # 第2次重试：等待5秒
            (3, 10)  # 第3次重试：等待10秒
        ]

        for attempt, delay in reconnect_attempts:
            try:
                if self.connection.is_connected():
                    self.connection.close()

                self._connect()
                print(f"成功重连 (第{attempt}次尝试)")
                return
            except Exception as e:
                print(f"重连失败 (第{attempt}次): {str(e)}")
                time.sleep(delay)

        raise ConnectionError("数据库重连失败，已达最大重试次数")

    def _connect(self):
        """私有方法，用于连接到MySQL数据库。"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password,
                autocommit=False  # 关闭自动提交，以便手动管理事务
            )
            if self.connection.is_connected():
                print("连接成功")
        except Error as e:
            print(f"连接错误: {e}")
            raise

    def close(self):
        """关闭数据库连接。"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("连接已关闭")

    def start_transaction(self):
        """开始一个新的事务。"""
        self.connection.start_transaction()

    def commit(self):
        """提交当前事务。"""
        self.connection.commit()

    def rollback(self):
        """回滚当前事务。"""
        self.connection.rollback()

    def execute(self, query, params=None):
        """执行单条SQL语句，不返回结果。"""
        try:
            with self.connection.cursor(dictionary=True) as cursor:
                cursor.execute(query, params)
            self.connection.commit()
        except Error as e:
            self.connection.rollback()
            print(f"执行错误: {e}")
            raise

    def executemany(self, query, param_list):
        """执行多条SQL语句，常用于批量插入或更新。"""
        try:
            with self.connection.cursor(dictionary=True) as cursor:
                cursor.executemany(query, param_list)
            self.connection.commit()
        except Error as e:
            self.connection.rollback()
            print(f"批量执行错误: {e}")
            raise

    def fetchall(self, query, params=None):
        """执行查询并返回所有结果。"""
        try:
            with self.connection.cursor(dictionary=True) as cursor:
                cursor.execute(query, params)
                result = cursor.fetchall()
            return result
        except Error as e:
            print(f"查询错误: {e}")
            raise

    def fetchone(self, query, params=None):
        """执行查询并返回第一条结果。"""
        try:
            with self.connection.cursor(dictionary=True) as cursor:
                cursor.execute(query, params)
                result = cursor.fetchone()
            return result
        except Error as e:
            print(f"查询错误: {e}")
            raise

    def insert(self, query, params=None):
        """执行插入操作并返回最后插入行的ID。"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                last_id = cursor.lastrowid
            self.connection.commit()
            return last_id
        except Error as e:
            self.connection.rollback()
            print(f"插入错误: {e}")
            raise

    def bulk_insert(self, query, param_list):
        """执行批量插入操作，param_list是参数的列表。"""
        try:
            with self.connection.cursor() as cursor:
                cursor.executemany(query, param_list)
            self.connection.commit()
            return cursor.rowcount
        except Error as e:
            self.connection.rollback()
            print(f"批量插入错误: {e}")
            raise

    def update(self, query, params=None):
        """执行更新操作，返回受影响的行数。"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
            self.connection.commit()
            return cursor.rowcount
        except Error as e:
            self.connection.rollback()
            print(f"更新错误: {e}")
            raise

    def bulk_update(self, query, param_list):
        """执行批量更新操作，param_list是参数的列表。"""
        try:
            with self.connection.cursor() as cursor:
                cursor.executemany(query, param_list)
            self.connection.commit()
            return cursor.rowcount
        except Error as e:
            self.connection.rollback()
            print(f"批量更新错误: {e}")
            raise

    def safe_bulk_operation(self, operation_type, query, param_list, batch_size=500):
        """
        通用的安全批量操作方法
        :param operation_type: 'update' 或 'insert'
        :param query: SQL模板
        :param param_list: 参数列表
        :param batch_size: 单批次处理量
        """
        success_count = 0
        total = len(param_list)

        for i in range(0, total, batch_size):
            batch_params = param_list[i:i + batch_size]
            retry_count = 0

            while retry_count < 3:
                try:
                    self.check_connection()  # 操作前检查连接

                    with self.connection.cursor() as cursor:
                        cursor.executemany(query, batch_params)
                        success_count += cursor.rowcount

                    self.connection.commit()
                    print(f"已处理 {min(i + batch_size, total)}/{total} 条记录")
                    break

                except (mysql.connector.OperationalError,
                        mysql.connector.InterfaceError) as e:
                    self.reconnect()
                    retry_count += 1
                    print(f"操作中断，开始第 {retry_count} 次重试")
                    time.sleep(2 ** retry_count)

                except Exception as e:
                    self.connection.rollback()
                    raise RuntimeError(f"批量操作失败: {str(e)}")

        return success_count

    def delete(self, query, params=None):
        """执行删除操作，返回受影响的行数。"""
        return self.update(query, params)

    def __enter__(self):
        """支持上下文管理协议。"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文时自动关闭连接。"""
        if exc_type:
            self.connection.rollback()
        else:
            self.connection.commit()
        self.close()
