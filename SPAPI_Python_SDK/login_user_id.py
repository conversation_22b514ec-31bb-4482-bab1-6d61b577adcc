import json

from django.contrib.auth.decorators import user_passes_test, login_required
from django.http import HttpResponseRedirect, HttpResponse
from django.urls import reverse
from urllib.parse import urlparse
import os

from django.utils.decorators import decorator_from_middleware

GLOBAL_IP_AUTO = "*************"
SESSION_PATHS = {
	'mpr.hkzhenshang.com': 'C:\\web\\MPR\\session',
	'rl.hkzhenshang.com': 'C:\\web\\RL\\session',
	'eo.hkzhenshang.com': 'C:\\web\\EO\\session',
	'zz.hkzhenshang.com': 'C:\\web\\zz_site\\session',
	'ad.hkzhenshang.com': 'C:\\web\\ad_site\\session',
	'wh.hkzhenshang.com': 'C:\\web\\WH\\session',
}

SESSION_SITE_ID = {
	'mpr.hkzhenshang.com': '1',
	'rl.hkzhenshang.com': '2',
	'eo.hkzhenshang.com': '3',
	'zz.hkzhenshang.com': '4',
	'ad.hkzhenshang.com': '5',
	'wh.hkzhenshang.com': '6',
}


def set_session(request):
	Return_URL = request.GET.get('self', "1")
	session_id = request.GET.get('s_id', None)  # 获取s_id，如果不存在则为None
	if not session_id:  # 如果s_id为空，则跳过
		return None
	domain = get_domain(Return_URL)
	session_save_path = SESSION_PATHS.get(domain, 'C:/web/site/session')
	# session_save_path = SESSION_PATHS.get(domain, 'X:/web/site/session')
	# 查找session文件
	session_file_path = os.path.join(session_save_path, 'sess_' + session_id)
	
	try:
		with open(session_file_path, 'r', encoding='utf-8') as file:
			session_data = file.read()
	except (FileNotFoundError, IOError) as e:
		print(f"Error reading session file: {e}")
		session_data = ""
	
	if session_data:
		session_vars = parse_php_session_data(session_data)
		# return HttpResponse(session_vars)
		# print(session_vars)
		if "user_name" in session_vars:
			request.session["user_name"] = session_vars["user_name"]
			request.session["user_id"] = session_vars["user_id"]
			return SESSION_SITE_ID.get(domain, "1")
		elif "partner_name" in session_vars:
			request.session["user_name"] = session_vars["partner_name"]
			request.session["user_id"] = session_vars["partner_id"]
			return SESSION_SITE_ID.get(domain, "1")
		elif "finance_name" in session_vars:
			request.session["user_name"] = session_vars["finance_name"]
			request.session["user_id"] = session_vars["finance_id"]
			return SESSION_SITE_ID.get(domain, "1")
		else:
			request.session.clear()
			session_vars = ""
			return session_vars
	else:
		request.session.clear()
		session_vars = ""
		return session_vars


def get_domain(url):
	parsed_uri = urlparse(url)
	domain = parsed_uri.netloc
	return domain


def get_ip_address(request):
	ip = (
			request.META.get('HTTP_CLIENT_IP')
			or request.META.get('HTTP_X_FORWARDED_FOR')
			or request.META.get('HTTP_X_FORWARDED')
			or request.META.get('HTTP_FORWARDED_FOR')
			or request.META.get('HTTP_FORWARDED')
			or request.META.get('REMOTE_ADDR')
	)
	return ip


def parse_php_session_data(session_data_str):
	session_data = {}
	
	# 分割字符串为多个session变量
	session_vars = session_data_str.split(';')
	
	for var in session_vars:
		if var:
			# 分割每个变量为key、type和数据
			parts = var.split('|')
			if len(parts) < 2:
				continue
			key, type_ = parts[0], parts[1]
			if len(type_.split(':')) < 3:
				continue
			# 移除key前缀'sess_'
			key = key[5:]
			# 处理数据
			value_type, value_num, value_data = type_.split(':', 2)
			# 根据类型转换值
			if value_type == 's':
				value = value_data.replace('\"', '')
			elif value_type == 'i':
				value = int(value_data)
			elif value_type == 'b':
				value = value_data.lower() == 'true'
			
			session_data[key] = value
	return session_data


def session_middleware(get_response):
	def middleware(request):
		# 调用set_session来设置会话
		site_id = set_session(request)
		request.site_id = site_id
		if site_id is not None:
			request.site_id = site_id
		# 继续处理请求
		response = get_response(request)
		return response
	
	return middleware


# 创建一个装饰器，它将检查会话中是否有user_id
def login_required_if_no_user_id(view_func):
	# 定义一个内部视图函数，它将包装view_func
	def _wrapped_view(request, *args, **kwargs):
		# 检查是否有go_url参数
		ip = get_ip_address(request)
		if ip != GLOBAL_IP_AUTO:
			print(request.session.get('user_id'))
			# 如果用户未登录，重定向到go_url参数指定的路径
			
			if not request.session.get('user_id'):
				return HttpResponse("没有登陆")
		# 调用包装的视图函数
		return view_func(request, *args, **kwargs)
	
	# 返回内部视图函数
	return _wrapped_view


# 使用Django内置的login_required装饰器
# @login_required
# def some_view(request):
#     # 如果用户已经登录，会话中会有user_id
#     user_id = request.session.get('user_id', "0")
#     return HttpResponse(f"User ID: {user_id}")

session_decorator = decorator_from_middleware(session_middleware)

# 在settings.py中注册中间件
# MIDDLEW
