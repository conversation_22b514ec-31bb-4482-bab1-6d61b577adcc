<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ page_title }}</title>
</head>
<body>
<div>{{ show_context }}<span id='show'></span>秒后跳转</div>
<script type="text/javascript">
    {% if second %}
        var i = {{ second }}
    {% else %}
        var i = Math.floor(Math.random() * (16 - 4 + 1)) + 4;
    {% endif %}
    function show() {
        i -= 1;
        document.getElementById('show').innerHTML = i;
        if (i == 1) {
            window.location.href = '{{ go_url|safe }}';
        }
        window.setTimeout("show()", 1000);
    }

    show();
</script>
<a href="{{ go_url|safe }}">没有跳转点击这里</a>
</body>
</html>