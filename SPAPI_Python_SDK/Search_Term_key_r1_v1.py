import requests
from django.http import HttpResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import get_user_key, site_query, get_user_info, common_config
from SPAPI_Python_SDK.login_user_id import session_middleware, login_required_if_no_user_id
from datetime import datetime
import json
import ast

api_key = 'sk-b4fe58c3d284492b8dd9c1eb3837c2a5'
model = 'deepseek-reasoner'
url = "https://api.deepseek.com/chat/completions"

from typing import List

MAX_BYTES = 250  # Amazon SearchTerms 上限


def trim_keywords_to_bytes(words: List[str], max_bytes: int = MAX_BYTES) -> str:
    """
    逐词累加，保证最终 UTF-8 字节数 ≤ max_bytes。
    空格作分隔；遇到超限即停止。
    """
    chosen: List[str] = []
    for w in words:
        tentative = (" ".join(chosen + [w])).strip()
        if len(tentative.encode("utf-8")) <= max_bytes:
            chosen.append(w)
        else:
            break  # 再加就超了
    return " ".join(chosen)


def generate_amazon_search_terms(rs):
    """
    生成亚马逊Search Term关键词（优化后仅接收已查询的产品数据）

    参数:
        rs: 从数据库获取的产品数据（需包含sku_title_en, sku_description_en, sku_des_en字段）

    返回:
        str: 生成的Search Term关键词字符串（250字节内）
        或 None（无数据/请求失败时）
    """
    # 验证rs有效性
    for f in ("sku_title_en", "sku_description_en", "sku_des_en"):
        if f not in rs:
            print(f"{f} 缺失")
            return None

    pro_info = {
        "title": rs["sku_title_en"],
        "bullet": rs["sku_des_en"],
        "desc": rs["sku_description_en"],
    }
    # 构造API请求体
    payload = {
        "messages": [
            {
                "content": """ 亚马逊Search Term生成协议
                                目标：
                                通过数据驱动的关键词工程，实现算法友好、跨文化兼容的搜索词组合，确保220字节内最大化曝光效率

                                输入规范
                                ▢ 必填字段
                                标题：完整产品标题（含核心参数）
                                核心参数：尺寸/材质/认证标准（如44mm/PVC/CE）
                                场景清单：至少3个使用场景（如home theater/office/gaming）

                                ▢ 推荐工具
                                • 关键词挖掘：Helium10 Magnet + Cerebro
                                • 竞争分析：Jungle Scout Keyword Scout
                                • 趋势验证：MerchantWords + Google Trends

                                生成引擎七步法
                                1. 核心词锚定
                                • 从标题提取2-3个核心名词（如cable hider）
                                • 验证标准：Helium10搜索量＞1500 & BSR前50竞品覆盖率＞60%

                                2. 参数强化
                                • 提取尺寸/颜色/材质（44mm/white/PVC）
                                • 验证标准：SellerApp搜索量＞800 & 竞争度＜0.5

                                3. 场景嫁接
                                • 植入"for [场景]"结构（如for home theater）
                                • 数据要求：MerchantWords趋势指数＞75

                                4. 欧洲合规改造
                                • 公制单位优先（28cm/11inch）
                                • 强制添加认证标注（certified CE/RoHS）

                                5. 痛点解决方案词
                                • 生成问题解决型词汇（cord concealer/wire organizer）
                                • 来源：Cerebro竞品长尾词（使用率＞40%）

                                6. 竞品反制策略
                                • 逆向TOP10竞品关键词（排除垄断词：占有率＞75%）
                                • 工具：Jungle Scout Competitive Intelligence

                                7. 字节级压缩
                                • 删除重复词 & 停用词（the/and）
                                • 编码优化：优先使用短拼写（organizer＞organisation）
                                """,
                "role": "system"
            },
            {
                "role": "user",
                "content": f"要求只输出结果并删除开始结尾符，不要其它，产品信息如下\n\n{pro_info}"
            }
        ],
        "model": model,
        "max_tokens": 4096,
        "temperature": 0.6,
        "top_p": 1,
        "frequency_penalty": 0,
        "presence_penalty": 0,
        "response_format": {"type": "json_object"}
    }

    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }

    try:
        # 发送API请求
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        kw_list = json.loads(response.json()["choices"][0]["message"]["content"])
        if not isinstance(kw_list, list):
            raise ValueError("模型返回格式异常")

        search_terms = trim_keywords_to_bytes([str(w).strip() for w in kw_list])
        print("最终字节：", len(search_terms.encode("utf-8")))  # 调试用
        return search_terms
    except requests.exceptions.HTTPError as err:
        print(f"API请求错误: {err}")
        return None
    except requests.exceptions.RequestException as err:
        print(f"网络错误: {err}")
        return None


import requests
from django.http import HttpResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import get_user_key, site_query, get_user_info, common_config
from SPAPI_Python_SDK.login_user_id import session_middleware, login_required_if_no_user_id
from datetime import datetime
import json
import ast

api_key = 'sk-b4fe58c3d284492b8dd9c1eb3837c2a5'
model = 'deepseek-reasoner'
url = "https://api.deepseek.com/chat/completions"


def generate_amazon_search_terms(rs):
    """
    生成亚马逊Search Term关键词（优化后仅接收已查询的产品数据）

    参数:
        rs: 从数据库获取的产品数据（需包含sku_title_en, sku_description_en, sku_des_en字段）

    返回:
        str: 生成的Search Term关键词字符串（250字节内）
        或 None（无数据/请求失败时）
    """
    # 验证rs有效性
    required_fields = ["sku_title_en", "sku_description_en", "sku_des_en"]
    if not all(field in rs for field in required_fields):
        print("产品数据缺失必要字段")
        return None

    # 构造产品信息字典（直接使用传入的rs）
    pro_info = {
        "产品名称": rs["sku_title_en"],
        "产品亮点": rs["sku_des_en"],
        "产品描述": rs["sku_description_en"],
    }

    # 构造API请求体
    payload = {
        "messages": [
            {
                "content": """ 亚马逊Search Term生成协议
                                目标：
                                通过数据驱动的关键词工程，实现算法友好、跨文化兼容的搜索词组合，确保250字节内最大化曝光效率

                                输入规范
                                ▢ 必填字段
                                标题：完整产品标题（含核心参数）
                                核心参数：尺寸/材质/认证标准（如44mm/PVC/CE）
                                场景清单：至少3个使用场景（如home theater/office/gaming）

                                ▢ 推荐工具
                                • 关键词挖掘：Helium10 Magnet + Cerebro
                                • 竞争分析：Jungle Scout Keyword Scout
                                • 趋势验证：MerchantWords + Google Trends

                                生成引擎七步法
                                1. 核心词锚定
                                • 从标题提取2-3个核心名词（如cable hider）
                                • 验证标准：Helium10搜索量＞1500 & BSR前50竞品覆盖率＞60%

                                2. 参数强化
                                • 提取尺寸/颜色/材质（44mm/white/PVC）
                                • 验证标准：SellerApp搜索量＞800 & 竞争度＜0.5

                                3. 场景嫁接
                                • 植入"for [场景]"结构（如for home theater）
                                • 数据要求：MerchantWords趋势指数＞75

                                4. 欧洲合规改造
                                • 公制单位优先（28cm/11inch）
                                • 强制添加认证标注（certified CE/RoHS）

                                5. 痛点解决方案词
                                • 生成问题解决型词汇（cord concealer/wire organizer）
                                • 来源：Cerebro竞品长尾词（使用率＞40%）

                                6. 竞品反制策略
                                • 逆向TOP10竞品关键词（排除垄断词：占有率＞75%）
                                • 工具：Jungle Scout Competitive Intelligence

                                7. 字节级压缩
                                • 删除重复词 & 停用词（the/and）
                                • 编码优化：优先使用短拼写（organizer＞organisation）""",
                "role": "system"
            },
            {
                "role": "user",
                "content": f"要求只输出结果并删除开始结尾符，不要其它，产品信息如下\n\n{pro_info}"
            }
        ],
        "model": model,
        "max_tokens": 4096,
        "temperature": 0.6,
        "top_p": 1,
        "frequency_penalty": 0,
        "presence_penalty": 0,
        "response_format": {
            "type": "text"
        }
    }

    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }

    try:
        # 发送API请求
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        data_json = response.json()
        return data_json['choices'][0]['message']['content']
    except requests.exceptions.HTTPError as err:
        print(f"API请求错误: {err}")
        return None
    except requests.exceptions.RequestException as err:
        print(f"网络错误: {err}")
        return None


@session_middleware
@login_required_if_no_user_id
def Search_Term(request):
    # 用华为的deepseek英译中
    page_title = "生成亚马逊Search Term"
    return_url = request.GET.get('self', "")
    pro_id = request.GET.get('pro_id', "")
    site_id = site_query(request, return_url)  # 网站ID
    dbdata = DB_DATA[site_id]

    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
        # 步骤1：先查询产品数据（外部获取rs）
        sql = f"SELECT library_sku.id, library_sku.Pro_id, library_sku.sku_title_en, library_sku.sku_description_en, library_sku.sku_des_en FROM library_sku WHERE Pro_id ={pro_id} ORDER BY id ASC"
        rs = db.fetchone(sql)
        if not rs:
            return HttpResponse("记录不存在。", status=400)

        # 步骤2：调用优化后的生成函数（仅传rs）
        core_long_word = generate_amazon_search_terms(rs)

        if not core_long_word:
            db.update("UPDATE library_produc SET Keyword_state=%s WHERE id=%s", (0, pro_id))
            return HttpResponse("记录不存在或API请求失败。", status=400)

        # 更新数据库
        try:
            db.update("UPDATE library_produc SET pro_Keyword=%s, Keyword_state=1 WHERE id=%s", (core_long_word, pro_id))
            # 记录操作日志（可选，根据实际需求保留）
            # now_date = datetime.now()
            # db.insert(f"INSERT INTO library_produc_log(LP_id, LP_status, data_log, des) VALUES({pro_id}, 12, '{now_date}', '关键词已添加')")
        except (ValueError, SyntaxError) as e:
            return HttpResponse(core_long_word, content_type="application/json")

        show_context = "关键词生成完成，请关闭"
        return HttpResponse(show_context, status=200)
