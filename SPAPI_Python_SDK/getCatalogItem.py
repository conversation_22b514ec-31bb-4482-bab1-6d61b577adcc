import re
import json
from django.http import HttpResponse, JsonResponse
from requests import get
from urllib3 import response
from SPAPI_Python_SDK.config import get_user_key, get_user_id, site_query, get_user_info, common_config
from SPAPI_Python_SDK.login_user_id import SESSION_SITE_ID, get_domain
from SPAPI_Python_SDK.Database import Database, DB_DATA
from spapi.spapiclient import SPAPIClient


def getCatalogItem(request):
    dbdata = DB_DATA[2]
    db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"],
                  dbdata["DB_USER"], dbdata["DB_PWD"])

    user_id = request.GET.get('user_id', "3")
    user_data = get_user_info(db, user_id)

    refresh_token = user_data['refresh_token']
    config = common_config(refresh_token)

    marketplace_id = ["ATVPDKIKX0DER"]

    asin = request.GET.get('asin', "")
    includedData = ["attributes", "images", "summaries"]
    locale = "en_US"

    api_client = SPAPIClient(config, "swagger_client_catalogItems_20201201")

    catalog_api = api_client.get_api_client('CatalogApi')

    CatalogItem = catalog_api.get_catalog_item(
        asin, marketplace_id, included_data=includedData, locale=locale)
    CatalogItem_dict = CatalogItem.to_dict()
    context = {
        'config': CatalogItem_dict
    }
    return JsonResponse(context, json_dumps_params={'indent': 2}, status=200)


def getItemOffers(request):
    dbdata = DB_DATA[1]
    db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])

    sql = "SELECT anazom_url FROM collect WHERE price is NULL AND collect_status=6 ORDER BY id DESC LIMIT 20"
    urls = db.fetchall(sql)

    asin_list = []
    for url_dict in urls:
        if 'anazom_url' in url_dict:
            url = url_dict['anazom_url']
            asin = extractAmazonAsin(url)
            if asin:
                asin_list.append(asin)
    print(asin_list)
    return HttpResponse(asin_list)

    # 2025年4月21日17:58:37
    # 备注：基本写完了，可以调用RL刷新令牌进行测试

    user_id = request.GET.get('user_id', "3")
    user_data = get_user_info(db, user_id)
    refresh_token = user_data['refresh_token']
    config = common_config(refresh_token)
    api_client = SPAPIClient(config, "swagger_client_Pricing")
    pricing_api = api_client.get_api_client('ProductPricingApi')

    marketplace_id = "ATVPDKIKX0DER"
    item_condition = "New"
    body = {"requests": []}
    for asin in asin_list:
        request_data = {
            "uri": f"/products/pricing/v0/items/{asin}/offers",
            "method": "GET",
            "MarketplaceId": marketplace_id,
            "ItemCondition": item_condition
        }
        body["requests"].append(request_data)

    # 拼接的body结果如下：['B088CT8FSG', 'B089YDCPS7', 'B0CQVR3R9J', 'B0BZL2WMPC']
    # {'requests': [{'uri': '/products/pricing/v0/items/B088CT8FSG/offers', 'method': 'GET', 'MarketplaceId': 'ATVPDKIKX0DER', 'ItemCondition': 'New'}, {'uri': '/products/
    # pricing/v0/items/B089YDCPS7/offers', 'method': 'GET', 'MarketplaceId': 'ATVPDKIKX0DER', 'ItemCondition': 'New'}, {'uri': '/products/pricing/v0/items/B0CQVR3R9J/offer
    # s', 'method': 'GET', 'MarketplaceId': 'ATVPDKIKX0DER', 'ItemCondition': 'New'}, {'uri': '/products/pricing/v0/items/B0BZL2WMPC/offers', 'method': 'GET', 'MarketplaceId': 'ATVPDKIKX0DER', 'ItemCondition': 'New'}]}

    price_response = pricing_api.get_listing_offers_batch(body)

    responses = price_response.to_dict().get('responses', [])

    context = {
        'config': responses  # Use the parsed dictionary
    }

    return JsonResponse(context, json_dumps_params={'indent': 2}, status=200)


def extractAmazonAsin(url):
    # 预处理：移除URL锚点和多余参数
    clean_url = re.sub(r'#.*$', '', url)

    # 主匹配流程
    patterns = [
        (r'/(?:dp|gp/product|exec/obidos/asin|gp/aw/d)/([A-Z0-9]{10})', 1),
        (r'^https?://[^/]+/([A-Z0-9]{10})', 1),  # 短格式
        (r'[?&](?:asin|productID)=([A-Z0-9]{10})', 1)  # 参数形式
    ]

    for pattern, group in patterns:
        match = re.search(pattern, clean_url, re.IGNORECASE)
        if match:
            potential_asin = match.group(group).upper()
            if re.match(r'^[A-Z0-9]{10}$', potential_asin):
                return potential_asin

    return False
