from django.contrib.auth.decorators import user_passes_test, login_required
from django.http import HttpResponseRedirect, HttpResponse
from django.urls import reverse
from urllib.parse import urlparse
import os

from django.utils.decorators import decorator_from_middleware


# 创建一个装饰器，它将检查会话中是否有user_id
def login_required_if_no_user_id(view_func):
	# 定义一个内部视图函数，它将包装view_func
	def _wrapped_view(request, *args, **kwargs):
		# 检查是否有go_url参数
		go_url = request.GET.get('self', None)
		
		# 如果用户未登录，重定向到go_url参数指定的路径
		if not request.session.get('user_id'):
			# return HttpResponseRedirect(go_url)
			return HttpResponse("没有登陆")
		# 调用包装的视图函数
		return view_func(request, *args, **kwargs)
	
	# 返回内部视图函数
	return _wrapped_view


# 使用Django内置的login_required装饰器
# @login_required
# def some_view(request):
#     # 如果用户已经登录，会话中会有user_id
#     user_id = request.session.get('user_id', "0")
#     return HttpResponse(f"User ID: {user_id}")

login_required_if_user_id = decorator_from_middleware(login_required_if_no_user_id)
# 在settings.py中注册中间件
# MIDDLEW
