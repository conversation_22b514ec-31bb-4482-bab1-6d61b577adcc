body { font-family: Arial, Helvetica, sans-serif; font-size:12px; color:#333;background:#F9F9F9;}
*{padding:0; margin:0;}
a:link,a:visited{ color: #666; text-decoration: none; }
a:active{ color: #1958BD; text-decoration: underline; }
a:hover{ color: #1958BD; text-decoration: underline; }

.logo_serch{width:980px; margin:15px auto;}
.logo{ width:190px; float:left; margin:0 0 15px 0;}
.logo_serch h1{float:left; font-size:32px; font-weight:normal; line-height:52px;}

.login_con{ width:980px; height:330px; border:#DEDEDE 1px solid; margin:auto;background:#FFF;
filter: progid:DXImageTransform.Microsoft.Shadow(color='#DEDEDE', Direction=135, Strength=5);/*for ie6,7,8*/
-moz-box-shadow:2px 2px 5px #DEDEDE;/*firefox*/
-webkit-box-shadow:2px 2px 5px #DEDEDE;/*webkit*/
box-shadow:4px 4px 6px #DEDEDE;/*opera或ie9*/
}
.login_l{ width:470px; margin-left:25px; height:100%; border-right:#CCC 1px solid; float:left;box-shadow:4px 0 2px 0 #DFDFDF;}
.reg_con{ width:980px; height:500px; border:#DEDEDE 1px solid; margin:auto;background:#FFF;
filter: progid:DXImageTransform.Microsoft.Shadow(color='#DEDEDE', Direction=135, Strength=5);/*for ie6,7,8*/
-moz-box-shadow:4px 4px 5px #DEDEDE;/*firefox*/
-webkit-box-shadow:4px 4px 5px #DEDEDE;/*webkit*/
box-shadow:4px 4px 6px #DEDEDE;/*opera或ie9*/

}
.reg_l{ width:700px; margin-left:25px; height:100%; border-right:#CCC 1px solid; float:left;box-shadow:4px 0 2px 0 #DFDFDF;}
.reg_h1{ float:left;font-size:20px; margin:25px 0 15px 0; }
.reg_help{ float:right; margin-right:15px; }
.u_name, .m_box, .s_pass, .c_pass, .c_code{ width:350px; height:25px;line-height:25px; font-size:14px; display:inline-block;}
.reg_con span, .login_con span{ margin-right:15px;}
.reg_note{ color:#666666;}
.reg_input{ border:#DDD 1px solid; height:30px; width:240px; line-height:30px; padding:0 8px;}
.l_c_code { margin-top:20px;}
.radio_v{vertical-align:middle;}

.login_r{ float:left;width:480px; line-height:30px; text-align:center; font-size:14px; padding-top:60px;}
.reg_r{float:right; width:240px;line-height:30px; text-align:center; font-size:14px; padding-top:80px;}
.return{font-size:16px; margin:25px 0; }
.n_account{font-size:20px; margin:25px 0; font-weight:bold;}
.l_u_name, .l_c_pass, .l_m_box{width:120px; height:25px;line-height:25px; font-size:14px; display:inline-block;}
.l_u_n{ margin-bottom:10px;}
.l_note{ line-height:25px;margin-bottom:10px;}
.l_note label{ padding-top:5px;}
.stay_note{ padding-left:7px;}
.stay_alert{ color:#999;}

.agree{ margin:15px 0; }
.agree ul li{line-height:35px;}
.pass_note{ line-height:35px; color:#666666; margin-left:60px;}


.b_nav{ background:#FFF; width:100%; border-top:#CCC 1px solid; margin-top:50px;}
.copyright{ background:#FFF;}

.order_title{border-bottom:#FF5500 2px solid; height:30px; line-height:32px; font-size:12pt; color:#FF5500; font-weight:bold;}
.buyer_right{width:840px; float:right;}
.order_st{ width:823px; height:40px; line-height:40px; padding-left:15px; float:right;border:#EBEBEB 1px solid; border-top:none;border-bottom:none;}
.order_t_bg{ height:30px; background:url(/image/order_t_bg.gif); }
.order_t_bg th{ font-weight:normal; color:#666666;}
.order_t table{ background:#EBEBEB;}
.order_t table tr{ min-height:28px; font-size:12px; font-family:Arial, Helvetica, sans-serif;}
.order_t table td{ background:#FFF; text-align:center;}


/**/
.orderstate{ height:16px; border:#FF6600 1px solid; padding:10px; background:#FFFCEB;}
.orange{ color:#FF5500;}
.os_detail{ border:solid 1px #8BD0F6; background:#F3FBFF; padding:0 10px; line-height:22px; margin-bottom:10px;}
.osd_title{ font-size:14px; line-height:35px;}
.osd_info{ background:#FFF; padding:0 10px;}
.osd_info dl{ border-bottom:#E8E8E8 dotted 1px; padding:8px 0;}
.p-list table{ margin-bottom:5px;}
.p-list tr{ height:25px;}
.p-list td{ padding:5px;}
.total{ width:200px; float:right; margin:10px 0; font-size:14px;}

/*ʻϢ*/
.a_info{ color:#666666; font-size:9pt;line-height:40px;}
.a_info table{ margin:10px 0;}
.a_info table tr{ height:40px;}
.a_info input{ border:#DDDDDD 1px solid; height:21px; line-height:21px; padding:0 5px;}
.a_info_prompt{ width:100px; text-align:right;}
.text_right{ text-align:right;}

/*ʻȫ*/
.safe{ margin:15px 0; font-size:14px;}
.safe_t{ font-weight:bold; margin-bottom:10px;}
.safe_n{color:#666666; margin-bottom:10px;}
.safe_m{ padding-bottom:10px; border-bottom:#DDDDDD 1px dashed;}

/*购物车*/
.cart_note1{ width:458px; height:25px;float:right; background:url(../cart_note1.gif); margin-top:30px; color:#999999;}
.cart_note2{ width:458px; height:25px;float:right; background:url(../cart_note2.gif); margin-top:30px; color:#999999;}
.cart_note1 li, .cart_note2 li{ float:left; margin:0 32px; line-height:28px;}
.cart_note2 a{color:#999999;}
.cart_note_a{ color:#2967AD;}
.cart_h1{ width:1250px; margin:auto;font-size:20px; font-weight:normal; line-height:45px; margin-top:10px;color:#333;}
.fill_note{ width:1250px; margin:auto; color:#666; margin-bottom:15px;}
.cart_title{ width:1250px; height:35px; margin:auto; border-top:#999 1px solid; background:#F3F3F3; line-height:35px;font-size:16px;}
.cart_title div{ text-align:center; float:left;}
.cart_checkbox{ width:100px;display:inline-block; border-right:#FFF 1px solid; line-height:35px;}
.cart_checkbox label{ margin-right:5px; display:inline-block;}
.cart_p_name{ width:700px;border-right:#FFF 1px solid;}
.cart_p_size{ width:99px;border-right:#FFF 1px solid;}
.cart_p_color{ width:99px;border-right:#FFF 1px solid;}
.cart_price{ width:120px;border-right:#FFF 1px solid;}
.cart_quantity{ width:120px;border-right:#FFF 1px solid;}
.cart_op{ width:105px;}
.pro_list{width:1250px; height:60px;margin:auto;}
.pro_list div{height:auto; line-height:60px; float:left;}
.p_l_code{ width:100px;border-right:#FFF 1px solid; line-height:60px;float:left;text-align:center;}
.p_l_p_name{ width:700px;border-right:#FFF 1px solid; text-align:left;}
.p_l_size{ width:99px;border-right:#FFF 1px solid; text-align:center;}
.p_l_color{ width:99px;border-right:#FFF 1px solid; text-align:center;}
.p_l_i_box{ height:60px; padding:5px;}
.p_l_p_name a,.p_l_op a{ color:#1958BD;}
.p_l_price{ width:120px;border-right:#FFF 1px solid;text-align:center;}
.p_l_quantity{ width:120px;border-right:#FFF 1px solid;text-align:center;}
.p_l_op{ width:105px;text-align:center;}

.cart_total{width:1250px; height:50px; margin:auto; border-top:#999 1px solid; font-size:16px; font-weight:bold; color:#333;}
.cart_total span{ margin-right:10px;}
.cart_total label{ margin-left:30px;}
.cart_t_1{ padding:10px; float:right;}
.cart_final{width:1250px; height:50px; margin:auto; border-top:#DDDDDD 1px solid; background:#F3F3F3;}
.final_amount label{padding-right:5px; font-weight:bold; font-size:18px;}
.final_amount span{ padding-right:15px; font-size:20px; color:#CC0000;font-weight:bold; font-family:Arial, Helvetica, sans-serif; line-height:50px;}
.freeshipping{ width:155px; height:28px; border:#EDD28B 1px solid; background:url(../freeshipping.gif) no-repeat 7px 7px; background-color:#FFFDEE; margin-top:10px; color:#FF6600; line-height:28px; padding-left:35px; margin-right:10px;}
.cart_button{width:1250px; height:50px; margin:10px auto;}
.go_check{ float:right;}
.go_on{ float:left; width:120px; height:36px; background:url(../go_on1.gif); text-align:center; line-height:36px;}
.go_on:hover{background:url(../go_on2.gif);}
.go_on a{ font-size:12px; color:#1958BD; display:block;}

.o_address{ width:1050px; margin:auto; padding:50px 100px 20px 100px;; background:#FFF; border:#DDD 1px solid; font-size:14px;}
.o_address tr{ height:40px;}
.o_address td{ padding:0 5px;}
.o_address_btn{height:50px; margin:10px auto;}
.o_address li{ height:60px; line-height:30px;}
.o_address .inp{ width:135px; text-align:right; font-weight:bold;}
.o_address .ipt{ margin-left:10px; width:320px;}
.o_address .txt{ width:290px; border:#DDD 1px solid; height:30px; line-height:30px; padding-left:10px; font-family:Arial, Helvetica, sans-serif; font-size:14px;}
.o_address b{ color:#F00;}

.o_span{padding:3px 0 0 10px;}
.p_span{padding-left:10px; color:#999999;}
.p_banks{width:770px; margin:auto;_margin-left:80px; border:#DDDDDD 1px dashed; background:#fff; padding:10px; margin-bottom:15px;}

.shipping_note{ line-height:20px; margin-bottom:25px;}
.cart_ept{ width:1250px; text-align:center; font-size:20px; margin:20px auto;}
.cart_ept a{color: #1958BD; text-decoration:none;}
.cart_ept a:hover{text-decoration:underline;}
.cart_ept_note{ width:1218px; margin:15px auto; font-size:14px;border:#DDD 1px solid; background:#FFF; padding:15px; line-height:22px;}
.ept_note_tit{ margin:5px 0; background:url(../note.gif) no-repeat 0 2px; padding-left:20px; font-size:16px;}
.cart_ept_note ul{ list-style:decimal; margin-left:30px;}

#jg{ border:#DDD 1px solid; background:#FFF; padding:15px; line-height:22px;}
#jg strong{ color:#Ff6600;}
.s_pay{ font-size:16px;}
.o_over{ font-size:16px; margin:10px 0;}
.tab02{ border:#DDD 1px solid; background:#FFF; padding:15px;}
.pay_note{ border:#f60 1px dashed; float:right; width:320px; background:#FFF; padding:10px; margin-top:30px;}
.pro_top_bg{ padding:15px; border:#DDD 1px solid; background:#FFF;}
.cart_shipping{ margin:15px 0; line-height:22px;}
.f_total{ float:right; margin:15px 0; font-size:16px; line-height:20px;}
.font_b o_over{ font-size:16px;}

.center_middle_top_left_2{ line-height:20px; border:#DDD 1px solid; background:#FFF; padding:15px;}
.center_middle_top_left_2 .li2{ font-weight:bold; margin-right:3px; width:90px;}
.center_middle_top_left_2 .li1{ font-weight:bold; margin-right:3px; width:90px;}
#wu .txt{ width:200px; height:25px; line-height:25px; border:#DDD 1px solid;}
#wu{ border:#DDD 1px solid; background:#FFF; padding:15px;}
#wu tr{ height:30px;}
/*买家订单管理*/
.buyer_left{ float:left;width:205px;font-size:12px;font-family:arial,sans-serif;margin-bottom:10px; background:#FFF;}
.buyer_left h2{background:url(/image/spring_06.gif) no-repeat;height:21px;font-size:14px;font-weight:bold;padding-top:8px;margin:0;}
.my_ango a{color:#000; margin-left:15px;}
.b_left_list{ border:#8BD0F6 1px solid; border-top:none;}
.b_left_list p{ color:#333; border-top:#8BD0F6 1px solid; border-bottom:1px solid #8BD0F6; height:26px; line-height:28px; padding-left:15px; background:#F3FBFF; font-weight:bold;}
.b_left_list li{ line-height:24px; padding-left:20px;}
.b_left_list a{ color:#333;}
.b_left_list a:hover{ color:#1958BD;}

.buyer_right{ width:1030px; float:right;}
.order_title{ font-size:16px; font-weight:bold; height:30px; line-height:30px; border-bottom:#DDD 1px solid; color:#333;}
.order_list{ margin:15px 0;}
.order_list table{ background:#DDD;}
.order_list th{ background:#F1F1F1;}
.order_list tr{ background:#FFF;height:28px; text-align:center;}
.order_list a{color: #1958BD; text-decoration:underline;}
.wantpay{ line-height:25px;}
.wp_title{ font-size:14px; font-weight:bold; color:#333;}
.pay_list{ width:980px; float:left;}
.pay_list li{ float:left; margin-right:20px;}
.amount{ text-align:right;font-size:14px; font-weight:bold; color:#333;border-bottom:#DDD 1px solid; padding-bottom:10px;}
.pay_m_title{margin:15px 0; font-size:14px; font-weight:bold; color:#333;}
.radio_v{vertical-align:middle;}
.p_note{ margin:15px 0;}
.p_check{margin:35px 0; float:right;}

.order_detail{ margin:15px 0;}
.order_detail th{ background:#F1F1F1;padding:2px 5px;text-align:center; height:25px; border-bottom:#DEDEDE 1px solid;}
.order_detail td{padding:5px;text-align:center; background:#FFF;}
.order_detail a{color: #1958BD; text-decoration:none;}
.order_detail a:hover{text-decoration:underline;}
.order_detail div{ text-align:left;}
.order_detail div image{ margin-left:5px;}
.order_detail div label{font-weight:bold; float:left;}
.total{font-size:16px; font-weight:bold;}

.myreviewfm{padding:15px 0 25px 120px; display:none;border:#EDD28B 1px solid;background-color:#FFFDEE;}
.myreviewfm .txt{ width:420px; padding:6px;border:#DDD 1px solid;}
.myreviewfm .edt{ width:600px; padding:6px;height:88px;border:#DDD 1px solid;}
.myreviewfm p span{ font-size:14px; font-weight:bold; line-height:30px;}

.order_note{ width:960px; background:url(../note.gif) no-repeat;; height:30px; padding-left:20px; font-size:14px; color:#333; font-weight:bold; }
.o_n_con{ margin-bottom:25px;}
.o_n_con ul{ margin-left:20px; line-height:22px; color:#666; list-style:decimal;}
.o_n_con strong{ color:#555;}

.msg_note{ margin-top:15px; color:#666; width:980px; border-bottom:#DDD 1px solid; padding-bottom:15px;}
.msg_note ul{ list-style:disc; margin-left:18px; line-height:18px;}
.msg_n_title{ font-weight:bold; margin-bottom:10px;}
.msg_filter{padding:15px 0;}
.msg_filter .date,.msg_filter .iput{ margin-left:20px;}
.msg_list th{ background:#F1F1F1;height:28px;}
.msg_list td{ background:#FFF;height:28px; text-align:center; padding:0 5px;}
.msg_list a{color: #1958BD; text-decoration:underline;}
.post_msg{ margin:15px 0;}
.msg_title{ margin-bottom:15px; color:#555;}
.post_msg table{ margin:15px 0;}
.post_msg th{ background:#F1F1F1;height:28px;}
.post_msg td{ background:#FFF;height:28px; text-align:center; padding:0 5px;}
.padd{ margin:10px 0;}

.my_account{ margin:15px 0; border:#DDD 1px solid; background:#FFF; padding:35px 25px;}
.my_account li{ height:60px; line-height:30px;}
.my_account .inp{ width:135px; text-align:right; font-weight:bold;}
.my_account .ipt{ margin-left:10px; width:270px;}
.my_account .txt{ width:240px; border:#DDD 1px solid; height:30px; line-height:30px; padding-left:10px; font-family:Arial, Helvetica, sans-serif; font-size:14px;}
.my_account b{ color:#F00;}

.my_content{margin:15px 0;}
.my_content table tr{ min-height:28px;}


.clr{ clear:both;}