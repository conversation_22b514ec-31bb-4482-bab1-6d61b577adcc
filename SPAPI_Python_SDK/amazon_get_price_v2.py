import re
import logging
from SPAPI_Python_SDK.Database import Database, DB_DATA
from django.shortcuts import render
from SPAPI_Python_SDK.login_user_id import login_required_if_no_user_id
from SPAPI_Python_SDK.config import get_user_info, common_config
from spapi.spapiclient import SPAPIClient
from SPAPI_Python_SDK.amazon_collect import extractAmazonAsin

logger = logging.getLogger('debug_logger')


def process_asin_data(asin_data):
    """
    处理ASIN数据，提取ASIN并创建映射
    :param asin_data: 从数据库获取的ASIN数据
    :return: ASIN到ID的映射和已处理ID的集合
    """
    asin_list = {}
    processed_ids = set()

    for url_dict in asin_data:
        if 'anazom_url' in url_dict:
            asin_id = url_dict['id']
            url = url_dict['anazom_url']
            asin = extractAmazonAsin(url)
            if asin:
                asin_list[asin] = asin_id
                processed_ids.add(asin_id)

    return asin_list, processed_ids


def get_item_offers_batch(db, asin_list, user_id=3):
    """
    批量获取商品报价信息
    :param db: 数据库连接对象
    :param asin_list: ASIN到ID的映射
    :param user_id: 用户ID，默认为3
    :return: API响应结果
    """
    if not asin_list:
        return {"status": "no_data", "message": "没有有效的ASIN数据"}

    try:
        # 获取用户信息和配置
        user_data = get_user_info(db, user_id)
        refresh_token = user_data['refresh_token']
        config = common_config(refresh_token)

        # 初始化API客户端
        api_client = SPAPIClient(config, "swagger_client_Pricing")
        pricing_api = api_client.get_api_client('ProductPricingApi')

        # 构建请求体
        marketplace_id = "ATVPDKIKX0DER"
        item_condition = "New"
        body = {"requests": []}

        for asin in asin_list.keys():
            request_data = {
                "uri": f"/products/pricing/v0/items/{asin}/offers",
                "method": "GET",
                "MarketplaceId": marketplace_id,
                "ItemCondition": item_condition
            }
            body["requests"].append(request_data)

        # 调用API获取价格
        price_response = pricing_api.get_item_offers_batch(body)
        responses = price_response.to_dict().get('responses', [])

        return {
            "status": "success",
            "responses": responses
        }

    except Exception as e:
        logger.error(f"获取商品报价信息时发生错误: {e}", exc_info=True)
        return {
            "status": "error:",
            "message": str(e)
        }


def process_price_responses(responses, asin_list, asin_db):
    """
    处理价格响应并更新数据库
    :param responses: API响应结果
    :param asin_list: ASIN到ID的映射
    :param asin_db: 数据库连接对象
    :return: 处理结果
    """
    results = []

    for response in responses:
        statusCode = response.get('status', {}).get('statusCode')
        if statusCode == 200:
            payload = response.get('body', {}).get('payload', {})
            asin = payload.get('ASIN')
            offers = payload.get('Offers', [])

            if not offers:
                results.append({"asin": asin, "status": "no_offers"})
                continue

            price = offers[0].get('ListingPrice', {}).get('Amount', 0)

            if price > 0 and asin in asin_list:
                # 更新价格信息
                update_sql = f"UPDATE collect SET collect_status=2, price = {price} WHERE id = '{asin_list[asin]}'"
                asin_db.update(update_sql)
                results.append({"asin": asin, "id": asin_list[asin], "price": price, "status": "success"})
            else:
                results.append({"asin": asin, "status": "invalid_price", "price": price})
        else:
            # 处理非200状态码的响应
            error_message = response.get('body', {}).get('errors', [])
            logger.error(f"API返回错误: {error_message}")
            results.append({"status": "error", "message": error_message})

    return {
        "status": "completed",
        "results": results
    }


@login_required_if_no_user_id
def getItemOffersBatch(request):
    page_title = "获取ASIN详情"

    asin_dbdata = DB_DATA[1]  # 先用site_id=1来获取需要抓取的asin
    asin_db = Database(asin_dbdata["DB_HOST"], asin_dbdata["DB_NAME"], asin_dbdata["DB_USER"], asin_dbdata["DB_PWD"])
    sql = "SELECT id, anazom_url FROM collect WHERE collect_status=6 ORDER BY id DESC LIMIT 20"
    asin_data = asin_db.fetchall(sql)

    if not asin_data:
        show_context = "采集完成,即将返回采集箱"
        go_url = "http://mpr.hkzhenshang.com/collection/collection_box.php"
        context = {"show_context": show_context,
                   "go_url": go_url, "page_title": page_title}
        return render(request, "SPAPI_Python_SDK/goto.html", context)

    # 处理ASIN数据，提取ASIN并创建映射
    asin_list, processed_ids = process_asin_data(asin_data)

    if not asin_list:
        show_context = f"ASIN 提取失败，正在跳转..."
        go_url = f"/GetListingOffersBatch/"
        context = {"show_context": show_context,
                   "go_url": go_url, "page_title": page_title}
        return render(request, "SPAPI_Python_SDK/goto.html", context)

    # 用asin来获取asin详情,获取asin用RL的信息
    dbdata = DB_DATA[2]
    db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])

    # 调用API获取价格信息
    api_result = get_item_offers_batch(db, asin_list)

    if api_result["status"] != "success":
        logger.error(f"API调用失败: {api_result.get('message', '未知错误')}")
        return render(request, "SPAPI_Python_SDK/show.html",
                      {"show_context": f"API调用失败: {api_result.get('message', '未知错误')}",
                       "page_title": page_title})

    # 处理API响应并更新数据库
    process_price_responses(api_result["responses"], asin_list, asin_db)

    # 返回结果
    second = 11
    show_context = "采集成功"
    go_url = request.get_full_path()
    context = {"show_context": show_context,
               "go_url": go_url, "page_title": page_title, "second": second}
    return render(request, "SPAPI_Python_SDK/goto.html", context)
