import os
import sys
import logging
import backoff
from requests.exceptions import HTTP<PERSON>rror
import importlib

# Update Path
# sys.path.append('client/productPricingV0')
# sys.path.append('client/all')
# from swagger_client.configuration import Configuration
# from swagger_client.api_client import ApiClient

# Update Path
sys.path.append('../auth')
from auth.LwaRequest import AccessTokenCache

logging.basicConfig(level=logging.INFO)


def is_rate_limit_error(e):
	"""Check if the exception is a rate limit error (HTTP 429)."""
	return isinstance(e, HTTPError) and e.response.status_code == 429


class SPAPIClient:
	region_to_endpoint = {
		"NA": "https://sellingpartnerapi-na.amazon.com",
		"EU": "https://sellingpartnerapi-eu.amazon.com",
		"FE": "https://sellingpartnerapi-fe.amazon.com",
		"SANDBOX": "https://sandbox.sellingpartnerapi-na.amazon.com"
	}
	
	def __init__(self, config, api_client_path):
		self.config = config
		self.api_base_url = self.region_to_endpoint.get(config.region)
		self.access_token_cache = AccessTokenCache()
		self.api_client = None
		self.api_client_path = api_client_path
		self._initialize_client()
	
	def _initialize_client(self):
		logging.debug("初始化API客户端...")
		
		access_token = self.access_token_cache.get_lwa_access_token(
			client_id=self.config.client_id,
			client_secret=self.config.client_secret,
			refresh_token=self.config.refresh_token
		)
		# 动态导入Configuration类
		configuration_full_module_path = f'{self.api_client_path}.configuration'
		configuration_module = self._import_module(configuration_full_module_path)
		# configuration_module = self._import_module('productPricingV0.swagger_client.configuration')
		Configuration = getattr(configuration_module, 'Configuration')
		# 动态导入ApiClient类
		api_client_full_module_path = f'{self.api_client_path}.api_client'
		api_client_module = self._import_module(api_client_full_module_path)
		# api_client_module = self._import_module('productPricingV0.swagger_client.api_client')
		ApiClient = getattr(api_client_module, 'ApiClient')
		
		configuration = Configuration()
		configuration.host = self.api_base_url
		configuration.access_token = access_token
		
		self.api_client = ApiClient(configuration=configuration)
	
	def _import_module(self, module_name):
		"""动态导入模块"""
		try:
			return importlib.import_module(module_name)
		except ImportError as e:
			logging.error(f"无法导入模块: {module_name}")
			raise e
	
	@backoff.on_exception(backoff.expo,
	                      HTTPError,
	                      giveup=is_rate_limit_error,
	                      max_tries=5,
	                      on_giveup=lambda e: logging.error(f"Too Many Retries: {e}"))
	def get_api_client(self, api_name):
		try:
			# 动态导入swagger_client.api模块
			api_module_path = f'{self.api_client_path}.api'
			module = __import__(api_module_path, fromlist=[api_name])
			api_class = getattr(module, api_name)
			return api_class(self.api_client)
		except AttributeError:
			raise Exception(f"API client for {api_name} not found.")
	
	@staticmethod
	def get_models_client(self, api_name):
		import importlib
		try:
			# 动态导入swagger_client.api模块
			# api_module = importlib.import_module(f'swagger_client.models.{api_name}')
			# 实例化API类并返回
			# return api_module
			api_module_path = f'{self.api_client_path}.models'
			module = __import__(api_module_path, fromlist=[api_name])
			api_class = getattr(module, api_name)
			return api_class
		except (ImportError, AttributeError) as e:
			# 抛出异常，说明API客户端未找到
			raise Exception(f"API client for {api_name} not found.") from e
