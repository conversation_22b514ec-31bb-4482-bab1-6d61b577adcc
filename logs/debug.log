INFO Watching for file changes with StatReloader
WARNING Not Found: /logs/
WARNING "GET /logs/ HTTP/1.1" 404 4884
WARNING Not Found: /favicon.ico
WARNING "GET /favicon.ico HTTP/1.1" 404 4902
WARNING Not Found: /logs/sebug
WARNING "GET /logs/sebug HTTP/1.1" 404 4899
WARNING Not Found: /logs/debug.log
WARNING "GET /logs/debug.log HTTP/1.1" 404 4911
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\urls.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\urls.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\urls.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\urls.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\urls.py changed, reloading.
INFO Watching for file changes with StatReloader
WARNING Not Found: /logs/log_view
WARNING "GET /logs/log_view HTTP/1.1" 404 5082
WARNING Not Found: /view/log_view
WARNING "GET /view/log_view HTTP/1.1" 404 5082
WARNING Not Found: /view/log_view
WARNING "GET /view/log_view HTTP/1.1" 404 5082
WARNING Not Found: /views/log_view
WARNING "GET /views/log_view HTTP/1.1" 404 5085
INFO "GET /log_view HTTP/1.1" 301 0
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 204, in _get_response
    self.check_response(response, callback)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 332, in check_response
    raise ValueError(
ValueError: The view SPAPI_Python_SDK.views.log_view didn't return an HttpResponse object. It returned None instead.
ERROR "GET /log_view/ HTTP/1.1" 500 72930
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\utils\deprecation.py", line 131, in __call__
    response = self.process_response(request, response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\middleware\clickjacking.py", line 27, in process_response
    if response.get("X-Frame-Options") is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'
ERROR "GET /log_view/ HTTP/1.1" 500 71540
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\utils\deprecation.py", line 131, in __call__
    response = self.process_response(request, response)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\middleware\clickjacking.py", line 27, in process_response
    if response.get("X-Frame-Options") is not None:
       ^^^^^^^^^^^^
AttributeError: 'int' object has no attribute 'get'
ERROR "GET /log_view/ HTTP/1.1" 500 71540
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 1
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/debug.log
WARNING "GET /log_view/debug.log HTTP/1.1" 404 5097
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
ERROR Internal Server Error: /log_view/
ERROR "GET /log_view/ HTTP/1.1" 500 76
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
WARNING Not Found: /log_view/
WARNING "GET /log_view/ HTTP/1.1" 404 24
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py", line 24, in log_view
    log_file = os.path.join(settings.BASE_DIR, 'logs', 'debug.log')  # 更可靠的路径写法
                            ^^^^^^^^
NameError: name 'settings' is not defined
ERROR "GET /log_view/ HTTP/1.1" 500 72078
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py", line 41, in log_view
    return render(request, 'logs/view_logs.html', {'log_content': log_content})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: logs/view_logs.html
ERROR "GET /log_view/ HTTP/1.1" 500 107981
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 5907
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 241
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 12514
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 6075
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 24035
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 27557
INFO "GET /log_view/ HTTP/1.1" 200 24087
INFO "GET /log_view/ HTTP/1.1" 200 30066
INFO "GET /log_view/ HTTP/1.1" 200 24212
INFO "GET /log_view/ HTTP/1.1" 200 30012
INFO "GET /log_view/ HTTP/1.1" 200 30209
INFO "GET /log_view/ HTTP/1.1" 200 30248
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\monthlysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\monthlysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\monthlysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\monthlysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\monthlysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\monthlysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\monthlysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\monthlysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\monthlysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\monthlysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO Watching for file changes with StatReloader
INFO "GET /log_view HTTP/1.1" 301 0
INFO "GET /log_view/ HTTP/1.1" 200 30851
WARNING Not Found: /favicon.ico
WARNING "GET /favicon.ico HTTP/1.1" 404 5076
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\inventory.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\reports\dailysales.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO Watching for file changes with StatReloader
WARNING Not Found: /debug.log
WARNING "GET /debug.log HTTP/1.1" 404 3739
WARNING Not Found: /favicon.ico
WARNING "GET /favicon.ico HTTP/1.1" 404 3745
WARNING Not Found: /logs/debug.log
WARNING "GET /logs/debug.log HTTP/1.1" 404 3754
WARNING Not Found: /logs/log_view
WARNING "GET /logs/log_view HTTP/1.1" 404 3751
INFO "GET /log_view HTTP/1.1" 301 0
INFO "GET /log_view/ HTTP/1.1" 200 29739
INFO "GET /log_view/ HTTP/1.1" 200 29743
INFO "GET /log_view/ HTTP/1.1" 200 29699
INFO "GET /log_view/ HTTP/1.1" 200 29703
INFO "GET /log_view/ HTTP/1.1" 200 18786
INFO "GET /log_view/ HTTP/1.1" 200 18788
INFO "GET /log_view/ HTTP/1.1" 200 19296
INFO "GET /log_view/ HTTP/1.1" 200 19298
INFO "GET /log_view/ HTTP/1.1" 200 29579
INFO "GET /log_view/ HTTP/1.1" 200 29172
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 102209
INFO "GET /log_view/ HTTP/1.1" 200 64628
INFO "GET /log_view/ HTTP/1.1" 200 7662657
INFO "GET /log_view/ HTTP/1.1" 200 64763
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\templates\log_filters.py changed, reloading.
INFO Watching for file changes with StatReloader
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1035, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'log_filters'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py", line 57, in log_view
    return render(request, 'SPAPI_Python_SDK/view_logs.html', {'log_content': log_content})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1097, in load
    lib = find_library(parser, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1037, in find_library
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'log_filters' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
i18n
l10n
log
static
tz
ERROR "GET /log_view/ HTTP/1.1" **********
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1035, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'log_filters'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py", line 73, in log_view
    return render(request, 'SPAPI_Python_SDK/view_logs.html', {'log_blocks': log_blocks})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1097, in load
    lib = find_library(parser, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1037, in find_library
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'log_filters' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
i18n
l10n
log
static
tz
ERROR "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" **********
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" **********
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 91606
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py", line 43, in log_view
    return render(request, 'SPAPI_Python_SDK/view_logs.html', {'log_content': log_content})
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 605, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 701, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 611, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'get_log_level'
ERROR "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" **********
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 74163
INFO "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" 200 20934161
INFO "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" **********
WARNING Not Found: /favicon.ico
WARNING "GET /favicon.ico HTTP/1.1" 404 3745
INFO "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" **********
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 108552
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 1532
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'static'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py", line 68, in log_view
    return render(request, 'SPAPI_Python_SDK/view_logs.html', {'log_entries': log_entries[::-1]})  # 倒序显示最新日志在前
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 573, in invalid_block_tag
    raise self.error(
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 4: 'static'. Did you forget to register or load this tag?
ERROR "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" 200 87119
INFO "GET /log_view/ HTTP/1.1" 200 1532
INFO "GET /log_view/ HTTP/1.1" 200 1321
INFO "GET /log_view/ HTTP/1.1" 200 1321
WARNING Not Found: /favicon.ico
WARNING "GET /favicon.ico HTTP/1.1" 404 3745
INFO "GET /log_view/ HTTP/1.1" 200 88157
INFO "GET /log_view/ HTTP/1.1" 200 86972
INFO "GET /log_view/ HTTP/1.1" 200 87145
INFO "GET /log_view/ HTTP/1.1" 200 87318
WARNING Not Found: /favicon.ico
WARNING "GET /favicon.ico HTTP/1.1" 404 3745
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\templates\custom_filters.py changed, reloading.
INFO Watching for file changes with StatReloader
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1035, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'custom_filters'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py", line 68, in log_view
    return render(request, 'SPAPI_Python_SDK/view_logs.html', {'log_entries': log_entries[::-1]})  # 倒序显示最新日志在前
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1097, in load
    lib = find_library(parser, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1037, in find_library
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'custom_filters' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
i18n
l10n
log
static
tz
ERROR "GET /log_view/ HTTP/1.1" **********
ERROR Internal Server Error: /log_view/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1035, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'custom_filters'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py", line 68, in log_view
    return render(request, 'SPAPI_Python_SDK/view_logs.html', {'log_entries': log_entries[::-1]})  # 倒序显示最新日志在前
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1097, in load
    lib = find_library(parser, name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\template\defaulttags.py", line 1037, in find_library
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: 'custom_filters' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
i18n
l10n
log
static
tz
ERROR "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" 200 97316
INFO "GET /log_view/ HTTP/1.1" 200 97489
INFO "GET /log_view/ HTTP/1.1" 200 97662
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 98006
INFO "GET /log_view/ HTTP/1.1" 200 98521
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 99062
WARNING Not Found: /favicon.ico
WARNING "GET /favicon.ico HTTP/1.1" 404 3745
INFO "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" **********
INFO "GET /log_view/ HTTP/1.1" **********
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" **********
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\views.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO "GET /log_view/ HTTP/1.1" 200 115817
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\retry_utils.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\retry_utils.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\urls.py changed, reloading.
INFO Watching for file changes with StatReloader
INFO C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\settings.py changed, reloading.
INFO 2025-03-25 15:27:06,336 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 11:46:02,267 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 11:46:31,033 basehttp "GET /Translate_en/?pro_id=38826&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D2%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 12
WARNING 2025-04-09 11:46:31,867 log Not Found: /favicon.ico
WARNING 2025-04-09 11:46:31,868 basehttp "GET /favicon.ico HTTP/1.1" 404 4721
WARNING 2025-04-09 11:46:47,139 log Not Found: /
WARNING 2025-04-09 11:46:47,140 basehttp "GET / HTTP/1.1" 404 4670
INFO 2025-04-09 11:47:15,048 basehttp "GET //Translate_en/?pro_id=38826&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D2%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 12
INFO 2025-04-09 11:47:38,253 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 11:47:38,893 autoreload Watching for file changes with StatReloader
ERROR 2025-04-09 11:47:45,061 log Internal Server Error: /Translate_en/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\login_user_id.py", line 132, in middleware
    response = get_response(request)
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py", line 94, in translate_to_en
    with Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"]) as db:
         ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py", line 75, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py", line 80, in _connect
    self.connection = mysql.connector.connect(
                      ~~~~~~~~~~~~~~~~~~~~~~~^
    	host=self.host,
     ^^^^^^^^^^^^^^^
    ...<3 lines>...
    	autocommit=False  # 关闭自动提交，以便手动管理事务
     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\pooling.py", line 323, in connect
    return MySQLConnection(*args, **kwargs)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection.py", line 179, in __init__
    self.connect(**kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\abstracts.py", line 1399, in connect
    self._open_connection()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection.py", line 363, in _open_connection
    self._do_auth(
    ~~~~~~~~~~~~~^
        self._user,
        ^^^^^^^^^^^
    ...<4 lines>...
        self._conn_attrs,
        ^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection.py", line 297, in _do_auth
    ok_pkt = self._authenticator.authenticate(
        sock=self._socket,
    ...<14 lines>...
        webauthn_callback=self._webauthn_callback,
    )
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\authentication.py", line 371, in authenticate
    ok_pkt = self._handle_server_response(sock, pkt)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\authentication.py", line 285, in _handle_server_response
    raise get_exception(pkt)
mysql.connector.errors.ProgrammingError: 1045 (28000): Access denied for user 'ad_site'@'localhost' (using password: YES)
ERROR 2025-04-09 11:47:45,069 basehttp "GET /Translate_en/?pro_id=38826&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D2%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 500 116995
WARNING 2025-04-09 11:48:55,111 log Bad Request: /Translate_en/
WARNING 2025-04-09 11:48:55,112 basehttp "GET /Translate_en/?pro_id=38826&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D2%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 400 18
WARNING 2025-04-09 11:48:59,597 log Bad Request: /Translate_en/
WARNING 2025-04-09 11:48:59,603 basehttp "GET /Translate_en/?pro_id=38825&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D2%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 400 18
INFO 2025-04-09 11:49:46,049 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 11:49:46,844 autoreload Watching for file changes with StatReloader
WARNING 2025-04-09 11:50:05,946 log Bad Request: /Translate_en/
WARNING 2025-04-09 11:50:05,947 basehttp "GET /Translate_en/?pro_id=38826&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D2%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 400 18
INFO 2025-04-09 11:50:50,729 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 11:50:51,367 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 11:51:05,931 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 11:51:06,564 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 11:51:33,147 basehttp "GET /Translate_en/?pro_id=38826&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D2%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 1264
WARNING 2025-04-09 11:51:42,381 log Not Found: /Search_Term_key_r1/Search_Term/
WARNING 2025-04-09 11:51:42,382 basehttp "GET /Search_Term_key_r1/Search_Term/?pro_id=38826&return_url=http://127.0.0.4/add_pro/pro_manage.php?user_id=&s_date=&e_date=&Img_assistant=&img_s_date=&img_e_date=&image_complete=&strpro_name=&strpro_id=&Archive=0&TA_id=&T_s_date=&T_e_date=&doc_assistant=&doc_s_date=&doc_e_date=&strstatus=2&allocation=1&button=+%E6%90%9C%E7%B4%A2+&site=4 HTTP/1.1" 404 5165
WARNING 2025-04-09 11:52:03,170 log Not Found: /Search_Term_key_r1/Search_Term
WARNING 2025-04-09 11:52:03,171 basehttp "GET /Search_Term_key_r1/Search_Term HTTP/1.1" 404 4778
WARNING 2025-04-09 11:54:54,176 log Not Found: /Search_Term_key_r1/Search_Term_key_r1
WARNING 2025-04-09 11:54:54,177 basehttp "GET /Search_Term_key_r1/Search_Term_key_r1 HTTP/1.1" 404 4799
INFO 2025-04-09 11:55:00,422 basehttp "GET /Search_Term_key_r1 HTTP/1.1" 301 0
INFO 2025-04-09 11:55:00,426 basehttp "GET /Search_Term_key_r1/ HTTP/1.1" 200 12
WARNING 2025-04-09 11:55:09,050 log Not Found: /Search_Term/
WARNING 2025-04-09 11:55:09,051 basehttp "GET /Search_Term/ HTTP/1.1" 404 4724
INFO 2025-04-09 11:55:32,470 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 11:55:33,100 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 11:55:46,686 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 11:55:47,381 autoreload Watching for file changes with StatReloader
WARNING 2025-04-09 11:55:51,902 log Not Found: /Search_Term/
WARNING 2025-04-09 11:55:51,902 basehttp "GET /Search_Term/ HTTP/1.1" 404 4724
WARNING 2025-04-09 11:56:01,897 log Bad Request: /Search_Term_key_r1/
WARNING 2025-04-09 11:56:01,897 basehttp "GET /Search_Term_key_r1/?pro_id=38826&return_url=http://127.0.0.4/add_pro/pro_manage.php?user_id=&s_date=&e_date=&Img_assistant=&img_s_date=&img_e_date=&image_complete=&strpro_name=&strpro_id=&Archive=0&TA_id=&T_s_date=&T_e_date=&doc_assistant=&doc_s_date=&doc_e_date=&strstatus=2&allocation=1&button=+%E6%90%9C%E7%B4%A2+&site=4 HTTP/1.1" 400 18
INFO 2025-04-09 11:56:19,850 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 11:56:20,494 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 11:57:32,614 basehttp "GET /Search_Term_key_r1/?pro_id=38826&return_url=http://127.0.0.4/add_pro/pro_manage.php?user_id=&s_date=&e_date=&Img_assistant=&img_s_date=&img_e_date=&image_complete=&strpro_name=&strpro_id=&Archive=0&TA_id=&T_s_date=&T_e_date=&doc_assistant=&doc_s_date=&doc_e_date=&strstatus=2&allocation=1&button=+%E6%90%9C%E7%B4%A2+&site=4 HTTP/1.1" 200 33
INFO 2025-04-09 11:58:21,581 basehttp "GET /Translate_en/?pro_id=38671&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D2%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 1240
INFO 2025-04-09 11:59:22,453 basehttp "GET /Search_Term_key_r1/?pro_id=38671&return_url=http://127.0.0.4/add_pro/pro_manage.php?user_id=&s_date=&e_date=&Img_assistant=&img_s_date=&img_e_date=&image_complete=&strpro_name=&strpro_id=&Archive=0&TA_id=&T_s_date=&T_e_date=&doc_assistant=&doc_s_date=&doc_e_date=&strstatus=2&allocation=1&button=+%E6%90%9C%E7%B4%A2+&site=4 HTTP/1.1" 200 33
INFO 2025-04-09 11:59:57,182 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 11:59:57,835 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 12:00:27,783 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 12:00:28,440 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:05:45,128 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:05:46,096 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:13:23,728 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:13:24,957 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:33:15,362 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 15:33:16,204 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:34:26,133 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 15:34:26,819 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:40:33,168 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:40:33,967 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:41:04,840 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:41:05,686 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:43:22,979 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:43:23,686 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:43:57,626 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:43:58,406 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:46:09,685 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:46:10,430 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:50:25,971 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:50:26,776 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:52:12,570 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:52:13,258 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:58:04,118 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:58:04,730 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:58:31,598 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:58:32,346 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:58:33,631 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:58:34,381 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:58:35,729 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:58:36,381 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:59:00,154 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:59:00,859 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 15:59:14,446 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 15:59:15,156 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 16:01:02,819 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 16:01:03,490 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 16:01:15,129 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 16:01:15,802 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 16:01:39,624 basehttp "GET /Translate_en/?pro_id=38674&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D4%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 12
INFO 2025-04-09 16:01:47,976 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 16:01:48,693 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 16:01:51,069 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 16:01:51,764 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 16:02:34,975 basehttp "GET /Translate_en/?pro_id=38674&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D4%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 1240
ERROR 2025-04-09 16:03:46,271 log Internal Server Error: /Search_Term_key_r1/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\login_user_id.py", line 132, in middleware
    response = get_response(request)
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py", line 122, in Search_Term
    db.update(sql)
    ~~~~~~~~~^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py", line 184, in update
    cursor.execute(query, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\cursor.py", line 537, in execute
    self._handle_result(self._connection.cmd_query(stmt))
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\opentelemetry\context_propagation.py", line 97, in wrapper
    return method(cnx, *args, **kwargs)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection.py", line 859, in cmd_query
    result = self._handle_result(self._send_cmd(ServerCmd.QUERY, query))
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection.py", line 635, in _handle_result
    raise get_exception(packet)
mysql.connector.errors.ProgrammingError: 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'cleaner adapter 35mm to 32mm, plastic hose adapter certified CE RoHS, 1-3/8 inch' at line 1
ERROR 2025-04-09 16:03:46,278 basehttp "GET /Search_Term_key_r1/?pro_id=38674&return_url=http://127.0.0.4/add_pro/pro_manage.php?user_id=&s_date=&e_date=&Img_assistant=&img_s_date=&img_e_date=&image_complete=&strpro_name=&strpro_id=&Archive=0&TA_id=&T_s_date=&T_e_date=&doc_assistant=&doc_s_date=&doc_e_date=&strstatus=4&allocation=1&button=+%E6%90%9C%E7%B4%A2+&site=4 HTTP/1.1" **********
INFO 2025-04-09 16:04:12,555 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 16:04:13,247 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 16:05:07,834 basehttp "GET /Search_Term_key_r1/?pro_id=38674&return_url=http://127.0.0.4/add_pro/pro_manage.php?user_id=&s_date=&e_date=&Img_assistant=&img_s_date=&img_e_date=&image_complete=&strpro_name=&strpro_id=&Archive=0&TA_id=&T_s_date=&T_e_date=&doc_assistant=&doc_s_date=&doc_e_date=&strstatus=4&allocation=1&button=+%E6%90%9C%E7%B4%A2+&site=4 HTTP/1.1" 200 476
INFO 2025-04-09 16:05:57,231 basehttp "GET /Translate_en/?pro_id=38665&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3Fuser_id%3D%26s_date%3D%26e_date%3D%26Img_assistant%3D%26img_s_date%3D%26img_e_date%3D%26image_complete%3D%26strpro_name%3D%26strpro_id%3D%26Archive%3D0%26TA_id%3D%26T_s_date%3D%26T_e_date%3D%26doc_assistant%3D%26doc_s_date%3D%26doc_e_date%3D%26strstatus%3D4%26allocation%3D1%26button%3D%2B%25E6%2590%259C%25E7%25B4%25A2%2B&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 1240
INFO 2025-04-09 16:06:47,183 basehttp "GET /Search_Term_key_r1/?pro_id=38665&return_url=http://127.0.0.4/add_pro/pro_manage.php?user_id=&s_date=&e_date=&Img_assistant=&img_s_date=&img_e_date=&image_complete=&strpro_name=&strpro_id=&Archive=0&TA_id=&T_s_date=&T_e_date=&doc_assistant=&doc_s_date=&doc_e_date=&strstatus=4&allocation=1&button=+%E6%90%9C%E7%B4%A2+&site=4 HTTP/1.1" 200 566
INFO 2025-04-09 16:26:56,880 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-04-09 16:26:57,878 autoreload Watching for file changes with StatReloader
INFO 2025-04-09 16:27:03,345 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Translate.py changed, reloading.
INFO 2025-04-09 16:27:04,169 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:39:17,425 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:45:26,371 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\urls.py changed, reloading.
INFO 2025-04-17 10:45:27,155 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:49:14,288 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py changed, reloading.
INFO 2025-04-17 10:49:15,006 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:51:38,555 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 10:51:39,210 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:53:25,434 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 10:53:26,215 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:53:42,899 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 10:53:43,788 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:54:17,202 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 10:54:18,083 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:54:39,045 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 10:54:39,838 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:55:04,759 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 10:55:05,639 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:56:10,391 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 10:56:11,380 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 10:59:19,805 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 10:59:20,463 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:00:42,498 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:00:43,361 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:00:56,064 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:00:56,971 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:01:13,857 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:01:14,812 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:01:35,758 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:01:36,397 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:01:50,993 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:01:51,781 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:02:08,584 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:02:09,375 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:03:14,282 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:03:14,926 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:04:14,563 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:04:15,391 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:06:23,153 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:07:04,068 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:07:50,312 autoreload C:\web\SPAPI_Python_SDK\Pricing\get_listing_item.py changed, reloading.
INFO 2025-04-17 11:07:50,970 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:11:47,908 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:11:48,728 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:12:56,543 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:12:57,454 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:15:11,509 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:15:12,269 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:15:22,936 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:15:23,668 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:15:40,406 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:15:41,302 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:16:28,767 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:16:29,607 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:16:47,464 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:16:48,111 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:19:18,812 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:19:19,535 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:21:54,083 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:21:54,858 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:23:54,686 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:23:55,503 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:24:23,415 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:24:24,158 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:27:33,031 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py changed, reloading.
INFO 2025-04-17 11:27:33,847 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:29:51,012 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:29:51,708 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:37:08,575 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:37:09,221 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:51:45,441 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:51:46,470 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:52:48,134 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:52:49,199 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:57:53,419 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:57:54,536 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:58:15,446 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:58:16,323 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:58:21,856 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:58:22,544 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:58:50,790 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:58:51,810 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 11:58:58,538 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 11:58:59,215 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 12:00:21,694 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 12:00:22,436 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 12:00:26,880 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 12:00:27,629 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 12:00:31,164 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 12:00:31,921 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 12:03:04,494 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 12:03:05,256 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 12:03:58,726 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 12:03:59,456 autoreload Watching for file changes with StatReloader
INFO 2025-04-17 12:04:26,746 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-17 12:04:27,468 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:00:38,620 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:01:24,981 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:01:25,370 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:01:53,847 basehttp "GET /catalog HTTP/1.1" 301 0
ERROR 2025-04-21 17:01:56,190 log Internal Server Error: /catalog/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 705, in cmd_query
    self._cmysql.query(
_mysql_connector.MySQLInterfaceError: Unknown column 'MWS_Switch' in 'field list'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py", line 18, in getCatalogItem
    user_data = get_user_info(db, user_id)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\config.py", line 144, in get_user_info
    user_data = db.fetchone(sql)
                ^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py", line 183, in fetchone
    cursor.execute(query, params)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\cursor_cext.py", line 357, in execute
    result = self._connection.cmd_query(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\opentelemetry\context_propagation.py", line 97, in wrapper
    return method(cnx, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 713, in cmd_query
    raise get_mysql_exception(
mysql.connector.errors.ProgrammingError: 1054 (42S22): Unknown column 'MWS_Switch' in 'field list'
ERROR 2025-04-21 17:01:56,197 basehttp "GET /catalog/ HTTP/1.1" 500 94099
WARNING 2025-04-21 17:01:56,996 log Not Found: /favicon.ico
WARNING 2025-04-21 17:01:56,997 basehttp "GET /favicon.ico HTTP/1.1" 404 4897
WARNING 2025-04-21 17:02:29,940 log Not Found: /getItemOffers/
WARNING 2025-04-21 17:02:29,941 basehttp "GET /getItemOffers/ HTTP/1.1" 404 4906
INFO 2025-04-21 17:03:03,819 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\urls.py changed, reloading.
INFO 2025-04-21 17:03:04,520 autoreload Watching for file changes with StatReloader
WARNING 2025-04-21 17:03:07,607 log Not Found: /getItemOffers/
WARNING 2025-04-21 17:03:07,607 basehttp "GET /getItemOffers/ HTTP/1.1" 404 5084
WARNING 2025-04-21 17:03:09,479 log Not Found: /getItemOffers/
WARNING 2025-04-21 17:03:09,479 basehttp "GET /getItemOffers/ HTTP/1.1" 404 5084
WARNING 2025-04-21 17:03:10,380 log Not Found: /getItemOffers/
WARNING 2025-04-21 17:03:10,381 basehttp "GET /getItemOffers/ HTTP/1.1" 404 5084
WARNING 2025-04-21 17:03:11,459 log Not Found: /getItemOffers/
WARNING 2025-04-21 17:03:11,460 basehttp "GET /getItemOffers/ HTTP/1.1" 404 5084
INFO 2025-04-21 17:03:46,046 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\urls.py changed, reloading.
INFO 2025-04-21 17:03:46,539 autoreload Watching for file changes with StatReloader
ERROR 2025-04-21 17:03:52,228 log Internal Server Error: /getItemOffers/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 705, in cmd_query
    self._cmysql.query(
_mysql_connector.MySQLInterfaceError: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py", line 47, in getItemOffers
    urls = db.fetchall(sql)
           ^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py", line 172, in fetchall
    cursor.execute(query, params)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\cursor_cext.py", line 357, in execute
    result = self._connection.cmd_query(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\opentelemetry\context_propagation.py", line 97, in wrapper
    return method(cnx, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 713, in cmd_query
    raise get_mysql_exception(
mysql.connector.errors.ProgrammingError: 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
ERROR 2025-04-21 17:03:52,228 basehttp "GET /getItemOffers/ HTTP/1.1" 500 90194
INFO 2025-04-21 17:04:58,880 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:04:59,352 autoreload Watching for file changes with StatReloader
ERROR 2025-04-21 17:05:04,170 log Internal Server Error: /getItemOffers/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 705, in cmd_query
    self._cmysql.query(
_mysql_connector.MySQLInterfaceError: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py", line 47, in getItemOffers
    urls = db.fetchall(sql)
           ^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py", line 172, in fetchall
    cursor.execute(query, params)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\cursor_cext.py", line 357, in execute
    result = self._connection.cmd_query(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\opentelemetry\context_propagation.py", line 97, in wrapper
    return method(cnx, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 713, in cmd_query
    raise get_mysql_exception(
mysql.connector.errors.ProgrammingError: 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
ERROR 2025-04-21 17:05:04,172 basehttp "GET /getItemOffers/ HTTP/1.1" 500 90355
INFO 2025-04-21 17:06:16,440 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:06:16,880 autoreload Watching for file changes with StatReloader
ERROR 2025-04-21 17:06:22,278 log Internal Server Error: /getItemOffers/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 705, in cmd_query
    self._cmysql.query(
_mysql_connector.MySQLInterfaceError: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py", line 47, in getItemOffers
    urls = db.fetchall(sql)
           ^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py", line 172, in fetchall
    cursor.execute(query, params)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\cursor_cext.py", line 357, in execute
    result = self._connection.cmd_query(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\opentelemetry\context_propagation.py", line 97, in wrapper
    return method(cnx, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 713, in cmd_query
    raise get_mysql_exception(
mysql.connector.errors.ProgrammingError: 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 1
ERROR 2025-04-21 17:06:22,278 basehttp "GET /getItemOffers/ HTTP/1.1" 500 90352
INFO 2025-04-21 17:06:50,102 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:06:50,838 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:06:55,292 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:06:55,685 autoreload Watching for file changes with StatReloader
ERROR 2025-04-21 17:07:00,129 log Internal Server Error: /getItemOffers/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 705, in cmd_query
    self._cmysql.query(
_mysql_connector.MySQLInterfaceError: Unknown column 'anazom' in 'field list'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py", line 47, in getItemOffers
    urls = db.fetchall(sql)
           ^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py", line 172, in fetchall
    cursor.execute(query, params)
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\cursor_cext.py", line 357, in execute
    result = self._connection.cmd_query(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\opentelemetry\context_propagation.py", line 97, in wrapper
    return method(cnx, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 713, in cmd_query
    raise get_mysql_exception(
mysql.connector.errors.ProgrammingError: 1054 (42S22): Unknown column 'anazom' in 'field list'
ERROR 2025-04-21 17:07:00,129 basehttp "GET /getItemOffers/ HTTP/1.1" 500 89870
INFO 2025-04-21 17:07:09,659 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:07:10,188 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:07:14,147 basehttp "GET /getItemOffers/ HTTP/1.1" 200 0
INFO 2025-04-21 17:07:57,869 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:07:58,301 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:08:02,786 basehttp "GET /getItemOffers/ HTTP/1.1" 200 0
INFO 2025-04-21 17:08:09,943 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:08:10,426 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:08:14,099 basehttp "GET /getItemOffers/ HTTP/1.1" 200 0
INFO 2025-04-21 17:08:28,194 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:08:28,625 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:08:35,070 basehttp "GET /getItemOffers/ HTTP/1.1" 200 0
INFO 2025-04-21 17:08:50,445 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:08:51,033 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:08:56,066 basehttp "GET /getItemOffers/ HTTP/1.1" 200 0
INFO 2025-04-21 17:09:03,734 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:09:04,178 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:09:16,600 basehttp "GET /getItemOffers/ HTTP/1.1" 200 1620
INFO 2025-04-21 17:13:43,294 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:13:44,399 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:14:26,927 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:14:27,403 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:14:35,878 basehttp "GET /getItemOffers/ HTTP/1.1" 200 216
INFO 2025-04-21 17:15:04,787 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:15:05,365 autoreload Watching for file changes with StatReloader
ERROR 2025-04-21 17:15:10,148 log Internal Server Error: /getItemOffers/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py", line 47, in getItemOffers
    urls = db.fetchone(sql)
           ^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Database.py", line 182, in fetchone
    with self.connection.cursor(dictionary=True) as cursor:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\abstracts.py", line 2244, in __exit__
    self.close()
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\cursor_cext.py", line 524, in close
    self._connection.handle_unread_result()
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\mysql\connector\connection_cext.py", line 1044, in handle_unread_result
    raise InternalError("Unread result found")
mysql.connector.errors.InternalError: Unread result found
ERROR 2025-04-21 17:15:10,150 basehttp "GET /getItemOffers/ HTTP/1.1" 500 83680
INFO 2025-04-21 17:15:15,138 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:15:15,779 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:15:50,816 basehttp "GET /getItemOffers/ HTTP/1.1" 200 216
INFO 2025-04-21 17:19:20,733 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:19:21,155 autoreload Watching for file changes with StatReloader
WARNING 2025-04-21 17:21:15,494 log Not Found: /favicon.ico
WARNING 2025-04-21 17:21:15,495 basehttp "GET /favicon.ico HTTP/1.1" 404 5076
INFO 2025-04-21 17:35:21,258 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:35:21,819 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:35:42,575 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:35:43,050 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:38:03,317 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:38:04,106 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:38:54,496 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:38:55,039 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:39:01,457 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:39:01,917 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:41:35,260 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:41:35,726 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:42:03,903 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:42:04,299 autoreload Watching for file changes with StatReloader
ERROR 2025-04-21 17:42:17,316 log Internal Server Error: /getItemOffers/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py", line 48, in getItemOffers
    urlList = extractAmazonAsin(urls)
              ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py", line 86, in extractAmazonAsin
    clean_url = re.sub(r'#.*$', '', url)
                ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\re\__init__.py", line 186, in sub
    return _compile(pattern, flags).sub(repl, string, count)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: expected string or bytes-like object, got 'list'
ERROR 2025-04-21 17:42:17,321 basehttp "GET /getItemOffers/ HTTP/1.1" 500 76651
INFO 2025-04-21 17:43:05,689 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:43:06,242 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:43:45,514 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:43:46,323 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:45:03,495 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:45:04,177 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:45:17,818 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:45:18,437 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:45:40,147 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:45:40,556 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:45:44,503 basehttp "GET /getItemOffers/ HTTP/1.1" 200 40
INFO 2025-04-21 17:48:42,552 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:48:42,973 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:48:58,602 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:48:58,999 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:49:19,699 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:49:20,135 autoreload Watching for file changes with StatReloader
ERROR 2025-04-21 17:49:29,285 log Internal Server Error: /getItemOffers/
Traceback (most recent call last):
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py", line 70, in getItemOffers
    body["requests"].append(request_data)
    ~~~~^^^^^^^^^^^^
TypeError: string indices must be integers, not 'str'
ERROR 2025-04-21 17:49:29,286 basehttp "GET /getItemOffers/ HTTP/1.1" 500 69805
INFO 2025-04-21 17:50:18,591 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:50:19,012 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:53:20,888 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:53:21,422 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:53:23,839 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:53:24,345 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:53:27,727 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:53:28,168 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:53:43,061 basehttp "GET /getItemOffers/ HTTP/1.1" 200 8
INFO 2025-04-21 17:55:42,394 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:55:42,864 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 17:59:04,334 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 17:59:04,954 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 18:01:51,558 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 18:01:52,222 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 18:02:50,116 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 18:02:50,792 autoreload Watching for file changes with StatReloader
INFO 2025-04-21 18:02:55,087 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\getCatalogItem.py changed, reloading.
INFO 2025-04-21 18:02:55,559 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:31:23,775 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:33:36,962 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\amazon_get_price.py changed, reloading.
INFO 2025-05-27 10:33:38,427 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:33:58,499 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:34:24,529 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:34:39,560 autoreload C:\web\SPAPI_Python_SDK\reports\report.py changed, reloading.
INFO 2025-05-27 10:34:40,242 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:40:04,686 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\urls.py changed, reloading.
INFO 2025-05-27 10:40:05,504 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:40:27,895 basehttp "GET /Search_Term_key_r1/?pro_id=38958&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3FArchive%3D0&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 12
WARNING 2025-05-27 10:40:29,232 log Not Found: /favicon.ico
WARNING 2025-05-27 10:40:29,233 basehttp "GET /favicon.ico HTTP/1.1" 404 5091
INFO 2025-05-27 10:40:33,254 basehttp "GET /Search_Term_key_r1/?pro_id=38958&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3FArchive%3D0&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 12
INFO 2025-05-27 10:42:41,920 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-05-27 10:42:42,443 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:43:33,338 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-05-27 10:43:33,849 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:43:51,024 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-05-27 10:43:52,002 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:44:06,455 basehttp "GET /Search_Term_key_r1/?pro_id=38958&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3FArchive%3D0&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 12
INFO 2025-05-27 10:44:33,120 basehttp "GET /Search_Term_key_r1/?pro_id=38958&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3FArchive%3D0&s_id=de80e1b22dbe0fd35a280092838f0ec8&user_id=50 HTTP/1.1" 200 12
INFO 2025-05-27 10:45:18,904 basehttp "GET /Search_Term_key_r1/?pro_id=38958&self=http%3A%2F%2F127.0.0.4%2Fadd_pro%2Fpro_manage.php%3FArchive%3D0&s_id=de80e1b22dbe0fd35a280092838f0ec8 HTTP/1.1" 200 12
INFO 2025-05-27 10:45:34,693 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-05-27 10:45:35,168 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:46:22,052 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-05-27 10:46:22,618 autoreload Watching for file changes with StatReloader
INFO 2025-05-27 10:47:41,852 autoreload C:\web\SPAPI_Python_SDK\SPAPI_Python_SDK\Search_Term_key_r1.py changed, reloading.
INFO 2025-05-27 10:47:42,795 autoreload Watching for file changes with StatReloader
