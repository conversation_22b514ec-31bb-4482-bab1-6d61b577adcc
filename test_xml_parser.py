#!/usr/bin/env python3
"""
测试XML解析功能
"""

import xml.etree.ElementTree as ET
from datetime import datetime, timedelta

def parse_xml_order_data(xml_data, target_date):
    """
    解析XML格式的订单数据
    :param xml_data: XML字符串数据
    :param target_date: 目标日期（date对象）
    :return: 解析后的订单数据列表
    """
    try:
        # 解析XML
        root = ET.fromstring(xml_data)
        
        # 存储解析后的数据
        parsed_orders = []
        
        # 遍历所有Message节点
        for message in root.findall('Message'):
            order = message.find('Order')
            if order is None:
                continue
                
            # 提取订单基本信息
            amazon_order_id = order.find('AmazonOrderID')
            amazon_order_id = amazon_order_id.text if amazon_order_id is not None else ""
            
            last_updated_date = order.find('LastUpdatedDate')
            if last_updated_date is None:
                continue
                
            # 解析最后更新时间
            try:
                last_updated_datetime = datetime.fromisoformat(last_updated_date.text.replace('Z', '+00:00'))
                print(f"订单 {amazon_order_id} 最后更新时间: {last_updated_datetime}")
                print(f"目标日期: {target_date}")
                print(f"匹配: {last_updated_datetime.date() == target_date}")
                
                # 检查是否为目标日期
                if last_updated_datetime.date() != target_date:
                    continue
            except (ValueError, AttributeError) as e:
                print(f"时间解析错误: {e}")
                continue
            
            # 提取地址信息
            address = order.find('.//Address')
            city = ""
            state = ""
            postal_code = ""
            
            if address is not None:
                city_elem = address.find('City')
                state_elem = address.find('State')
                postal_code_elem = address.find('PostalCode')
                
                city = city_elem.text if city_elem is not None else ""
                state = state_elem.text if state_elem is not None else ""
                postal_code = postal_code_elem.text if postal_code_elem is not None else ""
            
            # 遍历订单项目
            for order_item in order.findall('OrderItem'):
                # 提取SKU
                sku_elem = order_item.find('SKU')
                sku = sku_elem.text if sku_elem is not None else ""
                
                # 提取数量
                quantity_elem = order_item.find('Quantity')
                try:
                    quantity = int(quantity_elem.text) if quantity_elem is not None else 0
                except (ValueError, TypeError):
                    quantity = 0
                
                # 提取价格信息
                item_price_per_unit = 0.0
                shipping_price = 0.0
                currency = ""
                
                item_price = order_item.find('ItemPrice')
                if item_price is not None:
                    for component in item_price.findall('Component'):
                        comp_type = component.find('Type')
                        amount = component.find('Amount')
                        
                        if comp_type is not None and amount is not None:
                            currency = amount.get('currency', '')
                            try:
                                amount_value = float(amount.text)
                                if comp_type.text == 'Principal':
                                    item_price_per_unit = amount_value
                                elif comp_type.text == 'Shipping':
                                    shipping_price = amount_value
                            except (ValueError, TypeError):
                                pass
                
                # 创建订单数据记录
                order_data = {
                    'amazon_order_id': amazon_order_id,
                    'sku': sku,
                    'quantity': quantity,
                    'currency': currency,
                    'item_price_per_unit': item_price_per_unit,
                    'shipping_price': shipping_price,
                    'city': city,
                    'state': state,
                    'postal_code': postal_code,
                    'shipment_date': last_updated_datetime.strftime('%Y-%m-%d %H:%M:%S'),
                    'fulfillment_center_id': ''  # XML中没有这个字段，设为空
                }
                
                parsed_orders.append(order_data)
        
        return parsed_orders
        
    except ET.ParseError as e:
        print(f"XML解析错误: {e}")
        return []
    except Exception as e:
        print(f"处理XML数据时出错: {e}")
        return []

# 测试数据（您提供的XML片段）
test_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<AmazonEnvelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="amzn-envelope.xsd">
<Header>
 <DocumentVersion>1.00</DocumentVersion>
 </Header>
<MessageType>AllOrdersReport</MessageType>
<Message>
   <Order>
      <AmazonOrderID>701-0989738-9849837</AmazonOrderID>
      <MerchantOrderID>701-0989738-9849837</MerchantOrderID>
      <PurchaseDate>2025-06-02T21:56:07+00:00</PurchaseDate>
      <LastUpdatedDate>2025-06-05T07:24:07+00:00</LastUpdatedDate>
      <OrderStatus>Shipped</OrderStatus>
      <SalesChannel>Amazon.com.mx</SalesChannel>
      <FulfillmentData>
         <FulfillmentChannel>Amazon</FulfillmentChannel>
         <ShipServiceLevel>Standard</ShipServiceLevel>
         <Address>
            <City>MONTERREY</City>
            <State>NUEVO LEON</State>
            <PostalCode>64754</PostalCode>
            <Country>MX</Country>
         </Address>
     </FulfillmentData>
     <IsBusinessOrder>false</IsBusinessOrder>
     <OrderItem>
        <AmazonOrderItemCode>129995579298561</AmazonOrderItemCode>
        <ASIN>B095JL8XNC</ASIN>
        <SKU>AX-BSRBCPSC-1</SKU>
        <ItemStatus>Shipped</ItemStatus>
        <ProductName>AXLIZER - 1 cortina enrollable trenzada de 54 yardas de 2 mm, cuerda blanca, cuerda de elevación, cuerda trenzada para reparación de persianas de al</ProductName>
        <Quantity>1</Quantity>
        <ItemPrice>
           <Component>
              <Type>Principal</Type>
              <Amount currency="MXN">295.2</Amount>
           </Component>
        </ItemPrice>
        <SignatureConfirmationRecommended>false</SignatureConfirmationRecommended>
     </OrderItem>
  </Order>
</Message>
</AmazonEnvelope>'''

if __name__ == "__main__":
    # 测试解析
    target_date = datetime(2025, 6, 5).date()  # 2025年6月5日
    print(f"测试目标日期: {target_date}")
    print("=" * 50)
    
    result = parse_xml_order_data(test_xml, target_date)
    
    print(f"\n解析结果: 找到 {len(result)} 个订单项")
    for i, order in enumerate(result, 1):
        print(f"\n订单 {i}:")
        for key, value in order.items():
            print(f"  {key}: {value}")
