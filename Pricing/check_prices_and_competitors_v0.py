from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from spapi.spapiclient import SPAPIClient
from SPAPI_Python_SDK.Database import Database, DB_DATA
from SPAPI_Python_SDK.config import site_query, get_user_key, get_user_id
from SPAPI_Python_SDK.login_user_id import session_middleware, login_required_if_no_user_id, get_ip_address

from tenacity import retry, wait_exponential, stop_after_attempt, retry_if_exception_type
from requests.exceptions import HTTPError, ConnectionError, Timeout
import logging

logger = logging.getLogger('debug_logger')


def before_retry_callback(retry_state):
	exception = retry_state.outcome.exception()
	logger.warning(
		"重试中 | 函数: %s, 第%d次重试, 错误类型: %s, 错误详情: %s",
		retry_state.fn.__name__,
		retry_state.attempt_number,
		type(exception).__name__,  # 直接获取异常类型名称
		str(exception),  # 直接获取异常信息
		exc_info=True
	)


@retry(
	wait=wait_exponential(multiplier=1, min=2, max=60),
	stop=stop_after_attempt(3),
	retry=retry_if_exception_type((ConnectionError, Timeout, HTTPError)),
	before_sleep=before_retry_callback
)
def safe_call_spapi(price_api, body):
	try:
		response = price_api.get_listing_offers_batch(body, _request_timeout=(15, 30))
		return response
	except Exception as e:
		# 包装非指定异常为HTTPError
		if not isinstance(e, (HTTPError, ConnectionError, Timeout)):
			raise HTTPError(f"API请求底层错误: {str(e)}") from e
		else:
			raise


@session_middleware
@login_required_if_no_user_id
def check_prices_and_competitors_v0(request):
	try:
		page_size = 20
		page = int(request.GET.get('page', "1"))
		return_url = request.GET.get('self', "")

		site_id = site_query(request, return_url)
		user_id = get_user_id(request, site_id)

		dbdata = DB_DATA[site_id]
		db = Database(dbdata["DB_HOST"], dbdata["DB_NAME"], dbdata["DB_USER"], dbdata["DB_PWD"])
		if site_id in [2, 3]:
			sql = f"SELECT sku FROM sku_pro WHERE del_YN = 1 AND sku IS NOT NULL AND sku != '' ORDER BY id DESC LIMIT {page_size * (page - 1)},{page_size}"
		else:
			sql = f"SELECT sku FROM sku_pro WHERE user_id={user_id} AND del_YN<>0 AND sku !='' ORDER BY id DESC LIMIT {page_size * (page - 1)},{page_size}"

		# return HttpResponse(sql)

		data = db.fetchall(sql)
		if not data:
			if site_id in [2, 3]:
				return HttpResponse("检测完成")
			else:
				sql = f"SELECT id FROM user WHERE MWS_Switch=1 AND id>{user_id} AND Father_id=0 ORDER BY id ASC LIMIT 1"
				user_rs = db.fetchone(sql)
				if user_rs and len(user_rs) > 0:
					user_id = user_rs["id"]
					show_context = f"即将抓取用户{user_id}"

					go_url = f"/Pricing/check_prices_and_competitors_v0/?user_id={user_id}&self={return_url}&page=1"
					context = {"show_context": show_context, "go_url": go_url, "page_title": "查询所有sku跟卖和价格",
							   "second": 8}
					return render(request, "SPAPI_Python_SDK/goto.html", context)
				else:
					return HttpResponse("检测完成")
		else:
			config = get_user_key(db, user_id)
			api_client = SPAPIClient(config, "swagger_client_Pricing")
			price_api = api_client.get_api_client('ProductPricingApi')
			marketplace_id = "ATVPDKIKX0DER"
			item_condition = "New"
			body = {
				"requests": []
			}

			for row in data:
				request_data = {
					"uri": f"/products/pricing/v0/listings/{row['sku']}/offers",
					"method": "GET",
					"MarketplaceId": marketplace_id,
					"ItemCondition": item_condition
				}
				body["requests"].append(request_data)

			# price_response = price_api.get_listing_offers_batch(body)
			try:
				price_response = safe_call_spapi(price_api, body)
			except HTTPError as e:
				if e.response.status_code == 429:
					logging.error("触发速率限制，等待后重试")
					raise
				else:
					logging.error(f"API错误: {e.response.text}")
					return JsonResponse({"error": "API服务异常"}, status=500)
			except Exception as e:
				logging.exception("未捕获异常")
				return render(request, "SPAPI_Python_SDK/retry.html", status=500)
			responses = price_response.to_dict().get('responses', [])

			for response in responses:
				status = response.get('status', {}).get('statusCode')
				if status == 200:
					payload = response.get('body', {}).get('payload', {})
					sku = payload.get('SKU')
					summary = payload.get('Summary', {})
					total_offer_count = summary.get('TotalOfferCount', 0)  # 看是否有跟卖
					offers = payload.get('Offers', [])

					Field_money = {
						"AMAZON": "fba_money",
						"MERCHANT": "mba_money"
					}
					for offer in offers:
						my_offer = offer.get('MyOffer', False)
						channel = offer.get('IsFulfilledByAmazon', False)
						channel_type = "AMAZON" if channel else "MERCHANT"
						price = offer.get('ListingPrice', {}).get('Amount', 0)

						if price > 0:
							field_name = Field_money.get(channel_type, "")
							# 更新 SKU 价格信息
							update_sql = f"UPDATE sku_pro SET {field_name} = {price}, Channel = '{channel_type}', del_YN = 1 WHERE sku = '{sku}'"
							db.update(update_sql)

					sql = f"SELECT id, Lowest_Offer FROM sku_pro WHERE sku='{sku}'";
					result = db.fetchone(sql)
					sku_id = result['id']

					if total_offer_count > 1:
						# 更新最低 offer 标志
						update_sql = f"UPDATE sku_pro SET Lowest_Offer = 1 WHERE id = {sku_id}"
						db.update(update_sql)

						# 删除旧的最低 offer 列表记录
						delete_sql = f"DELETE FROM lowest_offer_listings WHERE sku_id = {sku_id}"
						db.delete(delete_sql)

						# 插入新的最低 offer 列表记录
						for offer in offers:
							price = offer.get('ListingPrice', {}).get('Amount', 0)
							insert_sql = f"INSERT INTO lowest_offer_listings (sku_id, Price) VALUES ({sku_id}, {price})"
							db.insert(insert_sql)

					else:
						# 如果只有一个 offer，设置 Lowest_Offer 为 0
						# 删除旧的最低 offer 列表记录
						update_offer_sql = f"UPDATE sku_pro SET Lowest_Offer = 0 WHERE id = {sku_id}"
						db.update(update_offer_sql)
						delete_offer_listings_sql = f"DELETE FROM lowest_offer_listings WHERE sku_id = {sku_id}"
						db.delete(delete_offer_listings_sql)

				else:
					# 处理非 200 状态码的响应
					error_message = response.get('body', {}).get('errors', [])
					# 可以在此处记录日志或处理错误信息
					if error_message == 500:
						return render(request, "SPAPI_Python_SDK/retry.html")

		# 单页数据操作结束，该跳转下一页
		show_context = f"第{page}页获取成功"
		page += 1
		go_url = f"/Pricing/check_prices_and_competitors_v0?user_id={user_id}&self={return_url}&page={page}"
		context = {"show_context": show_context, "go_url": go_url, "page_title": "查询所有sku跟卖和价格", "second": 8}

		return render(request, "SPAPI_Python_SDK/goto.html", context)

	except Exception as e:
		logger.exception("未捕获的全局异常")  # 记录完整堆栈信息
		return render(request, "SPAPI_Python_SDK/retry.html", status=500)
