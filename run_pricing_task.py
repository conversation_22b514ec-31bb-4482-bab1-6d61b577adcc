import os
import sys
import django
from django.conf import settings

# 添加项目根目录到Python路径
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(BASE_DIR)

# 设置Django环境
if not settings.configured:
	os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SPAPI_Python_SDK.settings')
	django.setup()

# 导入业务模块
from Pricing import up_package  # 确保目录名是pricing且包含__init__.py


def main():
	up_package.up_package(
		param1="value1",
		param2="value2"
	)


if __name__ == "__main__":
	main()
