#!/usr/bin/env python3
"""
数据库连接测试脚本
用于验证数据库配置是否正确
"""

import mysql.connector
from mysql.connector import Error

# 测试配置
DB_CONFIGS = {
    "localhost": {
        "host": "localhost",
        "user": "ad_site",
        "password": "ad_site0161",
        "database": "zom_ems"
    },
    "************": {
        "host": "************",
        "user": "ad_site", 
        "password": "ad_site0161",
        "database": "zom_ems"
    },
    "*************": {
        "host": "*************",
        "user": "ad_site",
        "password": "ad_site0161", 
        "database": "zom_ems"
    }
}

def test_connection(config_name, config):
    """测试单个数据库连接"""
    print(f"\n测试连接到 {config_name} ({config['host']})...")
    
    try:
        connection = mysql.connector.connect(
            host=config['host'],
            database=config['database'],
            user=config['user'],
            password=config['password'],
            connect_timeout=10
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ 连接成功！MySQL版本: {version[0]}")
            
            # 测试查询用户表
            cursor.execute("SELECT COUNT(*) FROM user")
            user_count = cursor.fetchone()
            print(f"✅ 用户表查询成功，共有 {user_count[0]} 个用户")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        print(f"❌ 连接失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def main():
    """主函数"""
    print("开始测试数据库连接...")
    
    successful_configs = []
    
    for config_name, config in DB_CONFIGS.items():
        if test_connection(config_name, config):
            successful_configs.append(config_name)
    
    print(f"\n{'='*50}")
    print("测试结果总结:")
    
    if successful_configs:
        print(f"✅ 成功连接的配置: {', '.join(successful_configs)}")
        print(f"建议使用: {successful_configs[0]}")
    else:
        print("❌ 所有连接都失败了")
        print("请检查:")
        print("1. 网络连接")
        print("2. 数据库服务器状态")
        print("3. 用户名和密码")
        print("4. 防火墙设置")

if __name__ == "__main__":
    main()
