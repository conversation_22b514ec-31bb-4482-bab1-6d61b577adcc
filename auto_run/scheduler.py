import schedule
import time
import subprocess
import sys
import logging
from pathlib import Path

# --- 配置 ---
# 要定时运行的 Python 脚本的文件名
TARGET_SCRIPT_NAME = "another_file.py"
# 设置运行间隔（例如，每 10 分钟）
# 其他选项: .seconds, .minutes, .hours, .days, .weeks
# 也可以指定具体时间: .day.at("10:30")
# 或每周某天: .monday.at("13:15")
RUN_INTERVAL_MINUTES = 10

# 日志配置
logging.basicConfig(level=logging.INFO,
					format='%(asctime)s - %(levelname)s - %(message)s')
# --- 配置结束 ---

# 获取当前脚本所在的目录
current_dir = Path(__file__).parent.resolve()
# 拼接目标脚本的完整路径
target_script_path = current_dir / TARGET_SCRIPT_NAME


def run_target_script():
	"""
	执行目标 Python 脚本。
	"""
	if not target_script_path.is_file():
		logging.error(f"错误：目标脚本 '{target_script_path}' 未找到。")
		return

	logging.info(f"开始运行脚本: {target_script_path}")
	try:
		# 使用 subprocess.run 运行脚本
		# sys.executable 确保使用与运行此调度程序相同的 Python 解释器
		# check=True 会在脚本返回非零退出码（表示错误）时抛出 CalledProcessError
		# capture_output=True 可以捕获标准输出和标准错误，如果需要的话
		# text=True (或 encoding='utf-8') 使输出/错误为字符串而非字节
		result = subprocess.run(
			[sys.executable, str(target_script_path)],
			check=True,
			capture_output=True,
			text=True,
			cwd=current_dir  # 在目标脚本所在目录运行，确保相对路径正确
		)
		logging.info(f"脚本 {TARGET_SCRIPT_NAME} 成功完成。")
		if result.stdout:
			logging.info(f"脚本输出:\n{result.stdout}")
		if result.stderr:
			# 有些程序可能将正常信息输出到 stderr，所以这里用 warning
			logging.warning(f"脚本错误输出(stderr):\n{result.stderr}")

	except FileNotFoundError:
		logging.error(f"错误：找不到 Python 解释器 '{sys.executable}' 或脚本 '{target_script_path}'。")
	except subprocess.CalledProcessError as e:
		logging.error(f"脚本 {TARGET_SCRIPT_NAME} 执行出错。返回码: {e.returncode}")
		if e.stdout:
			logging.error(f"脚本输出 (stdout):\n{e.stdout}")
		if e.stderr:
			logging.error(f"脚本错误输出 (stderr):\n{e.stderr}")
	except Exception as e:
		logging.exception(f"运行脚本 {TARGET_SCRIPT_NAME} 时发生未知错误: {e}")


def main():
	"""
	主函数，设置调度并启动循环。
	"""
	logging.info("调度器启动...")
	logging.info(f"将每隔 {RUN_INTERVAL_MINUTES} 分钟运行一次脚本 '{TARGET_SCRIPT_NAME}'")

	# --- 设置调度任务 ---
	# schedule.every(RUN_INTERVAL_MINUTES).minutes.do(run_target_script)
	# schedule.every().hour.do(run_target_script)
	# schedule.every().day.at("08:00").do(run_target_script)
	# schedule.every().monday.at("10:30").do(run_target_script)

	# 根据配置设置调度
	schedule.every(RUN_INTERVAL_MINUTES).minutes.do(run_target_script)

	# 立即执行一次（可选）
	# logging.info("立即执行第一次运行...")
	# run_target_script()

	# --- 运行调度循环 ---
	while True:
		# 检查是否有任何预定的任务需要运行
		schedule.run_pending()
		# 等待一秒钟，避免 CPU 占用过高
		time.sleep(1)


if __name__ == "__main__":
	main()
