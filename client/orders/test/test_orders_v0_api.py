# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_order
from swagger_client_order.api.orders_v0_api import OrdersV0Api  # noqa: E501
from swagger_client_order.rest import ApiException


class TestOrdersV0Api(unittest.TestCase):
    """OrdersV0Api unit test stubs"""

    def setUp(self):
        self.api = swagger_client_order.api.orders_v0_api.OrdersV0Api()  # noqa: E501

    def tearDown(self):
        pass

    def test_confirm_shipment(self):
        """Test case for confirm_shipment

        """
        pass

    def test_get_order(self):
        """Test case for get_order

        """
        pass

    def test_get_order_address(self):
        """Test case for get_order_address

        """
        pass

    def test_get_order_buyer_info(self):
        """Test case for get_order_buyer_info

        """
        pass

    def test_get_order_items(self):
        """Test case for get_order_items

        """
        pass

    def test_get_order_items_buyer_info(self):
        """Test case for get_order_items_buyer_info

        """
        pass

    def test_get_order_regulated_info(self):
        """Test case for get_order_regulated_info

        """
        pass

    def test_get_orders(self):
        """Test case for get_orders

        """
        pass

    def test_update_verification_status(self):
        """Test case for update_verification_status

        """
        pass


if __name__ == '__main__':
    unittest.main()