# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_order
from swagger_client_order.models.substitution_option_list import SubstitutionOptionList  # noqa: E501
from swagger_client_order.rest import ApiException


class TestSubstitutionOptionList(unittest.TestCase):
    """SubstitutionOptionList unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testSubstitutionOptionList(self):
        """Test SubstitutionOptionList"""
        # FIXME: construct object with mandatory attributes with example values
        # model = swagger_client_order.models.substitution_option_list.SubstitutionOptionList()  # noqa: E501
        pass


if __name__ == '__main__':
    unittest.main()
