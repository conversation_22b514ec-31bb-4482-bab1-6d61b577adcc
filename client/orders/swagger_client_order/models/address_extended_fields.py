# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class AddressExtendedFields(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'street_name': 'str',
        'street_number': 'str',
        'complement': 'str',
        'neighborhood': 'str'
    }

    attribute_map = {
        'street_name': 'StreetName',
        'street_number': 'StreetNumber',
        'complement': 'Complement',
        'neighborhood': 'Neighborhood'
    }

    def __init__(self, street_name=None, street_number=None, complement=None, neighborhood=None):  # noqa: E501
        """AddressExtendedFields - a model defined in Swagger"""  # noqa: E501

        self._street_name = None
        self._street_number = None
        self._complement = None
        self._neighborhood = None
        self.discriminator = None

        if street_name is not None:
            self.street_name = street_name
        if street_number is not None:
            self.street_number = street_number
        if complement is not None:
            self.complement = complement
        if neighborhood is not None:
            self.neighborhood = neighborhood

    @property
    def street_name(self):
        """Gets the street_name of this AddressExtendedFields.  # noqa: E501

        The street name.  # noqa: E501

        :return: The street_name of this AddressExtendedFields.  # noqa: E501
        :rtype: str
        """
        return self._street_name

    @street_name.setter
    def street_name(self, street_name):
        """Sets the street_name of this AddressExtendedFields.

        The street name.  # noqa: E501

        :param street_name: The street_name of this AddressExtendedFields.  # noqa: E501
        :type: str
        """

        self._street_name = street_name

    @property
    def street_number(self):
        """Gets the street_number of this AddressExtendedFields.  # noqa: E501

        The house number/building number/property number in the street.  # noqa: E501

        :return: The street_number of this AddressExtendedFields.  # noqa: E501
        :rtype: str
        """
        return self._street_number

    @street_number.setter
    def street_number(self, street_number):
        """Sets the street_number of this AddressExtendedFields.

        The house number/building number/property number in the street.  # noqa: E501

        :param street_number: The street_number of this AddressExtendedFields.  # noqa: E501
        :type: str
        """

        self._street_number = street_number

    @property
    def complement(self):
        """Gets the complement of this AddressExtendedFields.  # noqa: E501

        The floor number/unit number in the building/private house number.  # noqa: E501

        :return: The complement of this AddressExtendedFields.  # noqa: E501
        :rtype: str
        """
        return self._complement

    @complement.setter
    def complement(self, complement):
        """Sets the complement of this AddressExtendedFields.

        The floor number/unit number in the building/private house number.  # noqa: E501

        :param complement: The complement of this AddressExtendedFields.  # noqa: E501
        :type: str
        """

        self._complement = complement

    @property
    def neighborhood(self):
        """Gets the neighborhood of this AddressExtendedFields.  # noqa: E501

        The neighborhood. It's smaller than a region and an integral part of an address. It is used in some countries like Brazil.  # noqa: E501

        :return: The neighborhood of this AddressExtendedFields.  # noqa: E501
        :rtype: str
        """
        return self._neighborhood

    @neighborhood.setter
    def neighborhood(self, neighborhood):
        """Sets the neighborhood of this AddressExtendedFields.

        The neighborhood. It's smaller than a region and an integral part of an address. It is used in some countries like Brazil.  # noqa: E501

        :param neighborhood: The neighborhood of this AddressExtendedFields.  # noqa: E501
        :type: str
        """

        self._neighborhood = neighborhood

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AddressExtendedFields, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AddressExtendedFields):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
