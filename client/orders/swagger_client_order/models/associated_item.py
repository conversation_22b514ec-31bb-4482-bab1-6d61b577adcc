# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class AssociatedItem(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'order_id': 'str',
        'order_item_id': 'str',
        'association_type': 'AssociationType'
    }

    attribute_map = {
        'order_id': 'OrderId',
        'order_item_id': 'OrderItemId',
        'association_type': 'AssociationType'
    }

    def __init__(self, order_id=None, order_item_id=None, association_type=None):  # noqa: E501
        """AssociatedItem - a model defined in Swagger"""  # noqa: E501

        self._order_id = None
        self._order_item_id = None
        self._association_type = None
        self.discriminator = None

        if order_id is not None:
            self.order_id = order_id
        if order_item_id is not None:
            self.order_item_id = order_item_id
        if association_type is not None:
            self.association_type = association_type

    @property
    def order_id(self):
        """Gets the order_id of this AssociatedItem.  # noqa: E501

        The order item's order identifier, in 3-7-7 format.  # noqa: E501

        :return: The order_id of this AssociatedItem.  # noqa: E501
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        """Sets the order_id of this AssociatedItem.

        The order item's order identifier, in 3-7-7 format.  # noqa: E501

        :param order_id: The order_id of this AssociatedItem.  # noqa: E501
        :type: str
        """

        self._order_id = order_id

    @property
    def order_item_id(self):
        """Gets the order_item_id of this AssociatedItem.  # noqa: E501

        An Amazon-defined item identifier for the associated item.  # noqa: E501

        :return: The order_item_id of this AssociatedItem.  # noqa: E501
        :rtype: str
        """
        return self._order_item_id

    @order_item_id.setter
    def order_item_id(self, order_item_id):
        """Sets the order_item_id of this AssociatedItem.

        An Amazon-defined item identifier for the associated item.  # noqa: E501

        :param order_item_id: The order_item_id of this AssociatedItem.  # noqa: E501
        :type: str
        """

        self._order_item_id = order_item_id

    @property
    def association_type(self):
        """Gets the association_type of this AssociatedItem.  # noqa: E501


        :return: The association_type of this AssociatedItem.  # noqa: E501
        :rtype: AssociationType
        """
        return self._association_type

    @association_type.setter
    def association_type(self, association_type):
        """Sets the association_type of this AssociatedItem.


        :param association_type: The association_type of this AssociatedItem.  # noqa: E501
        :type: AssociationType
        """

        self._association_type = association_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AssociatedItem, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AssociatedItem):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
