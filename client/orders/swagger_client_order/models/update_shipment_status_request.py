# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class UpdateShipmentStatusRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'marketplace_id': 'MarketplaceId',
        'shipment_status': 'ShipmentStatus',
        'order_items': 'OrderItems'
    }

    attribute_map = {
        'marketplace_id': 'marketplaceId',
        'shipment_status': 'shipmentStatus',
        'order_items': 'orderItems'
    }

    def __init__(self, marketplace_id=None, shipment_status=None, order_items=None):  # noqa: E501
        """UpdateShipmentStatusRequest - a model defined in Swagger"""  # noqa: E501

        self._marketplace_id = None
        self._shipment_status = None
        self._order_items = None
        self.discriminator = None

        self.marketplace_id = marketplace_id
        self.shipment_status = shipment_status
        if order_items is not None:
            self.order_items = order_items

    @property
    def marketplace_id(self):
        """Gets the marketplace_id of this UpdateShipmentStatusRequest.  # noqa: E501


        :return: The marketplace_id of this UpdateShipmentStatusRequest.  # noqa: E501
        :rtype: MarketplaceId
        """
        return self._marketplace_id

    @marketplace_id.setter
    def marketplace_id(self, marketplace_id):
        """Sets the marketplace_id of this UpdateShipmentStatusRequest.


        :param marketplace_id: The marketplace_id of this UpdateShipmentStatusRequest.  # noqa: E501
        :type: MarketplaceId
        """
        if marketplace_id is None:
            raise ValueError("Invalid value for `marketplace_id`, must not be `None`")  # noqa: E501

        self._marketplace_id = marketplace_id

    @property
    def shipment_status(self):
        """Gets the shipment_status of this UpdateShipmentStatusRequest.  # noqa: E501


        :return: The shipment_status of this UpdateShipmentStatusRequest.  # noqa: E501
        :rtype: ShipmentStatus
        """
        return self._shipment_status

    @shipment_status.setter
    def shipment_status(self, shipment_status):
        """Sets the shipment_status of this UpdateShipmentStatusRequest.


        :param shipment_status: The shipment_status of this UpdateShipmentStatusRequest.  # noqa: E501
        :type: ShipmentStatus
        """
        if shipment_status is None:
            raise ValueError("Invalid value for `shipment_status`, must not be `None`")  # noqa: E501

        self._shipment_status = shipment_status

    @property
    def order_items(self):
        """Gets the order_items of this UpdateShipmentStatusRequest.  # noqa: E501


        :return: The order_items of this UpdateShipmentStatusRequest.  # noqa: E501
        :rtype: OrderItems
        """
        return self._order_items

    @order_items.setter
    def order_items(self, order_items):
        """Sets the order_items of this UpdateShipmentStatusRequest.


        :param order_items: The order_items of this UpdateShipmentStatusRequest.  # noqa: E501
        :type: OrderItems
        """

        self._order_items = order_items

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateShipmentStatusRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateShipmentStatusRequest):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
