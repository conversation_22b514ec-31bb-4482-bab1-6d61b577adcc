# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ProductInfoDetail(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'number_of_items': 'str'
    }

    attribute_map = {
        'number_of_items': 'NumberOfItems'
    }

    def __init__(self, number_of_items=None):  # noqa: E501
        """ProductInfoDetail - a model defined in Swagger"""  # noqa: E501

        self._number_of_items = None
        self.discriminator = None

        if number_of_items is not None:
            self.number_of_items = number_of_items

    @property
    def number_of_items(self):
        """Gets the number_of_items of this ProductInfoDetail.  # noqa: E501

        The total number of items that are included in the ASIN.  # noqa: E501

        :return: The number_of_items of this ProductInfoDetail.  # noqa: E501
        :rtype: str
        """
        return self._number_of_items

    @number_of_items.setter
    def number_of_items(self, number_of_items):
        """Sets the number_of_items of this ProductInfoDetail.

        The total number of items that are included in the ASIN.  # noqa: E501

        :param number_of_items: The number_of_items of this ProductInfoDetail.  # noqa: E501
        :type: str
        """

        self._number_of_items = number_of_items

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProductInfoDetail, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProductInfoDetail):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
