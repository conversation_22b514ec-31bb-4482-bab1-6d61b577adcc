# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class OrderAddress(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'amazon_order_id': 'str',
        'buyer_company_name': 'str',
        'shipping_address': 'Address',
        'delivery_preferences': 'DeliveryPreferences'
    }

    attribute_map = {
        'amazon_order_id': 'AmazonOrderId',
        'buyer_company_name': 'BuyerCompanyName',
        'shipping_address': 'ShippingAddress',
        'delivery_preferences': 'DeliveryPreferences'
    }

    def __init__(self, amazon_order_id=None, buyer_company_name=None, shipping_address=None, delivery_preferences=None):  # noqa: E501
        """OrderAddress - a model defined in Swagger"""  # noqa: E501

        self._amazon_order_id = None
        self._buyer_company_name = None
        self._shipping_address = None
        self._delivery_preferences = None
        self.discriminator = None

        self.amazon_order_id = amazon_order_id
        if buyer_company_name is not None:
            self.buyer_company_name = buyer_company_name
        if shipping_address is not None:
            self.shipping_address = shipping_address
        if delivery_preferences is not None:
            self.delivery_preferences = delivery_preferences

    @property
    def amazon_order_id(self):
        """Gets the amazon_order_id of this OrderAddress.  # noqa: E501

        An Amazon-defined order identifier, in 3-7-7 format.  # noqa: E501

        :return: The amazon_order_id of this OrderAddress.  # noqa: E501
        :rtype: str
        """
        return self._amazon_order_id

    @amazon_order_id.setter
    def amazon_order_id(self, amazon_order_id):
        """Sets the amazon_order_id of this OrderAddress.

        An Amazon-defined order identifier, in 3-7-7 format.  # noqa: E501

        :param amazon_order_id: The amazon_order_id of this OrderAddress.  # noqa: E501
        :type: str
        """
        if amazon_order_id is None:
            raise ValueError("Invalid value for `amazon_order_id`, must not be `None`")  # noqa: E501

        self._amazon_order_id = amazon_order_id

    @property
    def buyer_company_name(self):
        """Gets the buyer_company_name of this OrderAddress.  # noqa: E501

        The company name of the contact buyer. For Invoice By Amazon (IBA) orders, the buyer company should be Amazon entities.  # noqa: E501

        :return: The buyer_company_name of this OrderAddress.  # noqa: E501
        :rtype: str
        """
        return self._buyer_company_name

    @buyer_company_name.setter
    def buyer_company_name(self, buyer_company_name):
        """Sets the buyer_company_name of this OrderAddress.

        The company name of the contact buyer. For Invoice By Amazon (IBA) orders, the buyer company should be Amazon entities.  # noqa: E501

        :param buyer_company_name: The buyer_company_name of this OrderAddress.  # noqa: E501
        :type: str
        """

        self._buyer_company_name = buyer_company_name

    @property
    def shipping_address(self):
        """Gets the shipping_address of this OrderAddress.  # noqa: E501

        The shipping address for the order.  **Note**: `ShippingAddress` is only available for orders with the following status values: `Unshipped`, `PartiallyShipped`, `Shipped` and `InvoiceUnconfirmed`.  # noqa: E501

        :return: The shipping_address of this OrderAddress.  # noqa: E501
        :rtype: Address
        """
        return self._shipping_address

    @shipping_address.setter
    def shipping_address(self, shipping_address):
        """Sets the shipping_address of this OrderAddress.

        The shipping address for the order.  **Note**: `ShippingAddress` is only available for orders with the following status values: `Unshipped`, `PartiallyShipped`, `Shipped` and `InvoiceUnconfirmed`.  # noqa: E501

        :param shipping_address: The shipping_address of this OrderAddress.  # noqa: E501
        :type: Address
        """

        self._shipping_address = shipping_address

    @property
    def delivery_preferences(self):
        """Gets the delivery_preferences of this OrderAddress.  # noqa: E501


        :return: The delivery_preferences of this OrderAddress.  # noqa: E501
        :rtype: DeliveryPreferences
        """
        return self._delivery_preferences

    @delivery_preferences.setter
    def delivery_preferences(self, delivery_preferences):
        """Sets the delivery_preferences of this OrderAddress.


        :param delivery_preferences: The delivery_preferences of this OrderAddress.  # noqa: E501
        :type: DeliveryPreferences
        """

        self._delivery_preferences = delivery_preferences

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OrderAddress, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OrderAddress):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
