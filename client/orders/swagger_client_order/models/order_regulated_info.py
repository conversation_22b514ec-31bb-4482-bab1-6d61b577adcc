# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class OrderRegulatedInfo(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'amazon_order_id': 'str',
        'regulated_information': 'RegulatedInformation',
        'requires_dosage_label': 'bool',
        'regulated_order_verification_status': 'RegulatedOrderVerificationStatus'
    }

    attribute_map = {
        'amazon_order_id': 'AmazonOrderId',
        'regulated_information': 'RegulatedInformation',
        'requires_dosage_label': 'RequiresDosageLabel',
        'regulated_order_verification_status': 'RegulatedOrderVerificationStatus'
    }

    def __init__(self, amazon_order_id=None, regulated_information=None, requires_dosage_label=None, regulated_order_verification_status=None):  # noqa: E501
        """OrderRegulatedInfo - a model defined in Swagger"""  # noqa: E501

        self._amazon_order_id = None
        self._regulated_information = None
        self._requires_dosage_label = None
        self._regulated_order_verification_status = None
        self.discriminator = None

        self.amazon_order_id = amazon_order_id
        self.regulated_information = regulated_information
        self.requires_dosage_label = requires_dosage_label
        self.regulated_order_verification_status = regulated_order_verification_status

    @property
    def amazon_order_id(self):
        """Gets the amazon_order_id of this OrderRegulatedInfo.  # noqa: E501

        An Amazon-defined order identifier, in 3-7-7 format.  # noqa: E501

        :return: The amazon_order_id of this OrderRegulatedInfo.  # noqa: E501
        :rtype: str
        """
        return self._amazon_order_id

    @amazon_order_id.setter
    def amazon_order_id(self, amazon_order_id):
        """Sets the amazon_order_id of this OrderRegulatedInfo.

        An Amazon-defined order identifier, in 3-7-7 format.  # noqa: E501

        :param amazon_order_id: The amazon_order_id of this OrderRegulatedInfo.  # noqa: E501
        :type: str
        """
        if amazon_order_id is None:
            raise ValueError("Invalid value for `amazon_order_id`, must not be `None`")  # noqa: E501

        self._amazon_order_id = amazon_order_id

    @property
    def regulated_information(self):
        """Gets the regulated_information of this OrderRegulatedInfo.  # noqa: E501

        The regulated information collected during purchase and used to verify the order.  # noqa: E501

        :return: The regulated_information of this OrderRegulatedInfo.  # noqa: E501
        :rtype: RegulatedInformation
        """
        return self._regulated_information

    @regulated_information.setter
    def regulated_information(self, regulated_information):
        """Sets the regulated_information of this OrderRegulatedInfo.

        The regulated information collected during purchase and used to verify the order.  # noqa: E501

        :param regulated_information: The regulated_information of this OrderRegulatedInfo.  # noqa: E501
        :type: RegulatedInformation
        """
        if regulated_information is None:
            raise ValueError("Invalid value for `regulated_information`, must not be `None`")  # noqa: E501

        self._regulated_information = regulated_information

    @property
    def requires_dosage_label(self):
        """Gets the requires_dosage_label of this OrderRegulatedInfo.  # noqa: E501

        When true, the order requires attaching a dosage information label when shipped.  # noqa: E501

        :return: The requires_dosage_label of this OrderRegulatedInfo.  # noqa: E501
        :rtype: bool
        """
        return self._requires_dosage_label

    @requires_dosage_label.setter
    def requires_dosage_label(self, requires_dosage_label):
        """Sets the requires_dosage_label of this OrderRegulatedInfo.

        When true, the order requires attaching a dosage information label when shipped.  # noqa: E501

        :param requires_dosage_label: The requires_dosage_label of this OrderRegulatedInfo.  # noqa: E501
        :type: bool
        """
        if requires_dosage_label is None:
            raise ValueError("Invalid value for `requires_dosage_label`, must not be `None`")  # noqa: E501

        self._requires_dosage_label = requires_dosage_label

    @property
    def regulated_order_verification_status(self):
        """Gets the regulated_order_verification_status of this OrderRegulatedInfo.  # noqa: E501

        The order's verification status.  # noqa: E501

        :return: The regulated_order_verification_status of this OrderRegulatedInfo.  # noqa: E501
        :rtype: RegulatedOrderVerificationStatus
        """
        return self._regulated_order_verification_status

    @regulated_order_verification_status.setter
    def regulated_order_verification_status(self, regulated_order_verification_status):
        """Sets the regulated_order_verification_status of this OrderRegulatedInfo.

        The order's verification status.  # noqa: E501

        :param regulated_order_verification_status: The regulated_order_verification_status of this OrderRegulatedInfo.  # noqa: E501
        :type: RegulatedOrderVerificationStatus
        """
        if regulated_order_verification_status is None:
            raise ValueError("Invalid value for `regulated_order_verification_status`, must not be `None`")  # noqa: E501

        self._regulated_order_verification_status = regulated_order_verification_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OrderRegulatedInfo, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OrderRegulatedInfo):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
