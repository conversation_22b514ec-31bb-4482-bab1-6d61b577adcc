# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class RegulatedInformationField(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'field_id': 'str',
        'field_label': 'str',
        'field_type': 'str',
        'field_value': 'str'
    }

    attribute_map = {
        'field_id': 'FieldId',
        'field_label': 'FieldLabel',
        'field_type': 'FieldType',
        'field_value': 'FieldValue'
    }

    def __init__(self, field_id=None, field_label=None, field_type=None, field_value=None):  # noqa: E501
        """RegulatedInformationField - a model defined in Swagger"""  # noqa: E501

        self._field_id = None
        self._field_label = None
        self._field_type = None
        self._field_value = None
        self.discriminator = None

        self.field_id = field_id
        self.field_label = field_label
        self.field_type = field_type
        self.field_value = field_value

    @property
    def field_id(self):
        """Gets the field_id of this RegulatedInformationField.  # noqa: E501

        The unique identifier for the field.  # noqa: E501

        :return: The field_id of this RegulatedInformationField.  # noqa: E501
        :rtype: str
        """
        return self._field_id

    @field_id.setter
    def field_id(self, field_id):
        """Sets the field_id of this RegulatedInformationField.

        The unique identifier for the field.  # noqa: E501

        :param field_id: The field_id of this RegulatedInformationField.  # noqa: E501
        :type: str
        """
        if field_id is None:
            raise ValueError("Invalid value for `field_id`, must not be `None`")  # noqa: E501

        self._field_id = field_id

    @property
    def field_label(self):
        """Gets the field_label of this RegulatedInformationField.  # noqa: E501

        The name for the field.  # noqa: E501

        :return: The field_label of this RegulatedInformationField.  # noqa: E501
        :rtype: str
        """
        return self._field_label

    @field_label.setter
    def field_label(self, field_label):
        """Sets the field_label of this RegulatedInformationField.

        The name for the field.  # noqa: E501

        :param field_label: The field_label of this RegulatedInformationField.  # noqa: E501
        :type: str
        """
        if field_label is None:
            raise ValueError("Invalid value for `field_label`, must not be `None`")  # noqa: E501

        self._field_label = field_label

    @property
    def field_type(self):
        """Gets the field_type of this RegulatedInformationField.  # noqa: E501

        The type of field.  # noqa: E501

        :return: The field_type of this RegulatedInformationField.  # noqa: E501
        :rtype: str
        """
        return self._field_type

    @field_type.setter
    def field_type(self, field_type):
        """Sets the field_type of this RegulatedInformationField.

        The type of field.  # noqa: E501

        :param field_type: The field_type of this RegulatedInformationField.  # noqa: E501
        :type: str
        """
        if field_type is None:
            raise ValueError("Invalid value for `field_type`, must not be `None`")  # noqa: E501
        allowed_values = ["Text", "FileAttachment"]  # noqa: E501
        if field_type not in allowed_values:
            raise ValueError(
                "Invalid value for `field_type` ({0}), must be one of {1}"  # noqa: E501
                .format(field_type, allowed_values)
            )

        self._field_type = field_type

    @property
    def field_value(self):
        """Gets the field_value of this RegulatedInformationField.  # noqa: E501

        The content of the field as collected in regulatory form. Note that `FileAttachment` type fields will contain a URL to download the attachment here.  # noqa: E501

        :return: The field_value of this RegulatedInformationField.  # noqa: E501
        :rtype: str
        """
        return self._field_value

    @field_value.setter
    def field_value(self, field_value):
        """Sets the field_value of this RegulatedInformationField.

        The content of the field as collected in regulatory form. Note that `FileAttachment` type fields will contain a URL to download the attachment here.  # noqa: E501

        :param field_value: The field_value of this RegulatedInformationField.  # noqa: E501
        :type: str
        """
        if field_value is None:
            raise ValueError("Invalid value for `field_value`, must not be `None`")  # noqa: E501

        self._field_value = field_value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RegulatedInformationField, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RegulatedInformationField):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
