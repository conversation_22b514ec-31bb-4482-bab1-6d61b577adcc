# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ExceptionDates(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'exception_date': 'str',
        'is_open': 'bool',
        'open_intervals': 'list[OpenInterval]'
    }

    attribute_map = {
        'exception_date': 'ExceptionDate',
        'is_open': 'IsOpen',
        'open_intervals': 'OpenIntervals'
    }

    def __init__(self, exception_date=None, is_open=None, open_intervals=None):  # noqa: E501
        """ExceptionDates - a model defined in Swagger"""  # noqa: E501

        self._exception_date = None
        self._is_open = None
        self._open_intervals = None
        self.discriminator = None

        if exception_date is not None:
            self.exception_date = exception_date
        if is_open is not None:
            self.is_open = is_open
        if open_intervals is not None:
            self.open_intervals = open_intervals

    @property
    def exception_date(self):
        """Gets the exception_date of this ExceptionDates.  # noqa: E501

        Date when the business is closed, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date format.  # noqa: E501

        :return: The exception_date of this ExceptionDates.  # noqa: E501
        :rtype: str
        """
        return self._exception_date

    @exception_date.setter
    def exception_date(self, exception_date):
        """Sets the exception_date of this ExceptionDates.

        Date when the business is closed, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date format.  # noqa: E501

        :param exception_date: The exception_date of this ExceptionDates.  # noqa: E501
        :type: str
        """

        self._exception_date = exception_date

    @property
    def is_open(self):
        """Gets the is_open of this ExceptionDates.  # noqa: E501

        Boolean indicating if the business is closed or open on that date.  # noqa: E501

        :return: The is_open of this ExceptionDates.  # noqa: E501
        :rtype: bool
        """
        return self._is_open

    @is_open.setter
    def is_open(self, is_open):
        """Sets the is_open of this ExceptionDates.

        Boolean indicating if the business is closed or open on that date.  # noqa: E501

        :param is_open: The is_open of this ExceptionDates.  # noqa: E501
        :type: bool
        """

        self._is_open = is_open

    @property
    def open_intervals(self):
        """Gets the open_intervals of this ExceptionDates.  # noqa: E501

        Time window during the day when the business is open.  # noqa: E501

        :return: The open_intervals of this ExceptionDates.  # noqa: E501
        :rtype: list[OpenInterval]
        """
        return self._open_intervals

    @open_intervals.setter
    def open_intervals(self, open_intervals):
        """Sets the open_intervals of this ExceptionDates.

        Time window during the day when the business is open.  # noqa: E501

        :param open_intervals: The open_intervals of this ExceptionDates.  # noqa: E501
        :type: list[OpenInterval]
        """

        self._open_intervals = open_intervals

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ExceptionDates, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExceptionDates):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
