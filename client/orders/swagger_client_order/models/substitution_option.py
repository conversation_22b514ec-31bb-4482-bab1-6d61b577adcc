# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class SubstitutionOption(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asin': 'str',
        'quantity_ordered': 'int',
        'seller_sku': 'str',
        'title': 'str',
        'measurement': 'Measurement'
    }

    attribute_map = {
        'asin': 'ASIN',
        'quantity_ordered': 'QuantityOrdered',
        'seller_sku': 'SellerSKU',
        'title': 'Title',
        'measurement': 'Measurement'
    }

    def __init__(self, asin=None, quantity_ordered=None, seller_sku=None, title=None, measurement=None):  # noqa: E501
        """SubstitutionOption - a model defined in Swagger"""  # noqa: E501

        self._asin = None
        self._quantity_ordered = None
        self._seller_sku = None
        self._title = None
        self._measurement = None
        self.discriminator = None

        if asin is not None:
            self.asin = asin
        if quantity_ordered is not None:
            self.quantity_ordered = quantity_ordered
        if seller_sku is not None:
            self.seller_sku = seller_sku
        if title is not None:
            self.title = title
        if measurement is not None:
            self.measurement = measurement

    @property
    def asin(self):
        """Gets the asin of this SubstitutionOption.  # noqa: E501

        The Amazon Standard Identification Number (ASIN) of the item.  # noqa: E501

        :return: The asin of this SubstitutionOption.  # noqa: E501
        :rtype: str
        """
        return self._asin

    @asin.setter
    def asin(self, asin):
        """Sets the asin of this SubstitutionOption.

        The Amazon Standard Identification Number (ASIN) of the item.  # noqa: E501

        :param asin: The asin of this SubstitutionOption.  # noqa: E501
        :type: str
        """

        self._asin = asin

    @property
    def quantity_ordered(self):
        """Gets the quantity_ordered of this SubstitutionOption.  # noqa: E501

        The number of items to be picked for this substitution option.   # noqa: E501

        :return: The quantity_ordered of this SubstitutionOption.  # noqa: E501
        :rtype: int
        """
        return self._quantity_ordered

    @quantity_ordered.setter
    def quantity_ordered(self, quantity_ordered):
        """Sets the quantity_ordered of this SubstitutionOption.

        The number of items to be picked for this substitution option.   # noqa: E501

        :param quantity_ordered: The quantity_ordered of this SubstitutionOption.  # noqa: E501
        :type: int
        """

        self._quantity_ordered = quantity_ordered

    @property
    def seller_sku(self):
        """Gets the seller_sku of this SubstitutionOption.  # noqa: E501

        The seller stock keeping unit (SKU) of the item.  # noqa: E501

        :return: The seller_sku of this SubstitutionOption.  # noqa: E501
        :rtype: str
        """
        return self._seller_sku

    @seller_sku.setter
    def seller_sku(self, seller_sku):
        """Sets the seller_sku of this SubstitutionOption.

        The seller stock keeping unit (SKU) of the item.  # noqa: E501

        :param seller_sku: The seller_sku of this SubstitutionOption.  # noqa: E501
        :type: str
        """

        self._seller_sku = seller_sku

    @property
    def title(self):
        """Gets the title of this SubstitutionOption.  # noqa: E501

        The title of the item.  # noqa: E501

        :return: The title of this SubstitutionOption.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this SubstitutionOption.

        The title of the item.  # noqa: E501

        :param title: The title of this SubstitutionOption.  # noqa: E501
        :type: str
        """

        self._title = title

    @property
    def measurement(self):
        """Gets the measurement of this SubstitutionOption.  # noqa: E501

        Measurement information for the substitution option.  # noqa: E501

        :return: The measurement of this SubstitutionOption.  # noqa: E501
        :rtype: Measurement
        """
        return self._measurement

    @measurement.setter
    def measurement(self, measurement):
        """Sets the measurement of this SubstitutionOption.

        Measurement information for the substitution option.  # noqa: E501

        :param measurement: The measurement of this SubstitutionOption.  # noqa: E501
        :type: Measurement
        """

        self._measurement = measurement

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubstitutionOption, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubstitutionOption):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
