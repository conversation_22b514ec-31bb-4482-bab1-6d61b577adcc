# coding: utf-8

"""
    Selling Partner API for Orders

    The Selling Partner API for Orders helps you programmatically retrieve order information. These APIs let you develop fast, flexible, custom applications in areas like order synchronization, order research, and demand-based decision support tools. The Orders API supports orders that are two years old or less. Orders more than two years old will not show in the API response.  **Note:** The Orders API supports orders from 2016 and after for the JP, AU, and SG marketplaces.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class SubstitutionPreferences(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'substitution_type': 'str',
        'substitution_options': 'SubstitutionOptionList'
    }

    attribute_map = {
        'substitution_type': 'SubstitutionType',
        'substitution_options': 'SubstitutionOptions'
    }

    def __init__(self, substitution_type=None, substitution_options=None):  # noqa: E501
        """SubstitutionPreferences - a model defined in Swagger"""  # noqa: E501

        self._substitution_type = None
        self._substitution_options = None
        self.discriminator = None

        self.substitution_type = substitution_type
        if substitution_options is not None:
            self.substitution_options = substitution_options

    @property
    def substitution_type(self):
        """Gets the substitution_type of this SubstitutionPreferences.  # noqa: E501

        The type of substitution that these preferences represent.  # noqa: E501

        :return: The substitution_type of this SubstitutionPreferences.  # noqa: E501
        :rtype: str
        """
        return self._substitution_type

    @substitution_type.setter
    def substitution_type(self, substitution_type):
        """Sets the substitution_type of this SubstitutionPreferences.

        The type of substitution that these preferences represent.  # noqa: E501

        :param substitution_type: The substitution_type of this SubstitutionPreferences.  # noqa: E501
        :type: str
        """
        if substitution_type is None:
            raise ValueError("Invalid value for `substitution_type`, must not be `None`")  # noqa: E501
        allowed_values = ["CUSTOMER_PREFERENCE", "AMAZON_RECOMMENDED", "DO_NOT_SUBSTITUTE"]  # noqa: E501
        if substitution_type not in allowed_values:
            raise ValueError(
                "Invalid value for `substitution_type` ({0}), must be one of {1}"  # noqa: E501
                .format(substitution_type, allowed_values)
            )

        self._substitution_type = substitution_type

    @property
    def substitution_options(self):
        """Gets the substitution_options of this SubstitutionPreferences.  # noqa: E501

        Substitution options for the order item.  # noqa: E501

        :return: The substitution_options of this SubstitutionPreferences.  # noqa: E501
        :rtype: SubstitutionOptionList
        """
        return self._substitution_options

    @substitution_options.setter
    def substitution_options(self, substitution_options):
        """Sets the substitution_options of this SubstitutionPreferences.

        Substitution options for the order item.  # noqa: E501

        :param substitution_options: The substitution_options of this SubstitutionPreferences.  # noqa: E501
        :type: SubstitutionOptionList
        """

        self._substitution_options = substitution_options

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubstitutionPreferences, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubstitutionPreferences):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
