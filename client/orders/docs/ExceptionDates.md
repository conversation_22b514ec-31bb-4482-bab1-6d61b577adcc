# ExceptionDates

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**exception_date** | **str** | Date when the business is closed, in &lt;a href&#x3D;&#39;https://developer-docs.amazon.com/sp-api/docs/iso-8601&#39;&gt;ISO 8601&lt;/a&gt; date format. | [optional] 
**is_open** | **bool** | Boolean indicating if the business is closed or open on that date. | [optional] 
**open_intervals** | [**list[OpenInterval]**](OpenInterval.md) | Time window during the day when the business is open. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


