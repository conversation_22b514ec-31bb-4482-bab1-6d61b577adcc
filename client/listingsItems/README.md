# swagger-client-listingsItems
The Selling Partner API for Listings Items (Listings Items API) provides programmatic access to selling partner listings on Amazon. Use this API in collaboration with the Selling Partner API for Product Type Definitions, which you use to retrieve the information about Amazon product types needed to use the Listings Items API.  For more information, see the [Listings Items API Use Case Guide](https://developer-docs.amazon.com/sp-api/docs/listings-items-api-v2021-08-01-use-case-guide).

This Python package is automatically generated by the [Swagger Codegen](https://github.com/swagger-api/swagger-codegen) project:

- API version: 2021-08-01
- Package version: 1.0.0
- Build package: io.swagger.codegen.languages.PythonClientCodegen
For more information, please visit [https://sellercentral.amazon.com/gp/mws/contactus.html](https://sellercentral.amazon.com/gp/mws/contactus.html)

## Requirements.

Python 2.7 and 3.4+

## Installation & Usage
### pip install

If the python package is hosted on Github, you can install directly from Github

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```
(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:
```python
import swagger_client_listingsItems 
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```
(or `sudo python setup.py install` to install the package for all users)

Then import the package:
```python
import swagger_client_listingsItems
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python
from __future__ import print_function
import time
import swagger_client_listingsItems
from swagger_client_listingsItems.rest import ApiException
from pprint import pprint

# create an instance of the API class
api_instance = swagger_client_listingsItems.ListingsApi(swagger_client_listingsItems.ApiClient(configuration))
seller_id = 'seller_id_example' # str | A selling partner identifier, such as a merchant account or vendor code.
sku = 'sku_example' # str | A selling partner provided identifier for an Amazon listing.
marketplace_ids = ['ATVPDKIKX0DER'] # list[str] | A comma-delimited list of Amazon marketplace identifiers for the request.
issue_locale = 'en_US' # str | A locale for localization of issues. When not provided, the default language code of the first marketplace is used. Examples: `en_US`, `fr_CA`, `fr_FR`. Localized messages default to `en_US` when a localization is not available in the specified locale. (optional)

try:
    api_response = api_instance.delete_listings_item(seller_id, sku, marketplace_ids, issue_locale=issue_locale)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling ListingsApi->delete_listings_item: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *https://sellingpartnerapi-na.amazon.com*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*ListingsApi* | [**delete_listings_item**](docs/ListingsApi.md#delete_listings_item) | **DELETE** /listings/2021-08-01/items/{sellerId}/{sku} | 
*ListingsApi* | [**get_listings_item**](docs/ListingsApi.md#get_listings_item) | **GET** /listings/2021-08-01/items/{sellerId}/{sku} | 
*ListingsApi* | [**patch_listings_item**](docs/ListingsApi.md#patch_listings_item) | **PATCH** /listings/2021-08-01/items/{sellerId}/{sku} | 
*ListingsApi* | [**put_listings_item**](docs/ListingsApi.md#put_listings_item) | **PUT** /listings/2021-08-01/items/{sellerId}/{sku} | 


## Documentation For Models

 - [Decimal](docs/Decimal.md)
 - [Error](docs/Error.md)
 - [ErrorList](docs/ErrorList.md)
 - [FulfillmentAvailability](docs/FulfillmentAvailability.md)
 - [Issue](docs/Issue.md)
 - [IssueEnforcementAction](docs/IssueEnforcementAction.md)
 - [IssueEnforcements](docs/IssueEnforcements.md)
 - [IssueExemption](docs/IssueExemption.md)
 - [Item](docs/Item.md)
 - [ItemAttributes](docs/ItemAttributes.md)
 - [ItemIdentifiers](docs/ItemIdentifiers.md)
 - [ItemIdentifiersByMarketplace](docs/ItemIdentifiersByMarketplace.md)
 - [ItemImage](docs/ItemImage.md)
 - [ItemIssues](docs/ItemIssues.md)
 - [ItemOfferByMarketplace](docs/ItemOfferByMarketplace.md)
 - [ItemOffers](docs/ItemOffers.md)
 - [ItemProcurement](docs/ItemProcurement.md)
 - [ItemSummaries](docs/ItemSummaries.md)
 - [ItemSummaryByMarketplace](docs/ItemSummaryByMarketplace.md)
 - [ListingsItemPatchRequest](docs/ListingsItemPatchRequest.md)
 - [ListingsItemPutRequest](docs/ListingsItemPutRequest.md)
 - [ListingsItemSubmissionResponse](docs/ListingsItemSubmissionResponse.md)
 - [Money](docs/Money.md)
 - [PatchOperation](docs/PatchOperation.md)
 - [Points](docs/Points.md)


## Documentation For Authorization

 All endpoints do not require authorization.


## Author



