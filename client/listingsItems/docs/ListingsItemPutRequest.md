# ListingsItemPutRequest

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**product_type** | **str** | The Amazon product type of the listings item. | 
**requirements** | **str** | The name of the requirements set for the provided data. | [optional] 
**attributes** | **object** | A JSON object containing structured listings item attribute data keyed by attribute name. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


