# ItemSummaryByMarketplace

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**marketplace_id** | **str** | A marketplace identifier. Identifies the Amazon marketplace for the listings item. | 
**asin** | **str** | Amazon Standard Identification Number (ASIN) of the listings item. | 
**product_type** | **str** | The Amazon product type of the listings item. | 
**condition_type** | **str** | Identifies the condition of the listings item. | [optional] 
**status** | **list[str]** | Statuses that apply to the listings item. | 
**fn_sku** | **str** | The fulfillment network stock keeping unit is an identifier used by Amazon fulfillment centers to identify each unique item. | [optional] 
**item_name** | **str** | The name or title associated with an Amazon catalog item. | 
**created_date** | **datetime** | The date the listings item was created in ISO 8601 format. | 
**last_updated_date** | **datetime** | The date the listings item was last updated in ISO 8601 format. | 
**main_image** | [**ItemImage**](ItemImage.md) | The main image for the listings item. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


