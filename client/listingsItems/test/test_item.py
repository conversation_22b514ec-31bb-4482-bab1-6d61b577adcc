# coding: utf-8

"""
    Selling Partner API for Listings Items

    The Selling Partner API for Listings Items (Listings Items API) provides programmatic access to selling partner listings on Amazon. Use this API in collaboration with the Selling Partner API for Product Type Definitions, which you use to retrieve the information about Amazon product types needed to use the Listings Items API.  For more information, see the [Listings Items API Use Case Guide](https://developer-docs.amazon.com/sp-api/docs/listings-items-api-v2021-08-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2021-08-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_listingsItems
from swagger_client_listingsItems.models.item import Item  # noqa: E501
from swagger_client_listingsItems.rest import ApiException


class TestItem(unittest.TestCase):
    """Item unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testItem(self):
        """Test Item"""
        # FIXME: construct object with mandatory attributes with example values
        # model = swagger_client_listingsItems.models.item.Item()  # noqa: E501
        pass


if __name__ == '__main__':
    unittest.main()
