# coding: utf-8

"""
    Selling Partner API for Listings Items

    The Selling Partner API for Listings Items (Listings Items API) provides programmatic access to selling partner listings on Amazon. Use this API in collaboration with the Selling Partner API for Product Type Definitions, which you use to retrieve the information about Amazon product types needed to use the Listings Items API.  For more information, see the [Listings Items API Use Case Guide](https://developer-docs.amazon.com/sp-api/docs/listings-items-api-v2021-08-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2021-08-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class Item(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sku': 'str',
        'summaries': 'ItemSummaries',
        'attributes': 'ItemAttributes',
        'issues': 'ItemIssues',
        'offers': 'ItemOffers',
        'fulfillment_availability': 'list[FulfillmentAvailability]',
        'procurement': 'list[ItemProcurement]'
    }

    attribute_map = {
        'sku': 'sku',
        'summaries': 'summaries',
        'attributes': 'attributes',
        'issues': 'issues',
        'offers': 'offers',
        'fulfillment_availability': 'fulfillmentAvailability',
        'procurement': 'procurement'
    }

    def __init__(self, sku=None, summaries=None, attributes=None, issues=None, offers=None, fulfillment_availability=None, procurement=None):  # noqa: E501
        """Item - a model defined in Swagger"""  # noqa: E501

        self._sku = None
        self._summaries = None
        self._attributes = None
        self._issues = None
        self._offers = None
        self._fulfillment_availability = None
        self._procurement = None
        self.discriminator = None

        self.sku = sku
        if summaries is not None:
            self.summaries = summaries
        if attributes is not None:
            self.attributes = attributes
        if issues is not None:
            self.issues = issues
        if offers is not None:
            self.offers = offers
        if fulfillment_availability is not None:
            self.fulfillment_availability = fulfillment_availability
        if procurement is not None:
            self.procurement = procurement

    @property
    def sku(self):
        """Gets the sku of this Item.  # noqa: E501

        A selling partner provided identifier for an Amazon listing.  # noqa: E501

        :return: The sku of this Item.  # noqa: E501
        :rtype: str
        """
        return self._sku

    @sku.setter
    def sku(self, sku):
        """Sets the sku of this Item.

        A selling partner provided identifier for an Amazon listing.  # noqa: E501

        :param sku: The sku of this Item.  # noqa: E501
        :type: str
        """
        if sku is None:
            raise ValueError("Invalid value for `sku`, must not be `None`")  # noqa: E501

        self._sku = sku

    @property
    def summaries(self):
        """Gets the summaries of this Item.  # noqa: E501


        :return: The summaries of this Item.  # noqa: E501
        :rtype: ItemSummaries
        """
        return self._summaries

    @summaries.setter
    def summaries(self, summaries):
        """Sets the summaries of this Item.


        :param summaries: The summaries of this Item.  # noqa: E501
        :type: ItemSummaries
        """

        self._summaries = summaries

    @property
    def attributes(self):
        """Gets the attributes of this Item.  # noqa: E501


        :return: The attributes of this Item.  # noqa: E501
        :rtype: ItemAttributes
        """
        return self._attributes

    @attributes.setter
    def attributes(self, attributes):
        """Sets the attributes of this Item.


        :param attributes: The attributes of this Item.  # noqa: E501
        :type: ItemAttributes
        """

        self._attributes = attributes

    @property
    def issues(self):
        """Gets the issues of this Item.  # noqa: E501


        :return: The issues of this Item.  # noqa: E501
        :rtype: ItemIssues
        """
        return self._issues

    @issues.setter
    def issues(self, issues):
        """Sets the issues of this Item.


        :param issues: The issues of this Item.  # noqa: E501
        :type: ItemIssues
        """

        self._issues = issues

    @property
    def offers(self):
        """Gets the offers of this Item.  # noqa: E501


        :return: The offers of this Item.  # noqa: E501
        :rtype: ItemOffers
        """
        return self._offers

    @offers.setter
    def offers(self, offers):
        """Sets the offers of this Item.


        :param offers: The offers of this Item.  # noqa: E501
        :type: ItemOffers
        """

        self._offers = offers

    @property
    def fulfillment_availability(self):
        """Gets the fulfillment_availability of this Item.  # noqa: E501

        The fulfillment availability for the listings item.  # noqa: E501

        :return: The fulfillment_availability of this Item.  # noqa: E501
        :rtype: list[FulfillmentAvailability]
        """
        return self._fulfillment_availability

    @fulfillment_availability.setter
    def fulfillment_availability(self, fulfillment_availability):
        """Sets the fulfillment_availability of this Item.

        The fulfillment availability for the listings item.  # noqa: E501

        :param fulfillment_availability: The fulfillment_availability of this Item.  # noqa: E501
        :type: list[FulfillmentAvailability]
        """

        self._fulfillment_availability = fulfillment_availability

    @property
    def procurement(self):
        """Gets the procurement of this Item.  # noqa: E501

        The vendor procurement information for the listings item.  # noqa: E501

        :return: The procurement of this Item.  # noqa: E501
        :rtype: list[ItemProcurement]
        """
        return self._procurement

    @procurement.setter
    def procurement(self, procurement):
        """Sets the procurement of this Item.

        The vendor procurement information for the listings item.  # noqa: E501

        :param procurement: The procurement of this Item.  # noqa: E501
        :type: list[ItemProcurement]
        """

        self._procurement = procurement

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Item, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Item):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
