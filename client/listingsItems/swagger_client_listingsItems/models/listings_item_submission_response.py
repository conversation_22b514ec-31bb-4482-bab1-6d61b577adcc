# coding: utf-8

"""
    Selling Partner API for Listings Items

    The Selling Partner API for Listings Items (Listings Items API) provides programmatic access to selling partner listings on Amazon. Use this API in collaboration with the Selling Partner API for Product Type Definitions, which you use to retrieve the information about Amazon product types needed to use the Listings Items API.  For more information, see the [Listings Items API Use Case Guide](https://developer-docs.amazon.com/sp-api/docs/listings-items-api-v2021-08-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2021-08-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ListingsItemSubmissionResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sku': 'str',
        'status': 'str',
        'submission_id': 'str',
        'issues': 'list[Issue]',
        'identifiers': 'ItemIdentifiers'
    }

    attribute_map = {
        'sku': 'sku',
        'status': 'status',
        'submission_id': 'submissionId',
        'issues': 'issues',
        'identifiers': 'identifiers'
    }

    def __init__(self, sku=None, status=None, submission_id=None, issues=None, identifiers=None):  # noqa: E501
        """ListingsItemSubmissionResponse - a model defined in Swagger"""  # noqa: E501

        self._sku = None
        self._status = None
        self._submission_id = None
        self._issues = None
        self._identifiers = None
        self.discriminator = None

        self.sku = sku
        self.status = status
        self.submission_id = submission_id
        if issues is not None:
            self.issues = issues
        if identifiers is not None:
            self.identifiers = identifiers

    @property
    def sku(self):
        """Gets the sku of this ListingsItemSubmissionResponse.  # noqa: E501

        A selling partner provided identifier for an Amazon listing.  # noqa: E501

        :return: The sku of this ListingsItemSubmissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._sku

    @sku.setter
    def sku(self, sku):
        """Sets the sku of this ListingsItemSubmissionResponse.

        A selling partner provided identifier for an Amazon listing.  # noqa: E501

        :param sku: The sku of this ListingsItemSubmissionResponse.  # noqa: E501
        :type: str
        """
        if sku is None:
            raise ValueError("Invalid value for `sku`, must not be `None`")  # noqa: E501

        self._sku = sku

    @property
    def status(self):
        """Gets the status of this ListingsItemSubmissionResponse.  # noqa: E501

        The status of the listings item submission.  # noqa: E501

        :return: The status of this ListingsItemSubmissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListingsItemSubmissionResponse.

        The status of the listings item submission.  # noqa: E501

        :param status: The status of this ListingsItemSubmissionResponse.  # noqa: E501
        :type: str
        """
        if status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501
        allowed_values = ["ACCEPTED", "INVALID", "VALID"]  # noqa: E501
        if status not in allowed_values:
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}"  # noqa: E501
                .format(status, allowed_values)
            )

        self._status = status

    @property
    def submission_id(self):
        """Gets the submission_id of this ListingsItemSubmissionResponse.  # noqa: E501

        The unique identifier of the listings item submission.  # noqa: E501

        :return: The submission_id of this ListingsItemSubmissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._submission_id

    @submission_id.setter
    def submission_id(self, submission_id):
        """Sets the submission_id of this ListingsItemSubmissionResponse.

        The unique identifier of the listings item submission.  # noqa: E501

        :param submission_id: The submission_id of this ListingsItemSubmissionResponse.  # noqa: E501
        :type: str
        """
        if submission_id is None:
            raise ValueError("Invalid value for `submission_id`, must not be `None`")  # noqa: E501

        self._submission_id = submission_id

    @property
    def issues(self):
        """Gets the issues of this ListingsItemSubmissionResponse.  # noqa: E501

        Listings item issues related to the listings item submission.  # noqa: E501

        :return: The issues of this ListingsItemSubmissionResponse.  # noqa: E501
        :rtype: list[Issue]
        """
        return self._issues

    @issues.setter
    def issues(self, issues):
        """Sets the issues of this ListingsItemSubmissionResponse.

        Listings item issues related to the listings item submission.  # noqa: E501

        :param issues: The issues of this ListingsItemSubmissionResponse.  # noqa: E501
        :type: list[Issue]
        """

        self._issues = issues

    @property
    def identifiers(self):
        """Gets the identifiers of this ListingsItemSubmissionResponse.  # noqa: E501

        Identity attributes associated with the item in the Amazon catalog, such as the ASIN.  # noqa: E501

        :return: The identifiers of this ListingsItemSubmissionResponse.  # noqa: E501
        :rtype: ItemIdentifiers
        """
        return self._identifiers

    @identifiers.setter
    def identifiers(self, identifiers):
        """Sets the identifiers of this ListingsItemSubmissionResponse.

        Identity attributes associated with the item in the Amazon catalog, such as the ASIN.  # noqa: E501

        :param identifiers: The identifiers of this ListingsItemSubmissionResponse.  # noqa: E501
        :type: ItemIdentifiers
        """

        self._identifiers = identifiers

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListingsItemSubmissionResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListingsItemSubmissionResponse):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
