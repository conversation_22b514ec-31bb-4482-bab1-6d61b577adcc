# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, see the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2020-12-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2020-12-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemSalesRanksByMarketplace(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'marketplace_id': 'str',
        'ranks': 'list[ItemSalesRank]'
    }

    attribute_map = {
        'marketplace_id': 'marketplaceId',
        'ranks': 'ranks'
    }

    def __init__(self, marketplace_id=None, ranks=None):  # noqa: E501
        """ItemSalesRanksByMarketplace - a model defined in Swagger"""  # noqa: E501

        self._marketplace_id = None
        self._ranks = None
        self.discriminator = None

        self.marketplace_id = marketplace_id
        self.ranks = ranks

    @property
    def marketplace_id(self):
        """Gets the marketplace_id of this ItemSalesRanksByMarketplace.  # noqa: E501

        Amazon marketplace identifier.  # noqa: E501

        :return: The marketplace_id of this ItemSalesRanksByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._marketplace_id

    @marketplace_id.setter
    def marketplace_id(self, marketplace_id):
        """Sets the marketplace_id of this ItemSalesRanksByMarketplace.

        Amazon marketplace identifier.  # noqa: E501

        :param marketplace_id: The marketplace_id of this ItemSalesRanksByMarketplace.  # noqa: E501
        :type: str
        """
        if marketplace_id is None:
            raise ValueError("Invalid value for `marketplace_id`, must not be `None`")  # noqa: E501

        self._marketplace_id = marketplace_id

    @property
    def ranks(self):
        """Gets the ranks of this ItemSalesRanksByMarketplace.  # noqa: E501

        Sales ranks of an Amazon catalog item for an Amazon marketplace.  # noqa: E501

        :return: The ranks of this ItemSalesRanksByMarketplace.  # noqa: E501
        :rtype: list[ItemSalesRank]
        """
        return self._ranks

    @ranks.setter
    def ranks(self, ranks):
        """Sets the ranks of this ItemSalesRanksByMarketplace.

        Sales ranks of an Amazon catalog item for an Amazon marketplace.  # noqa: E501

        :param ranks: The ranks of this ItemSalesRanksByMarketplace.  # noqa: E501
        :type: list[ItemSalesRank]
        """
        if ranks is None:
            raise ValueError("Invalid value for `ranks`, must not be `None`")  # noqa: E501

        self._ranks = ranks

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemSalesRanksByMarketplace, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemSalesRanksByMarketplace):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
