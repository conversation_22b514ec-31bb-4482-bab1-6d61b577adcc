# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, see the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2020-12-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2020-12-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemVendorDetailsByMarketplace(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'marketplace_id': 'str',
        'brand_code': 'str',
        'category_code': 'str',
        'manufacturer_code': 'str',
        'manufacturer_code_parent': 'str',
        'product_group': 'str',
        'replenishment_category': 'str',
        'subcategory_code': 'str'
    }

    attribute_map = {
        'marketplace_id': 'marketplaceId',
        'brand_code': 'brandCode',
        'category_code': 'categoryCode',
        'manufacturer_code': 'manufacturerCode',
        'manufacturer_code_parent': 'manufacturerCodeParent',
        'product_group': 'productGroup',
        'replenishment_category': 'replenishmentCategory',
        'subcategory_code': 'subcategoryCode'
    }

    def __init__(self, marketplace_id=None, brand_code=None, category_code=None, manufacturer_code=None, manufacturer_code_parent=None, product_group=None, replenishment_category=None, subcategory_code=None):  # noqa: E501
        """ItemVendorDetailsByMarketplace - a model defined in Swagger"""  # noqa: E501

        self._marketplace_id = None
        self._brand_code = None
        self._category_code = None
        self._manufacturer_code = None
        self._manufacturer_code_parent = None
        self._product_group = None
        self._replenishment_category = None
        self._subcategory_code = None
        self.discriminator = None

        self.marketplace_id = marketplace_id
        if brand_code is not None:
            self.brand_code = brand_code
        if category_code is not None:
            self.category_code = category_code
        if manufacturer_code is not None:
            self.manufacturer_code = manufacturer_code
        if manufacturer_code_parent is not None:
            self.manufacturer_code_parent = manufacturer_code_parent
        if product_group is not None:
            self.product_group = product_group
        if replenishment_category is not None:
            self.replenishment_category = replenishment_category
        if subcategory_code is not None:
            self.subcategory_code = subcategory_code

    @property
    def marketplace_id(self):
        """Gets the marketplace_id of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Amazon marketplace identifier.  # noqa: E501

        :return: The marketplace_id of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._marketplace_id

    @marketplace_id.setter
    def marketplace_id(self, marketplace_id):
        """Sets the marketplace_id of this ItemVendorDetailsByMarketplace.

        Amazon marketplace identifier.  # noqa: E501

        :param marketplace_id: The marketplace_id of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """
        if marketplace_id is None:
            raise ValueError("Invalid value for `marketplace_id`, must not be `None`")  # noqa: E501

        self._marketplace_id = marketplace_id

    @property
    def brand_code(self):
        """Gets the brand_code of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Brand code associated with an Amazon catalog item.  # noqa: E501

        :return: The brand_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._brand_code

    @brand_code.setter
    def brand_code(self, brand_code):
        """Sets the brand_code of this ItemVendorDetailsByMarketplace.

        Brand code associated with an Amazon catalog item.  # noqa: E501

        :param brand_code: The brand_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """

        self._brand_code = brand_code

    @property
    def category_code(self):
        """Gets the category_code of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Product category associated with an Amazon catalog item.  # noqa: E501

        :return: The category_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._category_code

    @category_code.setter
    def category_code(self, category_code):
        """Sets the category_code of this ItemVendorDetailsByMarketplace.

        Product category associated with an Amazon catalog item.  # noqa: E501

        :param category_code: The category_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """

        self._category_code = category_code

    @property
    def manufacturer_code(self):
        """Gets the manufacturer_code of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Manufacturer code associated with an Amazon catalog item.  # noqa: E501

        :return: The manufacturer_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._manufacturer_code

    @manufacturer_code.setter
    def manufacturer_code(self, manufacturer_code):
        """Sets the manufacturer_code of this ItemVendorDetailsByMarketplace.

        Manufacturer code associated with an Amazon catalog item.  # noqa: E501

        :param manufacturer_code: The manufacturer_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """

        self._manufacturer_code = manufacturer_code

    @property
    def manufacturer_code_parent(self):
        """Gets the manufacturer_code_parent of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Parent vendor code of the manufacturer code.  # noqa: E501

        :return: The manufacturer_code_parent of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._manufacturer_code_parent

    @manufacturer_code_parent.setter
    def manufacturer_code_parent(self, manufacturer_code_parent):
        """Sets the manufacturer_code_parent of this ItemVendorDetailsByMarketplace.

        Parent vendor code of the manufacturer code.  # noqa: E501

        :param manufacturer_code_parent: The manufacturer_code_parent of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """

        self._manufacturer_code_parent = manufacturer_code_parent

    @property
    def product_group(self):
        """Gets the product_group of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Product group associated with an Amazon catalog item.  # noqa: E501

        :return: The product_group of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._product_group

    @product_group.setter
    def product_group(self, product_group):
        """Sets the product_group of this ItemVendorDetailsByMarketplace.

        Product group associated with an Amazon catalog item.  # noqa: E501

        :param product_group: The product_group of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """

        self._product_group = product_group

    @property
    def replenishment_category(self):
        """Gets the replenishment_category of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Replenishment category associated with an Amazon catalog item.  # noqa: E501

        :return: The replenishment_category of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._replenishment_category

    @replenishment_category.setter
    def replenishment_category(self, replenishment_category):
        """Sets the replenishment_category of this ItemVendorDetailsByMarketplace.

        Replenishment category associated with an Amazon catalog item.  # noqa: E501

        :param replenishment_category: The replenishment_category of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """
        allowed_values = ["ALLOCATED", "BASIC_REPLENISHMENT", "IN_SEASON", "LIMITED_REPLENISHMENT", "MANUFACTURER_OUT_OF_STOCK", "NEW_PRODUCT", "NON_REPLENISHABLE", "NON_STOCKUPABLE", "OBSOLETE", "PLANNED_REPLENISHMENT"]  # noqa: E501
        if replenishment_category not in allowed_values:
            raise ValueError(
                "Invalid value for `replenishment_category` ({0}), must be one of {1}"  # noqa: E501
                .format(replenishment_category, allowed_values)
            )

        self._replenishment_category = replenishment_category

    @property
    def subcategory_code(self):
        """Gets the subcategory_code of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Product subcategory associated with an Amazon catalog item.  # noqa: E501

        :return: The subcategory_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._subcategory_code

    @subcategory_code.setter
    def subcategory_code(self, subcategory_code):
        """Sets the subcategory_code of this ItemVendorDetailsByMarketplace.

        Product subcategory associated with an Amazon catalog item.  # noqa: E501

        :param subcategory_code: The subcategory_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """

        self._subcategory_code = subcategory_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemVendorDetailsByMarketplace, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemVendorDetailsByMarketplace):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
