# ItemVendorDetailsByMarketplace

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**marketplace_id** | **str** | Amazon marketplace identifier. | 
**brand_code** | **str** | Brand code associated with an Amazon catalog item. | [optional] 
**manufacturer_code** | **str** | Manufacturer code associated with an Amazon catalog item. | [optional] 
**manufacturer_code_parent** | **str** | Parent vendor code of the manufacturer code. | [optional] 
**product_category** | [**ItemVendorDetailsCategory**](ItemVendorDetailsCategory.md) | Product category associated with an Amazon catalog item. | [optional] 
**product_group** | **str** | Product group associated with an Amazon catalog item. | [optional] 
**product_subcategory** | [**ItemVendorDetailsCategory**](ItemVendorDetailsCategory.md) | Product subcategory associated with an Amazon catalog item. | [optional] 
**replenishment_category** | **str** | Replenishment category associated with an Amazon catalog item. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


