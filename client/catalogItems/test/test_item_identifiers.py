# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_catalogItems
from swagger_client_catalogItems.models.item_identifiers import ItemIdentifiers  # noqa: E501
from swagger_client_catalogItems.rest import ApiException


class TestItemIdentifiers(unittest.TestCase):
    """ItemIdentifiers unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testItemIdentifiers(self):
        """Test ItemIdentifiers"""
        # FIXME: construct object with mandatory attributes with example values
        # model = swagger_client_catalogItems.models.item_identifiers.ItemIdentifiers()  # noqa: E501
        pass


if __name__ == '__main__':
    unittest.main()
