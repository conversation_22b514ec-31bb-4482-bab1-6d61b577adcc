# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from swagger_client_catalogItems.api_client import ApiClient


class CatalogApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def get_catalog_item(self, asin, marketplace_ids, **kwargs):  # noqa: E501
        """get_catalog_item  # noqa: E501

        Retrieves details for an item in the Amazon catalog.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to the [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_catalog_item(asin, marketplace_ids, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str asin: The Amazon Standard Identification Number (ASIN) of the item. (required)
        :param list[str] marketplace_ids: A comma-delimited list of Amazon marketplace identifiers. Data sets in the response contain data only for the specified marketplaces. (required)
        :param list[str] included_data: A comma-delimited list of data sets to include in the response. Default: `summaries`.
        :param str locale: Locale for retrieving localized summaries. Defaults to the primary locale of the marketplace.
        :return: Item
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_catalog_item_with_http_info(asin, marketplace_ids, **kwargs)  # noqa: E501
        else:
            (data) = self.get_catalog_item_with_http_info(asin, marketplace_ids, **kwargs)  # noqa: E501
            return data

    def get_catalog_item_with_http_info(self, asin, marketplace_ids, **kwargs):  # noqa: E501
        """get_catalog_item  # noqa: E501

        Retrieves details for an item in the Amazon catalog.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to the [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_catalog_item_with_http_info(asin, marketplace_ids, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str asin: The Amazon Standard Identification Number (ASIN) of the item. (required)
        :param list[str] marketplace_ids: A comma-delimited list of Amazon marketplace identifiers. Data sets in the response contain data only for the specified marketplaces. (required)
        :param list[str] included_data: A comma-delimited list of data sets to include in the response. Default: `summaries`.
        :param str locale: Locale for retrieving localized summaries. Defaults to the primary locale of the marketplace.
        :return: Item
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['asin', 'marketplace_ids', 'included_data', 'locale']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_catalog_item" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'asin' is set
        if self.api_client.client_side_validation and ('asin' not in params or
                                                       params['asin'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `asin` when calling `get_catalog_item`")  # noqa: E501
        # verify the required parameter 'marketplace_ids' is set
        if self.api_client.client_side_validation and ('marketplace_ids' not in params or
                                                       params['marketplace_ids'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `marketplace_ids` when calling `get_catalog_item`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'asin' in params:
            path_params['asin'] = params['asin']  # noqa: E501

        query_params = []
        if 'marketplace_ids' in params:
            query_params.append(('marketplaceIds', params['marketplace_ids']))  # noqa: E501
            collection_formats['marketplaceIds'] = 'csv'  # noqa: E501
        if 'included_data' in params:
            query_params.append(('includedData', params['included_data']))  # noqa: E501
            collection_formats['includedData'] = 'csv'  # noqa: E501
        if 'locale' in params:
            query_params.append(('locale', params['locale']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/catalog/2022-04-01/items/{asin}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Item',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def search_catalog_items(self, marketplace_ids, **kwargs):  # noqa: E501
        """search_catalog_items  # noqa: E501

        Search for and return a list of Amazon catalog items and associated information either by identifier or by keywords.  **Usage Plans:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to the [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.search_catalog_items(marketplace_ids, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param list[str] marketplace_ids: A comma-delimited list of Amazon marketplace identifiers for the request. (required)
        :param list[str] identifiers: A comma-delimited list of product identifiers to search the Amazon catalog for. **Note:** Cannot be used with `keywords`.
        :param str identifiers_type: Type of product identifiers to search the Amazon catalog for. **Note:** Required when `identifiers` are provided.
        :param list[str] included_data: A comma-delimited list of data sets to include in the response. Default: `summaries`.
        :param str locale: Locale for retrieving localized summaries. Defaults to the primary locale of the marketplace.
        :param str seller_id: A selling partner identifier, such as a seller account or vendor code. **Note:** Required when `identifiersType` is `SKU`.
        :param list[str] keywords: A comma-delimited list of words to search the Amazon catalog for. **Note:** Cannot be used with `identifiers`.
        :param list[str] brand_names: A comma-delimited list of brand names to limit the search for `keywords`-based queries. **Note:** Cannot be used with `identifiers`.
        :param list[str] classification_ids: A comma-delimited list of classification identifiers to limit the search for `keywords`-based queries. **Note:** Cannot be used with `identifiers`.
        :param int page_size: Number of results to be returned per page.
        :param str page_token: A token to fetch a certain page when there are multiple pages worth of results.
        :param str keywords_locale: The language of the keywords provided for `keywords`-based queries. Defaults to the primary locale of the marketplace. **Note:** Cannot be used with `identifiers`.
        :return: ItemSearchResults
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.search_catalog_items_with_http_info(marketplace_ids, **kwargs)  # noqa: E501
        else:
            (data) = self.search_catalog_items_with_http_info(marketplace_ids, **kwargs)  # noqa: E501
            return data

    def search_catalog_items_with_http_info(self, marketplace_ids, **kwargs):  # noqa: E501
        """search_catalog_items  # noqa: E501

        Search for and return a list of Amazon catalog items and associated information either by identifier or by keywords.  **Usage Plans:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may observe higher rate and burst values than those shown here. For more information, refer to the [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.search_catalog_items_with_http_info(marketplace_ids, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param list[str] marketplace_ids: A comma-delimited list of Amazon marketplace identifiers for the request. (required)
        :param list[str] identifiers: A comma-delimited list of product identifiers to search the Amazon catalog for. **Note:** Cannot be used with `keywords`.
        :param str identifiers_type: Type of product identifiers to search the Amazon catalog for. **Note:** Required when `identifiers` are provided.
        :param list[str] included_data: A comma-delimited list of data sets to include in the response. Default: `summaries`.
        :param str locale: Locale for retrieving localized summaries. Defaults to the primary locale of the marketplace.
        :param str seller_id: A selling partner identifier, such as a seller account or vendor code. **Note:** Required when `identifiersType` is `SKU`.
        :param list[str] keywords: A comma-delimited list of words to search the Amazon catalog for. **Note:** Cannot be used with `identifiers`.
        :param list[str] brand_names: A comma-delimited list of brand names to limit the search for `keywords`-based queries. **Note:** Cannot be used with `identifiers`.
        :param list[str] classification_ids: A comma-delimited list of classification identifiers to limit the search for `keywords`-based queries. **Note:** Cannot be used with `identifiers`.
        :param int page_size: Number of results to be returned per page.
        :param str page_token: A token to fetch a certain page when there are multiple pages worth of results.
        :param str keywords_locale: The language of the keywords provided for `keywords`-based queries. Defaults to the primary locale of the marketplace. **Note:** Cannot be used with `identifiers`.
        :return: ItemSearchResults
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['marketplace_ids', 'identifiers', 'identifiers_type', 'included_data', 'locale', 'seller_id', 'keywords', 'brand_names', 'classification_ids', 'page_size', 'page_token', 'keywords_locale']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method search_catalog_items" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'marketplace_ids' is set
        if self.api_client.client_side_validation and ('marketplace_ids' not in params or
                                                       params['marketplace_ids'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `marketplace_ids` when calling `search_catalog_items`")  # noqa: E501

        if self.api_client.client_side_validation and ('marketplace_ids' in params and
                                            len(params['marketplace_ids']) > 1):
            raise ValueError("Invalid value for parameter `marketplace_ids` when calling `search_catalog_items`, number of items must be less than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('identifiers' in params and
                                            len(params['identifiers']) > 20):
            raise ValueError("Invalid value for parameter `identifiers` when calling `search_catalog_items`, number of items must be less than or equal to `20`")  # noqa: E501
        if self.api_client.client_side_validation and ('keywords' in params and
                                            len(params['keywords']) > 20):
            raise ValueError("Invalid value for parameter `keywords` when calling `search_catalog_items`, number of items must be less than or equal to `20`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 20):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `search_catalog_items`, must be a value less than or equal to `20`")  # noqa: E501
        collection_formats = {}

        path_params = {}

        query_params = []
        if 'identifiers' in params:
            query_params.append(('identifiers', params['identifiers']))  # noqa: E501
            collection_formats['identifiers'] = 'csv'  # noqa: E501
        if 'identifiers_type' in params:
            query_params.append(('identifiersType', params['identifiers_type']))  # noqa: E501
        if 'marketplace_ids' in params:
            query_params.append(('marketplaceIds', params['marketplace_ids']))  # noqa: E501
            collection_formats['marketplaceIds'] = 'csv'  # noqa: E501
        if 'included_data' in params:
            query_params.append(('includedData', params['included_data']))  # noqa: E501
            collection_formats['includedData'] = 'csv'  # noqa: E501
        if 'locale' in params:
            query_params.append(('locale', params['locale']))  # noqa: E501
        if 'seller_id' in params:
            query_params.append(('sellerId', params['seller_id']))  # noqa: E501
        if 'keywords' in params:
            query_params.append(('keywords', params['keywords']))  # noqa: E501
            collection_formats['keywords'] = 'csv'  # noqa: E501
        if 'brand_names' in params:
            query_params.append(('brandNames', params['brand_names']))  # noqa: E501
            collection_formats['brandNames'] = 'csv'  # noqa: E501
        if 'classification_ids' in params:
            query_params.append(('classificationIds', params['classification_ids']))  # noqa: E501
            collection_formats['classificationIds'] = 'csv'  # noqa: E501
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'page_token' in params:
            query_params.append(('pageToken', params['page_token']))  # noqa: E501
        if 'keywords_locale' in params:
            query_params.append(('keywordsLocale', params['keywords_locale']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/catalog/2022-04-01/items', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ItemSearchResults',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)
