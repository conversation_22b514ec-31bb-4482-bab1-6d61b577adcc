# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemContributorRole(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'display_name': 'str',
        'value': 'str'
    }

    attribute_map = {
        'display_name': 'displayName',
        'value': 'value'
    }

    def __init__(self, display_name=None, value=None):  # noqa: E501
        """ItemContributorRole - a model defined in Swagger"""  # noqa: E501

        self._display_name = None
        self._value = None
        self.discriminator = None

        if display_name is not None:
            self.display_name = display_name
        self.value = value

    @property
    def display_name(self):
        """Gets the display_name of this ItemContributorRole.  # noqa: E501

        Display name of the role in the requested locale, such as Author or Actor.  # noqa: E501

        :return: The display_name of this ItemContributorRole.  # noqa: E501
        :rtype: str
        """
        return self._display_name

    @display_name.setter
    def display_name(self, display_name):
        """Sets the display_name of this ItemContributorRole.

        Display name of the role in the requested locale, such as Author or Actor.  # noqa: E501

        :param display_name: The display_name of this ItemContributorRole.  # noqa: E501
        :type: str
        """

        self._display_name = display_name

    @property
    def value(self):
        """Gets the value of this ItemContributorRole.  # noqa: E501

        Role value for the Amazon catalog item, such as author or actor.  # noqa: E501

        :return: The value of this ItemContributorRole.  # noqa: E501
        :rtype: str
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this ItemContributorRole.

        Role value for the Amazon catalog item, such as author or actor.  # noqa: E501

        :param value: The value of this ItemContributorRole.  # noqa: E501
        :type: str
        """
        if value is None:
            raise ValueError("Invalid value for `value`, must not be `None`")  # noqa: E501

        self._value = value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemContributorRole, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemContributorRole):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
