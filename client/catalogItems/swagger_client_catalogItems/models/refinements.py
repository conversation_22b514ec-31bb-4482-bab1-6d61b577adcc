# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class Refinements(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'brands': 'list[BrandRefinement]',
        'classifications': 'list[ClassificationRefinement]'
    }

    attribute_map = {
        'brands': 'brands',
        'classifications': 'classifications'
    }

    def __init__(self, brands=None, classifications=None):  # noqa: E501
        """Refinements - a model defined in Swagger"""  # noqa: E501

        self._brands = None
        self._classifications = None
        self.discriminator = None

        self.brands = brands
        self.classifications = classifications

    @property
    def brands(self):
        """Gets the brands of this Refinements.  # noqa: E501

        Brand search refinements.  # noqa: E501

        :return: The brands of this Refinements.  # noqa: E501
        :rtype: list[BrandRefinement]
        """
        return self._brands

    @brands.setter
    def brands(self, brands):
        """Sets the brands of this Refinements.

        Brand search refinements.  # noqa: E501

        :param brands: The brands of this Refinements.  # noqa: E501
        :type: list[BrandRefinement]
        """
        if brands is None:
            raise ValueError("Invalid value for `brands`, must not be `None`")  # noqa: E501

        self._brands = brands

    @property
    def classifications(self):
        """Gets the classifications of this Refinements.  # noqa: E501

        Classification search refinements.  # noqa: E501

        :return: The classifications of this Refinements.  # noqa: E501
        :rtype: list[ClassificationRefinement]
        """
        return self._classifications

    @classifications.setter
    def classifications(self, classifications):
        """Sets the classifications of this Refinements.

        Classification search refinements.  # noqa: E501

        :param classifications: The classifications of this Refinements.  # noqa: E501
        :type: list[ClassificationRefinement]
        """
        if classifications is None:
            raise ValueError("Invalid value for `classifications`, must not be `None`")  # noqa: E501

        self._classifications = classifications

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Refinements, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Refinements):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
