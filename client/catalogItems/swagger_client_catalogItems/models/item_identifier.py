# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemIdentifier(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'identifier_type': 'str',
        'identifier': 'str'
    }

    attribute_map = {
        'identifier_type': 'identifierType',
        'identifier': 'identifier'
    }

    def __init__(self, identifier_type=None, identifier=None):  # noqa: E501
        """ItemIdentifier - a model defined in Swagger"""  # noqa: E501

        self._identifier_type = None
        self._identifier = None
        self.discriminator = None

        self.identifier_type = identifier_type
        self.identifier = identifier

    @property
    def identifier_type(self):
        """Gets the identifier_type of this ItemIdentifier.  # noqa: E501

        Type of identifier, such as UPC, EAN, or ISBN.  # noqa: E501

        :return: The identifier_type of this ItemIdentifier.  # noqa: E501
        :rtype: str
        """
        return self._identifier_type

    @identifier_type.setter
    def identifier_type(self, identifier_type):
        """Sets the identifier_type of this ItemIdentifier.

        Type of identifier, such as UPC, EAN, or ISBN.  # noqa: E501

        :param identifier_type: The identifier_type of this ItemIdentifier.  # noqa: E501
        :type: str
        """
        if identifier_type is None:
            raise ValueError("Invalid value for `identifier_type`, must not be `None`")  # noqa: E501

        self._identifier_type = identifier_type

    @property
    def identifier(self):
        """Gets the identifier of this ItemIdentifier.  # noqa: E501

        Identifier.  # noqa: E501

        :return: The identifier of this ItemIdentifier.  # noqa: E501
        :rtype: str
        """
        return self._identifier

    @identifier.setter
    def identifier(self, identifier):
        """Sets the identifier of this ItemIdentifier.

        Identifier.  # noqa: E501

        :param identifier: The identifier of this ItemIdentifier.  # noqa: E501
        :type: str
        """
        if identifier is None:
            raise ValueError("Invalid value for `identifier`, must not be `None`")  # noqa: E501

        self._identifier = identifier

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemIdentifier, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemIdentifier):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
