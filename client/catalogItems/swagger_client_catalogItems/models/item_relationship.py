# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemRelationship(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'child_asins': 'list[str]',
        'parent_asins': 'list[str]',
        'variation_theme': 'ItemVariationTheme',
        'type': 'str'
    }

    attribute_map = {
        'child_asins': 'childAsins',
        'parent_asins': 'parentAsins',
        'variation_theme': 'variationTheme',
        'type': 'type'
    }

    def __init__(self, child_asins=None, parent_asins=None, variation_theme=None, type=None):  # noqa: E501
        """ItemRelationship - a model defined in Swagger"""  # noqa: E501

        self._child_asins = None
        self._parent_asins = None
        self._variation_theme = None
        self._type = None
        self.discriminator = None

        if child_asins is not None:
            self.child_asins = child_asins
        if parent_asins is not None:
            self.parent_asins = parent_asins
        if variation_theme is not None:
            self.variation_theme = variation_theme
        self.type = type

    @property
    def child_asins(self):
        """Gets the child_asins of this ItemRelationship.  # noqa: E501

        Identifiers (ASINs) of the related items that are children of this item.  # noqa: E501

        :return: The child_asins of this ItemRelationship.  # noqa: E501
        :rtype: list[str]
        """
        return self._child_asins

    @child_asins.setter
    def child_asins(self, child_asins):
        """Sets the child_asins of this ItemRelationship.

        Identifiers (ASINs) of the related items that are children of this item.  # noqa: E501

        :param child_asins: The child_asins of this ItemRelationship.  # noqa: E501
        :type: list[str]
        """

        self._child_asins = child_asins

    @property
    def parent_asins(self):
        """Gets the parent_asins of this ItemRelationship.  # noqa: E501

        Identifiers (ASINs) of the related items that are parents of this item.  # noqa: E501

        :return: The parent_asins of this ItemRelationship.  # noqa: E501
        :rtype: list[str]
        """
        return self._parent_asins

    @parent_asins.setter
    def parent_asins(self, parent_asins):
        """Sets the parent_asins of this ItemRelationship.

        Identifiers (ASINs) of the related items that are parents of this item.  # noqa: E501

        :param parent_asins: The parent_asins of this ItemRelationship.  # noqa: E501
        :type: list[str]
        """

        self._parent_asins = parent_asins

    @property
    def variation_theme(self):
        """Gets the variation_theme of this ItemRelationship.  # noqa: E501

        For \"VARIATION\" relationships, variation theme indicating the combination of Amazon item catalog attributes that define the variation family.  # noqa: E501

        :return: The variation_theme of this ItemRelationship.  # noqa: E501
        :rtype: ItemVariationTheme
        """
        return self._variation_theme

    @variation_theme.setter
    def variation_theme(self, variation_theme):
        """Sets the variation_theme of this ItemRelationship.

        For \"VARIATION\" relationships, variation theme indicating the combination of Amazon item catalog attributes that define the variation family.  # noqa: E501

        :param variation_theme: The variation_theme of this ItemRelationship.  # noqa: E501
        :type: ItemVariationTheme
        """

        self._variation_theme = variation_theme

    @property
    def type(self):
        """Gets the type of this ItemRelationship.  # noqa: E501

        Type of relationship.  # noqa: E501

        :return: The type of this ItemRelationship.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ItemRelationship.

        Type of relationship.  # noqa: E501

        :param type: The type of this ItemRelationship.  # noqa: E501
        :type: str
        """
        if type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501
        allowed_values = ["VARIATION", "PACKAGE_HIERARCHY"]  # noqa: E501
        if type not in allowed_values:
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}"  # noqa: E501
                .format(type, allowed_values)
            )

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemRelationship, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemRelationship):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
