# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemVariationTheme(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attributes': 'list[str]',
        'theme': 'str'
    }

    attribute_map = {
        'attributes': 'attributes',
        'theme': 'theme'
    }

    def __init__(self, attributes=None, theme=None):  # noqa: E501
        """ItemVariationTheme - a model defined in Swagger"""  # noqa: E501

        self._attributes = None
        self._theme = None
        self.discriminator = None

        if attributes is not None:
            self.attributes = attributes
        if theme is not None:
            self.theme = theme

    @property
    def attributes(self):
        """Gets the attributes of this ItemVariationTheme.  # noqa: E501

        Names of the Amazon catalog item attributes associated with the variation theme.  # noqa: E501

        :return: The attributes of this ItemVariationTheme.  # noqa: E501
        :rtype: list[str]
        """
        return self._attributes

    @attributes.setter
    def attributes(self, attributes):
        """Sets the attributes of this ItemVariationTheme.

        Names of the Amazon catalog item attributes associated with the variation theme.  # noqa: E501

        :param attributes: The attributes of this ItemVariationTheme.  # noqa: E501
        :type: list[str]
        """

        self._attributes = attributes

    @property
    def theme(self):
        """Gets the theme of this ItemVariationTheme.  # noqa: E501

        Variation theme indicating the combination of Amazon item catalog attributes that define the variation family.  # noqa: E501

        :return: The theme of this ItemVariationTheme.  # noqa: E501
        :rtype: str
        """
        return self._theme

    @theme.setter
    def theme(self, theme):
        """Sets the theme of this ItemVariationTheme.

        Variation theme indicating the combination of Amazon item catalog attributes that define the variation family.  # noqa: E501

        :param theme: The theme of this ItemVariationTheme.  # noqa: E501
        :type: str
        """

        self._theme = theme

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemVariationTheme, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemVariationTheme):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
