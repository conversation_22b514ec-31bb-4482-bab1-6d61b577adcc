# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemImage(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'variant': 'str',
        'link': 'str',
        'height': 'int',
        'width': 'int'
    }

    attribute_map = {
        'variant': 'variant',
        'link': 'link',
        'height': 'height',
        'width': 'width'
    }

    def __init__(self, variant=None, link=None, height=None, width=None):  # noqa: E501
        """ItemImage - a model defined in Swagger"""  # noqa: E501

        self._variant = None
        self._link = None
        self._height = None
        self._width = None
        self.discriminator = None

        self.variant = variant
        self.link = link
        self.height = height
        self.width = width

    @property
    def variant(self):
        """Gets the variant of this ItemImage.  # noqa: E501

        Variant of the image, such as `MAIN` or `PT01`.  # noqa: E501

        :return: The variant of this ItemImage.  # noqa: E501
        :rtype: str
        """
        return self._variant

    @variant.setter
    def variant(self, variant):
        """Sets the variant of this ItemImage.

        Variant of the image, such as `MAIN` or `PT01`.  # noqa: E501

        :param variant: The variant of this ItemImage.  # noqa: E501
        :type: str
        """
        if variant is None:
            raise ValueError("Invalid value for `variant`, must not be `None`")  # noqa: E501
        allowed_values = ["MAIN", "PT01", "PT02", "PT03", "PT04", "PT05", "PT06", "PT07", "PT08", "SWCH"]  # noqa: E501
        if variant not in allowed_values:
            raise ValueError(
                "Invalid value for `variant` ({0}), must be one of {1}"  # noqa: E501
                .format(variant, allowed_values)
            )

        self._variant = variant

    @property
    def link(self):
        """Gets the link of this ItemImage.  # noqa: E501

        Link, or URL, for the image.  # noqa: E501

        :return: The link of this ItemImage.  # noqa: E501
        :rtype: str
        """
        return self._link

    @link.setter
    def link(self, link):
        """Sets the link of this ItemImage.

        Link, or URL, for the image.  # noqa: E501

        :param link: The link of this ItemImage.  # noqa: E501
        :type: str
        """
        if link is None:
            raise ValueError("Invalid value for `link`, must not be `None`")  # noqa: E501

        self._link = link

    @property
    def height(self):
        """Gets the height of this ItemImage.  # noqa: E501

        Height of the image in pixels.  # noqa: E501

        :return: The height of this ItemImage.  # noqa: E501
        :rtype: int
        """
        return self._height

    @height.setter
    def height(self, height):
        """Sets the height of this ItemImage.

        Height of the image in pixels.  # noqa: E501

        :param height: The height of this ItemImage.  # noqa: E501
        :type: int
        """
        if height is None:
            raise ValueError("Invalid value for `height`, must not be `None`")  # noqa: E501

        self._height = height

    @property
    def width(self):
        """Gets the width of this ItemImage.  # noqa: E501

        Width of the image in pixels.  # noqa: E501

        :return: The width of this ItemImage.  # noqa: E501
        :rtype: int
        """
        return self._width

    @width.setter
    def width(self, width):
        """Sets the width of this ItemImage.

        Width of the image in pixels.  # noqa: E501

        :param width: The width of this ItemImage.  # noqa: E501
        :type: int
        """
        if width is None:
            raise ValueError("Invalid value for `width`, must not be `None`")  # noqa: E501

        self._width = width

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemImage, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemImage):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
