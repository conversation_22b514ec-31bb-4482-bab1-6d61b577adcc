# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemVendorDetailsByMarketplace(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'marketplace_id': 'str',
        'brand_code': 'str',
        'manufacturer_code': 'str',
        'manufacturer_code_parent': 'str',
        'product_category': 'ItemVendorDetailsCategory',
        'product_group': 'str',
        'product_subcategory': 'ItemVendorDetailsCategory',
        'replenishment_category': 'str'
    }

    attribute_map = {
        'marketplace_id': 'marketplaceId',
        'brand_code': 'brandCode',
        'manufacturer_code': 'manufacturerCode',
        'manufacturer_code_parent': 'manufacturerCodeParent',
        'product_category': 'productCategory',
        'product_group': 'productGroup',
        'product_subcategory': 'productSubcategory',
        'replenishment_category': 'replenishmentCategory'
    }

    def __init__(self, marketplace_id=None, brand_code=None, manufacturer_code=None, manufacturer_code_parent=None, product_category=None, product_group=None, product_subcategory=None, replenishment_category=None):  # noqa: E501
        """ItemVendorDetailsByMarketplace - a model defined in Swagger"""  # noqa: E501

        self._marketplace_id = None
        self._brand_code = None
        self._manufacturer_code = None
        self._manufacturer_code_parent = None
        self._product_category = None
        self._product_group = None
        self._product_subcategory = None
        self._replenishment_category = None
        self.discriminator = None

        self.marketplace_id = marketplace_id
        if brand_code is not None:
            self.brand_code = brand_code
        if manufacturer_code is not None:
            self.manufacturer_code = manufacturer_code
        if manufacturer_code_parent is not None:
            self.manufacturer_code_parent = manufacturer_code_parent
        if product_category is not None:
            self.product_category = product_category
        if product_group is not None:
            self.product_group = product_group
        if product_subcategory is not None:
            self.product_subcategory = product_subcategory
        if replenishment_category is not None:
            self.replenishment_category = replenishment_category

    @property
    def marketplace_id(self):
        """Gets the marketplace_id of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Amazon marketplace identifier.  # noqa: E501

        :return: The marketplace_id of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._marketplace_id

    @marketplace_id.setter
    def marketplace_id(self, marketplace_id):
        """Sets the marketplace_id of this ItemVendorDetailsByMarketplace.

        Amazon marketplace identifier.  # noqa: E501

        :param marketplace_id: The marketplace_id of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """
        if marketplace_id is None:
            raise ValueError("Invalid value for `marketplace_id`, must not be `None`")  # noqa: E501

        self._marketplace_id = marketplace_id

    @property
    def brand_code(self):
        """Gets the brand_code of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Brand code associated with an Amazon catalog item.  # noqa: E501

        :return: The brand_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._brand_code

    @brand_code.setter
    def brand_code(self, brand_code):
        """Sets the brand_code of this ItemVendorDetailsByMarketplace.

        Brand code associated with an Amazon catalog item.  # noqa: E501

        :param brand_code: The brand_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """

        self._brand_code = brand_code

    @property
    def manufacturer_code(self):
        """Gets the manufacturer_code of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Manufacturer code associated with an Amazon catalog item.  # noqa: E501

        :return: The manufacturer_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._manufacturer_code

    @manufacturer_code.setter
    def manufacturer_code(self, manufacturer_code):
        """Sets the manufacturer_code of this ItemVendorDetailsByMarketplace.

        Manufacturer code associated with an Amazon catalog item.  # noqa: E501

        :param manufacturer_code: The manufacturer_code of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """

        self._manufacturer_code = manufacturer_code

    @property
    def manufacturer_code_parent(self):
        """Gets the manufacturer_code_parent of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Parent vendor code of the manufacturer code.  # noqa: E501

        :return: The manufacturer_code_parent of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._manufacturer_code_parent

    @manufacturer_code_parent.setter
    def manufacturer_code_parent(self, manufacturer_code_parent):
        """Sets the manufacturer_code_parent of this ItemVendorDetailsByMarketplace.

        Parent vendor code of the manufacturer code.  # noqa: E501

        :param manufacturer_code_parent: The manufacturer_code_parent of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """

        self._manufacturer_code_parent = manufacturer_code_parent

    @property
    def product_category(self):
        """Gets the product_category of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Product category associated with an Amazon catalog item.  # noqa: E501

        :return: The product_category of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: ItemVendorDetailsCategory
        """
        return self._product_category

    @product_category.setter
    def product_category(self, product_category):
        """Sets the product_category of this ItemVendorDetailsByMarketplace.

        Product category associated with an Amazon catalog item.  # noqa: E501

        :param product_category: The product_category of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: ItemVendorDetailsCategory
        """

        self._product_category = product_category

    @property
    def product_group(self):
        """Gets the product_group of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Product group associated with an Amazon catalog item.  # noqa: E501

        :return: The product_group of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._product_group

    @product_group.setter
    def product_group(self, product_group):
        """Sets the product_group of this ItemVendorDetailsByMarketplace.

        Product group associated with an Amazon catalog item.  # noqa: E501

        :param product_group: The product_group of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """

        self._product_group = product_group

    @property
    def product_subcategory(self):
        """Gets the product_subcategory of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Product subcategory associated with an Amazon catalog item.  # noqa: E501

        :return: The product_subcategory of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: ItemVendorDetailsCategory
        """
        return self._product_subcategory

    @product_subcategory.setter
    def product_subcategory(self, product_subcategory):
        """Sets the product_subcategory of this ItemVendorDetailsByMarketplace.

        Product subcategory associated with an Amazon catalog item.  # noqa: E501

        :param product_subcategory: The product_subcategory of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: ItemVendorDetailsCategory
        """

        self._product_subcategory = product_subcategory

    @property
    def replenishment_category(self):
        """Gets the replenishment_category of this ItemVendorDetailsByMarketplace.  # noqa: E501

        Replenishment category associated with an Amazon catalog item.  # noqa: E501

        :return: The replenishment_category of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._replenishment_category

    @replenishment_category.setter
    def replenishment_category(self, replenishment_category):
        """Sets the replenishment_category of this ItemVendorDetailsByMarketplace.

        Replenishment category associated with an Amazon catalog item.  # noqa: E501

        :param replenishment_category: The replenishment_category of this ItemVendorDetailsByMarketplace.  # noqa: E501
        :type: str
        """
        allowed_values = ["ALLOCATED", "BASIC_REPLENISHMENT", "IN_SEASON", "LIMITED_REPLENISHMENT", "MANUFACTURER_OUT_OF_STOCK", "NEW_PRODUCT", "NON_REPLENISHABLE", "NON_STOCKUPABLE", "OBSOLETE", "PLANNED_REPLENISHMENT"]  # noqa: E501
        if replenishment_category not in allowed_values:
            raise ValueError(
                "Invalid value for `replenishment_category` ({0}), must be one of {1}"  # noqa: E501
                .format(replenishment_category, allowed_values)
            )

        self._replenishment_category = replenishment_category

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemVendorDetailsByMarketplace, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemVendorDetailsByMarketplace):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
