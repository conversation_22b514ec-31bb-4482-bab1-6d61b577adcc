# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemBrowseClassificationsByMarketplace(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'marketplace_id': 'str',
        'classifications': 'list[ItemBrowseClassification]'
    }

    attribute_map = {
        'marketplace_id': 'marketplaceId',
        'classifications': 'classifications'
    }

    def __init__(self, marketplace_id=None, classifications=None):  # noqa: E501
        """ItemBrowseClassificationsByMarketplace - a model defined in Swagger"""  # noqa: E501

        self._marketplace_id = None
        self._classifications = None
        self.discriminator = None

        self.marketplace_id = marketplace_id
        if classifications is not None:
            self.classifications = classifications

    @property
    def marketplace_id(self):
        """Gets the marketplace_id of this ItemBrowseClassificationsByMarketplace.  # noqa: E501

        Amazon marketplace identifier.  # noqa: E501

        :return: The marketplace_id of this ItemBrowseClassificationsByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._marketplace_id

    @marketplace_id.setter
    def marketplace_id(self, marketplace_id):
        """Sets the marketplace_id of this ItemBrowseClassificationsByMarketplace.

        Amazon marketplace identifier.  # noqa: E501

        :param marketplace_id: The marketplace_id of this ItemBrowseClassificationsByMarketplace.  # noqa: E501
        :type: str
        """
        if marketplace_id is None:
            raise ValueError("Invalid value for `marketplace_id`, must not be `None`")  # noqa: E501

        self._marketplace_id = marketplace_id

    @property
    def classifications(self):
        """Gets the classifications of this ItemBrowseClassificationsByMarketplace.  # noqa: E501

        Classifications (browse nodes) associated with the item in the Amazon catalog for the indicated Amazon marketplace.  # noqa: E501

        :return: The classifications of this ItemBrowseClassificationsByMarketplace.  # noqa: E501
        :rtype: list[ItemBrowseClassification]
        """
        return self._classifications

    @classifications.setter
    def classifications(self, classifications):
        """Sets the classifications of this ItemBrowseClassificationsByMarketplace.

        Classifications (browse nodes) associated with the item in the Amazon catalog for the indicated Amazon marketplace.  # noqa: E501

        :param classifications: The classifications of this ItemBrowseClassificationsByMarketplace.  # noqa: E501
        :type: list[ItemBrowseClassification]
        """

        self._classifications = classifications

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemBrowseClassificationsByMarketplace, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemBrowseClassificationsByMarketplace):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
