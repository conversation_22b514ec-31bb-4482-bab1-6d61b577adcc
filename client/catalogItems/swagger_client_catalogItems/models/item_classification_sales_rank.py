# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, refer to the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2022-04-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2022-04-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemClassificationSalesRank(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'classification_id': 'str',
        'title': 'str',
        'link': 'str',
        'rank': 'int'
    }

    attribute_map = {
        'classification_id': 'classificationId',
        'title': 'title',
        'link': 'link',
        'rank': 'rank'
    }

    def __init__(self, classification_id=None, title=None, link=None, rank=None):  # noqa: E501
        """ItemClassificationSalesRank - a model defined in Swagger"""  # noqa: E501

        self._classification_id = None
        self._title = None
        self._link = None
        self._rank = None
        self.discriminator = None

        self.classification_id = classification_id
        self.title = title
        if link is not None:
            self.link = link
        self.rank = rank

    @property
    def classification_id(self):
        """Gets the classification_id of this ItemClassificationSalesRank.  # noqa: E501

        Identifier of the classification associated with the sales rank.  # noqa: E501

        :return: The classification_id of this ItemClassificationSalesRank.  # noqa: E501
        :rtype: str
        """
        return self._classification_id

    @classification_id.setter
    def classification_id(self, classification_id):
        """Sets the classification_id of this ItemClassificationSalesRank.

        Identifier of the classification associated with the sales rank.  # noqa: E501

        :param classification_id: The classification_id of this ItemClassificationSalesRank.  # noqa: E501
        :type: str
        """
        if classification_id is None:
            raise ValueError("Invalid value for `classification_id`, must not be `None`")  # noqa: E501

        self._classification_id = classification_id

    @property
    def title(self):
        """Gets the title of this ItemClassificationSalesRank.  # noqa: E501

        Title, or name, of the sales rank.  # noqa: E501

        :return: The title of this ItemClassificationSalesRank.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this ItemClassificationSalesRank.

        Title, or name, of the sales rank.  # noqa: E501

        :param title: The title of this ItemClassificationSalesRank.  # noqa: E501
        :type: str
        """
        if title is None:
            raise ValueError("Invalid value for `title`, must not be `None`")  # noqa: E501

        self._title = title

    @property
    def link(self):
        """Gets the link of this ItemClassificationSalesRank.  # noqa: E501

        Corresponding Amazon retail website link, or URL, for the sales rank.  # noqa: E501

        :return: The link of this ItemClassificationSalesRank.  # noqa: E501
        :rtype: str
        """
        return self._link

    @link.setter
    def link(self, link):
        """Sets the link of this ItemClassificationSalesRank.

        Corresponding Amazon retail website link, or URL, for the sales rank.  # noqa: E501

        :param link: The link of this ItemClassificationSalesRank.  # noqa: E501
        :type: str
        """

        self._link = link

    @property
    def rank(self):
        """Gets the rank of this ItemClassificationSalesRank.  # noqa: E501

        Sales rank value.  # noqa: E501

        :return: The rank of this ItemClassificationSalesRank.  # noqa: E501
        :rtype: int
        """
        return self._rank

    @rank.setter
    def rank(self, rank):
        """Sets the rank of this ItemClassificationSalesRank.

        Sales rank value.  # noqa: E501

        :param rank: The rank of this ItemClassificationSalesRank.  # noqa: E501
        :type: int
        """
        if rank is None:
            raise ValueError("Invalid value for `rank`, must not be `None`")  # noqa: E501

        self._rank = rank

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemClassificationSalesRank, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemClassificationSalesRank):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
