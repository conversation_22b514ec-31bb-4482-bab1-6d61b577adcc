# ItemVendorDetailsByMarketplace

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**marketplace_id** | **str** | Amazon marketplace identifier. | 
**brand_code** | **str** | Brand code associated with an Amazon catalog item. | [optional] 
**category_code** | **str** | Product category associated with an Amazon catalog item. | [optional] 
**manufacturer_code** | **str** | Manufacturer code associated with an Amazon catalog item. | [optional] 
**manufacturer_code_parent** | **str** | Parent vendor code of the manufacturer code. | [optional] 
**product_group** | **str** | Product group associated with an Amazon catalog item. | [optional] 
**replenishment_category** | **str** | Replenishment category associated with an Amazon catalog item. | [optional] 
**subcategory_code** | **str** | Product subcategory associated with an Amazon catalog item. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


