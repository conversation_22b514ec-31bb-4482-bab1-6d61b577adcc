# swagger_client_catalogItems_20201201.CatalogApi

All URIs are relative to *https://sellingpartnerapi-na.amazon.com*

Method | HTTP request | Description
------------- | ------------- | -------------
[**get_catalog_item**](CatalogApi.md#get_catalog_item) | **GET** /catalog/2020-12-01/items/{asin} | 
[**search_catalog_items**](CatalogApi.md#search_catalog_items) | **GET** /catalog/2020-12-01/items | 


# **get_catalog_item**
> Item get_catalog_item(asin, marketplace_ids, included_data=included_data, locale=locale)



Retrieves details for an item in the Amazon catalog.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).

### Example
```python
from __future__ import print_function
import time
import swagger_client_catalogItems_20201201
from swagger_client_catalogItems_20201201.rest import ApiException
from pprint import pprint

# create an instance of the API class
api_instance = swagger_client_catalogItems_20201201.CatalogApi()
asin = 'asin_example' # str | The Amazon Standard Identification Number (ASIN) of the item.
marketplace_ids = ['ATVPDKIKX0DER'] # list[str] | A comma-delimited list of Amazon marketplace identifiers. Data sets in the response contain data only for the specified marketplaces.
included_data = ['summaries'] # list[str] | A comma-delimited list of data sets to include in the response. Default: summaries. (optional)
locale = 'en_US' # str | Locale for retrieving localized summaries. Defaults to the primary locale of the marketplace. (optional)

try:
    api_response = api_instance.get_catalog_item(asin, marketplace_ids, included_data=included_data, locale=locale)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling CatalogApi->get_catalog_item: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **asin** | **str**| The Amazon Standard Identification Number (ASIN) of the item. | 
 **marketplace_ids** | [**list[str]**](str.md)| A comma-delimited list of Amazon marketplace identifiers. Data sets in the response contain data only for the specified marketplaces. | 
 **included_data** | [**list[str]**](str.md)| A comma-delimited list of data sets to include in the response. Default: summaries. | [optional] 
 **locale** | **str**| Locale for retrieving localized summaries. Defaults to the primary locale of the marketplace. | [optional] 

### Return type

[**Item**](Item.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **search_catalog_items**
> ItemSearchResults search_catalog_items(keywords, marketplace_ids, included_data=included_data, brand_names=brand_names, classification_ids=classification_ids, page_size=page_size, page_token=page_token, keywords_locale=keywords_locale, locale=locale)



Search for and return a list of Amazon catalog items and associated information.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, see [Usage Plans and Rate Limits in the Selling Partner API](doc:usage-plans-and-rate-limits-in-the-sp-api).

### Example
```python
from __future__ import print_function
import time
import swagger_client_catalogItems_20201201
from swagger_client_catalogItems_20201201.rest import ApiException
from pprint import pprint

# create an instance of the API class
api_instance = swagger_client_catalogItems_20201201.CatalogApi()
keywords = ['shoes'] # list[str] | A comma-delimited list of words or item identifiers to search the Amazon catalog for.
marketplace_ids = ['ATVPDKIKX0DER'] # list[str] | A comma-delimited list of Amazon marketplace identifiers for the request.
included_data = ['summaries'] # list[str] | A comma-delimited list of data sets to include in the response. Default: summaries. (optional)
brand_names = ['Beautiful Boats'] # list[str] | A comma-delimited list of brand names to limit the search to. (optional)
classification_ids = ['12345678'] # list[str] | A comma-delimited list of classification identifiers to limit the search to. (optional)
page_size = 10 # int | Number of results to be returned per page. (optional) (default to 10)
page_token = 'sdlkj234lkj234lksjdflkjwdflkjsfdlkj234234234234' # str | A token to fetch a certain page when there are multiple pages worth of results. (optional)
keywords_locale = 'en_US' # str | The language the keywords are provided in. Defaults to the primary locale of the marketplace. (optional)
locale = 'en_US' # str | Locale for retrieving localized summaries. Defaults to the primary locale of the marketplace. (optional)

try:
    api_response = api_instance.search_catalog_items(keywords, marketplace_ids, included_data=included_data, brand_names=brand_names, classification_ids=classification_ids, page_size=page_size, page_token=page_token, keywords_locale=keywords_locale, locale=locale)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling CatalogApi->search_catalog_items: %s\n" % e)
```

### Parameters

Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **keywords** | [**list[str]**](str.md)| A comma-delimited list of words or item identifiers to search the Amazon catalog for. | 
 **marketplace_ids** | [**list[str]**](str.md)| A comma-delimited list of Amazon marketplace identifiers for the request. | 
 **included_data** | [**list[str]**](str.md)| A comma-delimited list of data sets to include in the response. Default: summaries. | [optional] 
 **brand_names** | [**list[str]**](str.md)| A comma-delimited list of brand names to limit the search to. | [optional] 
 **classification_ids** | [**list[str]**](str.md)| A comma-delimited list of classification identifiers to limit the search to. | [optional] 
 **page_size** | **int**| Number of results to be returned per page. | [optional] [default to 10]
 **page_token** | **str**| A token to fetch a certain page when there are multiple pages worth of results. | [optional] 
 **keywords_locale** | **str**| The language the keywords are provided in. Defaults to the primary locale of the marketplace. | [optional] 
 **locale** | **str**| Locale for retrieving localized summaries. Defaults to the primary locale of the marketplace. | [optional] 

### Return type

[**ItemSearchResults**](ItemSearchResults.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

