# BrandRefinement

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**number_of_results** | **int** | The estimated number of results that would still be returned if refinement key applied. | 
**brand_name** | **str** | Brand name. For display and can be used as a search refinement. | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


