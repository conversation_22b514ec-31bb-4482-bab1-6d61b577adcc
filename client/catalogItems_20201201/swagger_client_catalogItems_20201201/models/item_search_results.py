# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, see the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2020-12-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2020-12-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemSearchResults(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'number_of_results': 'int',
        'pagination': 'Pagination',
        'refinements': 'Refinements',
        'items': 'list[Item]'
    }

    attribute_map = {
        'number_of_results': 'numberOfResults',
        'pagination': 'pagination',
        'refinements': 'refinements',
        'items': 'items'
    }

    def __init__(self, number_of_results=None, pagination=None, refinements=None, items=None):  # noqa: E501
        """ItemSearchResults - a model defined in Swagger"""  # noqa: E501

        self._number_of_results = None
        self._pagination = None
        self._refinements = None
        self._items = None
        self.discriminator = None

        self.number_of_results = number_of_results
        self.pagination = pagination
        self.refinements = refinements
        self.items = items

    @property
    def number_of_results(self):
        """Gets the number_of_results of this ItemSearchResults.  # noqa: E501

        The estimated total number of products matched by the search query (only results up to the page count limit will be returned per request regardless of the number found).  Note: The maximum number of items (ASINs) that can be returned and paged through is 1000.  # noqa: E501

        :return: The number_of_results of this ItemSearchResults.  # noqa: E501
        :rtype: int
        """
        return self._number_of_results

    @number_of_results.setter
    def number_of_results(self, number_of_results):
        """Sets the number_of_results of this ItemSearchResults.

        The estimated total number of products matched by the search query (only results up to the page count limit will be returned per request regardless of the number found).  Note: The maximum number of items (ASINs) that can be returned and paged through is 1000.  # noqa: E501

        :param number_of_results: The number_of_results of this ItemSearchResults.  # noqa: E501
        :type: int
        """
        if number_of_results is None:
            raise ValueError("Invalid value for `number_of_results`, must not be `None`")  # noqa: E501

        self._number_of_results = number_of_results

    @property
    def pagination(self):
        """Gets the pagination of this ItemSearchResults.  # noqa: E501

        If available, the nextToken and/or previousToken values required to return paginated results.  # noqa: E501

        :return: The pagination of this ItemSearchResults.  # noqa: E501
        :rtype: Pagination
        """
        return self._pagination

    @pagination.setter
    def pagination(self, pagination):
        """Sets the pagination of this ItemSearchResults.

        If available, the nextToken and/or previousToken values required to return paginated results.  # noqa: E501

        :param pagination: The pagination of this ItemSearchResults.  # noqa: E501
        :type: Pagination
        """
        if pagination is None:
            raise ValueError("Invalid value for `pagination`, must not be `None`")  # noqa: E501

        self._pagination = pagination

    @property
    def refinements(self):
        """Gets the refinements of this ItemSearchResults.  # noqa: E501


        :return: The refinements of this ItemSearchResults.  # noqa: E501
        :rtype: Refinements
        """
        return self._refinements

    @refinements.setter
    def refinements(self, refinements):
        """Sets the refinements of this ItemSearchResults.


        :param refinements: The refinements of this ItemSearchResults.  # noqa: E501
        :type: Refinements
        """
        if refinements is None:
            raise ValueError("Invalid value for `refinements`, must not be `None`")  # noqa: E501

        self._refinements = refinements

    @property
    def items(self):
        """Gets the items of this ItemSearchResults.  # noqa: E501

        A list of items from the Amazon catalog.  # noqa: E501

        :return: The items of this ItemSearchResults.  # noqa: E501
        :rtype: list[Item]
        """
        return self._items

    @items.setter
    def items(self, items):
        """Sets the items of this ItemSearchResults.

        A list of items from the Amazon catalog.  # noqa: E501

        :param items: The items of this ItemSearchResults.  # noqa: E501
        :type: list[Item]
        """
        if items is None:
            raise ValueError("Invalid value for `items`, must not be `None`")  # noqa: E501

        self._items = items

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemSearchResults, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemSearchResults):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
