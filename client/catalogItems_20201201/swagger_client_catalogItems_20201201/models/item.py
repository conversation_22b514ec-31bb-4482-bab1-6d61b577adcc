# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, see the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2020-12-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2020-12-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class Item(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asin': 'ItemAsin',
        'attributes': 'ItemAttributes',
        'identifiers': 'ItemIdentifiers',
        'images': 'ItemImages',
        'product_types': 'ItemProductTypes',
        'sales_ranks': 'ItemSalesRanks',
        'summaries': 'ItemSummaries',
        'variations': 'ItemVariations',
        'vendor_details': 'ItemVendorDetails'
    }

    attribute_map = {
        'asin': 'asin',
        'attributes': 'attributes',
        'identifiers': 'identifiers',
        'images': 'images',
        'product_types': 'productTypes',
        'sales_ranks': 'salesRanks',
        'summaries': 'summaries',
        'variations': 'variations',
        'vendor_details': 'vendorDetails'
    }

    def __init__(self, asin=None, attributes=None, identifiers=None, images=None, product_types=None, sales_ranks=None, summaries=None, variations=None, vendor_details=None):  # noqa: E501
        """Item - a model defined in Swagger"""  # noqa: E501

        self._asin = None
        self._attributes = None
        self._identifiers = None
        self._images = None
        self._product_types = None
        self._sales_ranks = None
        self._summaries = None
        self._variations = None
        self._vendor_details = None
        self.discriminator = None

        self.asin = asin
        if attributes is not None:
            self.attributes = attributes
        if identifiers is not None:
            self.identifiers = identifiers
        if images is not None:
            self.images = images
        if product_types is not None:
            self.product_types = product_types
        if sales_ranks is not None:
            self.sales_ranks = sales_ranks
        if summaries is not None:
            self.summaries = summaries
        if variations is not None:
            self.variations = variations
        if vendor_details is not None:
            self.vendor_details = vendor_details

    @property
    def asin(self):
        """Gets the asin of this Item.  # noqa: E501


        :return: The asin of this Item.  # noqa: E501
        :rtype: ItemAsin
        """
        return self._asin

    @asin.setter
    def asin(self, asin):
        """Sets the asin of this Item.


        :param asin: The asin of this Item.  # noqa: E501
        :type: ItemAsin
        """
        if asin is None:
            raise ValueError("Invalid value for `asin`, must not be `None`")  # noqa: E501

        self._asin = asin

    @property
    def attributes(self):
        """Gets the attributes of this Item.  # noqa: E501


        :return: The attributes of this Item.  # noqa: E501
        :rtype: ItemAttributes
        """
        return self._attributes

    @attributes.setter
    def attributes(self, attributes):
        """Sets the attributes of this Item.


        :param attributes: The attributes of this Item.  # noqa: E501
        :type: ItemAttributes
        """

        self._attributes = attributes

    @property
    def identifiers(self):
        """Gets the identifiers of this Item.  # noqa: E501


        :return: The identifiers of this Item.  # noqa: E501
        :rtype: ItemIdentifiers
        """
        return self._identifiers

    @identifiers.setter
    def identifiers(self, identifiers):
        """Sets the identifiers of this Item.


        :param identifiers: The identifiers of this Item.  # noqa: E501
        :type: ItemIdentifiers
        """

        self._identifiers = identifiers

    @property
    def images(self):
        """Gets the images of this Item.  # noqa: E501


        :return: The images of this Item.  # noqa: E501
        :rtype: ItemImages
        """
        return self._images

    @images.setter
    def images(self, images):
        """Sets the images of this Item.


        :param images: The images of this Item.  # noqa: E501
        :type: ItemImages
        """

        self._images = images

    @property
    def product_types(self):
        """Gets the product_types of this Item.  # noqa: E501


        :return: The product_types of this Item.  # noqa: E501
        :rtype: ItemProductTypes
        """
        return self._product_types

    @product_types.setter
    def product_types(self, product_types):
        """Sets the product_types of this Item.


        :param product_types: The product_types of this Item.  # noqa: E501
        :type: ItemProductTypes
        """

        self._product_types = product_types

    @property
    def sales_ranks(self):
        """Gets the sales_ranks of this Item.  # noqa: E501


        :return: The sales_ranks of this Item.  # noqa: E501
        :rtype: ItemSalesRanks
        """
        return self._sales_ranks

    @sales_ranks.setter
    def sales_ranks(self, sales_ranks):
        """Sets the sales_ranks of this Item.


        :param sales_ranks: The sales_ranks of this Item.  # noqa: E501
        :type: ItemSalesRanks
        """

        self._sales_ranks = sales_ranks

    @property
    def summaries(self):
        """Gets the summaries of this Item.  # noqa: E501


        :return: The summaries of this Item.  # noqa: E501
        :rtype: ItemSummaries
        """
        return self._summaries

    @summaries.setter
    def summaries(self, summaries):
        """Sets the summaries of this Item.


        :param summaries: The summaries of this Item.  # noqa: E501
        :type: ItemSummaries
        """

        self._summaries = summaries

    @property
    def variations(self):
        """Gets the variations of this Item.  # noqa: E501


        :return: The variations of this Item.  # noqa: E501
        :rtype: ItemVariations
        """
        return self._variations

    @variations.setter
    def variations(self, variations):
        """Sets the variations of this Item.


        :param variations: The variations of this Item.  # noqa: E501
        :type: ItemVariations
        """

        self._variations = variations

    @property
    def vendor_details(self):
        """Gets the vendor_details of this Item.  # noqa: E501


        :return: The vendor_details of this Item.  # noqa: E501
        :rtype: ItemVendorDetails
        """
        return self._vendor_details

    @vendor_details.setter
    def vendor_details(self, vendor_details):
        """Sets the vendor_details of this Item.


        :param vendor_details: The vendor_details of this Item.  # noqa: E501
        :type: ItemVendorDetails
        """

        self._vendor_details = vendor_details

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Item, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Item):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
