# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, see the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2020-12-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2020-12-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class BrandRefinement(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'number_of_results': 'int',
        'brand_name': 'str'
    }

    attribute_map = {
        'number_of_results': 'numberOfResults',
        'brand_name': 'brandName'
    }

    def __init__(self, number_of_results=None, brand_name=None):  # noqa: E501
        """BrandRefinement - a model defined in Swagger"""  # noqa: E501

        self._number_of_results = None
        self._brand_name = None
        self.discriminator = None

        self.number_of_results = number_of_results
        self.brand_name = brand_name

    @property
    def number_of_results(self):
        """Gets the number_of_results of this BrandRefinement.  # noqa: E501

        The estimated number of results that would still be returned if refinement key applied.  # noqa: E501

        :return: The number_of_results of this BrandRefinement.  # noqa: E501
        :rtype: int
        """
        return self._number_of_results

    @number_of_results.setter
    def number_of_results(self, number_of_results):
        """Sets the number_of_results of this BrandRefinement.

        The estimated number of results that would still be returned if refinement key applied.  # noqa: E501

        :param number_of_results: The number_of_results of this BrandRefinement.  # noqa: E501
        :type: int
        """
        if number_of_results is None:
            raise ValueError("Invalid value for `number_of_results`, must not be `None`")  # noqa: E501

        self._number_of_results = number_of_results

    @property
    def brand_name(self):
        """Gets the brand_name of this BrandRefinement.  # noqa: E501

        Brand name. For display and can be used as a search refinement.  # noqa: E501

        :return: The brand_name of this BrandRefinement.  # noqa: E501
        :rtype: str
        """
        return self._brand_name

    @brand_name.setter
    def brand_name(self, brand_name):
        """Sets the brand_name of this BrandRefinement.

        Brand name. For display and can be used as a search refinement.  # noqa: E501

        :param brand_name: The brand_name of this BrandRefinement.  # noqa: E501
        :type: str
        """
        if brand_name is None:
            raise ValueError("Invalid value for `brand_name`, must not be `None`")  # noqa: E501

        self._brand_name = brand_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BrandRefinement, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BrandRefinement):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
