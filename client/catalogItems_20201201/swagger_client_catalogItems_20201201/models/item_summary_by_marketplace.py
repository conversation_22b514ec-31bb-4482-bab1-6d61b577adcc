# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, see the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2020-12-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2020-12-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemSummaryByMarketplace(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'marketplace_id': 'str',
        'brand_name': 'str',
        'browse_node': 'str',
        'color_name': 'str',
        'item_name': 'str',
        'manufacturer': 'str',
        'model_number': 'str',
        'size_name': 'str',
        'style_name': 'str'
    }

    attribute_map = {
        'marketplace_id': 'marketplaceId',
        'brand_name': 'brandName',
        'browse_node': 'browseNode',
        'color_name': 'colorName',
        'item_name': 'itemName',
        'manufacturer': 'manufacturer',
        'model_number': 'modelNumber',
        'size_name': 'sizeName',
        'style_name': 'styleName'
    }

    def __init__(self, marketplace_id=None, brand_name=None, browse_node=None, color_name=None, item_name=None, manufacturer=None, model_number=None, size_name=None, style_name=None):  # noqa: E501
        """ItemSummaryByMarketplace - a model defined in Swagger"""  # noqa: E501

        self._marketplace_id = None
        self._brand_name = None
        self._browse_node = None
        self._color_name = None
        self._item_name = None
        self._manufacturer = None
        self._model_number = None
        self._size_name = None
        self._style_name = None
        self.discriminator = None

        self.marketplace_id = marketplace_id
        if brand_name is not None:
            self.brand_name = brand_name
        if browse_node is not None:
            self.browse_node = browse_node
        if color_name is not None:
            self.color_name = color_name
        if item_name is not None:
            self.item_name = item_name
        if manufacturer is not None:
            self.manufacturer = manufacturer
        if model_number is not None:
            self.model_number = model_number
        if size_name is not None:
            self.size_name = size_name
        if style_name is not None:
            self.style_name = style_name

    @property
    def marketplace_id(self):
        """Gets the marketplace_id of this ItemSummaryByMarketplace.  # noqa: E501

        Amazon marketplace identifier.  # noqa: E501

        :return: The marketplace_id of this ItemSummaryByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._marketplace_id

    @marketplace_id.setter
    def marketplace_id(self, marketplace_id):
        """Sets the marketplace_id of this ItemSummaryByMarketplace.

        Amazon marketplace identifier.  # noqa: E501

        :param marketplace_id: The marketplace_id of this ItemSummaryByMarketplace.  # noqa: E501
        :type: str
        """
        if marketplace_id is None:
            raise ValueError("Invalid value for `marketplace_id`, must not be `None`")  # noqa: E501

        self._marketplace_id = marketplace_id

    @property
    def brand_name(self):
        """Gets the brand_name of this ItemSummaryByMarketplace.  # noqa: E501

        Name of the brand associated with an Amazon catalog item.  # noqa: E501

        :return: The brand_name of this ItemSummaryByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._brand_name

    @brand_name.setter
    def brand_name(self, brand_name):
        """Sets the brand_name of this ItemSummaryByMarketplace.

        Name of the brand associated with an Amazon catalog item.  # noqa: E501

        :param brand_name: The brand_name of this ItemSummaryByMarketplace.  # noqa: E501
        :type: str
        """

        self._brand_name = brand_name

    @property
    def browse_node(self):
        """Gets the browse_node of this ItemSummaryByMarketplace.  # noqa: E501

        Identifier of the browse node associated with an Amazon catalog item.  # noqa: E501

        :return: The browse_node of this ItemSummaryByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._browse_node

    @browse_node.setter
    def browse_node(self, browse_node):
        """Sets the browse_node of this ItemSummaryByMarketplace.

        Identifier of the browse node associated with an Amazon catalog item.  # noqa: E501

        :param browse_node: The browse_node of this ItemSummaryByMarketplace.  # noqa: E501
        :type: str
        """

        self._browse_node = browse_node

    @property
    def color_name(self):
        """Gets the color_name of this ItemSummaryByMarketplace.  # noqa: E501

        Name of the color associated with an Amazon catalog item.  # noqa: E501

        :return: The color_name of this ItemSummaryByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._color_name

    @color_name.setter
    def color_name(self, color_name):
        """Sets the color_name of this ItemSummaryByMarketplace.

        Name of the color associated with an Amazon catalog item.  # noqa: E501

        :param color_name: The color_name of this ItemSummaryByMarketplace.  # noqa: E501
        :type: str
        """

        self._color_name = color_name

    @property
    def item_name(self):
        """Gets the item_name of this ItemSummaryByMarketplace.  # noqa: E501

        Name, or title, associated with an Amazon catalog item.  # noqa: E501

        :return: The item_name of this ItemSummaryByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._item_name

    @item_name.setter
    def item_name(self, item_name):
        """Sets the item_name of this ItemSummaryByMarketplace.

        Name, or title, associated with an Amazon catalog item.  # noqa: E501

        :param item_name: The item_name of this ItemSummaryByMarketplace.  # noqa: E501
        :type: str
        """

        self._item_name = item_name

    @property
    def manufacturer(self):
        """Gets the manufacturer of this ItemSummaryByMarketplace.  # noqa: E501

        Name of the manufacturer associated with an Amazon catalog item.  # noqa: E501

        :return: The manufacturer of this ItemSummaryByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._manufacturer

    @manufacturer.setter
    def manufacturer(self, manufacturer):
        """Sets the manufacturer of this ItemSummaryByMarketplace.

        Name of the manufacturer associated with an Amazon catalog item.  # noqa: E501

        :param manufacturer: The manufacturer of this ItemSummaryByMarketplace.  # noqa: E501
        :type: str
        """

        self._manufacturer = manufacturer

    @property
    def model_number(self):
        """Gets the model_number of this ItemSummaryByMarketplace.  # noqa: E501

        Model number associated with an Amazon catalog item.  # noqa: E501

        :return: The model_number of this ItemSummaryByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._model_number

    @model_number.setter
    def model_number(self, model_number):
        """Sets the model_number of this ItemSummaryByMarketplace.

        Model number associated with an Amazon catalog item.  # noqa: E501

        :param model_number: The model_number of this ItemSummaryByMarketplace.  # noqa: E501
        :type: str
        """

        self._model_number = model_number

    @property
    def size_name(self):
        """Gets the size_name of this ItemSummaryByMarketplace.  # noqa: E501

        Name of the size associated with an Amazon catalog item.  # noqa: E501

        :return: The size_name of this ItemSummaryByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._size_name

    @size_name.setter
    def size_name(self, size_name):
        """Sets the size_name of this ItemSummaryByMarketplace.

        Name of the size associated with an Amazon catalog item.  # noqa: E501

        :param size_name: The size_name of this ItemSummaryByMarketplace.  # noqa: E501
        :type: str
        """

        self._size_name = size_name

    @property
    def style_name(self):
        """Gets the style_name of this ItemSummaryByMarketplace.  # noqa: E501

        Name of the style associated with an Amazon catalog item.  # noqa: E501

        :return: The style_name of this ItemSummaryByMarketplace.  # noqa: E501
        :rtype: str
        """
        return self._style_name

    @style_name.setter
    def style_name(self, style_name):
        """Sets the style_name of this ItemSummaryByMarketplace.

        Name of the style associated with an Amazon catalog item.  # noqa: E501

        :param style_name: The style_name of this ItemSummaryByMarketplace.  # noqa: E501
        :type: str
        """

        self._style_name = style_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemSummaryByMarketplace, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemSummaryByMarketplace):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
