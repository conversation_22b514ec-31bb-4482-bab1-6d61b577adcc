# swagger-client-catalogItems-20201201
The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, see the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2020-12-01-use-case-guide).

This Python package is automatically generated by the [Swagger Codegen](https://github.com/swagger-api/swagger-codegen) project:

- API version: 2020-12-01
- Package version: 1.0.0
- Build package: io.swagger.codegen.languages.PythonClientCodegen
For more information, please visit [https://sellercentral.amazon.com/gp/mws/contactus.html](https://sellercentral.amazon.com/gp/mws/contactus.html)

## Requirements.

Python 2.7 and 3.4+

## Installation & Usage
### pip install

If the python package is hosted on Github, you can install directly from Github

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```
(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:
```python
import swagger_client_catalogItems_20201201 
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```
(or `sudo python setup.py install` to install the package for all users)

Then import the package:
```python
import swagger_client_catalogItems_20201201
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python
from __future__ import print_function
import time
import swagger_client_catalogItems_20201201
from swagger_client_catalogItems_20201201.rest import ApiException
from pprint import pprint

# create an instance of the API class
api_instance = swagger_client_catalogItems_20201201.CatalogApi(swagger_client_catalogItems_20201201.ApiClient(configuration))
asin = 'asin_example' # str | The Amazon Standard Identification Number (ASIN) of the item.
marketplace_ids = ['ATVPDKIKX0DER'] # list[str] | A comma-delimited list of Amazon marketplace identifiers. Data sets in the response contain data only for the specified marketplaces.
included_data = ['summaries'] # list[str] | A comma-delimited list of data sets to include in the response. Default: summaries. (optional)
locale = 'en_US' # str | Locale for retrieving localized summaries. Defaults to the primary locale of the marketplace. (optional)

try:
    api_response = api_instance.get_catalog_item(asin, marketplace_ids, included_data=included_data, locale=locale)
    pprint(api_response)
except ApiException as e:
    print("Exception when calling CatalogApi->get_catalog_item: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *https://sellingpartnerapi-na.amazon.com*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*CatalogApi* | [**get_catalog_item**](docs/CatalogApi.md#get_catalog_item) | **GET** /catalog/2020-12-01/items/{asin} | 
*CatalogApi* | [**search_catalog_items**](docs/CatalogApi.md#search_catalog_items) | **GET** /catalog/2020-12-01/items | 


## Documentation For Models

 - [BrandRefinement](docs/BrandRefinement.md)
 - [ClassificationRefinement](docs/ClassificationRefinement.md)
 - [Error](docs/Error.md)
 - [ErrorList](docs/ErrorList.md)
 - [Item](docs/Item.md)
 - [ItemAsin](docs/ItemAsin.md)
 - [ItemAttributes](docs/ItemAttributes.md)
 - [ItemIdentifier](docs/ItemIdentifier.md)
 - [ItemIdentifiers](docs/ItemIdentifiers.md)
 - [ItemIdentifiersByMarketplace](docs/ItemIdentifiersByMarketplace.md)
 - [ItemImage](docs/ItemImage.md)
 - [ItemImages](docs/ItemImages.md)
 - [ItemImagesByMarketplace](docs/ItemImagesByMarketplace.md)
 - [ItemProductTypeByMarketplace](docs/ItemProductTypeByMarketplace.md)
 - [ItemProductTypes](docs/ItemProductTypes.md)
 - [ItemSalesRank](docs/ItemSalesRank.md)
 - [ItemSalesRanks](docs/ItemSalesRanks.md)
 - [ItemSalesRanksByMarketplace](docs/ItemSalesRanksByMarketplace.md)
 - [ItemSearchResults](docs/ItemSearchResults.md)
 - [ItemSummaries](docs/ItemSummaries.md)
 - [ItemSummaryByMarketplace](docs/ItemSummaryByMarketplace.md)
 - [ItemVariations](docs/ItemVariations.md)
 - [ItemVariationsByMarketplace](docs/ItemVariationsByMarketplace.md)
 - [ItemVendorDetails](docs/ItemVendorDetails.md)
 - [ItemVendorDetailsByMarketplace](docs/ItemVendorDetailsByMarketplace.md)
 - [Pagination](docs/Pagination.md)
 - [Refinements](docs/Refinements.md)


## Documentation For Authorization

 All endpoints do not require authorization.


## Author



