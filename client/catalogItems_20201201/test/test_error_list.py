# coding: utf-8

"""
    Selling Partner API for Catalog Items

    The Selling Partner API for Catalog Items provides programmatic access to information about items in the Amazon catalog.  For more information, see the [Catalog Items API Use Case Guide](doc:catalog-items-api-v2020-12-01-use-case-guide).  # noqa: E501

    OpenAPI spec version: 2020-12-01
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_catalogItems_20201201
from swagger_client_catalogItems_20201201.models.error_list import ErrorList  # noqa: E501
from swagger_client_catalogItems_20201201.rest import ApiException


class TestErrorList(unittest.TestCase):
    """ErrorList unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testErrorList(self):
        """Test ErrorList"""
        # FIXME: construct object with mandatory attributes with example values
        # model = swagger_client_catalogItems_20201201.models.error_list.ErrorList()  # noqa: E501
        pass


if __name__ == '__main__':
    unittest.main()
