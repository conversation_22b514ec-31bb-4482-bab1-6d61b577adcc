# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ListingOffersResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'headers': 'HttpResponseHeaders',
        'status': 'GetOffersHttpStatusLine',
        'body': 'GetOffersResponse',
        'request': 'ListingOffersRequestParams'
    }

    attribute_map = {
        'headers': 'headers',
        'status': 'status',
        'body': 'body',
        'request': 'request'
    }

    def __init__(self, headers=None, status=None, body=None, request=None):  # noqa: E501
        """ListingOffersResponse - a model defined in Swagger"""  # noqa: E501

        self._headers = None
        self._status = None
        self._body = None
        self._request = None
        self.discriminator = None

        if headers is not None:
            self.headers = headers
        if status is not None:
            self.status = status
        self.body = body
        if request is not None:
            self.request = request

    @property
    def headers(self):
        """Gets the headers of this ListingOffersResponse.  # noqa: E501


        :return: The headers of this ListingOffersResponse.  # noqa: E501
        :rtype: HttpResponseHeaders
        """
        return self._headers

    @headers.setter
    def headers(self, headers):
        """Sets the headers of this ListingOffersResponse.


        :param headers: The headers of this ListingOffersResponse.  # noqa: E501
        :type: HttpResponseHeaders
        """

        self._headers = headers

    @property
    def status(self):
        """Gets the status of this ListingOffersResponse.  # noqa: E501


        :return: The status of this ListingOffersResponse.  # noqa: E501
        :rtype: GetOffersHttpStatusLine
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListingOffersResponse.


        :param status: The status of this ListingOffersResponse.  # noqa: E501
        :type: GetOffersHttpStatusLine
        """

        self._status = status

    @property
    def body(self):
        """Gets the body of this ListingOffersResponse.  # noqa: E501


        :return: The body of this ListingOffersResponse.  # noqa: E501
        :rtype: GetOffersResponse
        """
        return self._body

    @body.setter
    def body(self, body):
        """Sets the body of this ListingOffersResponse.


        :param body: The body of this ListingOffersResponse.  # noqa: E501
        :type: GetOffersResponse
        """
        if body is None:
            raise ValueError("Invalid value for `body`, must not be `None`")  # noqa: E501

        self._body = body

    @property
    def request(self):
        """Gets the request of this ListingOffersResponse.  # noqa: E501


        :return: The request of this ListingOffersResponse.  # noqa: E501
        :rtype: ListingOffersRequestParams
        """
        return self._request

    @request.setter
    def request(self, request):
        """Sets the request of this ListingOffersResponse.


        :param request: The request of this ListingOffersResponse.  # noqa: E501
        :type: ListingOffersRequestParams
        """

        self._request = request

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListingOffersResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListingOffersResponse):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
