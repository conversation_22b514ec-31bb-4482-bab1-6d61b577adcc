# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemOffersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'uri': 'str',
        'method': 'HttpMethod',
        'headers': 'HttpRequestHeaders',
        'marketplace_id': 'MarketplaceId',
        'item_condition': 'ItemCondition',
        'customer_type': 'CustomerType'
    }

    attribute_map = {
        'uri': 'uri',
        'method': 'method',
        'headers': 'headers',
        'marketplace_id': 'MarketplaceId',
        'item_condition': 'ItemCondition',
        'customer_type': 'CustomerType'
    }

    def __init__(self, uri=None, method=None, headers=None, marketplace_id=None, item_condition=None, customer_type=None):  # noqa: E501
        """ItemOffersRequest - a model defined in Swagger"""  # noqa: E501

        self._uri = None
        self._method = None
        self._headers = None
        self._marketplace_id = None
        self._item_condition = None
        self._customer_type = None
        self.discriminator = None

        self.uri = uri
        self.method = method
        if headers is not None:
            self.headers = headers
        self.marketplace_id = marketplace_id
        self.item_condition = item_condition
        if customer_type is not None:
            self.customer_type = customer_type

    @property
    def uri(self):
        """Gets the uri of this ItemOffersRequest.  # noqa: E501

        The resource path of the operation you are calling in batch without any query parameters.  If you are calling `getItemOffersBatch`, supply the path of `getItemOffers`.  **Example:** `/products/pricing/v0/items/B000P6Q7MY/offers`  If you are calling `getListingOffersBatch`, supply the path of `getListingOffers`.  **Example:** `/products/pricing/v0/listings/B000P6Q7MY/offers`  # noqa: E501

        :return: The uri of this ItemOffersRequest.  # noqa: E501
        :rtype: str
        """
        return self._uri

    @uri.setter
    def uri(self, uri):
        """Sets the uri of this ItemOffersRequest.

        The resource path of the operation you are calling in batch without any query parameters.  If you are calling `getItemOffersBatch`, supply the path of `getItemOffers`.  **Example:** `/products/pricing/v0/items/B000P6Q7MY/offers`  If you are calling `getListingOffersBatch`, supply the path of `getListingOffers`.  **Example:** `/products/pricing/v0/listings/B000P6Q7MY/offers`  # noqa: E501

        :param uri: The uri of this ItemOffersRequest.  # noqa: E501
        :type: str
        """
        if uri is None:
            raise ValueError("Invalid value for `uri`, must not be `None`")  # noqa: E501

        self._uri = uri

    @property
    def method(self):
        """Gets the method of this ItemOffersRequest.  # noqa: E501


        :return: The method of this ItemOffersRequest.  # noqa: E501
        :rtype: HttpMethod
        """
        return self._method

    @method.setter
    def method(self, method):
        """Sets the method of this ItemOffersRequest.


        :param method: The method of this ItemOffersRequest.  # noqa: E501
        :type: HttpMethod
        """
        if method is None:
            raise ValueError("Invalid value for `method`, must not be `None`")  # noqa: E501

        self._method = method

    @property
    def headers(self):
        """Gets the headers of this ItemOffersRequest.  # noqa: E501


        :return: The headers of this ItemOffersRequest.  # noqa: E501
        :rtype: HttpRequestHeaders
        """
        return self._headers

    @headers.setter
    def headers(self, headers):
        """Sets the headers of this ItemOffersRequest.


        :param headers: The headers of this ItemOffersRequest.  # noqa: E501
        :type: HttpRequestHeaders
        """

        self._headers = headers

    @property
    def marketplace_id(self):
        """Gets the marketplace_id of this ItemOffersRequest.  # noqa: E501


        :return: The marketplace_id of this ItemOffersRequest.  # noqa: E501
        :rtype: MarketplaceId
        """
        return self._marketplace_id

    @marketplace_id.setter
    def marketplace_id(self, marketplace_id):
        """Sets the marketplace_id of this ItemOffersRequest.


        :param marketplace_id: The marketplace_id of this ItemOffersRequest.  # noqa: E501
        :type: MarketplaceId
        """
        if marketplace_id is None:
            raise ValueError("Invalid value for `marketplace_id`, must not be `None`")  # noqa: E501

        self._marketplace_id = marketplace_id

    @property
    def item_condition(self):
        """Gets the item_condition of this ItemOffersRequest.  # noqa: E501

        Filters the offer listings to be considered based on item condition. Possible values: New, Used, Collectible, Refurbished, Club.  # noqa: E501

        :return: The item_condition of this ItemOffersRequest.  # noqa: E501
        :rtype: ItemCondition
        """
        return self._item_condition

    @item_condition.setter
    def item_condition(self, item_condition):
        """Sets the item_condition of this ItemOffersRequest.

        Filters the offer listings to be considered based on item condition. Possible values: New, Used, Collectible, Refurbished, Club.  # noqa: E501

        :param item_condition: The item_condition of this ItemOffersRequest.  # noqa: E501
        :type: ItemCondition
        """
        if item_condition is None:
            raise ValueError("Invalid value for `item_condition`, must not be `None`")  # noqa: E501

        self._item_condition = item_condition

    @property
    def customer_type(self):
        """Gets the customer_type of this ItemOffersRequest.  # noqa: E501

        Indicates whether to request Consumer or Business offers. Default is Consumer.  # noqa: E501

        :return: The customer_type of this ItemOffersRequest.  # noqa: E501
        :rtype: CustomerType
        """
        return self._customer_type

    @customer_type.setter
    def customer_type(self, customer_type):
        """Sets the customer_type of this ItemOffersRequest.

        Indicates whether to request Consumer or Business offers. Default is Consumer.  # noqa: E501

        :param customer_type: The customer_type of this ItemOffersRequest.  # noqa: E501
        :type: CustomerType
        """

        self._customer_type = customer_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemOffersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemOffersRequest):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
