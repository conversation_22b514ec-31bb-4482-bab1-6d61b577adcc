# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class BuyBoxPriceType(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'condition': 'str',
        'offer_type': 'OfferCustomerType',
        'quantity_tier': 'int',
        'quantity_discount_type': 'QuantityDiscountType',
        'landed_price': 'MoneyType',
        'listing_price': 'MoneyType',
        'shipping': 'MoneyType',
        'points': 'Points',
        'seller_id': 'str'
    }

    attribute_map = {
        'condition': 'condition',
        'offer_type': 'offerType',
        'quantity_tier': 'quantityTier',
        'quantity_discount_type': 'quantityDiscountType',
        'landed_price': 'LandedPrice',
        'listing_price': 'ListingPrice',
        'shipping': 'Shipping',
        'points': 'Points',
        'seller_id': 'sellerId'
    }

    def __init__(self, condition=None, offer_type=None, quantity_tier=None, quantity_discount_type=None, landed_price=None, listing_price=None, shipping=None, points=None, seller_id=None):  # noqa: E501
        """BuyBoxPriceType - a model defined in Swagger"""  # noqa: E501

        self._condition = None
        self._offer_type = None
        self._quantity_tier = None
        self._quantity_discount_type = None
        self._landed_price = None
        self._listing_price = None
        self._shipping = None
        self._points = None
        self._seller_id = None
        self.discriminator = None

        self.condition = condition
        if offer_type is not None:
            self.offer_type = offer_type
        if quantity_tier is not None:
            self.quantity_tier = quantity_tier
        if quantity_discount_type is not None:
            self.quantity_discount_type = quantity_discount_type
        self.landed_price = landed_price
        self.listing_price = listing_price
        self.shipping = shipping
        if points is not None:
            self.points = points
        if seller_id is not None:
            self.seller_id = seller_id

    @property
    def condition(self):
        """Gets the condition of this BuyBoxPriceType.  # noqa: E501

        Indicates the condition of the item. For example: New, Used, Collectible, Refurbished, or Club.  # noqa: E501

        :return: The condition of this BuyBoxPriceType.  # noqa: E501
        :rtype: str
        """
        return self._condition

    @condition.setter
    def condition(self, condition):
        """Sets the condition of this BuyBoxPriceType.

        Indicates the condition of the item. For example: New, Used, Collectible, Refurbished, or Club.  # noqa: E501

        :param condition: The condition of this BuyBoxPriceType.  # noqa: E501
        :type: str
        """
        if condition is None:
            raise ValueError("Invalid value for `condition`, must not be `None`")  # noqa: E501

        self._condition = condition

    @property
    def offer_type(self):
        """Gets the offer_type of this BuyBoxPriceType.  # noqa: E501

        Indicates the type of customer that the offer is valid for.<br><br>When the offer type is B2C in a quantity discount, the seller is winning the Buy Box because others do not have inventory at that quantity, not because they have a quantity discount on the ASIN.  # noqa: E501

        :return: The offer_type of this BuyBoxPriceType.  # noqa: E501
        :rtype: OfferCustomerType
        """
        return self._offer_type

    @offer_type.setter
    def offer_type(self, offer_type):
        """Sets the offer_type of this BuyBoxPriceType.

        Indicates the type of customer that the offer is valid for.<br><br>When the offer type is B2C in a quantity discount, the seller is winning the Buy Box because others do not have inventory at that quantity, not because they have a quantity discount on the ASIN.  # noqa: E501

        :param offer_type: The offer_type of this BuyBoxPriceType.  # noqa: E501
        :type: OfferCustomerType
        """

        self._offer_type = offer_type

    @property
    def quantity_tier(self):
        """Gets the quantity_tier of this BuyBoxPriceType.  # noqa: E501

        Indicates at what quantity this price becomes active.  # noqa: E501

        :return: The quantity_tier of this BuyBoxPriceType.  # noqa: E501
        :rtype: int
        """
        return self._quantity_tier

    @quantity_tier.setter
    def quantity_tier(self, quantity_tier):
        """Sets the quantity_tier of this BuyBoxPriceType.

        Indicates at what quantity this price becomes active.  # noqa: E501

        :param quantity_tier: The quantity_tier of this BuyBoxPriceType.  # noqa: E501
        :type: int
        """

        self._quantity_tier = quantity_tier

    @property
    def quantity_discount_type(self):
        """Gets the quantity_discount_type of this BuyBoxPriceType.  # noqa: E501

        Indicates the type of quantity discount this price applies to.  # noqa: E501

        :return: The quantity_discount_type of this BuyBoxPriceType.  # noqa: E501
        :rtype: QuantityDiscountType
        """
        return self._quantity_discount_type

    @quantity_discount_type.setter
    def quantity_discount_type(self, quantity_discount_type):
        """Sets the quantity_discount_type of this BuyBoxPriceType.

        Indicates the type of quantity discount this price applies to.  # noqa: E501

        :param quantity_discount_type: The quantity_discount_type of this BuyBoxPriceType.  # noqa: E501
        :type: QuantityDiscountType
        """

        self._quantity_discount_type = quantity_discount_type

    @property
    def landed_price(self):
        """Gets the landed_price of this BuyBoxPriceType.  # noqa: E501

        The value calculated by adding ListingPrice + Shipping - Points.  # noqa: E501

        :return: The landed_price of this BuyBoxPriceType.  # noqa: E501
        :rtype: MoneyType
        """
        return self._landed_price

    @landed_price.setter
    def landed_price(self, landed_price):
        """Sets the landed_price of this BuyBoxPriceType.

        The value calculated by adding ListingPrice + Shipping - Points.  # noqa: E501

        :param landed_price: The landed_price of this BuyBoxPriceType.  # noqa: E501
        :type: MoneyType
        """
        if landed_price is None:
            raise ValueError("Invalid value for `landed_price`, must not be `None`")  # noqa: E501

        self._landed_price = landed_price

    @property
    def listing_price(self):
        """Gets the listing_price of this BuyBoxPriceType.  # noqa: E501

        The price of the item.  # noqa: E501

        :return: The listing_price of this BuyBoxPriceType.  # noqa: E501
        :rtype: MoneyType
        """
        return self._listing_price

    @listing_price.setter
    def listing_price(self, listing_price):
        """Sets the listing_price of this BuyBoxPriceType.

        The price of the item.  # noqa: E501

        :param listing_price: The listing_price of this BuyBoxPriceType.  # noqa: E501
        :type: MoneyType
        """
        if listing_price is None:
            raise ValueError("Invalid value for `listing_price`, must not be `None`")  # noqa: E501

        self._listing_price = listing_price

    @property
    def shipping(self):
        """Gets the shipping of this BuyBoxPriceType.  # noqa: E501

        The shipping cost.  # noqa: E501

        :return: The shipping of this BuyBoxPriceType.  # noqa: E501
        :rtype: MoneyType
        """
        return self._shipping

    @shipping.setter
    def shipping(self, shipping):
        """Sets the shipping of this BuyBoxPriceType.

        The shipping cost.  # noqa: E501

        :param shipping: The shipping of this BuyBoxPriceType.  # noqa: E501
        :type: MoneyType
        """
        if shipping is None:
            raise ValueError("Invalid value for `shipping`, must not be `None`")  # noqa: E501

        self._shipping = shipping

    @property
    def points(self):
        """Gets the points of this BuyBoxPriceType.  # noqa: E501

        The number of Amazon Points offered with the purchase of an item.  # noqa: E501

        :return: The points of this BuyBoxPriceType.  # noqa: E501
        :rtype: Points
        """
        return self._points

    @points.setter
    def points(self, points):
        """Sets the points of this BuyBoxPriceType.

        The number of Amazon Points offered with the purchase of an item.  # noqa: E501

        :param points: The points of this BuyBoxPriceType.  # noqa: E501
        :type: Points
        """

        self._points = points

    @property
    def seller_id(self):
        """Gets the seller_id of this BuyBoxPriceType.  # noqa: E501

        The seller identifier for the offer.  # noqa: E501

        :return: The seller_id of this BuyBoxPriceType.  # noqa: E501
        :rtype: str
        """
        return self._seller_id

    @seller_id.setter
    def seller_id(self, seller_id):
        """Sets the seller_id of this BuyBoxPriceType.

        The seller identifier for the offer.  # noqa: E501

        :param seller_id: The seller_id of this BuyBoxPriceType.  # noqa: E501
        :type: str
        """

        self._seller_id = seller_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BuyBoxPriceType, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BuyBoxPriceType):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
