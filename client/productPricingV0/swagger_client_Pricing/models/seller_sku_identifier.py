# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class SellerSKUIdentifier(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'marketplace_id': 'str',
        'seller_id': 'str',
        'seller_sku': 'str'
    }

    attribute_map = {
        'marketplace_id': 'MarketplaceId',
        'seller_id': 'SellerId',
        'seller_sku': 'SellerSKU'
    }

    def __init__(self, marketplace_id=None, seller_id=None, seller_sku=None):  # noqa: E501
        """SellerSKUIdentifier - a model defined in Swagger"""  # noqa: E501

        self._marketplace_id = None
        self._seller_id = None
        self._seller_sku = None
        self.discriminator = None

        self.marketplace_id = marketplace_id
        self.seller_id = seller_id
        self.seller_sku = seller_sku

    @property
    def marketplace_id(self):
        """Gets the marketplace_id of this SellerSKUIdentifier.  # noqa: E501

        A marketplace identifier.  # noqa: E501

        :return: The marketplace_id of this SellerSKUIdentifier.  # noqa: E501
        :rtype: str
        """
        return self._marketplace_id

    @marketplace_id.setter
    def marketplace_id(self, marketplace_id):
        """Sets the marketplace_id of this SellerSKUIdentifier.

        A marketplace identifier.  # noqa: E501

        :param marketplace_id: The marketplace_id of this SellerSKUIdentifier.  # noqa: E501
        :type: str
        """
        if marketplace_id is None:
            raise ValueError("Invalid value for `marketplace_id`, must not be `None`")  # noqa: E501

        self._marketplace_id = marketplace_id

    @property
    def seller_id(self):
        """Gets the seller_id of this SellerSKUIdentifier.  # noqa: E501

        The seller identifier submitted for the operation.  # noqa: E501

        :return: The seller_id of this SellerSKUIdentifier.  # noqa: E501
        :rtype: str
        """
        return self._seller_id

    @seller_id.setter
    def seller_id(self, seller_id):
        """Sets the seller_id of this SellerSKUIdentifier.

        The seller identifier submitted for the operation.  # noqa: E501

        :param seller_id: The seller_id of this SellerSKUIdentifier.  # noqa: E501
        :type: str
        """
        if seller_id is None:
            raise ValueError("Invalid value for `seller_id`, must not be `None`")  # noqa: E501

        self._seller_id = seller_id

    @property
    def seller_sku(self):
        """Gets the seller_sku of this SellerSKUIdentifier.  # noqa: E501

        The seller stock keeping unit (SKU) of the item.  # noqa: E501

        :return: The seller_sku of this SellerSKUIdentifier.  # noqa: E501
        :rtype: str
        """
        return self._seller_sku

    @seller_sku.setter
    def seller_sku(self, seller_sku):
        """Sets the seller_sku of this SellerSKUIdentifier.

        The seller stock keeping unit (SKU) of the item.  # noqa: E501

        :param seller_sku: The seller_sku of this SellerSKUIdentifier.  # noqa: E501
        :type: str
        """
        if seller_sku is None:
            raise ValueError("Invalid value for `seller_sku`, must not be `None`")  # noqa: E501

        self._seller_sku = seller_sku

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SellerSKUIdentifier, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SellerSKUIdentifier):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
