# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class PrimeInformationType(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_prime': 'bool',
        'is_national_prime': 'bool'
    }

    attribute_map = {
        'is_prime': 'IsPrime',
        'is_national_prime': 'IsNationalPrime'
    }

    def __init__(self, is_prime=None, is_national_prime=None):  # noqa: E501
        """PrimeInformationType - a model defined in Swagger"""  # noqa: E501

        self._is_prime = None
        self._is_national_prime = None
        self.discriminator = None

        self.is_prime = is_prime
        self.is_national_prime = is_national_prime

    @property
    def is_prime(self):
        """Gets the is_prime of this PrimeInformationType.  # noqa: E501

        Indicates whether the offer is an Amazon Prime offer.  # noqa: E501

        :return: The is_prime of this PrimeInformationType.  # noqa: E501
        :rtype: bool
        """
        return self._is_prime

    @is_prime.setter
    def is_prime(self, is_prime):
        """Sets the is_prime of this PrimeInformationType.

        Indicates whether the offer is an Amazon Prime offer.  # noqa: E501

        :param is_prime: The is_prime of this PrimeInformationType.  # noqa: E501
        :type: bool
        """
        if is_prime is None:
            raise ValueError("Invalid value for `is_prime`, must not be `None`")  # noqa: E501

        self._is_prime = is_prime

    @property
    def is_national_prime(self):
        """Gets the is_national_prime of this PrimeInformationType.  # noqa: E501

        Indicates whether the offer is an Amazon Prime offer throughout the entire marketplace where it is listed.  # noqa: E501

        :return: The is_national_prime of this PrimeInformationType.  # noqa: E501
        :rtype: bool
        """
        return self._is_national_prime

    @is_national_prime.setter
    def is_national_prime(self, is_national_prime):
        """Sets the is_national_prime of this PrimeInformationType.

        Indicates whether the offer is an Amazon Prime offer throughout the entire marketplace where it is listed.  # noqa: E501

        :param is_national_prime: The is_national_prime of this PrimeInformationType.  # noqa: E501
        :type: bool
        """
        if is_national_prime is None:
            raise ValueError("Invalid value for `is_national_prime`, must not be `None`")  # noqa: E501

        self._is_national_prime = is_national_prime

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PrimeInformationType, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PrimeInformationType):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
