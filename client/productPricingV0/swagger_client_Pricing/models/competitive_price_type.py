# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class CompetitivePriceType(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'competitive_price_id': 'str',
        'price': 'PriceType',
        'condition': 'str',
        'subcondition': 'str',
        'offer_type': 'OfferCustomerType',
        'quantity_tier': 'int',
        'quantity_discount_type': 'QuantityDiscountType',
        'seller_id': 'str',
        'belongs_to_requester': 'bool'
    }

    attribute_map = {
        'competitive_price_id': 'CompetitivePriceId',
        'price': 'Price',
        'condition': 'condition',
        'subcondition': 'subcondition',
        'offer_type': 'offerType',
        'quantity_tier': 'quantityTier',
        'quantity_discount_type': 'quantityDiscountType',
        'seller_id': 'sellerId',
        'belongs_to_requester': 'belongsToRequester'
    }

    def __init__(self, competitive_price_id=None, price=None, condition=None, subcondition=None, offer_type=None, quantity_tier=None, quantity_discount_type=None, seller_id=None, belongs_to_requester=None):  # noqa: E501
        """CompetitivePriceType - a model defined in Swagger"""  # noqa: E501

        self._competitive_price_id = None
        self._price = None
        self._condition = None
        self._subcondition = None
        self._offer_type = None
        self._quantity_tier = None
        self._quantity_discount_type = None
        self._seller_id = None
        self._belongs_to_requester = None
        self.discriminator = None

        self.competitive_price_id = competitive_price_id
        self.price = price
        if condition is not None:
            self.condition = condition
        if subcondition is not None:
            self.subcondition = subcondition
        if offer_type is not None:
            self.offer_type = offer_type
        if quantity_tier is not None:
            self.quantity_tier = quantity_tier
        if quantity_discount_type is not None:
            self.quantity_discount_type = quantity_discount_type
        if seller_id is not None:
            self.seller_id = seller_id
        if belongs_to_requester is not None:
            self.belongs_to_requester = belongs_to_requester

    @property
    def competitive_price_id(self):
        """Gets the competitive_price_id of this CompetitivePriceType.  # noqa: E501

        The pricing model for each price that is returned.  Possible values:  * 1 - New Buy Box Price. * 2 - Used Buy Box Price.  # noqa: E501

        :return: The competitive_price_id of this CompetitivePriceType.  # noqa: E501
        :rtype: str
        """
        return self._competitive_price_id

    @competitive_price_id.setter
    def competitive_price_id(self, competitive_price_id):
        """Sets the competitive_price_id of this CompetitivePriceType.

        The pricing model for each price that is returned.  Possible values:  * 1 - New Buy Box Price. * 2 - Used Buy Box Price.  # noqa: E501

        :param competitive_price_id: The competitive_price_id of this CompetitivePriceType.  # noqa: E501
        :type: str
        """
        if competitive_price_id is None:
            raise ValueError("Invalid value for `competitive_price_id`, must not be `None`")  # noqa: E501

        self._competitive_price_id = competitive_price_id

    @property
    def price(self):
        """Gets the price of this CompetitivePriceType.  # noqa: E501

        Pricing information for a given CompetitivePriceId value.  # noqa: E501

        :return: The price of this CompetitivePriceType.  # noqa: E501
        :rtype: PriceType
        """
        return self._price

    @price.setter
    def price(self, price):
        """Sets the price of this CompetitivePriceType.

        Pricing information for a given CompetitivePriceId value.  # noqa: E501

        :param price: The price of this CompetitivePriceType.  # noqa: E501
        :type: PriceType
        """
        if price is None:
            raise ValueError("Invalid value for `price`, must not be `None`")  # noqa: E501

        self._price = price

    @property
    def condition(self):
        """Gets the condition of this CompetitivePriceType.  # noqa: E501

        Indicates the condition of the item whose pricing information is returned. Possible values are: New, Used, Collectible, Refurbished, or Club.  # noqa: E501

        :return: The condition of this CompetitivePriceType.  # noqa: E501
        :rtype: str
        """
        return self._condition

    @condition.setter
    def condition(self, condition):
        """Sets the condition of this CompetitivePriceType.

        Indicates the condition of the item whose pricing information is returned. Possible values are: New, Used, Collectible, Refurbished, or Club.  # noqa: E501

        :param condition: The condition of this CompetitivePriceType.  # noqa: E501
        :type: str
        """

        self._condition = condition

    @property
    def subcondition(self):
        """Gets the subcondition of this CompetitivePriceType.  # noqa: E501

        Indicates the subcondition of the item whose pricing information is returned. Possible values are: New, Mint, Very Good, Good, Acceptable, Poor, Club, OEM, Warranty, Refurbished Warranty, Refurbished, Open Box, or Other.  # noqa: E501

        :return: The subcondition of this CompetitivePriceType.  # noqa: E501
        :rtype: str
        """
        return self._subcondition

    @subcondition.setter
    def subcondition(self, subcondition):
        """Sets the subcondition of this CompetitivePriceType.

        Indicates the subcondition of the item whose pricing information is returned. Possible values are: New, Mint, Very Good, Good, Acceptable, Poor, Club, OEM, Warranty, Refurbished Warranty, Refurbished, Open Box, or Other.  # noqa: E501

        :param subcondition: The subcondition of this CompetitivePriceType.  # noqa: E501
        :type: str
        """

        self._subcondition = subcondition

    @property
    def offer_type(self):
        """Gets the offer_type of this CompetitivePriceType.  # noqa: E501

        Indicates the type of customer that the offer is valid for.<br><br>When the offer type is B2C in a quantity discount, the seller is winning the Buy Box because others do not have inventory at that quantity, not because they have a quantity discount on the ASIN.  # noqa: E501

        :return: The offer_type of this CompetitivePriceType.  # noqa: E501
        :rtype: OfferCustomerType
        """
        return self._offer_type

    @offer_type.setter
    def offer_type(self, offer_type):
        """Sets the offer_type of this CompetitivePriceType.

        Indicates the type of customer that the offer is valid for.<br><br>When the offer type is B2C in a quantity discount, the seller is winning the Buy Box because others do not have inventory at that quantity, not because they have a quantity discount on the ASIN.  # noqa: E501

        :param offer_type: The offer_type of this CompetitivePriceType.  # noqa: E501
        :type: OfferCustomerType
        """

        self._offer_type = offer_type

    @property
    def quantity_tier(self):
        """Gets the quantity_tier of this CompetitivePriceType.  # noqa: E501

        Indicates at what quantity this price becomes active.  # noqa: E501

        :return: The quantity_tier of this CompetitivePriceType.  # noqa: E501
        :rtype: int
        """
        return self._quantity_tier

    @quantity_tier.setter
    def quantity_tier(self, quantity_tier):
        """Sets the quantity_tier of this CompetitivePriceType.

        Indicates at what quantity this price becomes active.  # noqa: E501

        :param quantity_tier: The quantity_tier of this CompetitivePriceType.  # noqa: E501
        :type: int
        """

        self._quantity_tier = quantity_tier

    @property
    def quantity_discount_type(self):
        """Gets the quantity_discount_type of this CompetitivePriceType.  # noqa: E501

        Indicates the type of quantity discount this price applies to.  # noqa: E501

        :return: The quantity_discount_type of this CompetitivePriceType.  # noqa: E501
        :rtype: QuantityDiscountType
        """
        return self._quantity_discount_type

    @quantity_discount_type.setter
    def quantity_discount_type(self, quantity_discount_type):
        """Sets the quantity_discount_type of this CompetitivePriceType.

        Indicates the type of quantity discount this price applies to.  # noqa: E501

        :param quantity_discount_type: The quantity_discount_type of this CompetitivePriceType.  # noqa: E501
        :type: QuantityDiscountType
        """

        self._quantity_discount_type = quantity_discount_type

    @property
    def seller_id(self):
        """Gets the seller_id of this CompetitivePriceType.  # noqa: E501

        The seller identifier for the offer.  # noqa: E501

        :return: The seller_id of this CompetitivePriceType.  # noqa: E501
        :rtype: str
        """
        return self._seller_id

    @seller_id.setter
    def seller_id(self, seller_id):
        """Sets the seller_id of this CompetitivePriceType.

        The seller identifier for the offer.  # noqa: E501

        :param seller_id: The seller_id of this CompetitivePriceType.  # noqa: E501
        :type: str
        """

        self._seller_id = seller_id

    @property
    def belongs_to_requester(self):
        """Gets the belongs_to_requester of this CompetitivePriceType.  # noqa: E501

         Indicates whether or not the pricing information is for an offer listing that belongs to the requester. The requester is the seller associated with the SellerId that was submitted with the request. Possible values are: true and false.  # noqa: E501

        :return: The belongs_to_requester of this CompetitivePriceType.  # noqa: E501
        :rtype: bool
        """
        return self._belongs_to_requester

    @belongs_to_requester.setter
    def belongs_to_requester(self, belongs_to_requester):
        """Sets the belongs_to_requester of this CompetitivePriceType.

         Indicates whether or not the pricing information is for an offer listing that belongs to the requester. The requester is the seller associated with the SellerId that was submitted with the request. Possible values are: true and false.  # noqa: E501

        :param belongs_to_requester: The belongs_to_requester of this CompetitivePriceType.  # noqa: E501
        :type: bool
        """

        self._belongs_to_requester = belongs_to_requester

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CompetitivePriceType, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CompetitivePriceType):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
