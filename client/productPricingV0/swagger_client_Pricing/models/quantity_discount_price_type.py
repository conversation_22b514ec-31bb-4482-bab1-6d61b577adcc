# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class QuantityDiscountPriceType(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'quantity_tier': 'int',
        'quantity_discount_type': 'QuantityDiscountType',
        'listing_price': 'MoneyType'
    }

    attribute_map = {
        'quantity_tier': 'quantityTier',
        'quantity_discount_type': 'quantityDiscountType',
        'listing_price': 'listingPrice'
    }

    def __init__(self, quantity_tier=None, quantity_discount_type=None, listing_price=None):  # noqa: E501
        """QuantityDiscountPriceType - a model defined in Swagger"""  # noqa: E501

        self._quantity_tier = None
        self._quantity_discount_type = None
        self._listing_price = None
        self.discriminator = None

        self.quantity_tier = quantity_tier
        self.quantity_discount_type = quantity_discount_type
        self.listing_price = listing_price

    @property
    def quantity_tier(self):
        """Gets the quantity_tier of this QuantityDiscountPriceType.  # noqa: E501

        Indicates at what quantity this price becomes active.  # noqa: E501

        :return: The quantity_tier of this QuantityDiscountPriceType.  # noqa: E501
        :rtype: int
        """
        return self._quantity_tier

    @quantity_tier.setter
    def quantity_tier(self, quantity_tier):
        """Sets the quantity_tier of this QuantityDiscountPriceType.

        Indicates at what quantity this price becomes active.  # noqa: E501

        :param quantity_tier: The quantity_tier of this QuantityDiscountPriceType.  # noqa: E501
        :type: int
        """
        if quantity_tier is None:
            raise ValueError("Invalid value for `quantity_tier`, must not be `None`")  # noqa: E501

        self._quantity_tier = quantity_tier

    @property
    def quantity_discount_type(self):
        """Gets the quantity_discount_type of this QuantityDiscountPriceType.  # noqa: E501

        Indicates the type of quantity discount this price applies to.  # noqa: E501

        :return: The quantity_discount_type of this QuantityDiscountPriceType.  # noqa: E501
        :rtype: QuantityDiscountType
        """
        return self._quantity_discount_type

    @quantity_discount_type.setter
    def quantity_discount_type(self, quantity_discount_type):
        """Sets the quantity_discount_type of this QuantityDiscountPriceType.

        Indicates the type of quantity discount this price applies to.  # noqa: E501

        :param quantity_discount_type: The quantity_discount_type of this QuantityDiscountPriceType.  # noqa: E501
        :type: QuantityDiscountType
        """
        if quantity_discount_type is None:
            raise ValueError("Invalid value for `quantity_discount_type`, must not be `None`")  # noqa: E501

        self._quantity_discount_type = quantity_discount_type

    @property
    def listing_price(self):
        """Gets the listing_price of this QuantityDiscountPriceType.  # noqa: E501

        The price at this quantity tier.  # noqa: E501

        :return: The listing_price of this QuantityDiscountPriceType.  # noqa: E501
        :rtype: MoneyType
        """
        return self._listing_price

    @listing_price.setter
    def listing_price(self, listing_price):
        """Sets the listing_price of this QuantityDiscountPriceType.

        The price at this quantity tier.  # noqa: E501

        :param listing_price: The listing_price of this QuantityDiscountPriceType.  # noqa: E501
        :type: MoneyType
        """
        if listing_price is None:
            raise ValueError("Invalid value for `listing_price`, must not be `None`")  # noqa: E501

        self._listing_price = listing_price

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuantityDiscountPriceType, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuantityDiscountPriceType):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
