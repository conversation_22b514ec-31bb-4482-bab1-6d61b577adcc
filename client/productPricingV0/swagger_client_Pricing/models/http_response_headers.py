# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class HttpResponseHeaders(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        '_date': 'str',
        'x_amzn_request_id': 'str'
    }

    attribute_map = {
        '_date': 'Date',
        'x_amzn_request_id': 'x-amzn-RequestId'
    }

    def __init__(self, _date=None, x_amzn_request_id=None):  # noqa: E501
        """HttpResponseHeaders - a model defined in Swagger"""  # noqa: E501

        self.__date = None
        self._x_amzn_request_id = None
        self.discriminator = None

        if _date is not None:
            self._date = _date
        if x_amzn_request_id is not None:
            self.x_amzn_request_id = x_amzn_request_id

    @property
    def _date(self):
        """Gets the _date of this HttpResponseHeaders.  # noqa: E501

        The timestamp that the API request was received.  For more information, consult [RFC 2616 Section 14](https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html).  # noqa: E501

        :return: The _date of this HttpResponseHeaders.  # noqa: E501
        :rtype: str
        """
        return self.__date

    @_date.setter
    def _date(self, _date):
        """Sets the _date of this HttpResponseHeaders.

        The timestamp that the API request was received.  For more information, consult [RFC 2616 Section 14](https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html).  # noqa: E501

        :param _date: The _date of this HttpResponseHeaders.  # noqa: E501
        :type: str
        """

        self.__date = _date

    @property
    def x_amzn_request_id(self):
        """Gets the x_amzn_request_id of this HttpResponseHeaders.  # noqa: E501

        Unique request reference ID.  # noqa: E501

        :return: The x_amzn_request_id of this HttpResponseHeaders.  # noqa: E501
        :rtype: str
        """
        return self._x_amzn_request_id

    @x_amzn_request_id.setter
    def x_amzn_request_id(self, x_amzn_request_id):
        """Sets the x_amzn_request_id of this HttpResponseHeaders.

        Unique request reference ID.  # noqa: E501

        :param x_amzn_request_id: The x_amzn_request_id of this HttpResponseHeaders.  # noqa: E501
        :type: str
        """

        self._x_amzn_request_id = x_amzn_request_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HttpResponseHeaders, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HttpResponseHeaders):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
