# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class GetListingOffersBatchResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'responses': 'ListingOffersResponseList'
    }

    attribute_map = {
        'responses': 'responses'
    }

    def __init__(self, responses=None):  # noqa: E501
        """GetListingOffersBatchResponse - a model defined in Swagger"""  # noqa: E501

        self._responses = None
        self.discriminator = None

        if responses is not None:
            self.responses = responses

    @property
    def responses(self):
        """Gets the responses of this GetListingOffersBatchResponse.  # noqa: E501


        :return: The responses of this GetListingOffersBatchResponse.  # noqa: E501
        :rtype: ListingOffersResponseList
        """
        return self._responses

    @responses.setter
    def responses(self, responses):
        """Sets the responses of this GetListingOffersBatchResponse.


        :param responses: The responses of this GetListingOffersBatchResponse.  # noqa: E501
        :type: ListingOffersResponseList
        """

        self._responses = responses

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetListingOffersBatchResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetListingOffersBatchResponse):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
