# BatchOffersRequestParams

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**marketplace_id** | [**MarketplaceId**](MarketplaceId.md) |  | 
**item_condition** | [**ItemCondition**](ItemCondition.md) | Filters the offer listings to be considered based on item condition. Possible values: New, Used, Collectible, Refurbished, Club. | 
**customer_type** | [**CustomerType**](CustomerType.md) | Indicates whether to request Consumer or Business offers. Default is Consumer. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


