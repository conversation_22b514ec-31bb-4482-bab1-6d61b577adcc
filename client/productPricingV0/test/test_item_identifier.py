# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_Pricing
from swagger_client_Pricing.models.item_identifier import ItemIdentifier  # noqa: E501
from swagger_client_Pricing.rest import ApiException


class TestItemIdentifier(unittest.TestCase):
    """ItemIdentifier unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testItemIdentifier(self):
        """Test ItemIdentifier"""
        # FIXME: construct object with mandatory attributes with example values
        # model = swagger_client_Pricing.models.item_identifier.ItemIdentifier()  # noqa: E501
        pass


if __name__ == '__main__':
    unittest.main()
