# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_Pricing
from swagger_client_Pricing.models.get_offers_http_status_line import GetOffersHttpStatusLine  # noqa: E501
from swagger_client_Pricing.rest import ApiException


class TestGetOffersHttpStatusLine(unittest.TestCase):
    """GetOffersHttpStatusLine unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testGetOffersHttpStatusLine(self):
        """Test GetOffersHttpStatusLine"""
        # FIXME: construct object with mandatory attributes with example values
        # model = swagger_client_Pricing.models.get_offers_http_status_line.GetOffersHttpStatusLine()  # noqa: E501
        pass


if __name__ == '__main__':
    unittest.main()
