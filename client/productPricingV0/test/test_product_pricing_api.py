# coding: utf-8

"""
    Selling Partner API for Pricing

    The Selling Partner API for Pricing helps you programmatically retrieve product pricing and offer information for Amazon Marketplace products.  # noqa: E501

    OpenAPI spec version: v0
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_Pricing
from swagger_client_Pricing.api.product_pricing_api import ProductPricingApi  # noqa: E501
from swagger_client_Pricing.rest import ApiException


class TestProductPricingApi(unittest.TestCase):
    """ProductPricingApi unit test stubs"""

    def setUp(self):
        self.api = swagger_client_Pricing.api.product_pricing_api.ProductPricingApi()  # noqa: E501

    def tearDown(self):
        pass

    def test_get_competitive_pricing(self):
        """Test case for get_competitive_pricing

        """
        pass

    def test_get_item_offers(self):
        """Test case for get_item_offers

        """
        pass

    def test_get_item_offers_batch(self):
        """Test case for get_item_offers_batch

        """
        pass

    def test_get_listing_offers(self):
        """Test case for get_listing_offers

        """
        pass

    def test_get_listing_offers_batch(self):
        """Test case for get_listing_offers_batch

        """
        pass

    def test_get_pricing(self):
        """Test case for get_pricing

        """
        pass


if __name__ == '__main__':
    unittest.main()