# InboundOperationStatus

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**operation** | **str** | The name of the operation in the asynchronous API call. | 
**operation_id** | **str** | The operation ID returned by the asynchronous API call. | 
**operation_problems** | [**list[OperationProblem]**](OperationProblem.md) | The problems in the processing of the asynchronous operation. | 
**operation_status** | [**OperationStatus**](OperationStatus.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


