# ContentUpdatePreview

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**content_update_preview_id** | **str** | Identifier of a content update preview. | 
**expiration** | **datetime** | The time at which the content update expires. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format with pattern &#x60;yyyy-MM-ddTHH:mm:ss.sssZ&#x60;. | 
**requested_updates** | [**RequestedUpdates**](RequestedUpdates.md) |  | 
**transportation_option** | [**TransportationOption**](TransportationOption.md) |  | 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


