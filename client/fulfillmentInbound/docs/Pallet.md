# Pallet

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**dimensions** | [**Dimensions**](Dimensions.md) |  | [optional] 
**package_id** | **str** | Primary key to uniquely identify a Package (Box or Pallet). | 
**quantity** | **int** | The number of containers where all other properties like weight or dimensions are identical. | [optional] 
**stackability** | [**Stackability**](Stackability.md) |  | [optional] 
**weight** | [**Weight**](Weight.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


