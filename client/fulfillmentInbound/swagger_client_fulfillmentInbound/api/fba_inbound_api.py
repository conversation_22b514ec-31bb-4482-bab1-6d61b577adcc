# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from swagger_client_fulfillmentInbound.api_client import ApiClient


class FbaInboundApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def cancel_inbound_plan(self, inbound_plan_id, **kwargs):  # noqa: E501
        """cancel_inbound_plan  # noqa: E501

        Cancels an Inbound Plan. Charges may apply if the cancellation is performed outside of a void window. The window for Amazon Partnered Carriers is 24 hours for Small Parcel Delivery (SPD) and one hour for Less-Than-Truckload (LTL) carrier shipments.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_inbound_plan(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :return: CancelInboundPlanResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.cancel_inbound_plan_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
        else:
            (data) = self.cancel_inbound_plan_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
            return data

    def cancel_inbound_plan_with_http_info(self, inbound_plan_id, **kwargs):  # noqa: E501
        """cancel_inbound_plan  # noqa: E501

        Cancels an Inbound Plan. Charges may apply if the cancellation is performed outside of a void window. The window for Amazon Partnered Carriers is 24 hours for Small Parcel Delivery (SPD) and one hour for Less-Than-Truckload (LTL) carrier shipments.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_inbound_plan_with_http_info(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :return: CancelInboundPlanResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_inbound_plan" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `cancel_inbound_plan`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `cancel_inbound_plan`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `cancel_inbound_plan`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `cancel_inbound_plan`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/cancellation', 'PUT',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CancelInboundPlanResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_self_ship_appointment(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """cancel_self_ship_appointment  # noqa: E501

        Cancels a self-ship appointment slot against a shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_self_ship_appointment(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param CancelSelfShipAppointmentRequest body: The body of the request to `cancelSelfShipAppointment`. (required)
        :return: CancelSelfShipAppointmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.cancel_self_ship_appointment_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.cancel_self_ship_appointment_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
            return data

    def cancel_self_ship_appointment_with_http_info(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """cancel_self_ship_appointment  # noqa: E501

        Cancels a self-ship appointment slot against a shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_self_ship_appointment_with_http_info(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param CancelSelfShipAppointmentRequest body: The body of the request to `cancelSelfShipAppointment`. (required)
        :return: CancelSelfShipAppointmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_self_ship_appointment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `cancel_self_ship_appointment`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `cancel_self_ship_appointment`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `cancel_self_ship_appointment`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `cancel_self_ship_appointment`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `cancel_self_ship_appointment`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `cancel_self_ship_appointment`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `cancel_self_ship_appointment`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `cancel_self_ship_appointment`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `cancel_self_ship_appointment`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentCancellation', 'PUT',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CancelSelfShipAppointmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def confirm_delivery_window_options(self, inbound_plan_id, shipment_id, delivery_window_option_id, **kwargs):  # noqa: E501
        """confirm_delivery_window_options  # noqa: E501

        Confirms the delivery window option for chosen shipment within an inbound plan. A placement option must be confirmed prior to use of this API. Once confirmed, new delivery window options cannot be generated, but the chosen delivery window option can be updated before shipment closure. The window is used to provide the expected time when a shipment will arrive at the warehouse. All transportation options which have the program `CONFIRMED_DELIVERY_WINDOW` require a delivery window to be confirmed prior to transportation option confirmation.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.confirm_delivery_window_options(inbound_plan_id, shipment_id, delivery_window_option_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: The shipment to confirm the delivery window option for. (required)
        :param str delivery_window_option_id: The id of the delivery window option to be confirmed. (required)
        :return: ConfirmDeliveryWindowOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.confirm_delivery_window_options_with_http_info(inbound_plan_id, shipment_id, delivery_window_option_id, **kwargs)  # noqa: E501
        else:
            (data) = self.confirm_delivery_window_options_with_http_info(inbound_plan_id, shipment_id, delivery_window_option_id, **kwargs)  # noqa: E501
            return data

    def confirm_delivery_window_options_with_http_info(self, inbound_plan_id, shipment_id, delivery_window_option_id, **kwargs):  # noqa: E501
        """confirm_delivery_window_options  # noqa: E501

        Confirms the delivery window option for chosen shipment within an inbound plan. A placement option must be confirmed prior to use of this API. Once confirmed, new delivery window options cannot be generated, but the chosen delivery window option can be updated before shipment closure. The window is used to provide the expected time when a shipment will arrive at the warehouse. All transportation options which have the program `CONFIRMED_DELIVERY_WINDOW` require a delivery window to be confirmed prior to transportation option confirmation.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.confirm_delivery_window_options_with_http_info(inbound_plan_id, shipment_id, delivery_window_option_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: The shipment to confirm the delivery window option for. (required)
        :param str delivery_window_option_id: The id of the delivery window option to be confirmed. (required)
        :return: ConfirmDeliveryWindowOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'delivery_window_option_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method confirm_delivery_window_options" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `confirm_delivery_window_options`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `confirm_delivery_window_options`")  # noqa: E501
        # verify the required parameter 'delivery_window_option_id' is set
        if self.api_client.client_side_validation and ('delivery_window_option_id' not in params or
                                                       params['delivery_window_option_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `delivery_window_option_id` when calling `confirm_delivery_window_options`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_delivery_window_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_delivery_window_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_delivery_window_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `confirm_delivery_window_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `confirm_delivery_window_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `confirm_delivery_window_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('delivery_window_option_id' in params and
                                                       len(params['delivery_window_option_id']) > 38):
            raise ValueError("Invalid value for parameter `delivery_window_option_id` when calling `confirm_delivery_window_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('delivery_window_option_id' in params and
                                                       len(params['delivery_window_option_id']) < 36):
            raise ValueError("Invalid value for parameter `delivery_window_option_id` when calling `confirm_delivery_window_options`, length must be greater than or equal to `36`")  # noqa: E501
        if self.api_client.client_side_validation and ('delivery_window_option_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['delivery_window_option_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `delivery_window_option_id` when calling `confirm_delivery_window_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501
        if 'delivery_window_option_id' in params:
            path_params['deliveryWindowOptionId'] = params['delivery_window_option_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/deliveryWindowOptions/{deliveryWindowOptionId}/confirmation', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ConfirmDeliveryWindowOptionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def confirm_packing_option(self, inbound_plan_id, packing_option_id, **kwargs):  # noqa: E501
        """confirm_packing_option  # noqa: E501

        Confirms the packing option for an inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.confirm_packing_option(inbound_plan_id, packing_option_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str packing_option_id: Identifier of a packing option. (required)
        :return: ConfirmPackingOptionResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.confirm_packing_option_with_http_info(inbound_plan_id, packing_option_id, **kwargs)  # noqa: E501
        else:
            (data) = self.confirm_packing_option_with_http_info(inbound_plan_id, packing_option_id, **kwargs)  # noqa: E501
            return data

    def confirm_packing_option_with_http_info(self, inbound_plan_id, packing_option_id, **kwargs):  # noqa: E501
        """confirm_packing_option  # noqa: E501

        Confirms the packing option for an inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.confirm_packing_option_with_http_info(inbound_plan_id, packing_option_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str packing_option_id: Identifier of a packing option. (required)
        :return: ConfirmPackingOptionResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'packing_option_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method confirm_packing_option" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `confirm_packing_option`")  # noqa: E501
        # verify the required parameter 'packing_option_id' is set
        if self.api_client.client_side_validation and ('packing_option_id' not in params or
                                                       params['packing_option_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `packing_option_id` when calling `confirm_packing_option`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_packing_option`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_packing_option`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_packing_option`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('packing_option_id' in params and
                                                       len(params['packing_option_id']) > 38):
            raise ValueError("Invalid value for parameter `packing_option_id` when calling `confirm_packing_option`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('packing_option_id' in params and
                                                       len(params['packing_option_id']) < 38):
            raise ValueError("Invalid value for parameter `packing_option_id` when calling `confirm_packing_option`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('packing_option_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['packing_option_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `packing_option_id` when calling `confirm_packing_option`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'packing_option_id' in params:
            path_params['packingOptionId'] = params['packing_option_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions/{packingOptionId}/confirmation', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ConfirmPackingOptionResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def confirm_placement_option(self, inbound_plan_id, placement_option_id, **kwargs):  # noqa: E501
        """confirm_placement_option  # noqa: E501

        Confirms the placement option for an inbound plan. Once confirmed, it cannot be changed for the Inbound Plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.confirm_placement_option(inbound_plan_id, placement_option_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str placement_option_id: The identifier of a placement option. A placement option represents the shipment splits and destinations of SKUs. (required)
        :return: ConfirmPlacementOptionResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.confirm_placement_option_with_http_info(inbound_plan_id, placement_option_id, **kwargs)  # noqa: E501
        else:
            (data) = self.confirm_placement_option_with_http_info(inbound_plan_id, placement_option_id, **kwargs)  # noqa: E501
            return data

    def confirm_placement_option_with_http_info(self, inbound_plan_id, placement_option_id, **kwargs):  # noqa: E501
        """confirm_placement_option  # noqa: E501

        Confirms the placement option for an inbound plan. Once confirmed, it cannot be changed for the Inbound Plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.confirm_placement_option_with_http_info(inbound_plan_id, placement_option_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str placement_option_id: The identifier of a placement option. A placement option represents the shipment splits and destinations of SKUs. (required)
        :return: ConfirmPlacementOptionResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'placement_option_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method confirm_placement_option" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `confirm_placement_option`")  # noqa: E501
        # verify the required parameter 'placement_option_id' is set
        if self.api_client.client_side_validation and ('placement_option_id' not in params or
                                                       params['placement_option_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `placement_option_id` when calling `confirm_placement_option`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_placement_option`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_placement_option`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_placement_option`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('placement_option_id' in params and
                                                       len(params['placement_option_id']) > 38):
            raise ValueError("Invalid value for parameter `placement_option_id` when calling `confirm_placement_option`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('placement_option_id' in params and
                                                       len(params['placement_option_id']) < 38):
            raise ValueError("Invalid value for parameter `placement_option_id` when calling `confirm_placement_option`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('placement_option_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['placement_option_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `placement_option_id` when calling `confirm_placement_option`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'placement_option_id' in params:
            path_params['placementOptionId'] = params['placement_option_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions/{placementOptionId}/confirmation', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ConfirmPlacementOptionResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def confirm_shipment_content_update_preview(self, inbound_plan_id, shipment_id, content_update_preview_id, **kwargs):  # noqa: E501
        """confirm_shipment_content_update_preview  # noqa: E501

        Confirm a shipment content update preview and accept the changes in transportation cost.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.confirm_shipment_content_update_preview(inbound_plan_id, shipment_id, content_update_preview_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param str content_update_preview_id: Identifier of a content update preview. (required)
        :return: ConfirmShipmentContentUpdatePreviewResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.confirm_shipment_content_update_preview_with_http_info(inbound_plan_id, shipment_id, content_update_preview_id, **kwargs)  # noqa: E501
        else:
            (data) = self.confirm_shipment_content_update_preview_with_http_info(inbound_plan_id, shipment_id, content_update_preview_id, **kwargs)  # noqa: E501
            return data

    def confirm_shipment_content_update_preview_with_http_info(self, inbound_plan_id, shipment_id, content_update_preview_id, **kwargs):  # noqa: E501
        """confirm_shipment_content_update_preview  # noqa: E501

        Confirm a shipment content update preview and accept the changes in transportation cost.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.confirm_shipment_content_update_preview_with_http_info(inbound_plan_id, shipment_id, content_update_preview_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param str content_update_preview_id: Identifier of a content update preview. (required)
        :return: ConfirmShipmentContentUpdatePreviewResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'content_update_preview_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method confirm_shipment_content_update_preview" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `confirm_shipment_content_update_preview`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `confirm_shipment_content_update_preview`")  # noqa: E501
        # verify the required parameter 'content_update_preview_id' is set
        if self.api_client.client_side_validation and ('content_update_preview_id' not in params or
                                                       params['content_update_preview_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `content_update_preview_id` when calling `confirm_shipment_content_update_preview`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_shipment_content_update_preview`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_shipment_content_update_preview`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_shipment_content_update_preview`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `confirm_shipment_content_update_preview`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `confirm_shipment_content_update_preview`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `confirm_shipment_content_update_preview`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('content_update_preview_id' in params and
                                                       len(params['content_update_preview_id']) > 38):
            raise ValueError("Invalid value for parameter `content_update_preview_id` when calling `confirm_shipment_content_update_preview`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('content_update_preview_id' in params and
                                                       len(params['content_update_preview_id']) < 38):
            raise ValueError("Invalid value for parameter `content_update_preview_id` when calling `confirm_shipment_content_update_preview`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('content_update_preview_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['content_update_preview_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `content_update_preview_id` when calling `confirm_shipment_content_update_preview`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501
        if 'content_update_preview_id' in params:
            path_params['contentUpdatePreviewId'] = params['content_update_preview_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/contentUpdatePreviews/{contentUpdatePreviewId}/confirmation', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ConfirmShipmentContentUpdatePreviewResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def confirm_transportation_options(self, inbound_plan_id, body, **kwargs):  # noqa: E501
        """confirm_transportation_options  # noqa: E501

        Confirms all the transportation options for an inbound plan. A placement option must be confirmed prior to use of this API. Once confirmed, new transportation options can not be generated or confirmed for the Inbound Plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.confirm_transportation_options(inbound_plan_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param ConfirmTransportationOptionsRequest body: The body of the request to `confirmTransportationOptions`. (required)
        :return: ConfirmTransportationOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.confirm_transportation_options_with_http_info(inbound_plan_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.confirm_transportation_options_with_http_info(inbound_plan_id, body, **kwargs)  # noqa: E501
            return data

    def confirm_transportation_options_with_http_info(self, inbound_plan_id, body, **kwargs):  # noqa: E501
        """confirm_transportation_options  # noqa: E501

        Confirms all the transportation options for an inbound plan. A placement option must be confirmed prior to use of this API. Once confirmed, new transportation options can not be generated or confirmed for the Inbound Plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.confirm_transportation_options_with_http_info(inbound_plan_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param ConfirmTransportationOptionsRequest body: The body of the request to `confirmTransportationOptions`. (required)
        :return: ConfirmTransportationOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method confirm_transportation_options" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `confirm_transportation_options`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `confirm_transportation_options`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_transportation_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_transportation_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `confirm_transportation_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions/confirmation', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ConfirmTransportationOptionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_inbound_plan(self, body, **kwargs):  # noqa: E501
        """create_inbound_plan  # noqa: E501

        Creates an inbound plan. An inbound plan contains all the necessary information to send shipments into Amazon's fufillment network.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_inbound_plan(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateInboundPlanRequest body: The body of the request to `createInboundPlan`. (required)
        :return: CreateInboundPlanResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_inbound_plan_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_inbound_plan_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_inbound_plan_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_inbound_plan  # noqa: E501

        Creates an inbound plan. An inbound plan contains all the necessary information to send shipments into Amazon's fufillment network.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_inbound_plan_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateInboundPlanRequest body: The body of the request to `createInboundPlan`. (required)
        :return: CreateInboundPlanResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_inbound_plan" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_inbound_plan`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateInboundPlanResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_marketplace_item_labels(self, body, **kwargs):  # noqa: E501
        """create_marketplace_item_labels  # noqa: E501

        For a given marketplace - creates labels for a list of MSKUs.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_marketplace_item_labels(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateMarketplaceItemLabelsRequest body: The body of the request to `createMarketplaceItemLabels`. (required)
        :return: CreateMarketplaceItemLabelsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_marketplace_item_labels_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_marketplace_item_labels_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_marketplace_item_labels_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_marketplace_item_labels  # noqa: E501

        For a given marketplace - creates labels for a list of MSKUs.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_marketplace_item_labels_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateMarketplaceItemLabelsRequest body: The body of the request to `createMarketplaceItemLabels`. (required)
        :return: CreateMarketplaceItemLabelsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_marketplace_item_labels" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_marketplace_item_labels`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/items/labels', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateMarketplaceItemLabelsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def generate_delivery_window_options(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """generate_delivery_window_options  # noqa: E501

        Generates available delivery window options for a given shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_delivery_window_options(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: The shipment to generate delivery window options for. (required)
        :return: GenerateDeliveryWindowOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.generate_delivery_window_options_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
        else:
            (data) = self.generate_delivery_window_options_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
            return data

    def generate_delivery_window_options_with_http_info(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """generate_delivery_window_options  # noqa: E501

        Generates available delivery window options for a given shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_delivery_window_options_with_http_info(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: The shipment to generate delivery window options for. (required)
        :return: GenerateDeliveryWindowOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method generate_delivery_window_options" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `generate_delivery_window_options`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `generate_delivery_window_options`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_delivery_window_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_delivery_window_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_delivery_window_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `generate_delivery_window_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `generate_delivery_window_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `generate_delivery_window_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/deliveryWindowOptions', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='GenerateDeliveryWindowOptionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def generate_packing_options(self, inbound_plan_id, **kwargs):  # noqa: E501
        """generate_packing_options  # noqa: E501

        Generates available packing options for the inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_packing_options(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :return: GeneratePackingOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.generate_packing_options_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
        else:
            (data) = self.generate_packing_options_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
            return data

    def generate_packing_options_with_http_info(self, inbound_plan_id, **kwargs):  # noqa: E501
        """generate_packing_options  # noqa: E501

        Generates available packing options for the inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_packing_options_with_http_info(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :return: GeneratePackingOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method generate_packing_options" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `generate_packing_options`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_packing_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_packing_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_packing_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='GeneratePackingOptionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def generate_placement_options(self, inbound_plan_id, body, **kwargs):  # noqa: E501
        """generate_placement_options  # noqa: E501

        Generates placement options for the inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_placement_options(inbound_plan_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param GeneratePlacementOptionsRequest body: The body of the request to `generatePlacementOptions`. (required)
        :return: GeneratePlacementOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.generate_placement_options_with_http_info(inbound_plan_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.generate_placement_options_with_http_info(inbound_plan_id, body, **kwargs)  # noqa: E501
            return data

    def generate_placement_options_with_http_info(self, inbound_plan_id, body, **kwargs):  # noqa: E501
        """generate_placement_options  # noqa: E501

        Generates placement options for the inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_placement_options_with_http_info(inbound_plan_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param GeneratePlacementOptionsRequest body: The body of the request to `generatePlacementOptions`. (required)
        :return: GeneratePlacementOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method generate_placement_options" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `generate_placement_options`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `generate_placement_options`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_placement_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_placement_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_placement_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='GeneratePlacementOptionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def generate_self_ship_appointment_slots(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """generate_self_ship_appointment_slots  # noqa: E501

        Initiates the process of generating the appointment slots list.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_self_ship_appointment_slots(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param GenerateSelfShipAppointmentSlotsRequest body: The body of the request to `generateSelfShipAppointmentSlots`. (required)
        :return: GenerateSelfShipAppointmentSlotsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.generate_self_ship_appointment_slots_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.generate_self_ship_appointment_slots_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
            return data

    def generate_self_ship_appointment_slots_with_http_info(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """generate_self_ship_appointment_slots  # noqa: E501

        Initiates the process of generating the appointment slots list.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_self_ship_appointment_slots_with_http_info(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param GenerateSelfShipAppointmentSlotsRequest body: The body of the request to `generateSelfShipAppointmentSlots`. (required)
        :return: GenerateSelfShipAppointmentSlotsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method generate_self_ship_appointment_slots" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `generate_self_ship_appointment_slots`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `generate_self_ship_appointment_slots`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `generate_self_ship_appointment_slots`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_self_ship_appointment_slots`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_self_ship_appointment_slots`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_self_ship_appointment_slots`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `generate_self_ship_appointment_slots`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `generate_self_ship_appointment_slots`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `generate_self_ship_appointment_slots`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='GenerateSelfShipAppointmentSlotsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def generate_shipment_content_update_previews(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """generate_shipment_content_update_previews  # noqa: E501

        Generate a shipment content update preview given a set of intended boxes and/or items for a shipment with a confirmed carrier. The shipment content update preview will be viewable with the updated costs and contents prior to confirmation.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_shipment_content_update_previews(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param GenerateShipmentContentUpdatePreviewsRequest body: The body of the request to `generateShipmentContentUpdatePreviews`. (required)
        :return: GenerateShipmentContentUpdatePreviewsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.generate_shipment_content_update_previews_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.generate_shipment_content_update_previews_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
            return data

    def generate_shipment_content_update_previews_with_http_info(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """generate_shipment_content_update_previews  # noqa: E501

        Generate a shipment content update preview given a set of intended boxes and/or items for a shipment with a confirmed carrier. The shipment content update preview will be viewable with the updated costs and contents prior to confirmation.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_shipment_content_update_previews_with_http_info(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param GenerateShipmentContentUpdatePreviewsRequest body: The body of the request to `generateShipmentContentUpdatePreviews`. (required)
        :return: GenerateShipmentContentUpdatePreviewsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method generate_shipment_content_update_previews" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `generate_shipment_content_update_previews`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `generate_shipment_content_update_previews`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `generate_shipment_content_update_previews`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_shipment_content_update_previews`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_shipment_content_update_previews`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_shipment_content_update_previews`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `generate_shipment_content_update_previews`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `generate_shipment_content_update_previews`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `generate_shipment_content_update_previews`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/contentUpdatePreviews', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='GenerateShipmentContentUpdatePreviewsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def generate_transportation_options(self, inbound_plan_id, body, **kwargs):  # noqa: E501
        """generate_transportation_options  # noqa: E501

        Generates available transportation options for a given placement option.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_transportation_options(inbound_plan_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param GenerateTransportationOptionsRequest body: The body of the request to `generateTransportationOptions`. (required)
        :return: GenerateTransportationOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.generate_transportation_options_with_http_info(inbound_plan_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.generate_transportation_options_with_http_info(inbound_plan_id, body, **kwargs)  # noqa: E501
            return data

    def generate_transportation_options_with_http_info(self, inbound_plan_id, body, **kwargs):  # noqa: E501
        """generate_transportation_options  # noqa: E501

        Generates available transportation options for a given placement option.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.generate_transportation_options_with_http_info(inbound_plan_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param GenerateTransportationOptionsRequest body: The body of the request to `generateTransportationOptions`. (required)
        :return: GenerateTransportationOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method generate_transportation_options" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `generate_transportation_options`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `generate_transportation_options`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_transportation_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_transportation_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `generate_transportation_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='GenerateTransportationOptionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_delivery_challan_document(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """get_delivery_challan_document  # noqa: E501

        Provide delivery challan document for PCP transportation in IN marketplace.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_delivery_challan_document(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :return: GetDeliveryChallanDocumentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_delivery_challan_document_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_delivery_challan_document_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
            return data

    def get_delivery_challan_document_with_http_info(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """get_delivery_challan_document  # noqa: E501

        Provide delivery challan document for PCP transportation in IN marketplace.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_delivery_challan_document_with_http_info(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :return: GetDeliveryChallanDocumentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_delivery_challan_document" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `get_delivery_challan_document`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `get_delivery_challan_document`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_delivery_challan_document`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_delivery_challan_document`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_delivery_challan_document`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_delivery_challan_document`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_delivery_challan_document`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_delivery_challan_document`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/deliveryChallanDocument', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='GetDeliveryChallanDocumentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_inbound_operation_status(self, operation_id, **kwargs):  # noqa: E501
        """get_inbound_operation_status  # noqa: E501

        Gets the status of the processing of an asynchronous API call.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_inbound_operation_status(operation_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str operation_id: Identifier of an asynchronous operation. (required)
        :return: InboundOperationStatus
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_inbound_operation_status_with_http_info(operation_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_inbound_operation_status_with_http_info(operation_id, **kwargs)  # noqa: E501
            return data

    def get_inbound_operation_status_with_http_info(self, operation_id, **kwargs):  # noqa: E501
        """get_inbound_operation_status  # noqa: E501

        Gets the status of the processing of an asynchronous API call.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_inbound_operation_status_with_http_info(operation_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str operation_id: Identifier of an asynchronous operation. (required)
        :return: InboundOperationStatus
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['operation_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_inbound_operation_status" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'operation_id' is set
        if self.api_client.client_side_validation and ('operation_id' not in params or
                                                       params['operation_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `operation_id` when calling `get_inbound_operation_status`")  # noqa: E501

        if self.api_client.client_side_validation and ('operation_id' in params and
                                                       len(params['operation_id']) > 38):
            raise ValueError("Invalid value for parameter `operation_id` when calling `get_inbound_operation_status`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('operation_id' in params and
                                                       len(params['operation_id']) < 36):
            raise ValueError("Invalid value for parameter `operation_id` when calling `get_inbound_operation_status`, length must be greater than or equal to `36`")  # noqa: E501
        if self.api_client.client_side_validation and ('operation_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['operation_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `operation_id` when calling `get_inbound_operation_status`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'operation_id' in params:
            path_params['operationId'] = params['operation_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/operations/{operationId}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='InboundOperationStatus',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_inbound_plan(self, inbound_plan_id, **kwargs):  # noqa: E501
        """get_inbound_plan  # noqa: E501

        Fetches the top level information about an inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_inbound_plan(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :return: InboundPlan
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_inbound_plan_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_inbound_plan_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
            return data

    def get_inbound_plan_with_http_info(self, inbound_plan_id, **kwargs):  # noqa: E501
        """get_inbound_plan  # noqa: E501

        Fetches the top level information about an inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_inbound_plan_with_http_info(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :return: InboundPlan
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_inbound_plan" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `get_inbound_plan`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_inbound_plan`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_inbound_plan`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_inbound_plan`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='InboundPlan',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_self_ship_appointment_slots(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """get_self_ship_appointment_slots  # noqa: E501

        Retrieves a list of available self-ship appointment slots used to drop off a shipment at a warehouse.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_self_ship_appointment_slots(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param int page_size: The number of self ship appointment slots to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: GetSelfShipAppointmentSlotsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_self_ship_appointment_slots_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_self_ship_appointment_slots_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
            return data

    def get_self_ship_appointment_slots_with_http_info(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """get_self_ship_appointment_slots  # noqa: E501

        Retrieves a list of available self-ship appointment slots used to drop off a shipment at a warehouse.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_self_ship_appointment_slots_with_http_info(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param int page_size: The number of self ship appointment slots to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: GetSelfShipAppointmentSlotsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_self_ship_appointment_slots" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `get_self_ship_appointment_slots`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `get_self_ship_appointment_slots`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_self_ship_appointment_slots`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_self_ship_appointment_slots`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_self_ship_appointment_slots`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_self_ship_appointment_slots`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_self_ship_appointment_slots`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_self_ship_appointment_slots`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 100):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `get_self_ship_appointment_slots`, must be a value less than or equal to `100`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `get_self_ship_appointment_slots`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `get_self_ship_appointment_slots`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `get_self_ship_appointment_slots`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='GetSelfShipAppointmentSlotsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_shipment(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """get_shipment  # noqa: E501

        Provides the full details for a specific shipment within an inbound plan. The `transportationOptionId` inside `acceptedTransportationSelection` can be used to retrieve the transportation details for the shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_shipment(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :return: Shipment
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_shipment_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_shipment_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
            return data

    def get_shipment_with_http_info(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """get_shipment  # noqa: E501

        Provides the full details for a specific shipment within an inbound plan. The `transportationOptionId` inside `acceptedTransportationSelection` can be used to retrieve the transportation details for the shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_shipment_with_http_info(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :return: Shipment
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_shipment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `get_shipment`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `get_shipment`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_shipment`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_shipment`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_shipment`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_shipment`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_shipment`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_shipment`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Shipment',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_shipment_content_update_preview(self, inbound_plan_id, shipment_id, content_update_preview_id, **kwargs):  # noqa: E501
        """get_shipment_content_update_preview  # noqa: E501

        Retrieve a shipment content update preview which provides a summary of the requested shipment content changes along with the transportation cost implications of the change that can only be confirmed prior to the expiry date specified.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_shipment_content_update_preview(inbound_plan_id, shipment_id, content_update_preview_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param str content_update_preview_id: Identifier of a content update preview. (required)
        :return: ContentUpdatePreview
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_shipment_content_update_preview_with_http_info(inbound_plan_id, shipment_id, content_update_preview_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_shipment_content_update_preview_with_http_info(inbound_plan_id, shipment_id, content_update_preview_id, **kwargs)  # noqa: E501
            return data

    def get_shipment_content_update_preview_with_http_info(self, inbound_plan_id, shipment_id, content_update_preview_id, **kwargs):  # noqa: E501
        """get_shipment_content_update_preview  # noqa: E501

        Retrieve a shipment content update preview which provides a summary of the requested shipment content changes along with the transportation cost implications of the change that can only be confirmed prior to the expiry date specified.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_shipment_content_update_preview_with_http_info(inbound_plan_id, shipment_id, content_update_preview_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param str content_update_preview_id: Identifier of a content update preview. (required)
        :return: ContentUpdatePreview
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'content_update_preview_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_shipment_content_update_preview" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `get_shipment_content_update_preview`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `get_shipment_content_update_preview`")  # noqa: E501
        # verify the required parameter 'content_update_preview_id' is set
        if self.api_client.client_side_validation and ('content_update_preview_id' not in params or
                                                       params['content_update_preview_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `content_update_preview_id` when calling `get_shipment_content_update_preview`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_shipment_content_update_preview`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_shipment_content_update_preview`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `get_shipment_content_update_preview`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_shipment_content_update_preview`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_shipment_content_update_preview`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `get_shipment_content_update_preview`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('content_update_preview_id' in params and
                                                       len(params['content_update_preview_id']) > 38):
            raise ValueError("Invalid value for parameter `content_update_preview_id` when calling `get_shipment_content_update_preview`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('content_update_preview_id' in params and
                                                       len(params['content_update_preview_id']) < 38):
            raise ValueError("Invalid value for parameter `content_update_preview_id` when calling `get_shipment_content_update_preview`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('content_update_preview_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['content_update_preview_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `content_update_preview_id` when calling `get_shipment_content_update_preview`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501
        if 'content_update_preview_id' in params:
            path_params['contentUpdatePreviewId'] = params['content_update_preview_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/contentUpdatePreviews/{contentUpdatePreviewId}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ContentUpdatePreview',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_delivery_window_options(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """list_delivery_window_options  # noqa: E501

        Retrieves all delivery window options for a shipment. Delivery window options must first be generated by the `generateDeliveryWindowOptions` operation before becoming available.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_window_options(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: The shipment to get delivery window options for. (required)
        :param int page_size: The number of delivery window options to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListDeliveryWindowOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_delivery_window_options_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_delivery_window_options_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
            return data

    def list_delivery_window_options_with_http_info(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """list_delivery_window_options  # noqa: E501

        Retrieves all delivery window options for a shipment. Delivery window options must first be generated by the `generateDeliveryWindowOptions` operation before becoming available.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_delivery_window_options_with_http_info(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: The shipment to get delivery window options for. (required)
        :param int page_size: The number of delivery window options to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListDeliveryWindowOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_delivery_window_options" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_delivery_window_options`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `list_delivery_window_options`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_delivery_window_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_delivery_window_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_delivery_window_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_delivery_window_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_delivery_window_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_delivery_window_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 100):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_delivery_window_options`, must be a value less than or equal to `100`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_delivery_window_options`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_delivery_window_options`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_delivery_window_options`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/deliveryWindowOptions', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListDeliveryWindowOptionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_inbound_plan_boxes(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_inbound_plan_boxes  # noqa: E501

        Provides a paginated list of box packages in an inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_inbound_plan_boxes(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of boxes to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListInboundPlanBoxesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_inbound_plan_boxes_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_inbound_plan_boxes_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
            return data

    def list_inbound_plan_boxes_with_http_info(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_inbound_plan_boxes  # noqa: E501

        Provides a paginated list of box packages in an inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_inbound_plan_boxes_with_http_info(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of boxes to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListInboundPlanBoxesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_inbound_plan_boxes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_inbound_plan_boxes`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_inbound_plan_boxes`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_inbound_plan_boxes`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_inbound_plan_boxes`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 1000):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_inbound_plan_boxes`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_inbound_plan_boxes`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_inbound_plan_boxes`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_inbound_plan_boxes`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/boxes', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListInboundPlanBoxesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_inbound_plan_items(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_inbound_plan_items  # noqa: E501

        Provides a paginated list of item packages in an inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_inbound_plan_items(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of items to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListInboundPlanItemsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_inbound_plan_items_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_inbound_plan_items_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
            return data

    def list_inbound_plan_items_with_http_info(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_inbound_plan_items  # noqa: E501

        Provides a paginated list of item packages in an inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_inbound_plan_items_with_http_info(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of items to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListInboundPlanItemsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_inbound_plan_items" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_inbound_plan_items`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_inbound_plan_items`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_inbound_plan_items`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_inbound_plan_items`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 1000):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_inbound_plan_items`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_inbound_plan_items`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_inbound_plan_items`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_inbound_plan_items`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/items', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListInboundPlanItemsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_inbound_plan_pallets(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_inbound_plan_pallets  # noqa: E501

        Provides a paginated list of pallet packages in an inbound plan. An inbound plan will have pallets when the related details are provided after generating Less-Than-Truckload (LTL) carrier shipments.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_inbound_plan_pallets(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of pallets to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListInboundPlanPalletsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_inbound_plan_pallets_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_inbound_plan_pallets_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
            return data

    def list_inbound_plan_pallets_with_http_info(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_inbound_plan_pallets  # noqa: E501

        Provides a paginated list of pallet packages in an inbound plan. An inbound plan will have pallets when the related details are provided after generating Less-Than-Truckload (LTL) carrier shipments.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_inbound_plan_pallets_with_http_info(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of pallets to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListInboundPlanPalletsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_inbound_plan_pallets" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_inbound_plan_pallets`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_inbound_plan_pallets`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_inbound_plan_pallets`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_inbound_plan_pallets`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 1000):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_inbound_plan_pallets`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_inbound_plan_pallets`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_inbound_plan_pallets`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_inbound_plan_pallets`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/pallets', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListInboundPlanPalletsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_inbound_plans(self, **kwargs):  # noqa: E501
        """list_inbound_plans  # noqa: E501

        Provides a list of inbound plans with minimal information.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_inbound_plans(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int page_size: The number of inbound plans to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :param str status: The status of an inbound plan.
        :param str sort_by: Sort by field.
        :param str sort_order: The sort order.
        :return: ListInboundPlansResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_inbound_plans_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.list_inbound_plans_with_http_info(**kwargs)  # noqa: E501
            return data

    def list_inbound_plans_with_http_info(self, **kwargs):  # noqa: E501
        """list_inbound_plans  # noqa: E501

        Provides a list of inbound plans with minimal information.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_inbound_plans_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param int page_size: The number of inbound plans to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :param str status: The status of an inbound plan.
        :param str sort_by: Sort by field.
        :param str sort_order: The sort order.
        :return: ListInboundPlansResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['page_size', 'pagination_token', 'status', 'sort_by', 'sort_order']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_inbound_plans" % key
                )
            params[key] = val
        del params['kwargs']

        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 30):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_inbound_plans`, must be a value less than or equal to `30`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_inbound_plans`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_inbound_plans`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_inbound_plans`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501
        if 'status' in params:
            query_params.append(('status', params['status']))  # noqa: E501
        if 'sort_by' in params:
            query_params.append(('sortBy', params['sort_by']))  # noqa: E501
        if 'sort_order' in params:
            query_params.append(('sortOrder', params['sort_order']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListInboundPlansResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_item_compliance_details(self, mskus, marketplace_id, **kwargs):  # noqa: E501
        """list_item_compliance_details  # noqa: E501

        List the inbound compliance details for MSKUs in a given marketplace.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_item_compliance_details(mskus, marketplace_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param list[str] mskus: A list of merchant SKUs, a merchant-supplied identifier of a specific SKU. (required)
        :param str marketplace_id: The Marketplace ID. For a list of possible values, refer to [Marketplace IDs](https://developer-docs.amazon.com/sp-api/docs/marketplace-ids). (required)
        :return: ListItemComplianceDetailsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_item_compliance_details_with_http_info(mskus, marketplace_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_item_compliance_details_with_http_info(mskus, marketplace_id, **kwargs)  # noqa: E501
            return data

    def list_item_compliance_details_with_http_info(self, mskus, marketplace_id, **kwargs):  # noqa: E501
        """list_item_compliance_details  # noqa: E501

        List the inbound compliance details for MSKUs in a given marketplace.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_item_compliance_details_with_http_info(mskus, marketplace_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param list[str] mskus: A list of merchant SKUs, a merchant-supplied identifier of a specific SKU. (required)
        :param str marketplace_id: The Marketplace ID. For a list of possible values, refer to [Marketplace IDs](https://developer-docs.amazon.com/sp-api/docs/marketplace-ids). (required)
        :return: ListItemComplianceDetailsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['mskus', 'marketplace_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_item_compliance_details" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'mskus' is set
        if self.api_client.client_side_validation and ('mskus' not in params or
                                                       params['mskus'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `mskus` when calling `list_item_compliance_details`")  # noqa: E501
        # verify the required parameter 'marketplace_id' is set
        if self.api_client.client_side_validation and ('marketplace_id' not in params or
                                                       params['marketplace_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `marketplace_id` when calling `list_item_compliance_details`")  # noqa: E501

        if self.api_client.client_side_validation and ('mskus' in params and
                                            len(params['mskus']) > 100):
            raise ValueError("Invalid value for parameter `mskus` when calling `list_item_compliance_details`, number of items must be less than or equal to `100`")  # noqa: E501
        if self.api_client.client_side_validation and ('mskus' in params and
                                            len(params['mskus']) < 1):
            raise ValueError("Invalid value for parameter `mskus` when calling `list_item_compliance_details`, number of items must be greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('marketplace_id' in params and
                                                       len(params['marketplace_id']) > 20):
            raise ValueError("Invalid value for parameter `marketplace_id` when calling `list_item_compliance_details`, length must be less than or equal to `20`")  # noqa: E501
        if self.api_client.client_side_validation and ('marketplace_id' in params and
                                                       len(params['marketplace_id']) < 1):
            raise ValueError("Invalid value for parameter `marketplace_id` when calling `list_item_compliance_details`, length must be greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}

        query_params = []
        if 'mskus' in params:
            query_params.append(('mskus', params['mskus']))  # noqa: E501
            collection_formats['mskus'] = 'multi'  # noqa: E501
        if 'marketplace_id' in params:
            query_params.append(('marketplaceId', params['marketplace_id']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/items/compliance', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListItemComplianceDetailsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_packing_group_boxes(self, inbound_plan_id, packing_group_id, **kwargs):  # noqa: E501
        """list_packing_group_boxes  # noqa: E501

        Retrieves a page of boxes from a given packing group. These boxes were previously provided through the `setPackingInformation` operation. This API is used for workflows where boxes are packed before Amazon determines shipment splits.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_packing_group_boxes(inbound_plan_id, packing_group_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str packing_group_id: Identifier of a packing group. (required)
        :param int page_size: The number of packing group boxes to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListPackingGroupBoxesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_packing_group_boxes_with_http_info(inbound_plan_id, packing_group_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_packing_group_boxes_with_http_info(inbound_plan_id, packing_group_id, **kwargs)  # noqa: E501
            return data

    def list_packing_group_boxes_with_http_info(self, inbound_plan_id, packing_group_id, **kwargs):  # noqa: E501
        """list_packing_group_boxes  # noqa: E501

        Retrieves a page of boxes from a given packing group. These boxes were previously provided through the `setPackingInformation` operation. This API is used for workflows where boxes are packed before Amazon determines shipment splits.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_packing_group_boxes_with_http_info(inbound_plan_id, packing_group_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str packing_group_id: Identifier of a packing group. (required)
        :param int page_size: The number of packing group boxes to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListPackingGroupBoxesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'packing_group_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_packing_group_boxes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_packing_group_boxes`")  # noqa: E501
        # verify the required parameter 'packing_group_id' is set
        if self.api_client.client_side_validation and ('packing_group_id' not in params or
                                                       params['packing_group_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `packing_group_id` when calling `list_packing_group_boxes`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_packing_group_boxes`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_packing_group_boxes`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_packing_group_boxes`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('packing_group_id' in params and
                                                       len(params['packing_group_id']) > 38):
            raise ValueError("Invalid value for parameter `packing_group_id` when calling `list_packing_group_boxes`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('packing_group_id' in params and
                                                       len(params['packing_group_id']) < 38):
            raise ValueError("Invalid value for parameter `packing_group_id` when calling `list_packing_group_boxes`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('packing_group_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['packing_group_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `packing_group_id` when calling `list_packing_group_boxes`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 100):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_packing_group_boxes`, must be a value less than or equal to `100`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_packing_group_boxes`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_packing_group_boxes`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_packing_group_boxes`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'packing_group_id' in params:
            path_params['packingGroupId'] = params['packing_group_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingGroups/{packingGroupId}/boxes', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListPackingGroupBoxesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_packing_group_items(self, inbound_plan_id, packing_group_id, **kwargs):  # noqa: E501
        """list_packing_group_items  # noqa: E501

        Retrieves a page of items in a given packing group. Packing options must first be generated by the corresponding operation before packing group items can be listed.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_packing_group_items(inbound_plan_id, packing_group_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str packing_group_id: Identifier of a packing group. (required)
        :param int page_size: The number of packing group items to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListPackingGroupItemsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_packing_group_items_with_http_info(inbound_plan_id, packing_group_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_packing_group_items_with_http_info(inbound_plan_id, packing_group_id, **kwargs)  # noqa: E501
            return data

    def list_packing_group_items_with_http_info(self, inbound_plan_id, packing_group_id, **kwargs):  # noqa: E501
        """list_packing_group_items  # noqa: E501

        Retrieves a page of items in a given packing group. Packing options must first be generated by the corresponding operation before packing group items can be listed.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_packing_group_items_with_http_info(inbound_plan_id, packing_group_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str packing_group_id: Identifier of a packing group. (required)
        :param int page_size: The number of packing group items to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListPackingGroupItemsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'packing_group_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_packing_group_items" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_packing_group_items`")  # noqa: E501
        # verify the required parameter 'packing_group_id' is set
        if self.api_client.client_side_validation and ('packing_group_id' not in params or
                                                       params['packing_group_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `packing_group_id` when calling `list_packing_group_items`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_packing_group_items`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_packing_group_items`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_packing_group_items`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('packing_group_id' in params and
                                                       len(params['packing_group_id']) > 38):
            raise ValueError("Invalid value for parameter `packing_group_id` when calling `list_packing_group_items`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('packing_group_id' in params and
                                                       len(params['packing_group_id']) < 38):
            raise ValueError("Invalid value for parameter `packing_group_id` when calling `list_packing_group_items`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('packing_group_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['packing_group_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `packing_group_id` when calling `list_packing_group_items`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 100):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_packing_group_items`, must be a value less than or equal to `100`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_packing_group_items`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_packing_group_items`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_packing_group_items`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'packing_group_id' in params:
            path_params['packingGroupId'] = params['packing_group_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingGroups/{packingGroupId}/items', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListPackingGroupItemsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_packing_options(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_packing_options  # noqa: E501

        Retrieves a list of all packing options for an inbound plan. Packing options must first be generated by the corresponding operation before becoming available.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_packing_options(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of packing options to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListPackingOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_packing_options_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_packing_options_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
            return data

    def list_packing_options_with_http_info(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_packing_options  # noqa: E501

        Retrieves a list of all packing options for an inbound plan. Packing options must first be generated by the corresponding operation before becoming available.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_packing_options_with_http_info(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of packing options to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListPackingOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_packing_options" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_packing_options`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_packing_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_packing_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_packing_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 20):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_packing_options`, must be a value less than or equal to `20`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_packing_options`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_packing_options`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_packing_options`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListPackingOptionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_placement_options(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_placement_options  # noqa: E501

        Provides a list of all placement options for an inbound plan. Placement options must first be generated by the corresponding operation before becoming available.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_placement_options(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of placement options to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListPlacementOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_placement_options_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_placement_options_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
            return data

    def list_placement_options_with_http_info(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_placement_options  # noqa: E501

        Provides a list of all placement options for an inbound plan. Placement options must first be generated by the corresponding operation before becoming available.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_placement_options_with_http_info(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of placement options to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListPlacementOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_placement_options" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_placement_options`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_placement_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_placement_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_placement_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 20):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_placement_options`, must be a value less than or equal to `20`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_placement_options`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_placement_options`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_placement_options`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListPlacementOptionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_prep_details(self, marketplace_id, mskus, **kwargs):  # noqa: E501
        """list_prep_details  # noqa: E501

        Get preparation details for a list of MSKUs in a specified marketplace.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_prep_details(marketplace_id, mskus, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str marketplace_id: The marketplace ID. For a list of possible values, refer to [Marketplace IDs](https://developer-docs.amazon.com/sp-api/docs/marketplace-ids). (required)
        :param list[str] mskus: A list of merchant SKUs, a merchant-supplied identifier of a specific SKU. (required)
        :return: ListPrepDetailsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_prep_details_with_http_info(marketplace_id, mskus, **kwargs)  # noqa: E501
        else:
            (data) = self.list_prep_details_with_http_info(marketplace_id, mskus, **kwargs)  # noqa: E501
            return data

    def list_prep_details_with_http_info(self, marketplace_id, mskus, **kwargs):  # noqa: E501
        """list_prep_details  # noqa: E501

        Get preparation details for a list of MSKUs in a specified marketplace.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_prep_details_with_http_info(marketplace_id, mskus, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str marketplace_id: The marketplace ID. For a list of possible values, refer to [Marketplace IDs](https://developer-docs.amazon.com/sp-api/docs/marketplace-ids). (required)
        :param list[str] mskus: A list of merchant SKUs, a merchant-supplied identifier of a specific SKU. (required)
        :return: ListPrepDetailsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['marketplace_id', 'mskus']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_prep_details" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'marketplace_id' is set
        if self.api_client.client_side_validation and ('marketplace_id' not in params or
                                                       params['marketplace_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `marketplace_id` when calling `list_prep_details`")  # noqa: E501
        # verify the required parameter 'mskus' is set
        if self.api_client.client_side_validation and ('mskus' not in params or
                                                       params['mskus'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `mskus` when calling `list_prep_details`")  # noqa: E501

        if self.api_client.client_side_validation and ('marketplace_id' in params and
                                                       len(params['marketplace_id']) > 20):
            raise ValueError("Invalid value for parameter `marketplace_id` when calling `list_prep_details`, length must be less than or equal to `20`")  # noqa: E501
        if self.api_client.client_side_validation and ('marketplace_id' in params and
                                                       len(params['marketplace_id']) < 1):
            raise ValueError("Invalid value for parameter `marketplace_id` when calling `list_prep_details`, length must be greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('mskus' in params and
                                            len(params['mskus']) > 100):
            raise ValueError("Invalid value for parameter `mskus` when calling `list_prep_details`, number of items must be less than or equal to `100`")  # noqa: E501
        if self.api_client.client_side_validation and ('mskus' in params and
                                            len(params['mskus']) < 1):
            raise ValueError("Invalid value for parameter `mskus` when calling `list_prep_details`, number of items must be greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}

        query_params = []
        if 'marketplace_id' in params:
            query_params.append(('marketplaceId', params['marketplace_id']))  # noqa: E501
        if 'mskus' in params:
            query_params.append(('mskus', params['mskus']))  # noqa: E501
            collection_formats['mskus'] = 'multi'  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/items/prepDetails', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListPrepDetailsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_shipment_boxes(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """list_shipment_boxes  # noqa: E501

        Provides a paginated list of box packages in a shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_shipment_boxes(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param int page_size: The number of boxes to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListShipmentBoxesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_shipment_boxes_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_shipment_boxes_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
            return data

    def list_shipment_boxes_with_http_info(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """list_shipment_boxes  # noqa: E501

        Provides a paginated list of box packages in a shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_shipment_boxes_with_http_info(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param int page_size: The number of boxes to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListShipmentBoxesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_shipment_boxes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_shipment_boxes`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `list_shipment_boxes`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_boxes`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_boxes`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_boxes`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_boxes`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_boxes`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_boxes`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 1000):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_shipment_boxes`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_shipment_boxes`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_shipment_boxes`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_shipment_boxes`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/boxes', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListShipmentBoxesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_shipment_content_update_previews(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """list_shipment_content_update_previews  # noqa: E501

        Retrieve a paginated list of shipment content update previews for a given shipment. The shipment content update preview is a summary of the requested shipment content changes along with the transportation cost implications of the change that can only be confirmed prior to the expiry date specified.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_shipment_content_update_previews(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param int page_size: The number of content update previews to return.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListShipmentContentUpdatePreviewsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_shipment_content_update_previews_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_shipment_content_update_previews_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
            return data

    def list_shipment_content_update_previews_with_http_info(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """list_shipment_content_update_previews  # noqa: E501

        Retrieve a paginated list of shipment content update previews for a given shipment. The shipment content update preview is a summary of the requested shipment content changes along with the transportation cost implications of the change that can only be confirmed prior to the expiry date specified.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_shipment_content_update_previews_with_http_info(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param int page_size: The number of content update previews to return.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListShipmentContentUpdatePreviewsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_shipment_content_update_previews" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_shipment_content_update_previews`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `list_shipment_content_update_previews`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_content_update_previews`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_content_update_previews`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_content_update_previews`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_content_update_previews`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_content_update_previews`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_content_update_previews`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 20):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_shipment_content_update_previews`, must be a value less than or equal to `20`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_shipment_content_update_previews`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_shipment_content_update_previews`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_shipment_content_update_previews`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/contentUpdatePreviews', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListShipmentContentUpdatePreviewsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_shipment_items(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """list_shipment_items  # noqa: E501

        Provides a paginated list of item packages in a shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_shipment_items(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param int page_size: The number of items to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListShipmentItemsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_shipment_items_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_shipment_items_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
            return data

    def list_shipment_items_with_http_info(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """list_shipment_items  # noqa: E501

        Provides a paginated list of item packages in a shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_shipment_items_with_http_info(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param int page_size: The number of items to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListShipmentItemsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_shipment_items" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_shipment_items`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `list_shipment_items`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_items`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_items`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_items`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_items`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_items`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_items`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 1000):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_shipment_items`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_shipment_items`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_shipment_items`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_shipment_items`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/items', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListShipmentItemsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_shipment_pallets(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """list_shipment_pallets  # noqa: E501

        Provides a paginated list of pallet packages in a shipment. A palletized shipment will have pallets when the related details are provided after generating Less-Than-Truckload (LTL) carrier shipments.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_shipment_pallets(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param int page_size: The number of pallets to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListShipmentPalletsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_shipment_pallets_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_shipment_pallets_with_http_info(inbound_plan_id, shipment_id, **kwargs)  # noqa: E501
            return data

    def list_shipment_pallets_with_http_info(self, inbound_plan_id, shipment_id, **kwargs):  # noqa: E501
        """list_shipment_pallets  # noqa: E501

        Provides a paginated list of pallet packages in a shipment. A palletized shipment will have pallets when the related details are provided after generating Less-Than-Truckload (LTL) carrier shipments.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_shipment_pallets_with_http_info(inbound_plan_id, shipment_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param int page_size: The number of pallets to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :return: ListShipmentPalletsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'page_size', 'pagination_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_shipment_pallets" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_shipment_pallets`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `list_shipment_pallets`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_pallets`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_pallets`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_shipment_pallets`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_pallets`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_pallets`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_shipment_pallets`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 1000):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_shipment_pallets`, must be a value less than or equal to `1000`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_shipment_pallets`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_shipment_pallets`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_shipment_pallets`, length must be greater than or equal to `0`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/pallets', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListShipmentPalletsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_transportation_options(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_transportation_options  # noqa: E501

        Retrieves all transportation options for a shipment. Transportation options must first be generated by the `generateTransportationOptions` operation before becoming available.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_transportation_options(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of transportation options to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :param str placement_option_id: The placement option to get transportation options for. Either `placementOptionId` or `shipmentId` must be specified.
        :param str shipment_id: The shipment to get transportation options for. Either `placementOptionId` or `shipmentId` must be specified.
        :return: ListTransportationOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_transportation_options_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
        else:
            (data) = self.list_transportation_options_with_http_info(inbound_plan_id, **kwargs)  # noqa: E501
            return data

    def list_transportation_options_with_http_info(self, inbound_plan_id, **kwargs):  # noqa: E501
        """list_transportation_options  # noqa: E501

        Retrieves all transportation options for a shipment. Transportation options must first be generated by the `generateTransportationOptions` operation before becoming available.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_transportation_options_with_http_info(inbound_plan_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param int page_size: The number of transportation options to return in the response matching the given query.
        :param str pagination_token: A token to fetch a certain page when there are multiple pages worth of results. The value of this token is fetched from the `pagination` returned in the API response. In the absence of the token value from the query parameter the API returns the first page of the result.
        :param str placement_option_id: The placement option to get transportation options for. Either `placementOptionId` or `shipmentId` must be specified.
        :param str shipment_id: The shipment to get transportation options for. Either `placementOptionId` or `shipmentId` must be specified.
        :return: ListTransportationOptionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'page_size', 'pagination_token', 'placement_option_id', 'shipment_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_transportation_options" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `list_transportation_options`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_transportation_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_transportation_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `list_transportation_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 20):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_transportation_options`, must be a value less than or equal to `20`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `list_transportation_options`, must be a value greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) > 1024):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_transportation_options`, length must be less than or equal to `1024`")  # noqa: E501
        if self.api_client.client_side_validation and ('pagination_token' in params and
                                                       len(params['pagination_token']) < 0):
            raise ValueError("Invalid value for parameter `pagination_token` when calling `list_transportation_options`, length must be greater than or equal to `0`")  # noqa: E501
        if self.api_client.client_side_validation and ('placement_option_id' in params and
                                                       len(params['placement_option_id']) > 38):
            raise ValueError("Invalid value for parameter `placement_option_id` when calling `list_transportation_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('placement_option_id' in params and
                                                       len(params['placement_option_id']) < 38):
            raise ValueError("Invalid value for parameter `placement_option_id` when calling `list_transportation_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('placement_option_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['placement_option_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `placement_option_id` when calling `list_transportation_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_transportation_options`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_transportation_options`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `list_transportation_options`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'pagination_token' in params:
            query_params.append(('paginationToken', params['pagination_token']))  # noqa: E501
        if 'placement_option_id' in params:
            query_params.append(('placementOptionId', params['placement_option_id']))  # noqa: E501
        if 'shipment_id' in params:
            query_params.append(('shipmentId', params['shipment_id']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListTransportationOptionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def schedule_self_ship_appointment(self, inbound_plan_id, shipment_id, slot_id, body, **kwargs):  # noqa: E501
        """schedule_self_ship_appointment  # noqa: E501

        Confirms or reschedules a self-ship appointment slot against a shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.schedule_self_ship_appointment(inbound_plan_id, shipment_id, slot_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param str slot_id: An identifier to a self-ship appointment slot. (required)
        :param ScheduleSelfShipAppointmentRequest body: The body of the request to `scheduleSelfShipAppointment`. (required)
        :return: ScheduleSelfShipAppointmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.schedule_self_ship_appointment_with_http_info(inbound_plan_id, shipment_id, slot_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.schedule_self_ship_appointment_with_http_info(inbound_plan_id, shipment_id, slot_id, body, **kwargs)  # noqa: E501
            return data

    def schedule_self_ship_appointment_with_http_info(self, inbound_plan_id, shipment_id, slot_id, body, **kwargs):  # noqa: E501
        """schedule_self_ship_appointment  # noqa: E501

        Confirms or reschedules a self-ship appointment slot against a shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.schedule_self_ship_appointment_with_http_info(inbound_plan_id, shipment_id, slot_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param str slot_id: An identifier to a self-ship appointment slot. (required)
        :param ScheduleSelfShipAppointmentRequest body: The body of the request to `scheduleSelfShipAppointment`. (required)
        :return: ScheduleSelfShipAppointmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'slot_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method schedule_self_ship_appointment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `schedule_self_ship_appointment`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `schedule_self_ship_appointment`")  # noqa: E501
        # verify the required parameter 'slot_id' is set
        if self.api_client.client_side_validation and ('slot_id' not in params or
                                                       params['slot_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `slot_id` when calling `schedule_self_ship_appointment`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `schedule_self_ship_appointment`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `schedule_self_ship_appointment`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `schedule_self_ship_appointment`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `schedule_self_ship_appointment`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `schedule_self_ship_appointment`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `schedule_self_ship_appointment`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `schedule_self_ship_appointment`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('slot_id' in params and
                                                       len(params['slot_id']) > 38):
            raise ValueError("Invalid value for parameter `slot_id` when calling `schedule_self_ship_appointment`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('slot_id' in params and
                                                       len(params['slot_id']) < 38):
            raise ValueError("Invalid value for parameter `slot_id` when calling `schedule_self_ship_appointment`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('slot_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['slot_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `slot_id` when calling `schedule_self_ship_appointment`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501
        if 'slot_id' in params:
            path_params['slotId'] = params['slot_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/selfShipAppointmentSlots/{slotId}/schedule', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ScheduleSelfShipAppointmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def set_packing_information(self, inbound_plan_id, body, **kwargs):  # noqa: E501
        """set_packing_information  # noqa: E501

        Sets packing information for an inbound plan. This should be called after an inbound plan is created to populate the box level information required for planning and transportation estimates.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_packing_information(inbound_plan_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param SetPackingInformationRequest body: The body of the request to `setPackingInformation`. (required)
        :return: SetPackingInformationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.set_packing_information_with_http_info(inbound_plan_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.set_packing_information_with_http_info(inbound_plan_id, body, **kwargs)  # noqa: E501
            return data

    def set_packing_information_with_http_info(self, inbound_plan_id, body, **kwargs):  # noqa: E501
        """set_packing_information  # noqa: E501

        Sets packing information for an inbound plan. This should be called after an inbound plan is created to populate the box level information required for planning and transportation estimates.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_packing_information_with_http_info(inbound_plan_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param SetPackingInformationRequest body: The body of the request to `setPackingInformation`. (required)
        :return: SetPackingInformationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method set_packing_information" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `set_packing_information`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `set_packing_information`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `set_packing_information`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `set_packing_information`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `set_packing_information`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingInformation', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='SetPackingInformationResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def set_prep_details(self, body, **kwargs):  # noqa: E501
        """set_prep_details  # noqa: E501

        Set the preparation details for a list of MSKUs in a specified marketplace.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_prep_details(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param SetPrepDetailsRequest body: The body of the request to `setPrepDetails`. (required)
        :return: SetPrepDetailsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.set_prep_details_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.set_prep_details_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def set_prep_details_with_http_info(self, body, **kwargs):  # noqa: E501
        """set_prep_details  # noqa: E501

        Set the preparation details for a list of MSKUs in a specified marketplace.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_prep_details_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param SetPrepDetailsRequest body: The body of the request to `setPrepDetails`. (required)
        :return: SetPrepDetailsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method set_prep_details" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `set_prep_details`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/items/prepDetails', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='SetPrepDetailsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_inbound_plan_name(self, inbound_plan_id, body, **kwargs):  # noqa: E501
        """update_inbound_plan_name  # noqa: E501

        Updates the name of an existing inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_inbound_plan_name(inbound_plan_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param UpdateInboundPlanNameRequest body: The body of the request to `updateInboundPlanName`. (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.update_inbound_plan_name_with_http_info(inbound_plan_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.update_inbound_plan_name_with_http_info(inbound_plan_id, body, **kwargs)  # noqa: E501
            return data

    def update_inbound_plan_name_with_http_info(self, inbound_plan_id, body, **kwargs):  # noqa: E501
        """update_inbound_plan_name  # noqa: E501

        Updates the name of an existing inbound plan.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_inbound_plan_name_with_http_info(inbound_plan_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param UpdateInboundPlanNameRequest body: The body of the request to `updateInboundPlanName`. (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_inbound_plan_name" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `update_inbound_plan_name`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `update_inbound_plan_name`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_inbound_plan_name`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_inbound_plan_name`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_inbound_plan_name`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/name', 'PUT',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_item_compliance_details(self, marketplace_id, body, **kwargs):  # noqa: E501
        """update_item_compliance_details  # noqa: E501

        Update compliance details for a list of MSKUs. The details provided here are only used for the India (IN - A21TJRUUN4KGV) marketplace compliance validation.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_item_compliance_details(marketplace_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str marketplace_id: The Marketplace ID. For a list of possible values, refer to [Marketplace IDs](https://developer-docs.amazon.com/sp-api/docs/marketplace-ids). (required)
        :param UpdateItemComplianceDetailsRequest body: The body of the request to `updateItemComplianceDetails`. (required)
        :return: UpdateItemComplianceDetailsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.update_item_compliance_details_with_http_info(marketplace_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.update_item_compliance_details_with_http_info(marketplace_id, body, **kwargs)  # noqa: E501
            return data

    def update_item_compliance_details_with_http_info(self, marketplace_id, body, **kwargs):  # noqa: E501
        """update_item_compliance_details  # noqa: E501

        Update compliance details for a list of MSKUs. The details provided here are only used for the India (IN - A21TJRUUN4KGV) marketplace compliance validation.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 6 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_item_compliance_details_with_http_info(marketplace_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str marketplace_id: The Marketplace ID. For a list of possible values, refer to [Marketplace IDs](https://developer-docs.amazon.com/sp-api/docs/marketplace-ids). (required)
        :param UpdateItemComplianceDetailsRequest body: The body of the request to `updateItemComplianceDetails`. (required)
        :return: UpdateItemComplianceDetailsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['marketplace_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_item_compliance_details" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'marketplace_id' is set
        if self.api_client.client_side_validation and ('marketplace_id' not in params or
                                                       params['marketplace_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `marketplace_id` when calling `update_item_compliance_details`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `update_item_compliance_details`")  # noqa: E501

        if self.api_client.client_side_validation and ('marketplace_id' in params and
                                                       len(params['marketplace_id']) > 20):
            raise ValueError("Invalid value for parameter `marketplace_id` when calling `update_item_compliance_details`, length must be less than or equal to `20`")  # noqa: E501
        if self.api_client.client_side_validation and ('marketplace_id' in params and
                                                       len(params['marketplace_id']) < 1):
            raise ValueError("Invalid value for parameter `marketplace_id` when calling `update_item_compliance_details`, length must be greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}

        query_params = []
        if 'marketplace_id' in params:
            query_params.append(('marketplaceId', params['marketplace_id']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/items/compliance', 'PUT',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='UpdateItemComplianceDetailsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_shipment_name(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """update_shipment_name  # noqa: E501

        Updates the name of an existing shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_shipment_name(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param UpdateShipmentNameRequest body: The body of the request to `updateShipmentName`. (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.update_shipment_name_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.update_shipment_name_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
            return data

    def update_shipment_name_with_http_info(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """update_shipment_name  # noqa: E501

        Updates the name of an existing shipment.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_shipment_name_with_http_info(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param UpdateShipmentNameRequest body: The body of the request to `updateShipmentName`. (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_shipment_name" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `update_shipment_name`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `update_shipment_name`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `update_shipment_name`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_shipment_name`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_shipment_name`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_shipment_name`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `update_shipment_name`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `update_shipment_name`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `update_shipment_name`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/name', 'PUT',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_shipment_source_address(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """update_shipment_source_address  # noqa: E501

        Updates the source address of an existing shipment. The shipment source address can only be updated prior to the confirmation of the shipment carriers. As a result of the updated source address, existing transportation options will be invalidated and will need to be regenerated to capture the potential difference in transportation options and quotes due to the new source address.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_shipment_source_address(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param UpdateShipmentSourceAddressRequest body: The body of the request to `updateShipmentSourceAddress`. (required)
        :return: UpdateShipmentSourceAddressResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.update_shipment_source_address_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.update_shipment_source_address_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
            return data

    def update_shipment_source_address_with_http_info(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """update_shipment_source_address  # noqa: E501

        Updates the source address of an existing shipment. The shipment source address can only be updated prior to the confirmation of the shipment carriers. As a result of the updated source address, existing transportation options will be invalidated and will need to be regenerated to capture the potential difference in transportation options and quotes due to the new source address.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 30 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_shipment_source_address_with_http_info(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param UpdateShipmentSourceAddressRequest body: The body of the request to `updateShipmentSourceAddress`. (required)
        :return: UpdateShipmentSourceAddressResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_shipment_source_address" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `update_shipment_source_address`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `update_shipment_source_address`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `update_shipment_source_address`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_shipment_source_address`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_shipment_source_address`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_shipment_source_address`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `update_shipment_source_address`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `update_shipment_source_address`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `update_shipment_source_address`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/sourceAddress', 'PUT',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='UpdateShipmentSourceAddressResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def update_shipment_tracking_details(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """update_shipment_tracking_details  # noqa: E501

        Updates a shipment's tracking details.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_shipment_tracking_details(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param UpdateShipmentTrackingDetailsRequest body: The body of the request to `updateShipmentTrackingDetails`. (required)
        :return: UpdateShipmentTrackingDetailsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.update_shipment_tracking_details_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
        else:
            (data) = self.update_shipment_tracking_details_with_http_info(inbound_plan_id, shipment_id, body, **kwargs)  # noqa: E501
            return data

    def update_shipment_tracking_details_with_http_info(self, inbound_plan_id, shipment_id, body, **kwargs):  # noqa: E501
        """update_shipment_tracking_details  # noqa: E501

        Updates a shipment's tracking details.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 2 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The preceding table contains the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may have higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.update_shipment_tracking_details_with_http_info(inbound_plan_id, shipment_id, body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str inbound_plan_id: Identifier of an inbound plan. (required)
        :param str shipment_id: Identifier of a shipment. A shipment contains the boxes and units being inbounded. (required)
        :param UpdateShipmentTrackingDetailsRequest body: The body of the request to `updateShipmentTrackingDetails`. (required)
        :return: UpdateShipmentTrackingDetailsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['inbound_plan_id', 'shipment_id', 'body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method update_shipment_tracking_details" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'inbound_plan_id' is set
        if self.api_client.client_side_validation and ('inbound_plan_id' not in params or
                                                       params['inbound_plan_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `inbound_plan_id` when calling `update_shipment_tracking_details`")  # noqa: E501
        # verify the required parameter 'shipment_id' is set
        if self.api_client.client_side_validation and ('shipment_id' not in params or
                                                       params['shipment_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `shipment_id` when calling `update_shipment_tracking_details`")  # noqa: E501
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `update_shipment_tracking_details`")  # noqa: E501

        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) > 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_shipment_tracking_details`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and
                                                       len(params['inbound_plan_id']) < 38):
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_shipment_tracking_details`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('inbound_plan_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['inbound_plan_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `inbound_plan_id` when calling `update_shipment_tracking_details`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) > 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `update_shipment_tracking_details`, length must be less than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and
                                                       len(params['shipment_id']) < 38):
            raise ValueError("Invalid value for parameter `shipment_id` when calling `update_shipment_tracking_details`, length must be greater than or equal to `38`")  # noqa: E501
        if self.api_client.client_side_validation and ('shipment_id' in params and not re.search(r'^[a-zA-Z0-9-]*$', params['shipment_id'])):  # noqa: E501
            raise ValueError("Invalid value for parameter `shipment_id` when calling `update_shipment_tracking_details`, must conform to the pattern `/^[a-zA-Z0-9-]*$/`")  # noqa: E501
        collection_formats = {}

        path_params = {}
        if 'inbound_plan_id' in params:
            path_params['inboundPlanId'] = params['inbound_plan_id']  # noqa: E501
        if 'shipment_id' in params:
            path_params['shipmentId'] = params['shipment_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/shipments/{shipmentId}/trackingDetails', 'PUT',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='UpdateShipmentTrackingDetailsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)
