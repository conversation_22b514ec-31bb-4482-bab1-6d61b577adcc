# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class TaxDetails(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'declared_value': 'Currency',
        'hsn_code': 'str',
        'tax_rates': 'list[TaxRate]'
    }

    attribute_map = {
        'declared_value': 'declaredValue',
        'hsn_code': 'hsnCode',
        'tax_rates': 'taxRates'
    }

    def __init__(self, declared_value=None, hsn_code=None, tax_rates=None):  # noqa: E501
        """TaxDetails - a model defined in Swagger"""  # noqa: E501

        self._declared_value = None
        self._hsn_code = None
        self._tax_rates = None
        self.discriminator = None

        if declared_value is not None:
            self.declared_value = declared_value
        if hsn_code is not None:
            self.hsn_code = hsn_code
        if tax_rates is not None:
            self.tax_rates = tax_rates

    @property
    def declared_value(self):
        """Gets the declared_value of this TaxDetails.  # noqa: E501


        :return: The declared_value of this TaxDetails.  # noqa: E501
        :rtype: Currency
        """
        return self._declared_value

    @declared_value.setter
    def declared_value(self, declared_value):
        """Sets the declared_value of this TaxDetails.


        :param declared_value: The declared_value of this TaxDetails.  # noqa: E501
        :type: Currency
        """

        self._declared_value = declared_value

    @property
    def hsn_code(self):
        """Gets the hsn_code of this TaxDetails.  # noqa: E501

        Harmonized System of Nomenclature code.  # noqa: E501

        :return: The hsn_code of this TaxDetails.  # noqa: E501
        :rtype: str
        """
        return self._hsn_code

    @hsn_code.setter
    def hsn_code(self, hsn_code):
        """Sets the hsn_code of this TaxDetails.

        Harmonized System of Nomenclature code.  # noqa: E501

        :param hsn_code: The hsn_code of this TaxDetails.  # noqa: E501
        :type: str
        """
        if hsn_code is not None and len(hsn_code) > 1024:
            raise ValueError("Invalid value for `hsn_code`, length must be less than or equal to `1024`")  # noqa: E501
        if hsn_code is not None and len(hsn_code) < 1:
            raise ValueError("Invalid value for `hsn_code`, length must be greater than or equal to `1`")  # noqa: E501

        self._hsn_code = hsn_code

    @property
    def tax_rates(self):
        """Gets the tax_rates of this TaxDetails.  # noqa: E501

        List of tax rates.  # noqa: E501

        :return: The tax_rates of this TaxDetails.  # noqa: E501
        :rtype: list[TaxRate]
        """
        return self._tax_rates

    @tax_rates.setter
    def tax_rates(self, tax_rates):
        """Sets the tax_rates of this TaxDetails.

        List of tax rates.  # noqa: E501

        :param tax_rates: The tax_rates of this TaxDetails.  # noqa: E501
        :type: list[TaxRate]
        """

        self._tax_rates = tax_rates

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaxDetails, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaxDetails):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
