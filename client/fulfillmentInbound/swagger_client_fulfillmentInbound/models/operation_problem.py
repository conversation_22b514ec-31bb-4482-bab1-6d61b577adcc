# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class OperationProblem(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'code': 'str',
        'details': 'str',
        'message': 'str',
        'severity': 'str'
    }

    attribute_map = {
        'code': 'code',
        'details': 'details',
        'message': 'message',
        'severity': 'severity'
    }

    def __init__(self, code=None, details=None, message=None, severity=None):  # noqa: E501
        """OperationProblem - a model defined in Swagger"""  # noqa: E501

        self._code = None
        self._details = None
        self._message = None
        self._severity = None
        self.discriminator = None

        self.code = code
        if details is not None:
            self.details = details
        self.message = message
        self.severity = severity

    @property
    def code(self):
        """Gets the code of this OperationProblem.  # noqa: E501

        An error code that identifies the type of error that occurred.  # noqa: E501

        :return: The code of this OperationProblem.  # noqa: E501
        :rtype: str
        """
        return self._code

    @code.setter
    def code(self, code):
        """Sets the code of this OperationProblem.

        An error code that identifies the type of error that occurred.  # noqa: E501

        :param code: The code of this OperationProblem.  # noqa: E501
        :type: str
        """
        if code is None:
            raise ValueError("Invalid value for `code`, must not be `None`")  # noqa: E501
        if code is not None and len(code) > 256:
            raise ValueError("Invalid value for `code`, length must be less than or equal to `256`")  # noqa: E501
        if code is not None and len(code) < 1:
            raise ValueError("Invalid value for `code`, length must be greater than or equal to `1`")  # noqa: E501

        self._code = code

    @property
    def details(self):
        """Gets the details of this OperationProblem.  # noqa: E501

        Additional details that can help the caller understand or fix the issue.  # noqa: E501

        :return: The details of this OperationProblem.  # noqa: E501
        :rtype: str
        """
        return self._details

    @details.setter
    def details(self, details):
        """Sets the details of this OperationProblem.

        Additional details that can help the caller understand or fix the issue.  # noqa: E501

        :param details: The details of this OperationProblem.  # noqa: E501
        :type: str
        """
        if details is not None and len(details) > 8192:
            raise ValueError("Invalid value for `details`, length must be less than or equal to `8192`")  # noqa: E501
        if details is not None and len(details) < 0:
            raise ValueError("Invalid value for `details`, length must be greater than or equal to `0`")  # noqa: E501

        self._details = details

    @property
    def message(self):
        """Gets the message of this OperationProblem.  # noqa: E501

        A message that describes the error condition.  # noqa: E501

        :return: The message of this OperationProblem.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this OperationProblem.

        A message that describes the error condition.  # noqa: E501

        :param message: The message of this OperationProblem.  # noqa: E501
        :type: str
        """
        if message is None:
            raise ValueError("Invalid value for `message`, must not be `None`")  # noqa: E501
        if message is not None and len(message) > 2048:
            raise ValueError("Invalid value for `message`, length must be less than or equal to `2048`")  # noqa: E501
        if message is not None and len(message) < 1:
            raise ValueError("Invalid value for `message`, length must be greater than or equal to `1`")  # noqa: E501

        self._message = message

    @property
    def severity(self):
        """Gets the severity of this OperationProblem.  # noqa: E501

        The severity of the problem. Possible values: `WARNING`, `ERROR`.  # noqa: E501

        :return: The severity of this OperationProblem.  # noqa: E501
        :rtype: str
        """
        return self._severity

    @severity.setter
    def severity(self, severity):
        """Sets the severity of this OperationProblem.

        The severity of the problem. Possible values: `WARNING`, `ERROR`.  # noqa: E501

        :param severity: The severity of this OperationProblem.  # noqa: E501
        :type: str
        """
        if severity is None:
            raise ValueError("Invalid value for `severity`, must not be `None`")  # noqa: E501
        if severity is not None and len(severity) > 1024:
            raise ValueError("Invalid value for `severity`, length must be less than or equal to `1024`")  # noqa: E501
        if severity is not None and len(severity) < 1:
            raise ValueError("Invalid value for `severity`, length must be greater than or equal to `1`")  # noqa: E501

        self._severity = severity

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OperationProblem, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OperationProblem):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
