# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class TaxRate(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cess_rate': 'float',
        'gst_rate': 'float',
        'tax_type': 'str'
    }

    attribute_map = {
        'cess_rate': 'cessRate',
        'gst_rate': 'gstRate',
        'tax_type': 'taxType'
    }

    def __init__(self, cess_rate=None, gst_rate=None, tax_type=None):  # noqa: E501
        """TaxRate - a model defined in Swagger"""  # noqa: E501

        self._cess_rate = None
        self._gst_rate = None
        self._tax_type = None
        self.discriminator = None

        if cess_rate is not None:
            self.cess_rate = cess_rate
        if gst_rate is not None:
            self.gst_rate = gst_rate
        if tax_type is not None:
            self.tax_type = tax_type

    @property
    def cess_rate(self):
        """Gets the cess_rate of this TaxRate.  # noqa: E501

        Rate of cess tax.  # noqa: E501

        :return: The cess_rate of this TaxRate.  # noqa: E501
        :rtype: float
        """
        return self._cess_rate

    @cess_rate.setter
    def cess_rate(self, cess_rate):
        """Sets the cess_rate of this TaxRate.

        Rate of cess tax.  # noqa: E501

        :param cess_rate: The cess_rate of this TaxRate.  # noqa: E501
        :type: float
        """

        self._cess_rate = cess_rate

    @property
    def gst_rate(self):
        """Gets the gst_rate of this TaxRate.  # noqa: E501

        Rate of gst tax.  # noqa: E501

        :return: The gst_rate of this TaxRate.  # noqa: E501
        :rtype: float
        """
        return self._gst_rate

    @gst_rate.setter
    def gst_rate(self, gst_rate):
        """Sets the gst_rate of this TaxRate.

        Rate of gst tax.  # noqa: E501

        :param gst_rate: The gst_rate of this TaxRate.  # noqa: E501
        :type: float
        """

        self._gst_rate = gst_rate

    @property
    def tax_type(self):
        """Gets the tax_type of this TaxRate.  # noqa: E501

        Type of tax. Possible values: `CGST`, `SGST`, `IGST`, `TOTAL_TAX`.  # noqa: E501

        :return: The tax_type of this TaxRate.  # noqa: E501
        :rtype: str
        """
        return self._tax_type

    @tax_type.setter
    def tax_type(self, tax_type):
        """Sets the tax_type of this TaxRate.

        Type of tax. Possible values: `CGST`, `SGST`, `IGST`, `TOTAL_TAX`.  # noqa: E501

        :param tax_type: The tax_type of this TaxRate.  # noqa: E501
        :type: str
        """
        if tax_type is not None and len(tax_type) > 1024:
            raise ValueError("Invalid value for `tax_type`, length must be less than or equal to `1024`")  # noqa: E501
        if tax_type is not None and len(tax_type) < 1:
            raise ValueError("Invalid value for `tax_type`, length must be greater than or equal to `1`")  # noqa: E501

        self._tax_type = tax_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaxRate, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaxRate):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
