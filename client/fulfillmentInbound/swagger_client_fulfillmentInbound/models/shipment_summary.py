# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ShipmentSummary(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'shipment_id': 'str',
        'status': 'str'
    }

    attribute_map = {
        'shipment_id': 'shipmentId',
        'status': 'status'
    }

    def __init__(self, shipment_id=None, status=None):  # noqa: E501
        """ShipmentSummary - a model defined in Swagger"""  # noqa: E501

        self._shipment_id = None
        self._status = None
        self.discriminator = None

        self.shipment_id = shipment_id
        self.status = status

    @property
    def shipment_id(self):
        """Gets the shipment_id of this ShipmentSummary.  # noqa: E501

        Identifier of a shipment. A shipment contains the boxes and units being inbounded.  # noqa: E501

        :return: The shipment_id of this ShipmentSummary.  # noqa: E501
        :rtype: str
        """
        return self._shipment_id

    @shipment_id.setter
    def shipment_id(self, shipment_id):
        """Sets the shipment_id of this ShipmentSummary.

        Identifier of a shipment. A shipment contains the boxes and units being inbounded.  # noqa: E501

        :param shipment_id: The shipment_id of this ShipmentSummary.  # noqa: E501
        :type: str
        """
        if shipment_id is None:
            raise ValueError("Invalid value for `shipment_id`, must not be `None`")  # noqa: E501
        if shipment_id is not None and len(shipment_id) > 38:
            raise ValueError("Invalid value for `shipment_id`, length must be less than or equal to `38`")  # noqa: E501
        if shipment_id is not None and len(shipment_id) < 38:
            raise ValueError("Invalid value for `shipment_id`, length must be greater than or equal to `38`")  # noqa: E501
        if shipment_id is not None and not re.search(r'^[a-zA-Z0-9-]*$', shipment_id):  # noqa: E501
            raise ValueError(r"Invalid value for `shipment_id`, must be a follow pattern or equal to `/^[a-zA-Z0-9-]*$/`")  # noqa: E501

        self._shipment_id = shipment_id

    @property
    def status(self):
        """Gets the status of this ShipmentSummary.  # noqa: E501

        The status of a shipment. The state of the shipment will typically start as `UNCONFIRMED`, then transition to `WORKING` after a placement option has been confirmed, and then to `READY_TO_SHIP` once labels are generated.  Possible values: `ABANDONED`, `CANCELLED`, `CHECKED_IN`, `CLOSED`, `DELETED`, `DELIVERED`, `IN_TRANSIT`, `MIXED`, `READY_TO_SHIP`, `RECEIVING`, `SHIPPED`, `UNCONFIRMED`, `WORKING`  # noqa: E501

        :return: The status of this ShipmentSummary.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ShipmentSummary.

        The status of a shipment. The state of the shipment will typically start as `UNCONFIRMED`, then transition to `WORKING` after a placement option has been confirmed, and then to `READY_TO_SHIP` once labels are generated.  Possible values: `ABANDONED`, `CANCELLED`, `CHECKED_IN`, `CLOSED`, `DELETED`, `DELIVERED`, `IN_TRANSIT`, `MIXED`, `READY_TO_SHIP`, `RECEIVING`, `SHIPPED`, `UNCONFIRMED`, `WORKING`  # noqa: E501

        :param status: The status of this ShipmentSummary.  # noqa: E501
        :type: str
        """
        if status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501
        if status is not None and len(status) > 1024:
            raise ValueError("Invalid value for `status`, length must be less than or equal to `1024`")  # noqa: E501
        if status is not None and len(status) < 1:
            raise ValueError("Invalid value for `status`, length must be greater than or equal to `1`")  # noqa: E501

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ShipmentSummary, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ShipmentSummary):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
