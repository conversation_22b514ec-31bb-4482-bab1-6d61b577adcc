# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class GenerateDeliveryWindowOptionsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'operation_id': 'str'
    }

    attribute_map = {
        'operation_id': 'operationId'
    }

    def __init__(self, operation_id=None):  # noqa: E501
        """GenerateDeliveryWindowOptionsResponse - a model defined in Swagger"""  # noqa: E501

        self._operation_id = None
        self.discriminator = None

        self.operation_id = operation_id

    @property
    def operation_id(self):
        """Gets the operation_id of this GenerateDeliveryWindowOptionsResponse.  # noqa: E501

        UUID for the given operation.  # noqa: E501

        :return: The operation_id of this GenerateDeliveryWindowOptionsResponse.  # noqa: E501
        :rtype: str
        """
        return self._operation_id

    @operation_id.setter
    def operation_id(self, operation_id):
        """Sets the operation_id of this GenerateDeliveryWindowOptionsResponse.

        UUID for the given operation.  # noqa: E501

        :param operation_id: The operation_id of this GenerateDeliveryWindowOptionsResponse.  # noqa: E501
        :type: str
        """
        if operation_id is None:
            raise ValueError("Invalid value for `operation_id`, must not be `None`")  # noqa: E501
        if operation_id is not None and len(operation_id) > 38:
            raise ValueError("Invalid value for `operation_id`, length must be less than or equal to `38`")  # noqa: E501
        if operation_id is not None and len(operation_id) < 36:
            raise ValueError("Invalid value for `operation_id`, length must be greater than or equal to `36`")  # noqa: E501
        if operation_id is not None and not re.search(r'^[a-zA-Z0-9-]*$', operation_id):  # noqa: E501
            raise ValueError(r"Invalid value for `operation_id`, must be a follow pattern or equal to `/^[a-zA-Z0-9-]*$/`")  # noqa: E501

        self._operation_id = operation_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GenerateDeliveryWindowOptionsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GenerateDeliveryWindowOptionsResponse):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
