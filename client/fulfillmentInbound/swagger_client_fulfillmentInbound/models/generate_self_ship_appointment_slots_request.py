# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class GenerateSelfShipAppointmentSlotsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'desired_end_date': 'datetime',
        'desired_start_date': 'datetime'
    }

    attribute_map = {
        'desired_end_date': 'desiredEndDate',
        'desired_start_date': 'desiredStartDate'
    }

    def __init__(self, desired_end_date=None, desired_start_date=None):  # noqa: E501
        """GenerateSelfShipAppointmentSlotsRequest - a model defined in Swagger"""  # noqa: E501

        self._desired_end_date = None
        self._desired_start_date = None
        self.discriminator = None

        if desired_end_date is not None:
            self.desired_end_date = desired_end_date
        if desired_start_date is not None:
            self.desired_start_date = desired_start_date

    @property
    def desired_end_date(self):
        """Gets the desired_end_date of this GenerateSelfShipAppointmentSlotsRequest.  # noqa: E501

        The desired end date. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format.  # noqa: E501

        :return: The desired_end_date of this GenerateSelfShipAppointmentSlotsRequest.  # noqa: E501
        :rtype: datetime
        """
        return self._desired_end_date

    @desired_end_date.setter
    def desired_end_date(self, desired_end_date):
        """Sets the desired_end_date of this GenerateSelfShipAppointmentSlotsRequest.

        The desired end date. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format.  # noqa: E501

        :param desired_end_date: The desired_end_date of this GenerateSelfShipAppointmentSlotsRequest.  # noqa: E501
        :type: datetime
        """

        self._desired_end_date = desired_end_date

    @property
    def desired_start_date(self):
        """Gets the desired_start_date of this GenerateSelfShipAppointmentSlotsRequest.  # noqa: E501

        The desired start date. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format.  # noqa: E501

        :return: The desired_start_date of this GenerateSelfShipAppointmentSlotsRequest.  # noqa: E501
        :rtype: datetime
        """
        return self._desired_start_date

    @desired_start_date.setter
    def desired_start_date(self, desired_start_date):
        """Sets the desired_start_date of this GenerateSelfShipAppointmentSlotsRequest.

        The desired start date. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format.  # noqa: E501

        :param desired_start_date: The desired_start_date of this GenerateSelfShipAppointmentSlotsRequest.  # noqa: E501
        :type: datetime
        """

        self._desired_start_date = desired_start_date

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GenerateSelfShipAppointmentSlotsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GenerateSelfShipAppointmentSlotsRequest):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
