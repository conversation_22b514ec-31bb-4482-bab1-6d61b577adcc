# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class Pagination(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'next_token': 'str'
    }

    attribute_map = {
        'next_token': 'nextToken'
    }

    def __init__(self, next_token=None):  # noqa: E501
        """Pagination - a model defined in Swagger"""  # noqa: E501

        self._next_token = None
        self.discriminator = None

        if next_token is not None:
            self.next_token = next_token

    @property
    def next_token(self):
        """Gets the next_token of this Pagination.  # noqa: E501

        When present, pass this string token in the next request to return the next response page.  # noqa: E501

        :return: The next_token of this Pagination.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this Pagination.

        When present, pass this string token in the next request to return the next response page.  # noqa: E501

        :param next_token: The next_token of this Pagination.  # noqa: E501
        :type: str
        """
        if next_token is not None and len(next_token) > 1024:
            raise ValueError("Invalid value for `next_token`, length must be less than or equal to `1024`")  # noqa: E501
        if next_token is not None and len(next_token) < 1:
            raise ValueError("Invalid value for `next_token`, length must be greater than or equal to `1`")  # noqa: E501

        self._next_token = next_token

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Pagination, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Pagination):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
