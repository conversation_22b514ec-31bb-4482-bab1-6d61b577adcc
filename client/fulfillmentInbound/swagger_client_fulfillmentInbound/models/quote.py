# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class Quote(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cost': 'Currency',
        'expiration': 'datetime',
        'voidable_until': 'datetime'
    }

    attribute_map = {
        'cost': 'cost',
        'expiration': 'expiration',
        'voidable_until': 'voidableUntil'
    }

    def __init__(self, cost=None, expiration=None, voidable_until=None):  # noqa: E501
        """Quote - a model defined in Swagger"""  # noqa: E501

        self._cost = None
        self._expiration = None
        self._voidable_until = None
        self.discriminator = None

        self.cost = cost
        if expiration is not None:
            self.expiration = expiration
        if voidable_until is not None:
            self.voidable_until = voidable_until

    @property
    def cost(self):
        """Gets the cost of this Quote.  # noqa: E501


        :return: The cost of this Quote.  # noqa: E501
        :rtype: Currency
        """
        return self._cost

    @cost.setter
    def cost(self, cost):
        """Sets the cost of this Quote.


        :param cost: The cost of this Quote.  # noqa: E501
        :type: Currency
        """
        if cost is None:
            raise ValueError("Invalid value for `cost`, must not be `None`")  # noqa: E501

        self._cost = cost

    @property
    def expiration(self):
        """Gets the expiration of this Quote.  # noqa: E501

        The time at which this transportation option quote expires. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime with pattern `yyyy-MM-ddTHH:mm:ss.sssZ`.  # noqa: E501

        :return: The expiration of this Quote.  # noqa: E501
        :rtype: datetime
        """
        return self._expiration

    @expiration.setter
    def expiration(self, expiration):
        """Sets the expiration of this Quote.

        The time at which this transportation option quote expires. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime with pattern `yyyy-MM-ddTHH:mm:ss.sssZ`.  # noqa: E501

        :param expiration: The expiration of this Quote.  # noqa: E501
        :type: datetime
        """

        self._expiration = expiration

    @property
    def voidable_until(self):
        """Gets the voidable_until of this Quote.  # noqa: E501

        Voidable until timestamp.  # noqa: E501

        :return: The voidable_until of this Quote.  # noqa: E501
        :rtype: datetime
        """
        return self._voidable_until

    @voidable_until.setter
    def voidable_until(self, voidable_until):
        """Sets the voidable_until of this Quote.

        Voidable until timestamp.  # noqa: E501

        :param voidable_until: The voidable_until of this Quote.  # noqa: E501
        :type: datetime
        """

        self._voidable_until = voidable_until

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Quote, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Quote):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
