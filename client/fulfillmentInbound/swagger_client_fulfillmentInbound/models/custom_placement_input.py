# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class CustomPlacementInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'items': 'list[ItemInput]',
        'warehouse_id': 'str'
    }

    attribute_map = {
        'items': 'items',
        'warehouse_id': 'warehouseId'
    }

    def __init__(self, items=None, warehouse_id=None):  # noqa: E501
        """CustomPlacementInput - a model defined in Swagger"""  # noqa: E501

        self._items = None
        self._warehouse_id = None
        self.discriminator = None

        self.items = items
        self.warehouse_id = warehouse_id

    @property
    def items(self):
        """Gets the items of this CustomPlacementInput.  # noqa: E501

        Items included while creating Inbound Plan.  # noqa: E501

        :return: The items of this CustomPlacementInput.  # noqa: E501
        :rtype: list[ItemInput]
        """
        return self._items

    @items.setter
    def items(self, items):
        """Sets the items of this CustomPlacementInput.

        Items included while creating Inbound Plan.  # noqa: E501

        :param items: The items of this CustomPlacementInput.  # noqa: E501
        :type: list[ItemInput]
        """
        if items is None:
            raise ValueError("Invalid value for `items`, must not be `None`")  # noqa: E501

        self._items = items

    @property
    def warehouse_id(self):
        """Gets the warehouse_id of this CustomPlacementInput.  # noqa: E501

        Warehouse Id.  # noqa: E501

        :return: The warehouse_id of this CustomPlacementInput.  # noqa: E501
        :rtype: str
        """
        return self._warehouse_id

    @warehouse_id.setter
    def warehouse_id(self, warehouse_id):
        """Sets the warehouse_id of this CustomPlacementInput.

        Warehouse Id.  # noqa: E501

        :param warehouse_id: The warehouse_id of this CustomPlacementInput.  # noqa: E501
        :type: str
        """
        if warehouse_id is None:
            raise ValueError("Invalid value for `warehouse_id`, must not be `None`")  # noqa: E501
        if warehouse_id is not None and len(warehouse_id) > 1024:
            raise ValueError("Invalid value for `warehouse_id`, length must be less than or equal to `1024`")  # noqa: E501
        if warehouse_id is not None and len(warehouse_id) < 1:
            raise ValueError("Invalid value for `warehouse_id`, length must be greater than or equal to `1`")  # noqa: E501

        self._warehouse_id = warehouse_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CustomPlacementInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CustomPlacementInput):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
