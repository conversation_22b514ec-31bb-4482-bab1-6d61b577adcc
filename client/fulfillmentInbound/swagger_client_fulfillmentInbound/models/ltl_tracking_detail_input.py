# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class LtlTrackingDetailInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bill_of_lading_number': 'str',
        'freight_bill_number': 'list[str]'
    }

    attribute_map = {
        'bill_of_lading_number': 'billOfLadingNumber',
        'freight_bill_number': 'freightBillNumber'
    }

    def __init__(self, bill_of_lading_number=None, freight_bill_number=None):  # noqa: E501
        """LtlTrackingDetailInput - a model defined in Swagger"""  # noqa: E501

        self._bill_of_lading_number = None
        self._freight_bill_number = None
        self.discriminator = None

        if bill_of_lading_number is not None:
            self.bill_of_lading_number = bill_of_lading_number
        self.freight_bill_number = freight_bill_number

    @property
    def bill_of_lading_number(self):
        """Gets the bill_of_lading_number of this LtlTrackingDetailInput.  # noqa: E501

        The number of the carrier shipment acknowledgement document.  # noqa: E501

        :return: The bill_of_lading_number of this LtlTrackingDetailInput.  # noqa: E501
        :rtype: str
        """
        return self._bill_of_lading_number

    @bill_of_lading_number.setter
    def bill_of_lading_number(self, bill_of_lading_number):
        """Sets the bill_of_lading_number of this LtlTrackingDetailInput.

        The number of the carrier shipment acknowledgement document.  # noqa: E501

        :param bill_of_lading_number: The bill_of_lading_number of this LtlTrackingDetailInput.  # noqa: E501
        :type: str
        """
        if bill_of_lading_number is not None and len(bill_of_lading_number) > 1024:
            raise ValueError("Invalid value for `bill_of_lading_number`, length must be less than or equal to `1024`")  # noqa: E501
        if bill_of_lading_number is not None and len(bill_of_lading_number) < 1:
            raise ValueError("Invalid value for `bill_of_lading_number`, length must be greater than or equal to `1`")  # noqa: E501

        self._bill_of_lading_number = bill_of_lading_number

    @property
    def freight_bill_number(self):
        """Gets the freight_bill_number of this LtlTrackingDetailInput.  # noqa: E501

        Number associated with the freight bill.  # noqa: E501

        :return: The freight_bill_number of this LtlTrackingDetailInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._freight_bill_number

    @freight_bill_number.setter
    def freight_bill_number(self, freight_bill_number):
        """Sets the freight_bill_number of this LtlTrackingDetailInput.

        Number associated with the freight bill.  # noqa: E501

        :param freight_bill_number: The freight_bill_number of this LtlTrackingDetailInput.  # noqa: E501
        :type: list[str]
        """
        if freight_bill_number is None:
            raise ValueError("Invalid value for `freight_bill_number`, must not be `None`")  # noqa: E501

        self._freight_bill_number = freight_bill_number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LtlTrackingDetailInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LtlTrackingDetailInput):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
