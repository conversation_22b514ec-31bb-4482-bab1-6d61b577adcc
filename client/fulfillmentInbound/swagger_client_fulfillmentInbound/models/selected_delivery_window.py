# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class SelectedDeliveryWindow(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'availability_type': 'str',
        'delivery_window_option_id': 'str',
        'editable_until': 'datetime',
        'end_date': 'datetime',
        'start_date': 'datetime'
    }

    attribute_map = {
        'availability_type': 'availabilityType',
        'delivery_window_option_id': 'deliveryWindowOptionId',
        'editable_until': 'editableUntil',
        'end_date': 'endDate',
        'start_date': 'startDate'
    }

    def __init__(self, availability_type=None, delivery_window_option_id=None, editable_until=None, end_date=None, start_date=None):  # noqa: E501
        """SelectedDeliveryWindow - a model defined in Swagger"""  # noqa: E501

        self._availability_type = None
        self._delivery_window_option_id = None
        self._editable_until = None
        self._end_date = None
        self._start_date = None
        self.discriminator = None

        self.availability_type = availability_type
        self.delivery_window_option_id = delivery_window_option_id
        if editable_until is not None:
            self.editable_until = editable_until
        self.end_date = end_date
        self.start_date = start_date

    @property
    def availability_type(self):
        """Gets the availability_type of this SelectedDeliveryWindow.  # noqa: E501

        Identifies type of Delivery Window Availability. Values: `AVAILABLE`, `CONGESTED`  # noqa: E501

        :return: The availability_type of this SelectedDeliveryWindow.  # noqa: E501
        :rtype: str
        """
        return self._availability_type

    @availability_type.setter
    def availability_type(self, availability_type):
        """Sets the availability_type of this SelectedDeliveryWindow.

        Identifies type of Delivery Window Availability. Values: `AVAILABLE`, `CONGESTED`  # noqa: E501

        :param availability_type: The availability_type of this SelectedDeliveryWindow.  # noqa: E501
        :type: str
        """
        if availability_type is None:
            raise ValueError("Invalid value for `availability_type`, must not be `None`")  # noqa: E501

        self._availability_type = availability_type

    @property
    def delivery_window_option_id(self):
        """Gets the delivery_window_option_id of this SelectedDeliveryWindow.  # noqa: E501

        Identifier of a delivery window option. A delivery window option represent one option for when a shipment is expected to be delivered.  # noqa: E501

        :return: The delivery_window_option_id of this SelectedDeliveryWindow.  # noqa: E501
        :rtype: str
        """
        return self._delivery_window_option_id

    @delivery_window_option_id.setter
    def delivery_window_option_id(self, delivery_window_option_id):
        """Sets the delivery_window_option_id of this SelectedDeliveryWindow.

        Identifier of a delivery window option. A delivery window option represent one option for when a shipment is expected to be delivered.  # noqa: E501

        :param delivery_window_option_id: The delivery_window_option_id of this SelectedDeliveryWindow.  # noqa: E501
        :type: str
        """
        if delivery_window_option_id is None:
            raise ValueError("Invalid value for `delivery_window_option_id`, must not be `None`")  # noqa: E501
        if delivery_window_option_id is not None and len(delivery_window_option_id) > 38:
            raise ValueError("Invalid value for `delivery_window_option_id`, length must be less than or equal to `38`")  # noqa: E501
        if delivery_window_option_id is not None and len(delivery_window_option_id) < 36:
            raise ValueError("Invalid value for `delivery_window_option_id`, length must be greater than or equal to `36`")  # noqa: E501
        if delivery_window_option_id is not None and not re.search(r'^[a-zA-Z0-9-]*$', delivery_window_option_id):  # noqa: E501
            raise ValueError(r"Invalid value for `delivery_window_option_id`, must be a follow pattern or equal to `/^[a-zA-Z0-9-]*$/`")  # noqa: E501

        self._delivery_window_option_id = delivery_window_option_id

    @property
    def editable_until(self):
        """Gets the editable_until of this SelectedDeliveryWindow.  # noqa: E501

        The timestamp at which this Window can no longer be edited.  # noqa: E501

        :return: The editable_until of this SelectedDeliveryWindow.  # noqa: E501
        :rtype: datetime
        """
        return self._editable_until

    @editable_until.setter
    def editable_until(self, editable_until):
        """Sets the editable_until of this SelectedDeliveryWindow.

        The timestamp at which this Window can no longer be edited.  # noqa: E501

        :param editable_until: The editable_until of this SelectedDeliveryWindow.  # noqa: E501
        :type: datetime
        """

        self._editable_until = editable_until

    @property
    def end_date(self):
        """Gets the end_date of this SelectedDeliveryWindow.  # noqa: E501

        The end timestamp of the window.  # noqa: E501

        :return: The end_date of this SelectedDeliveryWindow.  # noqa: E501
        :rtype: datetime
        """
        return self._end_date

    @end_date.setter
    def end_date(self, end_date):
        """Sets the end_date of this SelectedDeliveryWindow.

        The end timestamp of the window.  # noqa: E501

        :param end_date: The end_date of this SelectedDeliveryWindow.  # noqa: E501
        :type: datetime
        """
        if end_date is None:
            raise ValueError("Invalid value for `end_date`, must not be `None`")  # noqa: E501

        self._end_date = end_date

    @property
    def start_date(self):
        """Gets the start_date of this SelectedDeliveryWindow.  # noqa: E501

        The start timestamp of the window.  # noqa: E501

        :return: The start_date of this SelectedDeliveryWindow.  # noqa: E501
        :rtype: datetime
        """
        return self._start_date

    @start_date.setter
    def start_date(self, start_date):
        """Sets the start_date of this SelectedDeliveryWindow.

        The start timestamp of the window.  # noqa: E501

        :param start_date: The start_date of this SelectedDeliveryWindow.  # noqa: E501
        :type: datetime
        """
        if start_date is None:
            raise ValueError("Invalid value for `start_date`, must not be `None`")  # noqa: E501

        self._start_date = start_date

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SelectedDeliveryWindow, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SelectedDeliveryWindow):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
