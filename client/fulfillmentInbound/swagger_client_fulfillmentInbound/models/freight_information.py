# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class FreightInformation(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'declared_value': 'Currency',
        'freight_class': 'str'
    }

    attribute_map = {
        'declared_value': 'declaredValue',
        'freight_class': 'freightClass'
    }

    def __init__(self, declared_value=None, freight_class=None):  # noqa: E501
        """FreightInformation - a model defined in Swagger"""  # noqa: E501

        self._declared_value = None
        self._freight_class = None
        self.discriminator = None

        if declared_value is not None:
            self.declared_value = declared_value
        if freight_class is not None:
            self.freight_class = freight_class

    @property
    def declared_value(self):
        """Gets the declared_value of this FreightInformation.  # noqa: E501


        :return: The declared_value of this FreightInformation.  # noqa: E501
        :rtype: Currency
        """
        return self._declared_value

    @declared_value.setter
    def declared_value(self, declared_value):
        """Sets the declared_value of this FreightInformation.


        :param declared_value: The declared_value of this FreightInformation.  # noqa: E501
        :type: Currency
        """

        self._declared_value = declared_value

    @property
    def freight_class(self):
        """Gets the freight_class of this FreightInformation.  # noqa: E501

        Freight class.  Possible values: `NONE`, `FC_50`, `FC_55`, `FC_60`, `FC_65`, `FC_70`, `FC_77_5`, `FC_85`, `FC_92_5`, `FC_100`, `FC_110`, `FC_125`, `FC_150`, `FC_175`, `FC_200`, `FC_250`, `FC_300`, `FC_400`, `FC_500`.  # noqa: E501

        :return: The freight_class of this FreightInformation.  # noqa: E501
        :rtype: str
        """
        return self._freight_class

    @freight_class.setter
    def freight_class(self, freight_class):
        """Sets the freight_class of this FreightInformation.

        Freight class.  Possible values: `NONE`, `FC_50`, `FC_55`, `FC_60`, `FC_65`, `FC_70`, `FC_77_5`, `FC_85`, `FC_92_5`, `FC_100`, `FC_110`, `FC_125`, `FC_150`, `FC_175`, `FC_200`, `FC_250`, `FC_300`, `FC_400`, `FC_500`.  # noqa: E501

        :param freight_class: The freight_class of this FreightInformation.  # noqa: E501
        :type: str
        """
        if freight_class is not None and len(freight_class) > 1024:
            raise ValueError("Invalid value for `freight_class`, length must be less than or equal to `1024`")  # noqa: E501
        if freight_class is not None and len(freight_class) < 1:
            raise ValueError("Invalid value for `freight_class`, length must be greater than or equal to `1`")  # noqa: E501

        self._freight_class = freight_class

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FreightInformation, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FreightInformation):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
