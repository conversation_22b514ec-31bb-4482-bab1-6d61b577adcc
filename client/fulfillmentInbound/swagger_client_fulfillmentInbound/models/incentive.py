# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class Incentive(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'target': 'str',
        'type': 'str',
        'value': 'Currency'
    }

    attribute_map = {
        'description': 'description',
        'target': 'target',
        'type': 'type',
        'value': 'value'
    }

    def __init__(self, description=None, target=None, type=None, value=None):  # noqa: E501
        """Incentive - a model defined in Swagger"""  # noqa: E501

        self._description = None
        self._target = None
        self._type = None
        self._value = None
        self.discriminator = None

        self.description = description
        self.target = target
        self.type = type
        self.value = value

    @property
    def description(self):
        """Gets the description of this Incentive.  # noqa: E501

        Description of the incentive.  # noqa: E501

        :return: The description of this Incentive.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this Incentive.

        Description of the incentive.  # noqa: E501

        :param description: The description of this Incentive.  # noqa: E501
        :type: str
        """
        if description is None:
            raise ValueError("Invalid value for `description`, must not be `None`")  # noqa: E501
        if description is not None and len(description) > 1024:
            raise ValueError("Invalid value for `description`, length must be less than or equal to `1024`")  # noqa: E501
        if description is not None and len(description) < 1:
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def target(self):
        """Gets the target of this Incentive.  # noqa: E501

        Target of the incentive. Possible values: 'Placement Services', 'Fulfillment Fee Discount'.  # noqa: E501

        :return: The target of this Incentive.  # noqa: E501
        :rtype: str
        """
        return self._target

    @target.setter
    def target(self, target):
        """Sets the target of this Incentive.

        Target of the incentive. Possible values: 'Placement Services', 'Fulfillment Fee Discount'.  # noqa: E501

        :param target: The target of this Incentive.  # noqa: E501
        :type: str
        """
        if target is None:
            raise ValueError("Invalid value for `target`, must not be `None`")  # noqa: E501
        if target is not None and len(target) > 1024:
            raise ValueError("Invalid value for `target`, length must be less than or equal to `1024`")  # noqa: E501
        if target is not None and len(target) < 1:
            raise ValueError("Invalid value for `target`, length must be greater than or equal to `1`")  # noqa: E501

        self._target = target

    @property
    def type(self):
        """Gets the type of this Incentive.  # noqa: E501

        Type of incentive. Possible values: `FEE`, `DISCOUNT`.  # noqa: E501

        :return: The type of this Incentive.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this Incentive.

        Type of incentive. Possible values: `FEE`, `DISCOUNT`.  # noqa: E501

        :param type: The type of this Incentive.  # noqa: E501
        :type: str
        """
        if type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501
        if type is not None and len(type) > 1024:
            raise ValueError("Invalid value for `type`, length must be less than or equal to `1024`")  # noqa: E501
        if type is not None and len(type) < 1:
            raise ValueError("Invalid value for `type`, length must be greater than or equal to `1`")  # noqa: E501

        self._type = type

    @property
    def value(self):
        """Gets the value of this Incentive.  # noqa: E501


        :return: The value of this Incentive.  # noqa: E501
        :rtype: Currency
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this Incentive.


        :param value: The value of this Incentive.  # noqa: E501
        :type: Currency
        """
        if value is None:
            raise ValueError("Invalid value for `value`, must not be `None`")  # noqa: E501

        self._value = value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Incentive, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Incentive):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
