# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ListInboundPlanPalletsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'pagination': 'Pagination',
        'pallets': 'list[Pallet]'
    }

    attribute_map = {
        'pagination': 'pagination',
        'pallets': 'pallets'
    }

    def __init__(self, pagination=None, pallets=None):  # noqa: E501
        """ListInboundPlanPalletsResponse - a model defined in Swagger"""  # noqa: E501

        self._pagination = None
        self._pallets = None
        self.discriminator = None

        if pagination is not None:
            self.pagination = pagination
        self.pallets = pallets

    @property
    def pagination(self):
        """Gets the pagination of this ListInboundPlanPalletsResponse.  # noqa: E501


        :return: The pagination of this ListInboundPlanPalletsResponse.  # noqa: E501
        :rtype: Pagination
        """
        return self._pagination

    @pagination.setter
    def pagination(self, pagination):
        """Sets the pagination of this ListInboundPlanPalletsResponse.


        :param pagination: The pagination of this ListInboundPlanPalletsResponse.  # noqa: E501
        :type: Pagination
        """

        self._pagination = pagination

    @property
    def pallets(self):
        """Gets the pallets of this ListInboundPlanPalletsResponse.  # noqa: E501

        The pallets in an inbound plan.  # noqa: E501

        :return: The pallets of this ListInboundPlanPalletsResponse.  # noqa: E501
        :rtype: list[Pallet]
        """
        return self._pallets

    @pallets.setter
    def pallets(self, pallets):
        """Sets the pallets of this ListInboundPlanPalletsResponse.

        The pallets in an inbound plan.  # noqa: E501

        :param pallets: The pallets of this ListInboundPlanPalletsResponse.  # noqa: E501
        :type: list[Pallet]
        """
        if pallets is None:
            raise ValueError("Invalid value for `pallets`, must not be `None`")  # noqa: E501

        self._pallets = pallets

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListInboundPlanPalletsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListInboundPlanPalletsResponse):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
