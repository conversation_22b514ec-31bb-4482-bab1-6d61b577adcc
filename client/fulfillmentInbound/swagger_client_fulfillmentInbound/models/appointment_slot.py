# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class AppointmentSlot(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'slot_id': 'str',
        'slot_time': 'AppointmentSlotTime'
    }

    attribute_map = {
        'slot_id': 'slotId',
        'slot_time': 'slotTime'
    }

    def __init__(self, slot_id=None, slot_time=None):  # noqa: E501
        """AppointmentSlot - a model defined in Swagger"""  # noqa: E501

        self._slot_id = None
        self._slot_time = None
        self.discriminator = None

        self.slot_id = slot_id
        self.slot_time = slot_time

    @property
    def slot_id(self):
        """Gets the slot_id of this AppointmentSlot.  # noqa: E501

        An identifier to a self-ship appointment slot.  # noqa: E501

        :return: The slot_id of this AppointmentSlot.  # noqa: E501
        :rtype: str
        """
        return self._slot_id

    @slot_id.setter
    def slot_id(self, slot_id):
        """Sets the slot_id of this AppointmentSlot.

        An identifier to a self-ship appointment slot.  # noqa: E501

        :param slot_id: The slot_id of this AppointmentSlot.  # noqa: E501
        :type: str
        """
        if slot_id is None:
            raise ValueError("Invalid value for `slot_id`, must not be `None`")  # noqa: E501
        if slot_id is not None and len(slot_id) > 38:
            raise ValueError("Invalid value for `slot_id`, length must be less than or equal to `38`")  # noqa: E501
        if slot_id is not None and len(slot_id) < 38:
            raise ValueError("Invalid value for `slot_id`, length must be greater than or equal to `38`")  # noqa: E501
        if slot_id is not None and not re.search(r'^[a-zA-Z0-9-]*$', slot_id):  # noqa: E501
            raise ValueError(r"Invalid value for `slot_id`, must be a follow pattern or equal to `/^[a-zA-Z0-9-]*$/`")  # noqa: E501

        self._slot_id = slot_id

    @property
    def slot_time(self):
        """Gets the slot_time of this AppointmentSlot.  # noqa: E501


        :return: The slot_time of this AppointmentSlot.  # noqa: E501
        :rtype: AppointmentSlotTime
        """
        return self._slot_time

    @slot_time.setter
    def slot_time(self, slot_time):
        """Sets the slot_time of this AppointmentSlot.


        :param slot_time: The slot_time of this AppointmentSlot.  # noqa: E501
        :type: AppointmentSlotTime
        """
        if slot_time is None:
            raise ValueError("Invalid value for `slot_time`, must not be `None`")  # noqa: E501

        self._slot_time = slot_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AppointmentSlot, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AppointmentSlot):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
