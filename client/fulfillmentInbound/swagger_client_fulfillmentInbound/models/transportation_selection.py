# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class TransportationSelection(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'contact_information': 'ContactInformation',
        'shipment_id': 'str',
        'transportation_option_id': 'str'
    }

    attribute_map = {
        'contact_information': 'contactInformation',
        'shipment_id': 'shipmentId',
        'transportation_option_id': 'transportationOptionId'
    }

    def __init__(self, contact_information=None, shipment_id=None, transportation_option_id=None):  # noqa: E501
        """TransportationSelection - a model defined in Swagger"""  # noqa: E501

        self._contact_information = None
        self._shipment_id = None
        self._transportation_option_id = None
        self.discriminator = None

        if contact_information is not None:
            self.contact_information = contact_information
        self.shipment_id = shipment_id
        self.transportation_option_id = transportation_option_id

    @property
    def contact_information(self):
        """Gets the contact_information of this TransportationSelection.  # noqa: E501


        :return: The contact_information of this TransportationSelection.  # noqa: E501
        :rtype: ContactInformation
        """
        return self._contact_information

    @contact_information.setter
    def contact_information(self, contact_information):
        """Sets the contact_information of this TransportationSelection.


        :param contact_information: The contact_information of this TransportationSelection.  # noqa: E501
        :type: ContactInformation
        """

        self._contact_information = contact_information

    @property
    def shipment_id(self):
        """Gets the shipment_id of this TransportationSelection.  # noqa: E501

        Shipment ID that the transportation Option is for.  # noqa: E501

        :return: The shipment_id of this TransportationSelection.  # noqa: E501
        :rtype: str
        """
        return self._shipment_id

    @shipment_id.setter
    def shipment_id(self, shipment_id):
        """Sets the shipment_id of this TransportationSelection.

        Shipment ID that the transportation Option is for.  # noqa: E501

        :param shipment_id: The shipment_id of this TransportationSelection.  # noqa: E501
        :type: str
        """
        if shipment_id is None:
            raise ValueError("Invalid value for `shipment_id`, must not be `None`")  # noqa: E501
        if shipment_id is not None and len(shipment_id) > 38:
            raise ValueError("Invalid value for `shipment_id`, length must be less than or equal to `38`")  # noqa: E501
        if shipment_id is not None and len(shipment_id) < 38:
            raise ValueError("Invalid value for `shipment_id`, length must be greater than or equal to `38`")  # noqa: E501
        if shipment_id is not None and not re.search(r'^[a-zA-Z0-9-]*$', shipment_id):  # noqa: E501
            raise ValueError(r"Invalid value for `shipment_id`, must be a follow pattern or equal to `/^[a-zA-Z0-9-]*$/`")  # noqa: E501

        self._shipment_id = shipment_id

    @property
    def transportation_option_id(self):
        """Gets the transportation_option_id of this TransportationSelection.  # noqa: E501

        Transportation option being selected for the provided shipment.  # noqa: E501

        :return: The transportation_option_id of this TransportationSelection.  # noqa: E501
        :rtype: str
        """
        return self._transportation_option_id

    @transportation_option_id.setter
    def transportation_option_id(self, transportation_option_id):
        """Sets the transportation_option_id of this TransportationSelection.

        Transportation option being selected for the provided shipment.  # noqa: E501

        :param transportation_option_id: The transportation_option_id of this TransportationSelection.  # noqa: E501
        :type: str
        """
        if transportation_option_id is None:
            raise ValueError("Invalid value for `transportation_option_id`, must not be `None`")  # noqa: E501
        if transportation_option_id is not None and len(transportation_option_id) > 38:
            raise ValueError("Invalid value for `transportation_option_id`, length must be less than or equal to `38`")  # noqa: E501
        if transportation_option_id is not None and len(transportation_option_id) < 38:
            raise ValueError("Invalid value for `transportation_option_id`, length must be greater than or equal to `38`")  # noqa: E501
        if transportation_option_id is not None and not re.search(r'^[a-zA-Z0-9-]*$', transportation_option_id):  # noqa: E501
            raise ValueError(r"Invalid value for `transportation_option_id`, must be a follow pattern or equal to `/^[a-zA-Z0-9-]*$/`")  # noqa: E501

        self._transportation_option_id = transportation_option_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransportationSelection, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransportationSelection):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
