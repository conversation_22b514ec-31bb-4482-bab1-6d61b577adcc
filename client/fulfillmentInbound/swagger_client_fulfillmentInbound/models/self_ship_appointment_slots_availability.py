# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class SelfShipAppointmentSlotsAvailability(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'expires_at': 'datetime',
        'slots': 'list[AppointmentSlot]'
    }

    attribute_map = {
        'expires_at': 'expiresAt',
        'slots': 'slots'
    }

    def __init__(self, expires_at=None, slots=None):  # noqa: E501
        """SelfShipAppointmentSlotsAvailability - a model defined in Swagger"""  # noqa: E501

        self._expires_at = None
        self._slots = None
        self.discriminator = None

        if expires_at is not None:
            self.expires_at = expires_at
        if slots is not None:
            self.slots = slots

    @property
    def expires_at(self):
        """Gets the expires_at of this SelfShipAppointmentSlotsAvailability.  # noqa: E501

        The time at which the self ship appointment slot expires. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format.  # noqa: E501

        :return: The expires_at of this SelfShipAppointmentSlotsAvailability.  # noqa: E501
        :rtype: datetime
        """
        return self._expires_at

    @expires_at.setter
    def expires_at(self, expires_at):
        """Sets the expires_at of this SelfShipAppointmentSlotsAvailability.

        The time at which the self ship appointment slot expires. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format.  # noqa: E501

        :param expires_at: The expires_at of this SelfShipAppointmentSlotsAvailability.  # noqa: E501
        :type: datetime
        """

        self._expires_at = expires_at

    @property
    def slots(self):
        """Gets the slots of this SelfShipAppointmentSlotsAvailability.  # noqa: E501

        A list of appointment slots.  # noqa: E501

        :return: The slots of this SelfShipAppointmentSlotsAvailability.  # noqa: E501
        :rtype: list[AppointmentSlot]
        """
        return self._slots

    @slots.setter
    def slots(self, slots):
        """Sets the slots of this SelfShipAppointmentSlotsAvailability.

        A list of appointment slots.  # noqa: E501

        :param slots: The slots of this SelfShipAppointmentSlotsAvailability.  # noqa: E501
        :type: list[AppointmentSlot]
        """

        self._slots = slots

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SelfShipAppointmentSlotsAvailability, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SelfShipAppointmentSlotsAvailability):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
