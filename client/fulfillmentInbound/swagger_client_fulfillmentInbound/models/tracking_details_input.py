# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class TrackingDetailsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ltl_tracking_detail': 'LtlTrackingDetailInput',
        'spd_tracking_detail': 'SpdTrackingDetailInput'
    }

    attribute_map = {
        'ltl_tracking_detail': 'ltlTrackingDetail',
        'spd_tracking_detail': 'spdTrackingDetail'
    }

    def __init__(self, ltl_tracking_detail=None, spd_tracking_detail=None):  # noqa: E501
        """TrackingDetailsInput - a model defined in Swagger"""  # noqa: E501

        self._ltl_tracking_detail = None
        self._spd_tracking_detail = None
        self.discriminator = None

        if ltl_tracking_detail is not None:
            self.ltl_tracking_detail = ltl_tracking_detail
        if spd_tracking_detail is not None:
            self.spd_tracking_detail = spd_tracking_detail

    @property
    def ltl_tracking_detail(self):
        """Gets the ltl_tracking_detail of this TrackingDetailsInput.  # noqa: E501


        :return: The ltl_tracking_detail of this TrackingDetailsInput.  # noqa: E501
        :rtype: LtlTrackingDetailInput
        """
        return self._ltl_tracking_detail

    @ltl_tracking_detail.setter
    def ltl_tracking_detail(self, ltl_tracking_detail):
        """Sets the ltl_tracking_detail of this TrackingDetailsInput.


        :param ltl_tracking_detail: The ltl_tracking_detail of this TrackingDetailsInput.  # noqa: E501
        :type: LtlTrackingDetailInput
        """

        self._ltl_tracking_detail = ltl_tracking_detail

    @property
    def spd_tracking_detail(self):
        """Gets the spd_tracking_detail of this TrackingDetailsInput.  # noqa: E501


        :return: The spd_tracking_detail of this TrackingDetailsInput.  # noqa: E501
        :rtype: SpdTrackingDetailInput
        """
        return self._spd_tracking_detail

    @spd_tracking_detail.setter
    def spd_tracking_detail(self, spd_tracking_detail):
        """Sets the spd_tracking_detail of this TrackingDetailsInput.


        :param spd_tracking_detail: The spd_tracking_detail of this TrackingDetailsInput.  # noqa: E501
        :type: SpdTrackingDetailInput
        """

        self._spd_tracking_detail = spd_tracking_detail

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TrackingDetailsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TrackingDetailsInput):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
