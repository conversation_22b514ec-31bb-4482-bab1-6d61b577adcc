# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class Shipment(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'amazon_reference_id': 'str',
        'contact_information': 'ContactInformation',
        'dates': 'Dates',
        'destination': 'ShipmentDestination',
        'freight_information': 'FreightInformation',
        'name': 'str',
        'placement_option_id': 'str',
        'selected_delivery_window': 'SelectedDeliveryWindow',
        'selected_transportation_option_id': 'str',
        'self_ship_appointment_details': 'list[SelfShipAppointmentDetails]',
        'shipment_confirmation_id': 'str',
        'shipment_id': 'str',
        'source': 'ShipmentSource',
        'status': 'str',
        'tracking_details': 'TrackingDetails'
    }

    attribute_map = {
        'amazon_reference_id': 'amazonReferenceId',
        'contact_information': 'contactInformation',
        'dates': 'dates',
        'destination': 'destination',
        'freight_information': 'freightInformation',
        'name': 'name',
        'placement_option_id': 'placementOptionId',
        'selected_delivery_window': 'selectedDeliveryWindow',
        'selected_transportation_option_id': 'selectedTransportationOptionId',
        'self_ship_appointment_details': 'selfShipAppointmentDetails',
        'shipment_confirmation_id': 'shipmentConfirmationId',
        'shipment_id': 'shipmentId',
        'source': 'source',
        'status': 'status',
        'tracking_details': 'trackingDetails'
    }

    def __init__(self, amazon_reference_id=None, contact_information=None, dates=None, destination=None, freight_information=None, name=None, placement_option_id=None, selected_delivery_window=None, selected_transportation_option_id=None, self_ship_appointment_details=None, shipment_confirmation_id=None, shipment_id=None, source=None, status=None, tracking_details=None):  # noqa: E501
        """Shipment - a model defined in Swagger"""  # noqa: E501

        self._amazon_reference_id = None
        self._contact_information = None
        self._dates = None
        self._destination = None
        self._freight_information = None
        self._name = None
        self._placement_option_id = None
        self._selected_delivery_window = None
        self._selected_transportation_option_id = None
        self._self_ship_appointment_details = None
        self._shipment_confirmation_id = None
        self._shipment_id = None
        self._source = None
        self._status = None
        self._tracking_details = None
        self.discriminator = None

        if amazon_reference_id is not None:
            self.amazon_reference_id = amazon_reference_id
        if contact_information is not None:
            self.contact_information = contact_information
        if dates is not None:
            self.dates = dates
        self.destination = destination
        if freight_information is not None:
            self.freight_information = freight_information
        if name is not None:
            self.name = name
        self.placement_option_id = placement_option_id
        if selected_delivery_window is not None:
            self.selected_delivery_window = selected_delivery_window
        if selected_transportation_option_id is not None:
            self.selected_transportation_option_id = selected_transportation_option_id
        if self_ship_appointment_details is not None:
            self.self_ship_appointment_details = self_ship_appointment_details
        if shipment_confirmation_id is not None:
            self.shipment_confirmation_id = shipment_confirmation_id
        self.shipment_id = shipment_id
        self.source = source
        if status is not None:
            self.status = status
        if tracking_details is not None:
            self.tracking_details = tracking_details

    @property
    def amazon_reference_id(self):
        """Gets the amazon_reference_id of this Shipment.  # noqa: E501

        A unique identifier created by Amazon that identifies this Amazon-partnered, Less Than Truckload/Full Truckload (LTL/FTL) shipment.  # noqa: E501

        :return: The amazon_reference_id of this Shipment.  # noqa: E501
        :rtype: str
        """
        return self._amazon_reference_id

    @amazon_reference_id.setter
    def amazon_reference_id(self, amazon_reference_id):
        """Sets the amazon_reference_id of this Shipment.

        A unique identifier created by Amazon that identifies this Amazon-partnered, Less Than Truckload/Full Truckload (LTL/FTL) shipment.  # noqa: E501

        :param amazon_reference_id: The amazon_reference_id of this Shipment.  # noqa: E501
        :type: str
        """
        if amazon_reference_id is not None and len(amazon_reference_id) > 1024:
            raise ValueError("Invalid value for `amazon_reference_id`, length must be less than or equal to `1024`")  # noqa: E501
        if amazon_reference_id is not None and len(amazon_reference_id) < 1:
            raise ValueError("Invalid value for `amazon_reference_id`, length must be greater than or equal to `1`")  # noqa: E501

        self._amazon_reference_id = amazon_reference_id

    @property
    def contact_information(self):
        """Gets the contact_information of this Shipment.  # noqa: E501


        :return: The contact_information of this Shipment.  # noqa: E501
        :rtype: ContactInformation
        """
        return self._contact_information

    @contact_information.setter
    def contact_information(self, contact_information):
        """Sets the contact_information of this Shipment.


        :param contact_information: The contact_information of this Shipment.  # noqa: E501
        :type: ContactInformation
        """

        self._contact_information = contact_information

    @property
    def dates(self):
        """Gets the dates of this Shipment.  # noqa: E501


        :return: The dates of this Shipment.  # noqa: E501
        :rtype: Dates
        """
        return self._dates

    @dates.setter
    def dates(self, dates):
        """Sets the dates of this Shipment.


        :param dates: The dates of this Shipment.  # noqa: E501
        :type: Dates
        """

        self._dates = dates

    @property
    def destination(self):
        """Gets the destination of this Shipment.  # noqa: E501


        :return: The destination of this Shipment.  # noqa: E501
        :rtype: ShipmentDestination
        """
        return self._destination

    @destination.setter
    def destination(self, destination):
        """Sets the destination of this Shipment.


        :param destination: The destination of this Shipment.  # noqa: E501
        :type: ShipmentDestination
        """
        if destination is None:
            raise ValueError("Invalid value for `destination`, must not be `None`")  # noqa: E501

        self._destination = destination

    @property
    def freight_information(self):
        """Gets the freight_information of this Shipment.  # noqa: E501


        :return: The freight_information of this Shipment.  # noqa: E501
        :rtype: FreightInformation
        """
        return self._freight_information

    @freight_information.setter
    def freight_information(self, freight_information):
        """Sets the freight_information of this Shipment.


        :param freight_information: The freight_information of this Shipment.  # noqa: E501
        :type: FreightInformation
        """

        self._freight_information = freight_information

    @property
    def name(self):
        """Gets the name of this Shipment.  # noqa: E501

        The name of the shipment.  # noqa: E501

        :return: The name of this Shipment.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this Shipment.

        The name of the shipment.  # noqa: E501

        :param name: The name of this Shipment.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def placement_option_id(self):
        """Gets the placement_option_id of this Shipment.  # noqa: E501

        The identifier of a placement option. A placement option represents the shipment splits and destinations of SKUs.  # noqa: E501

        :return: The placement_option_id of this Shipment.  # noqa: E501
        :rtype: str
        """
        return self._placement_option_id

    @placement_option_id.setter
    def placement_option_id(self, placement_option_id):
        """Sets the placement_option_id of this Shipment.

        The identifier of a placement option. A placement option represents the shipment splits and destinations of SKUs.  # noqa: E501

        :param placement_option_id: The placement_option_id of this Shipment.  # noqa: E501
        :type: str
        """
        if placement_option_id is None:
            raise ValueError("Invalid value for `placement_option_id`, must not be `None`")  # noqa: E501
        if placement_option_id is not None and len(placement_option_id) > 38:
            raise ValueError("Invalid value for `placement_option_id`, length must be less than or equal to `38`")  # noqa: E501
        if placement_option_id is not None and len(placement_option_id) < 38:
            raise ValueError("Invalid value for `placement_option_id`, length must be greater than or equal to `38`")  # noqa: E501
        if placement_option_id is not None and not re.search(r'^[a-zA-Z0-9-]*$', placement_option_id):  # noqa: E501
            raise ValueError(r"Invalid value for `placement_option_id`, must be a follow pattern or equal to `/^[a-zA-Z0-9-]*$/`")  # noqa: E501

        self._placement_option_id = placement_option_id

    @property
    def selected_delivery_window(self):
        """Gets the selected_delivery_window of this Shipment.  # noqa: E501


        :return: The selected_delivery_window of this Shipment.  # noqa: E501
        :rtype: SelectedDeliveryWindow
        """
        return self._selected_delivery_window

    @selected_delivery_window.setter
    def selected_delivery_window(self, selected_delivery_window):
        """Sets the selected_delivery_window of this Shipment.


        :param selected_delivery_window: The selected_delivery_window of this Shipment.  # noqa: E501
        :type: SelectedDeliveryWindow
        """

        self._selected_delivery_window = selected_delivery_window

    @property
    def selected_transportation_option_id(self):
        """Gets the selected_transportation_option_id of this Shipment.  # noqa: E501

        Identifier of a transportation option. A transportation option represent one option for how to send a shipment.  # noqa: E501

        :return: The selected_transportation_option_id of this Shipment.  # noqa: E501
        :rtype: str
        """
        return self._selected_transportation_option_id

    @selected_transportation_option_id.setter
    def selected_transportation_option_id(self, selected_transportation_option_id):
        """Sets the selected_transportation_option_id of this Shipment.

        Identifier of a transportation option. A transportation option represent one option for how to send a shipment.  # noqa: E501

        :param selected_transportation_option_id: The selected_transportation_option_id of this Shipment.  # noqa: E501
        :type: str
        """
        if selected_transportation_option_id is not None and len(selected_transportation_option_id) > 38:
            raise ValueError("Invalid value for `selected_transportation_option_id`, length must be less than or equal to `38`")  # noqa: E501
        if selected_transportation_option_id is not None and len(selected_transportation_option_id) < 38:
            raise ValueError("Invalid value for `selected_transportation_option_id`, length must be greater than or equal to `38`")  # noqa: E501
        if selected_transportation_option_id is not None and not re.search(r'^[a-zA-Z0-9-]*$', selected_transportation_option_id):  # noqa: E501
            raise ValueError(r"Invalid value for `selected_transportation_option_id`, must be a follow pattern or equal to `/^[a-zA-Z0-9-]*$/`")  # noqa: E501

        self._selected_transportation_option_id = selected_transportation_option_id

    @property
    def self_ship_appointment_details(self):
        """Gets the self_ship_appointment_details of this Shipment.  # noqa: E501

        List of self ship appointment details.  # noqa: E501

        :return: The self_ship_appointment_details of this Shipment.  # noqa: E501
        :rtype: list[SelfShipAppointmentDetails]
        """
        return self._self_ship_appointment_details

    @self_ship_appointment_details.setter
    def self_ship_appointment_details(self, self_ship_appointment_details):
        """Sets the self_ship_appointment_details of this Shipment.

        List of self ship appointment details.  # noqa: E501

        :param self_ship_appointment_details: The self_ship_appointment_details of this Shipment.  # noqa: E501
        :type: list[SelfShipAppointmentDetails]
        """

        self._self_ship_appointment_details = self_ship_appointment_details

    @property
    def shipment_confirmation_id(self):
        """Gets the shipment_confirmation_id of this Shipment.  # noqa: E501

        The confirmed shipment ID which shows up on labels (for example, `FBA1234ABCD`).  # noqa: E501

        :return: The shipment_confirmation_id of this Shipment.  # noqa: E501
        :rtype: str
        """
        return self._shipment_confirmation_id

    @shipment_confirmation_id.setter
    def shipment_confirmation_id(self, shipment_confirmation_id):
        """Sets the shipment_confirmation_id of this Shipment.

        The confirmed shipment ID which shows up on labels (for example, `FBA1234ABCD`).  # noqa: E501

        :param shipment_confirmation_id: The shipment_confirmation_id of this Shipment.  # noqa: E501
        :type: str
        """
        if shipment_confirmation_id is not None and len(shipment_confirmation_id) > 1024:
            raise ValueError("Invalid value for `shipment_confirmation_id`, length must be less than or equal to `1024`")  # noqa: E501
        if shipment_confirmation_id is not None and len(shipment_confirmation_id) < 1:
            raise ValueError("Invalid value for `shipment_confirmation_id`, length must be greater than or equal to `1`")  # noqa: E501

        self._shipment_confirmation_id = shipment_confirmation_id

    @property
    def shipment_id(self):
        """Gets the shipment_id of this Shipment.  # noqa: E501

        Identifier of a shipment. A shipment contains the boxes and units being inbounded.  # noqa: E501

        :return: The shipment_id of this Shipment.  # noqa: E501
        :rtype: str
        """
        return self._shipment_id

    @shipment_id.setter
    def shipment_id(self, shipment_id):
        """Sets the shipment_id of this Shipment.

        Identifier of a shipment. A shipment contains the boxes and units being inbounded.  # noqa: E501

        :param shipment_id: The shipment_id of this Shipment.  # noqa: E501
        :type: str
        """
        if shipment_id is None:
            raise ValueError("Invalid value for `shipment_id`, must not be `None`")  # noqa: E501
        if shipment_id is not None and len(shipment_id) > 38:
            raise ValueError("Invalid value for `shipment_id`, length must be less than or equal to `38`")  # noqa: E501
        if shipment_id is not None and len(shipment_id) < 38:
            raise ValueError("Invalid value for `shipment_id`, length must be greater than or equal to `38`")  # noqa: E501
        if shipment_id is not None and not re.search(r'^[a-zA-Z0-9-]*$', shipment_id):  # noqa: E501
            raise ValueError(r"Invalid value for `shipment_id`, must be a follow pattern or equal to `/^[a-zA-Z0-9-]*$/`")  # noqa: E501

        self._shipment_id = shipment_id

    @property
    def source(self):
        """Gets the source of this Shipment.  # noqa: E501


        :return: The source of this Shipment.  # noqa: E501
        :rtype: ShipmentSource
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this Shipment.


        :param source: The source of this Shipment.  # noqa: E501
        :type: ShipmentSource
        """
        if source is None:
            raise ValueError("Invalid value for `source`, must not be `None`")  # noqa: E501

        self._source = source

    @property
    def status(self):
        """Gets the status of this Shipment.  # noqa: E501

        The status of a shipment. The state of the shipment will typically start as `UNCONFIRMED`, then transition to `WORKING` after a placement option has been confirmed, and then to `READY_TO_SHIP` once labels are generated.  Possible values: `ABANDONED`, `CANCELLED`, `CHECKED_IN`, `CLOSED`, `DELETED`, `DELIVERED`, `IN_TRANSIT`, `MIXED`, `READY_TO_SHIP`, `RECEIVING`, `SHIPPED`, `UNCONFIRMED`, `WORKING`  # noqa: E501

        :return: The status of this Shipment.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this Shipment.

        The status of a shipment. The state of the shipment will typically start as `UNCONFIRMED`, then transition to `WORKING` after a placement option has been confirmed, and then to `READY_TO_SHIP` once labels are generated.  Possible values: `ABANDONED`, `CANCELLED`, `CHECKED_IN`, `CLOSED`, `DELETED`, `DELIVERED`, `IN_TRANSIT`, `MIXED`, `READY_TO_SHIP`, `RECEIVING`, `SHIPPED`, `UNCONFIRMED`, `WORKING`  # noqa: E501

        :param status: The status of this Shipment.  # noqa: E501
        :type: str
        """
        if status is not None and len(status) > 1024:
            raise ValueError("Invalid value for `status`, length must be less than or equal to `1024`")  # noqa: E501
        if status is not None and len(status) < 1:
            raise ValueError("Invalid value for `status`, length must be greater than or equal to `1`")  # noqa: E501

        self._status = status

    @property
    def tracking_details(self):
        """Gets the tracking_details of this Shipment.  # noqa: E501


        :return: The tracking_details of this Shipment.  # noqa: E501
        :rtype: TrackingDetails
        """
        return self._tracking_details

    @tracking_details.setter
    def tracking_details(self, tracking_details):
        """Sets the tracking_details of this Shipment.


        :param tracking_details: The tracking_details of this Shipment.  # noqa: E501
        :type: TrackingDetails
        """

        self._tracking_details = tracking_details

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Shipment, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Shipment):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
