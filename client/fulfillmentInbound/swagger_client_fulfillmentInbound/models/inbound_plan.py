# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class InboundPlan(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'datetime',
        'inbound_plan_id': 'str',
        'last_updated_at': 'datetime',
        'marketplace_ids': 'list[str]',
        'name': 'str',
        'packing_options': 'list[PackingOptionSummary]',
        'placement_options': 'list[PlacementOptionSummary]',
        'shipments': 'list[ShipmentSummary]',
        'source_address': 'Address',
        'status': 'str'
    }

    attribute_map = {
        'created_at': 'createdAt',
        'inbound_plan_id': 'inboundPlanId',
        'last_updated_at': 'lastUpdatedAt',
        'marketplace_ids': 'marketplaceIds',
        'name': 'name',
        'packing_options': 'packingOptions',
        'placement_options': 'placementOptions',
        'shipments': 'shipments',
        'source_address': 'sourceAddress',
        'status': 'status'
    }

    def __init__(self, created_at=None, inbound_plan_id=None, last_updated_at=None, marketplace_ids=None, name=None, packing_options=None, placement_options=None, shipments=None, source_address=None, status=None):  # noqa: E501
        """InboundPlan - a model defined in Swagger"""  # noqa: E501

        self._created_at = None
        self._inbound_plan_id = None
        self._last_updated_at = None
        self._marketplace_ids = None
        self._name = None
        self._packing_options = None
        self._placement_options = None
        self._shipments = None
        self._source_address = None
        self._status = None
        self.discriminator = None

        self.created_at = created_at
        self.inbound_plan_id = inbound_plan_id
        self.last_updated_at = last_updated_at
        self.marketplace_ids = marketplace_ids
        self.name = name
        if packing_options is not None:
            self.packing_options = packing_options
        if placement_options is not None:
            self.placement_options = placement_options
        if shipments is not None:
            self.shipments = shipments
        self.source_address = source_address
        self.status = status

    @property
    def created_at(self):
        """Gets the created_at of this InboundPlan.  # noqa: E501

        The time at which the inbound plan was created. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime with pattern `yyyy-MM-ddTHH:mm:ssZ`.  # noqa: E501

        :return: The created_at of this InboundPlan.  # noqa: E501
        :rtype: datetime
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this InboundPlan.

        The time at which the inbound plan was created. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime with pattern `yyyy-MM-ddTHH:mm:ssZ`.  # noqa: E501

        :param created_at: The created_at of this InboundPlan.  # noqa: E501
        :type: datetime
        """
        if created_at is None:
            raise ValueError("Invalid value for `created_at`, must not be `None`")  # noqa: E501

        self._created_at = created_at

    @property
    def inbound_plan_id(self):
        """Gets the inbound_plan_id of this InboundPlan.  # noqa: E501

        Identifier of an inbound plan.  # noqa: E501

        :return: The inbound_plan_id of this InboundPlan.  # noqa: E501
        :rtype: str
        """
        return self._inbound_plan_id

    @inbound_plan_id.setter
    def inbound_plan_id(self, inbound_plan_id):
        """Sets the inbound_plan_id of this InboundPlan.

        Identifier of an inbound plan.  # noqa: E501

        :param inbound_plan_id: The inbound_plan_id of this InboundPlan.  # noqa: E501
        :type: str
        """
        if inbound_plan_id is None:
            raise ValueError("Invalid value for `inbound_plan_id`, must not be `None`")  # noqa: E501
        if inbound_plan_id is not None and len(inbound_plan_id) > 38:
            raise ValueError("Invalid value for `inbound_plan_id`, length must be less than or equal to `38`")  # noqa: E501
        if inbound_plan_id is not None and len(inbound_plan_id) < 38:
            raise ValueError("Invalid value for `inbound_plan_id`, length must be greater than or equal to `38`")  # noqa: E501
        if inbound_plan_id is not None and not re.search(r'^[a-zA-Z0-9-]*$', inbound_plan_id):  # noqa: E501
            raise ValueError(r"Invalid value for `inbound_plan_id`, must be a follow pattern or equal to `/^[a-zA-Z0-9-]*$/`")  # noqa: E501

        self._inbound_plan_id = inbound_plan_id

    @property
    def last_updated_at(self):
        """Gets the last_updated_at of this InboundPlan.  # noqa: E501

        The time at which the inbound plan was last updated. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format with pattern `yyyy-MM-ddTHH:mm:ssZ`.  # noqa: E501

        :return: The last_updated_at of this InboundPlan.  # noqa: E501
        :rtype: datetime
        """
        return self._last_updated_at

    @last_updated_at.setter
    def last_updated_at(self, last_updated_at):
        """Sets the last_updated_at of this InboundPlan.

        The time at which the inbound plan was last updated. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format with pattern `yyyy-MM-ddTHH:mm:ssZ`.  # noqa: E501

        :param last_updated_at: The last_updated_at of this InboundPlan.  # noqa: E501
        :type: datetime
        """
        if last_updated_at is None:
            raise ValueError("Invalid value for `last_updated_at`, must not be `None`")  # noqa: E501

        self._last_updated_at = last_updated_at

    @property
    def marketplace_ids(self):
        """Gets the marketplace_ids of this InboundPlan.  # noqa: E501

        A list of marketplace IDs.  # noqa: E501

        :return: The marketplace_ids of this InboundPlan.  # noqa: E501
        :rtype: list[str]
        """
        return self._marketplace_ids

    @marketplace_ids.setter
    def marketplace_ids(self, marketplace_ids):
        """Sets the marketplace_ids of this InboundPlan.

        A list of marketplace IDs.  # noqa: E501

        :param marketplace_ids: The marketplace_ids of this InboundPlan.  # noqa: E501
        :type: list[str]
        """
        if marketplace_ids is None:
            raise ValueError("Invalid value for `marketplace_ids`, must not be `None`")  # noqa: E501

        self._marketplace_ids = marketplace_ids

    @property
    def name(self):
        """Gets the name of this InboundPlan.  # noqa: E501

        Human-readable name of the inbound plan.  # noqa: E501

        :return: The name of this InboundPlan.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this InboundPlan.

        Human-readable name of the inbound plan.  # noqa: E501

        :param name: The name of this InboundPlan.  # noqa: E501
        :type: str
        """
        if name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def packing_options(self):
        """Gets the packing_options of this InboundPlan.  # noqa: E501

        Packing options for the inbound plan. This property will be populated when it has been generated via the corresponding operation. If there is a chosen placement option, only packing options for that placement option will be returned. If there are confirmed shipments, only packing options for those shipments will be returned. Query the packing option for more details.  # noqa: E501

        :return: The packing_options of this InboundPlan.  # noqa: E501
        :rtype: list[PackingOptionSummary]
        """
        return self._packing_options

    @packing_options.setter
    def packing_options(self, packing_options):
        """Sets the packing_options of this InboundPlan.

        Packing options for the inbound plan. This property will be populated when it has been generated via the corresponding operation. If there is a chosen placement option, only packing options for that placement option will be returned. If there are confirmed shipments, only packing options for those shipments will be returned. Query the packing option for more details.  # noqa: E501

        :param packing_options: The packing_options of this InboundPlan.  # noqa: E501
        :type: list[PackingOptionSummary]
        """

        self._packing_options = packing_options

    @property
    def placement_options(self):
        """Gets the placement_options of this InboundPlan.  # noqa: E501

        Placement options for the inbound plan. This property will be populated when it has been generated via the corresponding operation. If there is a chosen placement option, that will be the only returned option. Query the placement option for more details.  # noqa: E501

        :return: The placement_options of this InboundPlan.  # noqa: E501
        :rtype: list[PlacementOptionSummary]
        """
        return self._placement_options

    @placement_options.setter
    def placement_options(self, placement_options):
        """Sets the placement_options of this InboundPlan.

        Placement options for the inbound plan. This property will be populated when it has been generated via the corresponding operation. If there is a chosen placement option, that will be the only returned option. Query the placement option for more details.  # noqa: E501

        :param placement_options: The placement_options of this InboundPlan.  # noqa: E501
        :type: list[PlacementOptionSummary]
        """

        self._placement_options = placement_options

    @property
    def shipments(self):
        """Gets the shipments of this InboundPlan.  # noqa: E501

        A list of shipment IDs for the inbound plan. This property is populated when it has been generated with the `confirmPlacementOptions` operation. Only shipments from the chosen placement option are returned. Query the shipment for more details.  # noqa: E501

        :return: The shipments of this InboundPlan.  # noqa: E501
        :rtype: list[ShipmentSummary]
        """
        return self._shipments

    @shipments.setter
    def shipments(self, shipments):
        """Sets the shipments of this InboundPlan.

        A list of shipment IDs for the inbound plan. This property is populated when it has been generated with the `confirmPlacementOptions` operation. Only shipments from the chosen placement option are returned. Query the shipment for more details.  # noqa: E501

        :param shipments: The shipments of this InboundPlan.  # noqa: E501
        :type: list[ShipmentSummary]
        """

        self._shipments = shipments

    @property
    def source_address(self):
        """Gets the source_address of this InboundPlan.  # noqa: E501


        :return: The source_address of this InboundPlan.  # noqa: E501
        :rtype: Address
        """
        return self._source_address

    @source_address.setter
    def source_address(self, source_address):
        """Sets the source_address of this InboundPlan.


        :param source_address: The source_address of this InboundPlan.  # noqa: E501
        :type: Address
        """
        if source_address is None:
            raise ValueError("Invalid value for `source_address`, must not be `None`")  # noqa: E501

        self._source_address = source_address

    @property
    def status(self):
        """Gets the status of this InboundPlan.  # noqa: E501

        Current status of the inbound plan. Possible values: `ACTIVE`, `VOIDED`, `SHIPPED`, `ERRORED`.  # noqa: E501

        :return: The status of this InboundPlan.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this InboundPlan.

        Current status of the inbound plan. Possible values: `ACTIVE`, `VOIDED`, `SHIPPED`, `ERRORED`.  # noqa: E501

        :param status: The status of this InboundPlan.  # noqa: E501
        :type: str
        """
        if status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501
        if status is not None and len(status) > 1024:
            raise ValueError("Invalid value for `status`, length must be less than or equal to `1024`")  # noqa: E501
        if status is not None and len(status) < 1:
            raise ValueError("Invalid value for `status`, length must be greater than or equal to `1`")  # noqa: E501

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InboundPlan, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InboundPlan):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
