# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class PrepInstruction(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'fee': 'Currency',
        'prep_owner': 'str',
        'prep_type': 'str'
    }

    attribute_map = {
        'fee': 'fee',
        'prep_owner': 'prepOwner',
        'prep_type': 'prepType'
    }

    def __init__(self, fee=None, prep_owner=None, prep_type=None):  # noqa: E501
        """PrepInstruction - a model defined in Swagger"""  # noqa: E501

        self._fee = None
        self._prep_owner = None
        self._prep_type = None
        self.discriminator = None

        if fee is not None:
            self.fee = fee
        if prep_owner is not None:
            self.prep_owner = prep_owner
        if prep_type is not None:
            self.prep_type = prep_type

    @property
    def fee(self):
        """Gets the fee of this PrepInstruction.  # noqa: E501


        :return: The fee of this PrepInstruction.  # noqa: E501
        :rtype: Currency
        """
        return self._fee

    @fee.setter
    def fee(self, fee):
        """Sets the fee of this PrepInstruction.


        :param fee: The fee of this PrepInstruction.  # noqa: E501
        :type: Currency
        """

        self._fee = fee

    @property
    def prep_owner(self):
        """Gets the prep_owner of this PrepInstruction.  # noqa: E501

        In some situations, special preparations are required for items and this field reflects the owner of the preparations. Options include `AMAZON`, `SELLER` or `NONE`.  # noqa: E501

        :return: The prep_owner of this PrepInstruction.  # noqa: E501
        :rtype: str
        """
        return self._prep_owner

    @prep_owner.setter
    def prep_owner(self, prep_owner):
        """Sets the prep_owner of this PrepInstruction.

        In some situations, special preparations are required for items and this field reflects the owner of the preparations. Options include `AMAZON`, `SELLER` or `NONE`.  # noqa: E501

        :param prep_owner: The prep_owner of this PrepInstruction.  # noqa: E501
        :type: str
        """
        if prep_owner is not None and len(prep_owner) > 1024:
            raise ValueError("Invalid value for `prep_owner`, length must be less than or equal to `1024`")  # noqa: E501
        if prep_owner is not None and len(prep_owner) < 1:
            raise ValueError("Invalid value for `prep_owner`, length must be greater than or equal to `1`")  # noqa: E501

        self._prep_owner = prep_owner

    @property
    def prep_type(self):
        """Gets the prep_type of this PrepInstruction.  # noqa: E501

        Type of preparation that should be done.  Possible values: `ITEM_LABELING`, `ITEM_BUBBLEWRAP`, `ITEM_POLYBAGGING`, `ITEM_TAPING`, `ITEM_BLACK_SHRINKWRAP`, `ITEM_HANG_GARMENT`, `ITEM_BOXING`, `ITEM_SETCREAT`, `ITEM_RMOVHANG`, `ITEM_SUFFOSTK`, `ITEM_CAP_SEALING`, `ITEM_DEBUNDLE`, `ITEM_SETSTK`, `ITEM_SIOC`, `ITEM_NO_PREP`, `ADULT`, `BABY`, `TEXTILE`, `HANGER`, `FRAGILE`, `LIQUID`, `SHARP`, `SMALL`, `PERFORATED`, `GRANULAR`, `SET`, `FC_PROVIDED`, `UNKNOWN`, `NONE`.  # noqa: E501

        :return: The prep_type of this PrepInstruction.  # noqa: E501
        :rtype: str
        """
        return self._prep_type

    @prep_type.setter
    def prep_type(self, prep_type):
        """Sets the prep_type of this PrepInstruction.

        Type of preparation that should be done.  Possible values: `ITEM_LABELING`, `ITEM_BUBBLEWRAP`, `ITEM_POLYBAGGING`, `ITEM_TAPING`, `ITEM_BLACK_SHRINKWRAP`, `ITEM_HANG_GARMENT`, `ITEM_BOXING`, `ITEM_SETCREAT`, `ITEM_RMOVHANG`, `ITEM_SUFFOSTK`, `ITEM_CAP_SEALING`, `ITEM_DEBUNDLE`, `ITEM_SETSTK`, `ITEM_SIOC`, `ITEM_NO_PREP`, `ADULT`, `BABY`, `TEXTILE`, `HANGER`, `FRAGILE`, `LIQUID`, `SHARP`, `SMALL`, `PERFORATED`, `GRANULAR`, `SET`, `FC_PROVIDED`, `UNKNOWN`, `NONE`.  # noqa: E501

        :param prep_type: The prep_type of this PrepInstruction.  # noqa: E501
        :type: str
        """
        if prep_type is not None and len(prep_type) > 1024:
            raise ValueError("Invalid value for `prep_type`, length must be less than or equal to `1024`")  # noqa: E501
        if prep_type is not None and len(prep_type) < 1:
            raise ValueError("Invalid value for `prep_type`, length must be greater than or equal to `1`")  # noqa: E501

        self._prep_type = prep_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PrepInstruction, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PrepInstruction):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
