# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ComplianceDetail(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asin': 'str',
        'fnsku': 'str',
        'msku': 'str',
        'tax_details': 'TaxDetails'
    }

    attribute_map = {
        'asin': 'asin',
        'fnsku': 'fnsku',
        'msku': 'msku',
        'tax_details': 'taxDetails'
    }

    def __init__(self, asin=None, fnsku=None, msku=None, tax_details=None):  # noqa: E501
        """ComplianceDetail - a model defined in Swagger"""  # noqa: E501

        self._asin = None
        self._fnsku = None
        self._msku = None
        self._tax_details = None
        self.discriminator = None

        if asin is not None:
            self.asin = asin
        if fnsku is not None:
            self.fnsku = fnsku
        if msku is not None:
            self.msku = msku
        if tax_details is not None:
            self.tax_details = tax_details

    @property
    def asin(self):
        """Gets the asin of this ComplianceDetail.  # noqa: E501

        The Amazon Standard Identification Number, which identifies the detail page identifier.  # noqa: E501

        :return: The asin of this ComplianceDetail.  # noqa: E501
        :rtype: str
        """
        return self._asin

    @asin.setter
    def asin(self, asin):
        """Sets the asin of this ComplianceDetail.

        The Amazon Standard Identification Number, which identifies the detail page identifier.  # noqa: E501

        :param asin: The asin of this ComplianceDetail.  # noqa: E501
        :type: str
        """
        if asin is not None and len(asin) > 10:
            raise ValueError("Invalid value for `asin`, length must be less than or equal to `10`")  # noqa: E501
        if asin is not None and len(asin) < 1:
            raise ValueError("Invalid value for `asin`, length must be greater than or equal to `1`")  # noqa: E501

        self._asin = asin

    @property
    def fnsku(self):
        """Gets the fnsku of this ComplianceDetail.  # noqa: E501

        The Fulfillment Network SKU, which identifies a real fulfillable item with catalog data and condition.  # noqa: E501

        :return: The fnsku of this ComplianceDetail.  # noqa: E501
        :rtype: str
        """
        return self._fnsku

    @fnsku.setter
    def fnsku(self, fnsku):
        """Sets the fnsku of this ComplianceDetail.

        The Fulfillment Network SKU, which identifies a real fulfillable item with catalog data and condition.  # noqa: E501

        :param fnsku: The fnsku of this ComplianceDetail.  # noqa: E501
        :type: str
        """
        if fnsku is not None and len(fnsku) > 10:
            raise ValueError("Invalid value for `fnsku`, length must be less than or equal to `10`")  # noqa: E501
        if fnsku is not None and len(fnsku) < 1:
            raise ValueError("Invalid value for `fnsku`, length must be greater than or equal to `1`")  # noqa: E501

        self._fnsku = fnsku

    @property
    def msku(self):
        """Gets the msku of this ComplianceDetail.  # noqa: E501

        The merchant SKU, a merchant-supplied identifier for a specific SKU.  # noqa: E501

        :return: The msku of this ComplianceDetail.  # noqa: E501
        :rtype: str
        """
        return self._msku

    @msku.setter
    def msku(self, msku):
        """Sets the msku of this ComplianceDetail.

        The merchant SKU, a merchant-supplied identifier for a specific SKU.  # noqa: E501

        :param msku: The msku of this ComplianceDetail.  # noqa: E501
        :type: str
        """
        if msku is not None and len(msku) > 40:
            raise ValueError("Invalid value for `msku`, length must be less than or equal to `40`")  # noqa: E501
        if msku is not None and len(msku) < 1:
            raise ValueError("Invalid value for `msku`, length must be greater than or equal to `1`")  # noqa: E501

        self._msku = msku

    @property
    def tax_details(self):
        """Gets the tax_details of this ComplianceDetail.  # noqa: E501


        :return: The tax_details of this ComplianceDetail.  # noqa: E501
        :rtype: TaxDetails
        """
        return self._tax_details

    @tax_details.setter
    def tax_details(self, tax_details):
        """Sets the tax_details of this ComplianceDetail.


        :param tax_details: The tax_details of this ComplianceDetail.  # noqa: E501
        :type: TaxDetails
        """

        self._tax_details = tax_details

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ComplianceDetail, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ComplianceDetail):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
