# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ItemInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'expiration': 'str',
        'label_owner': 'LabelOwner',
        'manufacturing_lot_code': 'str',
        'msku': 'str',
        'prep_owner': 'PrepOwner',
        'quantity': 'int'
    }

    attribute_map = {
        'expiration': 'expiration',
        'label_owner': 'labelOwner',
        'manufacturing_lot_code': 'manufacturingLotCode',
        'msku': 'msku',
        'prep_owner': 'prepOwner',
        'quantity': 'quantity'
    }

    def __init__(self, expiration=None, label_owner=None, manufacturing_lot_code=None, msku=None, prep_owner=None, quantity=None):  # noqa: E501
        """ItemInput - a model defined in Swagger"""  # noqa: E501

        self._expiration = None
        self._label_owner = None
        self._manufacturing_lot_code = None
        self._msku = None
        self._prep_owner = None
        self._quantity = None
        self.discriminator = None

        if expiration is not None:
            self.expiration = expiration
        self.label_owner = label_owner
        if manufacturing_lot_code is not None:
            self.manufacturing_lot_code = manufacturing_lot_code
        self.msku = msku
        self.prep_owner = prep_owner
        self.quantity = quantity

    @property
    def expiration(self):
        """Gets the expiration of this ItemInput.  # noqa: E501

        The expiration date of the MSKU. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format with pattern `YYYY-MM-DD`. Items with the same MSKU but different expiration dates cannot go into the same box.  # noqa: E501

        :return: The expiration of this ItemInput.  # noqa: E501
        :rtype: str
        """
        return self._expiration

    @expiration.setter
    def expiration(self, expiration):
        """Sets the expiration of this ItemInput.

        The expiration date of the MSKU. In [ISO 8601](https://developer-docs.amazon.com/sp-api/docs/iso-8601) datetime format with pattern `YYYY-MM-DD`. Items with the same MSKU but different expiration dates cannot go into the same box.  # noqa: E501

        :param expiration: The expiration of this ItemInput.  # noqa: E501
        :type: str
        """
        if expiration is not None and not re.search(r'^([0-9]{4})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$', expiration):  # noqa: E501
            raise ValueError(r"Invalid value for `expiration`, must be a follow pattern or equal to `/^([0-9]{4})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/`")  # noqa: E501

        self._expiration = expiration

    @property
    def label_owner(self):
        """Gets the label_owner of this ItemInput.  # noqa: E501


        :return: The label_owner of this ItemInput.  # noqa: E501
        :rtype: LabelOwner
        """
        return self._label_owner

    @label_owner.setter
    def label_owner(self, label_owner):
        """Sets the label_owner of this ItemInput.


        :param label_owner: The label_owner of this ItemInput.  # noqa: E501
        :type: LabelOwner
        """
        if label_owner is None:
            raise ValueError("Invalid value for `label_owner`, must not be `None`")  # noqa: E501

        self._label_owner = label_owner

    @property
    def manufacturing_lot_code(self):
        """Gets the manufacturing_lot_code of this ItemInput.  # noqa: E501

        The manufacturing lot code.  # noqa: E501

        :return: The manufacturing_lot_code of this ItemInput.  # noqa: E501
        :rtype: str
        """
        return self._manufacturing_lot_code

    @manufacturing_lot_code.setter
    def manufacturing_lot_code(self, manufacturing_lot_code):
        """Sets the manufacturing_lot_code of this ItemInput.

        The manufacturing lot code.  # noqa: E501

        :param manufacturing_lot_code: The manufacturing_lot_code of this ItemInput.  # noqa: E501
        :type: str
        """
        if manufacturing_lot_code is not None and len(manufacturing_lot_code) > 256:
            raise ValueError("Invalid value for `manufacturing_lot_code`, length must be less than or equal to `256`")  # noqa: E501
        if manufacturing_lot_code is not None and len(manufacturing_lot_code) < 1:
            raise ValueError("Invalid value for `manufacturing_lot_code`, length must be greater than or equal to `1`")  # noqa: E501

        self._manufacturing_lot_code = manufacturing_lot_code

    @property
    def msku(self):
        """Gets the msku of this ItemInput.  # noqa: E501

        The merchant SKU, a merchant-supplied identifier of a specific SKU.  # noqa: E501

        :return: The msku of this ItemInput.  # noqa: E501
        :rtype: str
        """
        return self._msku

    @msku.setter
    def msku(self, msku):
        """Sets the msku of this ItemInput.

        The merchant SKU, a merchant-supplied identifier of a specific SKU.  # noqa: E501

        :param msku: The msku of this ItemInput.  # noqa: E501
        :type: str
        """
        if msku is None:
            raise ValueError("Invalid value for `msku`, must not be `None`")  # noqa: E501
        if msku is not None and len(msku) > 40:
            raise ValueError("Invalid value for `msku`, length must be less than or equal to `40`")  # noqa: E501
        if msku is not None and len(msku) < 1:
            raise ValueError("Invalid value for `msku`, length must be greater than or equal to `1`")  # noqa: E501

        self._msku = msku

    @property
    def prep_owner(self):
        """Gets the prep_owner of this ItemInput.  # noqa: E501


        :return: The prep_owner of this ItemInput.  # noqa: E501
        :rtype: PrepOwner
        """
        return self._prep_owner

    @prep_owner.setter
    def prep_owner(self, prep_owner):
        """Sets the prep_owner of this ItemInput.


        :param prep_owner: The prep_owner of this ItemInput.  # noqa: E501
        :type: PrepOwner
        """
        if prep_owner is None:
            raise ValueError("Invalid value for `prep_owner`, must not be `None`")  # noqa: E501

        self._prep_owner = prep_owner

    @property
    def quantity(self):
        """Gets the quantity of this ItemInput.  # noqa: E501

        The number of units of the specified MSKU that will be shipped.  # noqa: E501

        :return: The quantity of this ItemInput.  # noqa: E501
        :rtype: int
        """
        return self._quantity

    @quantity.setter
    def quantity(self, quantity):
        """Sets the quantity of this ItemInput.

        The number of units of the specified MSKU that will be shipped.  # noqa: E501

        :param quantity: The quantity of this ItemInput.  # noqa: E501
        :type: int
        """
        if quantity is None:
            raise ValueError("Invalid value for `quantity`, must not be `None`")  # noqa: E501
        if quantity is not None and quantity > 10000:  # noqa: E501
            raise ValueError("Invalid value for `quantity`, must be a value less than or equal to `10000`")  # noqa: E501
        if quantity is not None and quantity < 1:  # noqa: E501
            raise ValueError("Invalid value for `quantity`, must be a value greater than or equal to `1`")  # noqa: E501

        self._quantity = quantity

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemInput):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
