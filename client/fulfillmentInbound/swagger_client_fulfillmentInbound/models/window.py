# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class Window(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'editable_until': 'datetime',
        'end': 'datetime',
        'start': 'datetime'
    }

    attribute_map = {
        'editable_until': 'editableUntil',
        'end': 'end',
        'start': 'start'
    }

    def __init__(self, editable_until=None, end=None, start=None):  # noqa: E501
        """Window - a model defined in Swagger"""  # noqa: E501

        self._editable_until = None
        self._end = None
        self._start = None
        self.discriminator = None

        if editable_until is not None:
            self.editable_until = editable_until
        self.end = end
        self.start = start

    @property
    def editable_until(self):
        """Gets the editable_until of this Window.  # noqa: E501

        The timestamp at which this Window can no longer be edited.  # noqa: E501

        :return: The editable_until of this Window.  # noqa: E501
        :rtype: datetime
        """
        return self._editable_until

    @editable_until.setter
    def editable_until(self, editable_until):
        """Sets the editable_until of this Window.

        The timestamp at which this Window can no longer be edited.  # noqa: E501

        :param editable_until: The editable_until of this Window.  # noqa: E501
        :type: datetime
        """

        self._editable_until = editable_until

    @property
    def end(self):
        """Gets the end of this Window.  # noqa: E501

        The end timestamp of the window.  # noqa: E501

        :return: The end of this Window.  # noqa: E501
        :rtype: datetime
        """
        return self._end

    @end.setter
    def end(self, end):
        """Sets the end of this Window.

        The end timestamp of the window.  # noqa: E501

        :param end: The end of this Window.  # noqa: E501
        :type: datetime
        """
        if end is None:
            raise ValueError("Invalid value for `end`, must not be `None`")  # noqa: E501

        self._end = end

    @property
    def start(self):
        """Gets the start of this Window.  # noqa: E501

        The start timestamp of the window.  # noqa: E501

        :return: The start of this Window.  # noqa: E501
        :rtype: datetime
        """
        return self._start

    @start.setter
    def start(self, start):
        """Sets the start of this Window.

        The start timestamp of the window.  # noqa: E501

        :param start: The start of this Window.  # noqa: E501
        :type: datetime
        """
        if start is None:
            raise ValueError("Invalid value for `start`, must not be `None`")  # noqa: E501

        self._start = start

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Window, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Window):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
