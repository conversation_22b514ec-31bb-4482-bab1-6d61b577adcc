# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ListInboundPlansResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'inbound_plans': 'list[InboundPlanSummary]',
        'pagination': 'Pagination'
    }

    attribute_map = {
        'inbound_plans': 'inboundPlans',
        'pagination': 'pagination'
    }

    def __init__(self, inbound_plans=None, pagination=None):  # noqa: E501
        """ListInboundPlansResponse - a model defined in Swagger"""  # noqa: E501

        self._inbound_plans = None
        self._pagination = None
        self.discriminator = None

        if inbound_plans is not None:
            self.inbound_plans = inbound_plans
        if pagination is not None:
            self.pagination = pagination

    @property
    def inbound_plans(self):
        """Gets the inbound_plans of this ListInboundPlansResponse.  # noqa: E501

        A list of inbound plans with minimal information.  # noqa: E501

        :return: The inbound_plans of this ListInboundPlansResponse.  # noqa: E501
        :rtype: list[InboundPlanSummary]
        """
        return self._inbound_plans

    @inbound_plans.setter
    def inbound_plans(self, inbound_plans):
        """Sets the inbound_plans of this ListInboundPlansResponse.

        A list of inbound plans with minimal information.  # noqa: E501

        :param inbound_plans: The inbound_plans of this ListInboundPlansResponse.  # noqa: E501
        :type: list[InboundPlanSummary]
        """

        self._inbound_plans = inbound_plans

    @property
    def pagination(self):
        """Gets the pagination of this ListInboundPlansResponse.  # noqa: E501


        :return: The pagination of this ListInboundPlansResponse.  # noqa: E501
        :rtype: Pagination
        """
        return self._pagination

    @pagination.setter
    def pagination(self, pagination):
        """Sets the pagination of this ListInboundPlansResponse.


        :param pagination: The pagination of this ListInboundPlansResponse.  # noqa: E501
        :type: Pagination
        """

        self._pagination = pagination

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListInboundPlansResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListInboundPlansResponse):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
