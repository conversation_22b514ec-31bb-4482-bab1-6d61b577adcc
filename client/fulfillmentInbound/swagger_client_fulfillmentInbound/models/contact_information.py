# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ContactInformation(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'email': 'str',
        'name': 'str',
        'phone_number': 'str'
    }

    attribute_map = {
        'email': 'email',
        'name': 'name',
        'phone_number': 'phoneNumber'
    }

    def __init__(self, email=None, name=None, phone_number=None):  # noqa: E501
        """ContactInformation - a model defined in Swagger"""  # noqa: E501

        self._email = None
        self._name = None
        self._phone_number = None
        self.discriminator = None

        if email is not None:
            self.email = email
        self.name = name
        self.phone_number = phone_number

    @property
    def email(self):
        """Gets the email of this ContactInformation.  # noqa: E501

        The email address.  # noqa: E501

        :return: The email of this ContactInformation.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this ContactInformation.

        The email address.  # noqa: E501

        :param email: The email of this ContactInformation.  # noqa: E501
        :type: str
        """
        if email is not None and len(email) > 1024:
            raise ValueError("Invalid value for `email`, length must be less than or equal to `1024`")  # noqa: E501
        if email is not None and len(email) < 1:
            raise ValueError("Invalid value for `email`, length must be greater than or equal to `1`")  # noqa: E501

        self._email = email

    @property
    def name(self):
        """Gets the name of this ContactInformation.  # noqa: E501

        The contact's name.  # noqa: E501

        :return: The name of this ContactInformation.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ContactInformation.

        The contact's name.  # noqa: E501

        :param name: The name of this ContactInformation.  # noqa: E501
        :type: str
        """
        if name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501
        if name is not None and len(name) > 50:
            raise ValueError("Invalid value for `name`, length must be less than or equal to `50`")  # noqa: E501
        if name is not None and len(name) < 1:
            raise ValueError("Invalid value for `name`, length must be greater than or equal to `1`")  # noqa: E501

        self._name = name

    @property
    def phone_number(self):
        """Gets the phone_number of this ContactInformation.  # noqa: E501

        The phone number.  # noqa: E501

        :return: The phone_number of this ContactInformation.  # noqa: E501
        :rtype: str
        """
        return self._phone_number

    @phone_number.setter
    def phone_number(self, phone_number):
        """Sets the phone_number of this ContactInformation.

        The phone number.  # noqa: E501

        :param phone_number: The phone_number of this ContactInformation.  # noqa: E501
        :type: str
        """
        if phone_number is None:
            raise ValueError("Invalid value for `phone_number`, must not be `None`")  # noqa: E501
        if phone_number is not None and len(phone_number) > 20:
            raise ValueError("Invalid value for `phone_number`, length must be less than or equal to `20`")  # noqa: E501
        if phone_number is not None and len(phone_number) < 1:
            raise ValueError("Invalid value for `phone_number`, length must be greater than or equal to `1`")  # noqa: E501

        self._phone_number = phone_number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ContactInformation, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ContactInformation):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
