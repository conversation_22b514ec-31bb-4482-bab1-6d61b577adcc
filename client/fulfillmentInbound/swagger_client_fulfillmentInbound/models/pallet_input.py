# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class PalletInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dimensions': 'Dimensions',
        'quantity': 'int',
        'stackability': 'Stackability',
        'weight': 'Weight'
    }

    attribute_map = {
        'dimensions': 'dimensions',
        'quantity': 'quantity',
        'stackability': 'stackability',
        'weight': 'weight'
    }

    def __init__(self, dimensions=None, quantity=None, stackability=None, weight=None):  # noqa: E501
        """PalletInput - a model defined in Swagger"""  # noqa: E501

        self._dimensions = None
        self._quantity = None
        self._stackability = None
        self._weight = None
        self.discriminator = None

        if dimensions is not None:
            self.dimensions = dimensions
        self.quantity = quantity
        if stackability is not None:
            self.stackability = stackability
        if weight is not None:
            self.weight = weight

    @property
    def dimensions(self):
        """Gets the dimensions of this PalletInput.  # noqa: E501


        :return: The dimensions of this PalletInput.  # noqa: E501
        :rtype: Dimensions
        """
        return self._dimensions

    @dimensions.setter
    def dimensions(self, dimensions):
        """Sets the dimensions of this PalletInput.


        :param dimensions: The dimensions of this PalletInput.  # noqa: E501
        :type: Dimensions
        """

        self._dimensions = dimensions

    @property
    def quantity(self):
        """Gets the quantity of this PalletInput.  # noqa: E501

        The number of containers where all other properties like weight or dimensions are identical.  # noqa: E501

        :return: The quantity of this PalletInput.  # noqa: E501
        :rtype: int
        """
        return self._quantity

    @quantity.setter
    def quantity(self, quantity):
        """Sets the quantity of this PalletInput.

        The number of containers where all other properties like weight or dimensions are identical.  # noqa: E501

        :param quantity: The quantity of this PalletInput.  # noqa: E501
        :type: int
        """
        if quantity is None:
            raise ValueError("Invalid value for `quantity`, must not be `None`")  # noqa: E501
        if quantity is not None and quantity > 10000:  # noqa: E501
            raise ValueError("Invalid value for `quantity`, must be a value less than or equal to `10000`")  # noqa: E501
        if quantity is not None and quantity < 1:  # noqa: E501
            raise ValueError("Invalid value for `quantity`, must be a value greater than or equal to `1`")  # noqa: E501

        self._quantity = quantity

    @property
    def stackability(self):
        """Gets the stackability of this PalletInput.  # noqa: E501


        :return: The stackability of this PalletInput.  # noqa: E501
        :rtype: Stackability
        """
        return self._stackability

    @stackability.setter
    def stackability(self, stackability):
        """Sets the stackability of this PalletInput.


        :param stackability: The stackability of this PalletInput.  # noqa: E501
        :type: Stackability
        """

        self._stackability = stackability

    @property
    def weight(self):
        """Gets the weight of this PalletInput.  # noqa: E501


        :return: The weight of this PalletInput.  # noqa: E501
        :rtype: Weight
        """
        return self._weight

    @weight.setter
    def weight(self, weight):
        """Sets the weight of this PalletInput.


        :param weight: The weight of this PalletInput.  # noqa: E501
        :type: Weight
        """

        self._weight = weight

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PalletInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PalletInput):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
