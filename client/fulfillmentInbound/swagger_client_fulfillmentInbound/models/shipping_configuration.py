# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ShippingConfiguration(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'shipping_mode': 'str',
        'shipping_solution': 'str'
    }

    attribute_map = {
        'shipping_mode': 'shippingMode',
        'shipping_solution': 'shippingSolution'
    }

    def __init__(self, shipping_mode=None, shipping_solution=None):  # noqa: E501
        """ShippingConfiguration - a model defined in Swagger"""  # noqa: E501

        self._shipping_mode = None
        self._shipping_solution = None
        self.discriminator = None

        if shipping_mode is not None:
            self.shipping_mode = shipping_mode
        if shipping_solution is not None:
            self.shipping_solution = shipping_solution

    @property
    def shipping_mode(self):
        """Gets the shipping_mode of this ShippingConfiguration.  # noqa: E501

        Mode of shipment transportation that this option will provide.  Possible values: `GROUND_SMALL_PARCEL`, `FREIGHT_LTL`, `FREIGHT_FTL_PALLET`, `FREIGHT_FTL_NONPALLET`, `OCEAN_LCL`, `OCEAN_FCL`, `AIR_SMALL_PARCEL`, `AIR_SMALL_PARCEL_EXPRESS`.  # noqa: E501

        :return: The shipping_mode of this ShippingConfiguration.  # noqa: E501
        :rtype: str
        """
        return self._shipping_mode

    @shipping_mode.setter
    def shipping_mode(self, shipping_mode):
        """Sets the shipping_mode of this ShippingConfiguration.

        Mode of shipment transportation that this option will provide.  Possible values: `GROUND_SMALL_PARCEL`, `FREIGHT_LTL`, `FREIGHT_FTL_PALLET`, `FREIGHT_FTL_NONPALLET`, `OCEAN_LCL`, `OCEAN_FCL`, `AIR_SMALL_PARCEL`, `AIR_SMALL_PARCEL_EXPRESS`.  # noqa: E501

        :param shipping_mode: The shipping_mode of this ShippingConfiguration.  # noqa: E501
        :type: str
        """
        if shipping_mode is not None and len(shipping_mode) > 1024:
            raise ValueError("Invalid value for `shipping_mode`, length must be less than or equal to `1024`")  # noqa: E501
        if shipping_mode is not None and len(shipping_mode) < 1:
            raise ValueError("Invalid value for `shipping_mode`, length must be greater than or equal to `1`")  # noqa: E501

        self._shipping_mode = shipping_mode

    @property
    def shipping_solution(self):
        """Gets the shipping_solution of this ShippingConfiguration.  # noqa: E501

        Shipping program for the option. Possible values: `AMAZON_PARTNERED_CARRIER`, `USE_YOUR_OWN_CARRIER`.  # noqa: E501

        :return: The shipping_solution of this ShippingConfiguration.  # noqa: E501
        :rtype: str
        """
        return self._shipping_solution

    @shipping_solution.setter
    def shipping_solution(self, shipping_solution):
        """Sets the shipping_solution of this ShippingConfiguration.

        Shipping program for the option. Possible values: `AMAZON_PARTNERED_CARRIER`, `USE_YOUR_OWN_CARRIER`.  # noqa: E501

        :param shipping_solution: The shipping_solution of this ShippingConfiguration.  # noqa: E501
        :type: str
        """
        if shipping_solution is not None and len(shipping_solution) > 1024:
            raise ValueError("Invalid value for `shipping_solution`, length must be less than or equal to `1024`")  # noqa: E501
        if shipping_solution is not None and len(shipping_solution) < 1:
            raise ValueError("Invalid value for `shipping_solution`, length must be greater than or equal to `1`")  # noqa: E501

        self._shipping_solution = shipping_solution

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ShippingConfiguration, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ShippingConfiguration):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
