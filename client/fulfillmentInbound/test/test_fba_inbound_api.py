# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_fulfillmentInbound
from swagger_client_fulfillmentInbound.api.fba_inbound_api import FbaInboundApi  # noqa: E501
from swagger_client_fulfillmentInbound.rest import ApiException


class TestFbaInboundApi(unittest.TestCase):
    """FbaInboundApi unit test stubs"""

    def setUp(self):
        self.api = swagger_client_fulfillmentInbound.api.fba_inbound_api.FbaInboundApi()  # noqa: E501

    def tearDown(self):
        pass

    def test_cancel_inbound_plan(self):
        """Test case for cancel_inbound_plan

        """
        pass

    def test_cancel_self_ship_appointment(self):
        """Test case for cancel_self_ship_appointment

        """
        pass

    def test_confirm_delivery_window_options(self):
        """Test case for confirm_delivery_window_options

        """
        pass

    def test_confirm_packing_option(self):
        """Test case for confirm_packing_option

        """
        pass

    def test_confirm_placement_option(self):
        """Test case for confirm_placement_option

        """
        pass

    def test_confirm_shipment_content_update_preview(self):
        """Test case for confirm_shipment_content_update_preview

        """
        pass

    def test_confirm_transportation_options(self):
        """Test case for confirm_transportation_options

        """
        pass

    def test_create_inbound_plan(self):
        """Test case for create_inbound_plan

        """
        pass

    def test_create_marketplace_item_labels(self):
        """Test case for create_marketplace_item_labels

        """
        pass

    def test_generate_delivery_window_options(self):
        """Test case for generate_delivery_window_options

        """
        pass

    def test_generate_packing_options(self):
        """Test case for generate_packing_options

        """
        pass

    def test_generate_placement_options(self):
        """Test case for generate_placement_options

        """
        pass

    def test_generate_self_ship_appointment_slots(self):
        """Test case for generate_self_ship_appointment_slots

        """
        pass

    def test_generate_shipment_content_update_previews(self):
        """Test case for generate_shipment_content_update_previews

        """
        pass

    def test_generate_transportation_options(self):
        """Test case for generate_transportation_options

        """
        pass

    def test_get_delivery_challan_document(self):
        """Test case for get_delivery_challan_document

        """
        pass

    def test_get_inbound_operation_status(self):
        """Test case for get_inbound_operation_status

        """
        pass

    def test_get_inbound_plan(self):
        """Test case for get_inbound_plan

        """
        pass

    def test_get_self_ship_appointment_slots(self):
        """Test case for get_self_ship_appointment_slots

        """
        pass

    def test_get_shipment(self):
        """Test case for get_shipment

        """
        pass

    def test_get_shipment_content_update_preview(self):
        """Test case for get_shipment_content_update_preview

        """
        pass

    def test_list_delivery_window_options(self):
        """Test case for list_delivery_window_options

        """
        pass

    def test_list_inbound_plan_boxes(self):
        """Test case for list_inbound_plan_boxes

        """
        pass

    def test_list_inbound_plan_items(self):
        """Test case for list_inbound_plan_items

        """
        pass

    def test_list_inbound_plan_pallets(self):
        """Test case for list_inbound_plan_pallets

        """
        pass

    def test_list_inbound_plans(self):
        """Test case for list_inbound_plans

        """
        pass

    def test_list_item_compliance_details(self):
        """Test case for list_item_compliance_details

        """
        pass

    def test_list_packing_group_boxes(self):
        """Test case for list_packing_group_boxes

        """
        pass

    def test_list_packing_group_items(self):
        """Test case for list_packing_group_items

        """
        pass

    def test_list_packing_options(self):
        """Test case for list_packing_options

        """
        pass

    def test_list_placement_options(self):
        """Test case for list_placement_options

        """
        pass

    def test_list_shipment_boxes(self):
        """Test case for list_shipment_boxes

        """
        pass

    def test_list_shipment_content_update_previews(self):
        """Test case for list_shipment_content_update_previews

        """
        pass

    def test_list_shipment_items(self):
        """Test case for list_shipment_items

        """
        pass

    def test_list_shipment_pallets(self):
        """Test case for list_shipment_pallets

        """
        pass

    def test_list_transportation_options(self):
        """Test case for list_transportation_options

        """
        pass

    def test_schedule_self_ship_appointment(self):
        """Test case for schedule_self_ship_appointment

        """
        pass

    def test_set_packing_information(self):
        """Test case for set_packing_information

        """
        pass

    def test_update_inbound_plan_name(self):
        """Test case for update_inbound_plan_name

        """
        pass

    def test_update_item_compliance_details(self):
        """Test case for update_item_compliance_details

        """
        pass

    def test_update_shipment_name(self):
        """Test case for update_shipment_name

        """
        pass

    def test_update_shipment_source_address(self):
        """Test case for update_shipment_source_address

        """
        pass

    def test_update_shipment_tracking_details(self):
        """Test case for update_shipment_tracking_details

        """
        pass


if __name__ == '__main__':
    unittest.main()