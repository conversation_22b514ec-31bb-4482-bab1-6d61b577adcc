# coding: utf-8

"""
    The Selling Partner API for FBA inbound operations.

    The Selling Partner API for Fulfillment By Amazon (FBA) Inbound. The FBA Inbound API enables building inbound workflows to create, manage, and send shipments into Amazon's fulfillment network. The API has interoperability with the Send-to-Amazon user interface.  # noqa: E501

    OpenAPI spec version: 2024-03-20
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_fulfillmentInbound
from swagger_client_fulfillmentInbound.models.tax_rate import TaxRate  # noqa: E501
from swagger_client_fulfillmentInbound.rest import ApiException


class TestTaxRate(unittest.TestCase):
    """TaxRate unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testTaxRate(self):
        """Test TaxRate"""
        # FIXME: construct object with mandatory attributes with example values
        # model = swagger_client_fulfillmentInbound.models.tax_rate.TaxRate()  # noqa: E501
        pass


if __name__ == '__main__':
    unittest.main()
