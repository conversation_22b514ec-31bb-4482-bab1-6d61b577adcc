# swagger-client-reports
The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.

This Python package is automatically generated by the [Swagger Codegen](https://github.com/swagger-api/swagger-codegen) project:

- API version: 2021-06-30
- Package version: 1.0.0
- Build package: io.swagger.codegen.languages.PythonClientCodegen
For more information, please visit [https://sellercentral.amazon.com/gp/mws/contactus.html](https://sellercentral.amazon.com/gp/mws/contactus.html)

## Requirements.

Python 2.7 and 3.4+

## Installation & Usage
### pip install

If the python package is hosted on Github, you can install directly from Github

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```
(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:
```python
import swagger_client_reports 
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```
(or `sudo python setup.py install` to install the package for all users)

Then import the package:
```python
import swagger_client_reports
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python
from __future__ import print_function
import time
import swagger_client_reports
from swagger_client_reports.rest import ApiException
from pprint import pprint

# create an instance of the API class
api_instance = swagger_client_reports.ReportsApi(swagger_client_reports.ApiClient(configuration))
report_id = 'report_id_example' # str | The identifier for the report. This identifier is unique only in combination with a seller ID.

try:
    api_instance.cancel_report(report_id)
except ApiException as e:
    print("Exception when calling ReportsApi->cancel_report: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *https://sellingpartnerapi-na.amazon.com*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*ReportsApi* | [**cancel_report**](docs/ReportsApi.md#cancel_report) | **DELETE** /reports/2021-06-30/reports/{reportId} | 
*ReportsApi* | [**cancel_report_schedule**](docs/ReportsApi.md#cancel_report_schedule) | **DELETE** /reports/2021-06-30/schedules/{reportScheduleId} | 
*ReportsApi* | [**create_report**](docs/ReportsApi.md#create_report) | **POST** /reports/2021-06-30/reports | 
*ReportsApi* | [**create_report_schedule**](docs/ReportsApi.md#create_report_schedule) | **POST** /reports/2021-06-30/schedules | 
*ReportsApi* | [**get_report**](docs/ReportsApi.md#get_report) | **GET** /reports/2021-06-30/reports/{reportId} | 
*ReportsApi* | [**get_report_document**](docs/ReportsApi.md#get_report_document) | **GET** /reports/2021-06-30/documents/{reportDocumentId} | 
*ReportsApi* | [**get_report_schedule**](docs/ReportsApi.md#get_report_schedule) | **GET** /reports/2021-06-30/schedules/{reportScheduleId} | 
*ReportsApi* | [**get_report_schedules**](docs/ReportsApi.md#get_report_schedules) | **GET** /reports/2021-06-30/schedules | 
*ReportsApi* | [**get_reports**](docs/ReportsApi.md#get_reports) | **GET** /reports/2021-06-30/reports | 


## Documentation For Models

 - [CreateReportResponse](docs/CreateReportResponse.md)
 - [CreateReportScheduleResponse](docs/CreateReportScheduleResponse.md)
 - [CreateReportScheduleSpecification](docs/CreateReportScheduleSpecification.md)
 - [CreateReportSpecification](docs/CreateReportSpecification.md)
 - [Error](docs/Error.md)
 - [ErrorList](docs/ErrorList.md)
 - [GetReportsResponse](docs/GetReportsResponse.md)
 - [Report](docs/Report.md)
 - [ReportDocument](docs/ReportDocument.md)
 - [ReportList](docs/ReportList.md)
 - [ReportOptions](docs/ReportOptions.md)
 - [ReportSchedule](docs/ReportSchedule.md)
 - [ReportScheduleList](docs/ReportScheduleList.md)


## Documentation For Authorization

 All endpoints do not require authorization.


## Author



