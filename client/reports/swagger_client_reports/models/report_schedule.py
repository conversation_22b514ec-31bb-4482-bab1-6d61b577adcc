# coding: utf-8

"""
    Selling Partner API for Reports

    The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.  # noqa: E501

    OpenAPI spec version: 2021-06-30
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ReportSchedule(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'report_schedule_id': 'str',
        'report_type': 'str',
        'marketplace_ids': 'list[str]',
        'report_options': 'ReportOptions',
        'period': 'str',
        'next_report_creation_time': 'datetime'
    }

    attribute_map = {
        'report_schedule_id': 'reportScheduleId',
        'report_type': 'reportType',
        'marketplace_ids': 'marketplaceIds',
        'report_options': 'reportOptions',
        'period': 'period',
        'next_report_creation_time': 'nextReportCreationTime'
    }

    def __init__(self, report_schedule_id=None, report_type=None, marketplace_ids=None, report_options=None, period=None, next_report_creation_time=None):  # noqa: E501
        """ReportSchedule - a model defined in Swagger"""  # noqa: E501

        self._report_schedule_id = None
        self._report_type = None
        self._marketplace_ids = None
        self._report_options = None
        self._period = None
        self._next_report_creation_time = None
        self.discriminator = None

        self.report_schedule_id = report_schedule_id
        self.report_type = report_type
        if marketplace_ids is not None:
            self.marketplace_ids = marketplace_ids
        if report_options is not None:
            self.report_options = report_options
        self.period = period
        if next_report_creation_time is not None:
            self.next_report_creation_time = next_report_creation_time

    @property
    def report_schedule_id(self):
        """Gets the report_schedule_id of this ReportSchedule.  # noqa: E501

        The identifier for the report schedule. This identifier is unique only in combination with a seller ID.  # noqa: E501

        :return: The report_schedule_id of this ReportSchedule.  # noqa: E501
        :rtype: str
        """
        return self._report_schedule_id

    @report_schedule_id.setter
    def report_schedule_id(self, report_schedule_id):
        """Sets the report_schedule_id of this ReportSchedule.

        The identifier for the report schedule. This identifier is unique only in combination with a seller ID.  # noqa: E501

        :param report_schedule_id: The report_schedule_id of this ReportSchedule.  # noqa: E501
        :type: str
        """
        if report_schedule_id is None:
            raise ValueError("Invalid value for `report_schedule_id`, must not be `None`")  # noqa: E501

        self._report_schedule_id = report_schedule_id

    @property
    def report_type(self):
        """Gets the report_type of this ReportSchedule.  # noqa: E501

        The report type. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information.  # noqa: E501

        :return: The report_type of this ReportSchedule.  # noqa: E501
        :rtype: str
        """
        return self._report_type

    @report_type.setter
    def report_type(self, report_type):
        """Sets the report_type of this ReportSchedule.

        The report type. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information.  # noqa: E501

        :param report_type: The report_type of this ReportSchedule.  # noqa: E501
        :type: str
        """
        if report_type is None:
            raise ValueError("Invalid value for `report_type`, must not be `None`")  # noqa: E501

        self._report_type = report_type

    @property
    def marketplace_ids(self):
        """Gets the marketplace_ids of this ReportSchedule.  # noqa: E501

        A list of marketplace identifiers. The report document's contents will contain data for all of the specified marketplaces, unless the report type indicates otherwise.  # noqa: E501

        :return: The marketplace_ids of this ReportSchedule.  # noqa: E501
        :rtype: list[str]
        """
        return self._marketplace_ids

    @marketplace_ids.setter
    def marketplace_ids(self, marketplace_ids):
        """Sets the marketplace_ids of this ReportSchedule.

        A list of marketplace identifiers. The report document's contents will contain data for all of the specified marketplaces, unless the report type indicates otherwise.  # noqa: E501

        :param marketplace_ids: The marketplace_ids of this ReportSchedule.  # noqa: E501
        :type: list[str]
        """

        self._marketplace_ids = marketplace_ids

    @property
    def report_options(self):
        """Gets the report_options of this ReportSchedule.  # noqa: E501


        :return: The report_options of this ReportSchedule.  # noqa: E501
        :rtype: ReportOptions
        """
        return self._report_options

    @report_options.setter
    def report_options(self, report_options):
        """Sets the report_options of this ReportSchedule.


        :param report_options: The report_options of this ReportSchedule.  # noqa: E501
        :type: ReportOptions
        """

        self._report_options = report_options

    @property
    def period(self):
        """Gets the period of this ReportSchedule.  # noqa: E501

        An <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> period value that indicates how often a report should be created.  # noqa: E501

        :return: The period of this ReportSchedule.  # noqa: E501
        :rtype: str
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this ReportSchedule.

        An <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> period value that indicates how often a report should be created.  # noqa: E501

        :param period: The period of this ReportSchedule.  # noqa: E501
        :type: str
        """
        if period is None:
            raise ValueError("Invalid value for `period`, must not be `None`")  # noqa: E501

        self._period = period

    @property
    def next_report_creation_time(self):
        """Gets the next_report_creation_time of this ReportSchedule.  # noqa: E501

        The date and time when the schedule will create its next report, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format.  # noqa: E501

        :return: The next_report_creation_time of this ReportSchedule.  # noqa: E501
        :rtype: datetime
        """
        return self._next_report_creation_time

    @next_report_creation_time.setter
    def next_report_creation_time(self, next_report_creation_time):
        """Sets the next_report_creation_time of this ReportSchedule.

        The date and time when the schedule will create its next report, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format.  # noqa: E501

        :param next_report_creation_time: The next_report_creation_time of this ReportSchedule.  # noqa: E501
        :type: datetime
        """

        self._next_report_creation_time = next_report_creation_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ReportSchedule, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ReportSchedule):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
