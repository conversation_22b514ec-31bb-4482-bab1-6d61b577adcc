# coding: utf-8

"""
    Selling Partner API for Reports

    The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.  # noqa: E501

    OpenAPI spec version: 2021-06-30
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class CreateReportScheduleResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'report_schedule_id': 'str'
    }

    attribute_map = {
        'report_schedule_id': 'reportScheduleId'
    }

    def __init__(self, report_schedule_id=None):  # noqa: E501
        """CreateReportScheduleResponse - a model defined in Swagger"""  # noqa: E501

        self._report_schedule_id = None
        self.discriminator = None

        self.report_schedule_id = report_schedule_id

    @property
    def report_schedule_id(self):
        """Gets the report_schedule_id of this CreateReportScheduleResponse.  # noqa: E501

        The identifier for the report schedule. This identifier is unique only in combination with a seller ID.  # noqa: E501

        :return: The report_schedule_id of this CreateReportScheduleResponse.  # noqa: E501
        :rtype: str
        """
        return self._report_schedule_id

    @report_schedule_id.setter
    def report_schedule_id(self, report_schedule_id):
        """Sets the report_schedule_id of this CreateReportScheduleResponse.

        The identifier for the report schedule. This identifier is unique only in combination with a seller ID.  # noqa: E501

        :param report_schedule_id: The report_schedule_id of this CreateReportScheduleResponse.  # noqa: E501
        :type: str
        """
        if report_schedule_id is None:
            raise ValueError("Invalid value for `report_schedule_id`, must not be `None`")  # noqa: E501

        self._report_schedule_id = report_schedule_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateReportScheduleResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateReportScheduleResponse):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
