# coding: utf-8

"""
    Selling Partner API for Reports

    The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.  # noqa: E501

    OpenAPI spec version: 2021-06-30
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class Report(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'marketplace_ids': 'list[str]',
        'report_id': 'str',
        'report_type': 'str',
        'data_start_time': 'datetime',
        'data_end_time': 'datetime',
        'report_schedule_id': 'str',
        'created_time': 'datetime',
        'processing_status': 'str',
        'processing_start_time': 'datetime',
        'processing_end_time': 'datetime',
        'report_document_id': 'str'
    }

    attribute_map = {
        'marketplace_ids': 'marketplaceIds',
        'report_id': 'reportId',
        'report_type': 'reportType',
        'data_start_time': 'dataStartTime',
        'data_end_time': 'dataEndTime',
        'report_schedule_id': 'reportScheduleId',
        'created_time': 'createdTime',
        'processing_status': 'processingStatus',
        'processing_start_time': 'processingStartTime',
        'processing_end_time': 'processingEndTime',
        'report_document_id': 'reportDocumentId'
    }

    def __init__(self, marketplace_ids=None, report_id=None, report_type=None, data_start_time=None, data_end_time=None, report_schedule_id=None, created_time=None, processing_status=None, processing_start_time=None, processing_end_time=None, report_document_id=None):  # noqa: E501
        """Report - a model defined in Swagger"""  # noqa: E501

        self._marketplace_ids = None
        self._report_id = None
        self._report_type = None
        self._data_start_time = None
        self._data_end_time = None
        self._report_schedule_id = None
        self._created_time = None
        self._processing_status = None
        self._processing_start_time = None
        self._processing_end_time = None
        self._report_document_id = None
        self.discriminator = None

        if marketplace_ids is not None:
            self.marketplace_ids = marketplace_ids
        self.report_id = report_id
        self.report_type = report_type
        if data_start_time is not None:
            self.data_start_time = data_start_time
        if data_end_time is not None:
            self.data_end_time = data_end_time
        if report_schedule_id is not None:
            self.report_schedule_id = report_schedule_id
        self.created_time = created_time
        self.processing_status = processing_status
        if processing_start_time is not None:
            self.processing_start_time = processing_start_time
        if processing_end_time is not None:
            self.processing_end_time = processing_end_time
        if report_document_id is not None:
            self.report_document_id = report_document_id

    @property
    def marketplace_ids(self):
        """Gets the marketplace_ids of this Report.  # noqa: E501

        A list of marketplace identifiers for the report.  # noqa: E501

        :return: The marketplace_ids of this Report.  # noqa: E501
        :rtype: list[str]
        """
        return self._marketplace_ids

    @marketplace_ids.setter
    def marketplace_ids(self, marketplace_ids):
        """Sets the marketplace_ids of this Report.

        A list of marketplace identifiers for the report.  # noqa: E501

        :param marketplace_ids: The marketplace_ids of this Report.  # noqa: E501
        :type: list[str]
        """

        self._marketplace_ids = marketplace_ids

    @property
    def report_id(self):
        """Gets the report_id of this Report.  # noqa: E501

        The identifier for the report. This identifier is unique only in combination with a seller ID.  # noqa: E501

        :return: The report_id of this Report.  # noqa: E501
        :rtype: str
        """
        return self._report_id

    @report_id.setter
    def report_id(self, report_id):
        """Sets the report_id of this Report.

        The identifier for the report. This identifier is unique only in combination with a seller ID.  # noqa: E501

        :param report_id: The report_id of this Report.  # noqa: E501
        :type: str
        """
        if report_id is None:
            raise ValueError("Invalid value for `report_id`, must not be `None`")  # noqa: E501

        self._report_id = report_id

    @property
    def report_type(self):
        """Gets the report_type of this Report.  # noqa: E501

        The report type. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information.  # noqa: E501

        :return: The report_type of this Report.  # noqa: E501
        :rtype: str
        """
        return self._report_type

    @report_type.setter
    def report_type(self, report_type):
        """Sets the report_type of this Report.

        The report type. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information.  # noqa: E501

        :param report_type: The report_type of this Report.  # noqa: E501
        :type: str
        """
        if report_type is None:
            raise ValueError("Invalid value for `report_type`, must not be `None`")  # noqa: E501

        self._report_type = report_type

    @property
    def data_start_time(self):
        """Gets the data_start_time of this Report.  # noqa: E501

        The start of a date and time range used for selecting the data to report.  # noqa: E501

        :return: The data_start_time of this Report.  # noqa: E501
        :rtype: datetime
        """
        return self._data_start_time

    @data_start_time.setter
    def data_start_time(self, data_start_time):
        """Sets the data_start_time of this Report.

        The start of a date and time range used for selecting the data to report.  # noqa: E501

        :param data_start_time: The data_start_time of this Report.  # noqa: E501
        :type: datetime
        """

        self._data_start_time = data_start_time

    @property
    def data_end_time(self):
        """Gets the data_end_time of this Report.  # noqa: E501

        The end of a date and time range used for selecting the data to report.  # noqa: E501

        :return: The data_end_time of this Report.  # noqa: E501
        :rtype: datetime
        """
        return self._data_end_time

    @data_end_time.setter
    def data_end_time(self, data_end_time):
        """Sets the data_end_time of this Report.

        The end of a date and time range used for selecting the data to report.  # noqa: E501

        :param data_end_time: The data_end_time of this Report.  # noqa: E501
        :type: datetime
        """

        self._data_end_time = data_end_time

    @property
    def report_schedule_id(self):
        """Gets the report_schedule_id of this Report.  # noqa: E501

        The identifier of the report schedule that created this report (if any). This identifier is unique only in combination with a seller ID.  # noqa: E501

        :return: The report_schedule_id of this Report.  # noqa: E501
        :rtype: str
        """
        return self._report_schedule_id

    @report_schedule_id.setter
    def report_schedule_id(self, report_schedule_id):
        """Sets the report_schedule_id of this Report.

        The identifier of the report schedule that created this report (if any). This identifier is unique only in combination with a seller ID.  # noqa: E501

        :param report_schedule_id: The report_schedule_id of this Report.  # noqa: E501
        :type: str
        """

        self._report_schedule_id = report_schedule_id

    @property
    def created_time(self):
        """Gets the created_time of this Report.  # noqa: E501

        The date and time when the report was created.  # noqa: E501

        :return: The created_time of this Report.  # noqa: E501
        :rtype: datetime
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this Report.

        The date and time when the report was created.  # noqa: E501

        :param created_time: The created_time of this Report.  # noqa: E501
        :type: datetime
        """
        if created_time is None:
            raise ValueError("Invalid value for `created_time`, must not be `None`")  # noqa: E501

        self._created_time = created_time

    @property
    def processing_status(self):
        """Gets the processing_status of this Report.  # noqa: E501

        The processing status of the report.  # noqa: E501

        :return: The processing_status of this Report.  # noqa: E501
        :rtype: str
        """
        return self._processing_status

    @processing_status.setter
    def processing_status(self, processing_status):
        """Sets the processing_status of this Report.

        The processing status of the report.  # noqa: E501

        :param processing_status: The processing_status of this Report.  # noqa: E501
        :type: str
        """
        if processing_status is None:
            raise ValueError("Invalid value for `processing_status`, must not be `None`")  # noqa: E501
        allowed_values = ["CANCELLED", "DONE", "FATAL", "IN_PROGRESS", "IN_QUEUE"]  # noqa: E501
        if processing_status not in allowed_values:
            raise ValueError(
                "Invalid value for `processing_status` ({0}), must be one of {1}"  # noqa: E501
                .format(processing_status, allowed_values)
            )

        self._processing_status = processing_status

    @property
    def processing_start_time(self):
        """Gets the processing_start_time of this Report.  # noqa: E501

        The date and time when the report processing started, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format.  # noqa: E501

        :return: The processing_start_time of this Report.  # noqa: E501
        :rtype: datetime
        """
        return self._processing_start_time

    @processing_start_time.setter
    def processing_start_time(self, processing_start_time):
        """Sets the processing_start_time of this Report.

        The date and time when the report processing started, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format.  # noqa: E501

        :param processing_start_time: The processing_start_time of this Report.  # noqa: E501
        :type: datetime
        """

        self._processing_start_time = processing_start_time

    @property
    def processing_end_time(self):
        """Gets the processing_end_time of this Report.  # noqa: E501

        The date and time when the report processing completed, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format.  # noqa: E501

        :return: The processing_end_time of this Report.  # noqa: E501
        :rtype: datetime
        """
        return self._processing_end_time

    @processing_end_time.setter
    def processing_end_time(self, processing_end_time):
        """Sets the processing_end_time of this Report.

        The date and time when the report processing completed, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format.  # noqa: E501

        :param processing_end_time: The processing_end_time of this Report.  # noqa: E501
        :type: datetime
        """

        self._processing_end_time = processing_end_time

    @property
    def report_document_id(self):
        """Gets the report_document_id of this Report.  # noqa: E501

        The identifier for the report document. Pass this into the `getReportDocument` operation to get the information you will need to retrieve the report document's contents.  # noqa: E501

        :return: The report_document_id of this Report.  # noqa: E501
        :rtype: str
        """
        return self._report_document_id

    @report_document_id.setter
    def report_document_id(self, report_document_id):
        """Sets the report_document_id of this Report.

        The identifier for the report document. Pass this into the `getReportDocument` operation to get the information you will need to retrieve the report document's contents.  # noqa: E501

        :param report_document_id: The report_document_id of this Report.  # noqa: E501
        :type: str
        """

        self._report_document_id = report_document_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Report, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Report):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
