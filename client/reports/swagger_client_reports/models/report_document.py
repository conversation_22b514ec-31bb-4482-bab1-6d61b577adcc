# coding: utf-8

"""
    Selling Partner API for Reports

    The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.  # noqa: E501

    OpenAPI spec version: 2021-06-30
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six


class ReportDocument(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'report_document_id': 'str',
        'url': 'str',
        'compression_algorithm': 'str'
    }

    attribute_map = {
        'report_document_id': 'reportDocumentId',
        'url': 'url',
        'compression_algorithm': 'compressionAlgorithm'
    }

    def __init__(self, report_document_id=None, url=None, compression_algorithm=None):  # noqa: E501
        """ReportDocument - a model defined in Swagger"""  # noqa: E501

        self._report_document_id = None
        self._url = None
        self._compression_algorithm = None
        self.discriminator = None

        self.report_document_id = report_document_id
        self.url = url
        if compression_algorithm is not None:
            self.compression_algorithm = compression_algorithm

    @property
    def report_document_id(self):
        """Gets the report_document_id of this ReportDocument.  # noqa: E501

        The identifier for the report document. This identifier is unique only in combination with a seller ID.  # noqa: E501

        :return: The report_document_id of this ReportDocument.  # noqa: E501
        :rtype: str
        """
        return self._report_document_id

    @report_document_id.setter
    def report_document_id(self, report_document_id):
        """Sets the report_document_id of this ReportDocument.

        The identifier for the report document. This identifier is unique only in combination with a seller ID.  # noqa: E501

        :param report_document_id: The report_document_id of this ReportDocument.  # noqa: E501
        :type: str
        """
        if report_document_id is None:
            raise ValueError("Invalid value for `report_document_id`, must not be `None`")  # noqa: E501

        self._report_document_id = report_document_id

    @property
    def url(self):
        """Gets the url of this ReportDocument.  # noqa: E501

        A presigned URL for the report document. If `compressionAlgorithm` is not returned, you can download the report directly from this URL. This URL expires after 5 minutes.  # noqa: E501

        :return: The url of this ReportDocument.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this ReportDocument.

        A presigned URL for the report document. If `compressionAlgorithm` is not returned, you can download the report directly from this URL. This URL expires after 5 minutes.  # noqa: E501

        :param url: The url of this ReportDocument.  # noqa: E501
        :type: str
        """
        if url is None:
            raise ValueError("Invalid value for `url`, must not be `None`")  # noqa: E501

        self._url = url

    @property
    def compression_algorithm(self):
        """Gets the compression_algorithm of this ReportDocument.  # noqa: E501

        If the report document contents have been compressed, the compression algorithm used is returned in this property and you must decompress the report when you download. Otherwise, you can download the report directly. Refer to [Step 2. Download the report](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-retrieve-a-report#step-2-download-the-report) in the use case guide, where sample code is provided.  # noqa: E501

        :return: The compression_algorithm of this ReportDocument.  # noqa: E501
        :rtype: str
        """
        return self._compression_algorithm

    @compression_algorithm.setter
    def compression_algorithm(self, compression_algorithm):
        """Sets the compression_algorithm of this ReportDocument.

        If the report document contents have been compressed, the compression algorithm used is returned in this property and you must decompress the report when you download. Otherwise, you can download the report directly. Refer to [Step 2. Download the report](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-retrieve-a-report#step-2-download-the-report) in the use case guide, where sample code is provided.  # noqa: E501

        :param compression_algorithm: The compression_algorithm of this ReportDocument.  # noqa: E501
        :type: str
        """
        allowed_values = ["GZIP"]  # noqa: E501
        if compression_algorithm not in allowed_values:
            raise ValueError(
                "Invalid value for `compression_algorithm` ({0}), must be one of {1}"  # noqa: E501
                .format(compression_algorithm, allowed_values)
            )

        self._compression_algorithm = compression_algorithm

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ReportDocument, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ReportDocument):
            return False

        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        return not self == other
