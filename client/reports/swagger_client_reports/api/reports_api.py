# coding: utf-8

"""
    Selling Partner API for Reports

    The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.  # noqa: E501

    OpenAPI spec version: 2021-06-30
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

from swagger_client_reports.api_client import ApiClient


class ReportsApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client

    def cancel_report(self, report_id, **kwargs):  # noqa: E501
        """cancel_report  # noqa: E501

        Cancels the report that you specify. Only reports with `processingStatus=IN_QUEUE` can be cancelled. Cancelled reports are returned in subsequent calls to the `getReport` and `getReports` operations.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_report(report_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str report_id: The identifier for the report. This identifier is unique only in combination with a seller ID. (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.cancel_report_with_http_info(report_id, **kwargs)  # noqa: E501
        else:
            (data) = self.cancel_report_with_http_info(report_id, **kwargs)  # noqa: E501
            return data

    def cancel_report_with_http_info(self, report_id, **kwargs):  # noqa: E501
        """cancel_report  # noqa: E501

        Cancels the report that you specify. Only reports with `processingStatus=IN_QUEUE` can be cancelled. Cancelled reports are returned in subsequent calls to the `getReport` and `getReports` operations.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_report_with_http_info(report_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str report_id: The identifier for the report. This identifier is unique only in combination with a seller ID. (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['report_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_report" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'report_id' is set
        if self.api_client.client_side_validation and ('report_id' not in params or
                                                       params['report_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `report_id` when calling `cancel_report`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'report_id' in params:
            path_params['reportId'] = params['report_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/reports/2021-06-30/reports/{reportId}', 'DELETE',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def cancel_report_schedule(self, report_schedule_id, **kwargs):  # noqa: E501
        """cancel_report_schedule  # noqa: E501

        Cancels the report schedule that you specify.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_report_schedule(report_schedule_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str report_schedule_id: The identifier for the report schedule. This identifier is unique only in combination with a seller ID. (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.cancel_report_schedule_with_http_info(report_schedule_id, **kwargs)  # noqa: E501
        else:
            (data) = self.cancel_report_schedule_with_http_info(report_schedule_id, **kwargs)  # noqa: E501
            return data

    def cancel_report_schedule_with_http_info(self, report_schedule_id, **kwargs):  # noqa: E501
        """cancel_report_schedule  # noqa: E501

        Cancels the report schedule that you specify.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.cancel_report_schedule_with_http_info(report_schedule_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str report_schedule_id: The identifier for the report schedule. This identifier is unique only in combination with a seller ID. (required)
        :return: None
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['report_schedule_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method cancel_report_schedule" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'report_schedule_id' is set
        if self.api_client.client_side_validation and ('report_schedule_id' not in params or
                                                       params['report_schedule_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `report_schedule_id` when calling `cancel_report_schedule`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'report_schedule_id' in params:
            path_params['reportScheduleId'] = params['report_schedule_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/reports/2021-06-30/schedules/{reportScheduleId}', 'DELETE',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type=None,  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_report(self, body, **kwargs):  # noqa: E501
        """create_report  # noqa: E501

        Creates a report.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0167 | 15 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_report(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateReportSpecification body: Information required to create the report. (required)
        :return: CreateReportResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_report_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_report_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_report_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_report  # noqa: E501

        Creates a report.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0167 | 15 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_report_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateReportSpecification body: Information required to create the report. (required)
        :return: CreateReportResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_report" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_report`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/reports/2021-06-30/reports', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateReportResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_report_schedule(self, body, **kwargs):  # noqa: E501
        """create_report_schedule  # noqa: E501

        Creates a report schedule. If a report schedule with the same report type and marketplace IDs already exists, it will be cancelled and replaced with this one.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_report_schedule(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateReportScheduleSpecification body: Information required to create the report schedule. (required)
        :return: CreateReportScheduleResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_report_schedule_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_report_schedule_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_report_schedule_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_report_schedule  # noqa: E501

        Creates a report schedule. If a report schedule with the same report type and marketplace IDs already exists, it will be cancelled and replaced with this one.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_report_schedule_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateReportScheduleSpecification body: Information required to create the report schedule. (required)
        :return: CreateReportScheduleResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_report_schedule" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_report_schedule`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/reports/2021-06-30/schedules', 'POST',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateReportScheduleResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_report(self, report_id, **kwargs):  # noqa: E501
        """get_report  # noqa: E501

        Returns report details (including the `reportDocumentId`, if available) for the report that you specify.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 15 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_report(report_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str report_id: The identifier for the report. This identifier is unique only in combination with a seller ID. (required)
        :return: Report
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_report_with_http_info(report_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_report_with_http_info(report_id, **kwargs)  # noqa: E501
            return data

    def get_report_with_http_info(self, report_id, **kwargs):  # noqa: E501
        """get_report  # noqa: E501

        Returns report details (including the `reportDocumentId`, if available) for the report that you specify.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 2 | 15 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_report_with_http_info(report_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str report_id: The identifier for the report. This identifier is unique only in combination with a seller ID. (required)
        :return: Report
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['report_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_report" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'report_id' is set
        if self.api_client.client_side_validation and ('report_id' not in params or
                                                       params['report_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `report_id` when calling `get_report`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'report_id' in params:
            path_params['reportId'] = params['report_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/reports/2021-06-30/reports/{reportId}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='Report',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_report_document(self, report_document_id, **kwargs):  # noqa: E501
        """get_report_document  # noqa: E501

        Returns the information required for retrieving a report document's contents.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0167 | 15 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_report_document(report_document_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str report_document_id: The identifier for the report document. (required)
        :return: ReportDocument
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_report_document_with_http_info(report_document_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_report_document_with_http_info(report_document_id, **kwargs)  # noqa: E501
            return data

    def get_report_document_with_http_info(self, report_document_id, **kwargs):  # noqa: E501
        """get_report_document  # noqa: E501

        Returns the information required for retrieving a report document's contents.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0167 | 15 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_report_document_with_http_info(report_document_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str report_document_id: The identifier for the report document. (required)
        :return: ReportDocument
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['report_document_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_report_document" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'report_document_id' is set
        if self.api_client.client_side_validation and ('report_document_id' not in params or
                                                       params['report_document_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `report_document_id` when calling `get_report_document`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'report_document_id' in params:
            path_params['reportDocumentId'] = params['report_document_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/reports/2021-06-30/documents/{reportDocumentId}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ReportDocument',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_report_schedule(self, report_schedule_id, **kwargs):  # noqa: E501
        """get_report_schedule  # noqa: E501

        Returns report schedule details for the report schedule that you specify.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_report_schedule(report_schedule_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str report_schedule_id: The identifier for the report schedule. This identifier is unique only in combination with a seller ID. (required)
        :return: ReportSchedule
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_report_schedule_with_http_info(report_schedule_id, **kwargs)  # noqa: E501
        else:
            (data) = self.get_report_schedule_with_http_info(report_schedule_id, **kwargs)  # noqa: E501
            return data

    def get_report_schedule_with_http_info(self, report_schedule_id, **kwargs):  # noqa: E501
        """get_report_schedule  # noqa: E501

        Returns report schedule details for the report schedule that you specify.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_report_schedule_with_http_info(report_schedule_id, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param str report_schedule_id: The identifier for the report schedule. This identifier is unique only in combination with a seller ID. (required)
        :return: ReportSchedule
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['report_schedule_id']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_report_schedule" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'report_schedule_id' is set
        if self.api_client.client_side_validation and ('report_schedule_id' not in params or
                                                       params['report_schedule_id'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `report_schedule_id` when calling `get_report_schedule`")  # noqa: E501

        collection_formats = {}

        path_params = {}
        if 'report_schedule_id' in params:
            path_params['reportScheduleId'] = params['report_schedule_id']  # noqa: E501

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/reports/2021-06-30/schedules/{reportScheduleId}', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ReportSchedule',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_report_schedules(self, report_types, **kwargs):  # noqa: E501
        """get_report_schedules  # noqa: E501

        Returns report schedule details that match the filters that you specify.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_report_schedules(report_types, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param list[str] report_types: A list of report types used to filter report schedules. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information. (required)
        :return: ReportScheduleList
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_report_schedules_with_http_info(report_types, **kwargs)  # noqa: E501
        else:
            (data) = self.get_report_schedules_with_http_info(report_types, **kwargs)  # noqa: E501
            return data

    def get_report_schedules_with_http_info(self, report_types, **kwargs):  # noqa: E501
        """get_report_schedules  # noqa: E501

        Returns report schedule details that match the filters that you specify.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_report_schedules_with_http_info(report_types, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param list[str] report_types: A list of report types used to filter report schedules. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information. (required)
        :return: ReportScheduleList
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['report_types']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_report_schedules" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'report_types' is set
        if self.api_client.client_side_validation and ('report_types' not in params or
                                                       params['report_types'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `report_types` when calling `get_report_schedules`")  # noqa: E501

        if self.api_client.client_side_validation and ('report_types' in params and
                                            len(params['report_types']) > 10):
            raise ValueError("Invalid value for parameter `report_types` when calling `get_report_schedules`, number of items must be less than or equal to `10`")  # noqa: E501
        if self.api_client.client_side_validation and ('report_types' in params and
                                            len(params['report_types']) < 1):
            raise ValueError("Invalid value for parameter `report_types` when calling `get_report_schedules`, number of items must be greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}

        query_params = []
        if 'report_types' in params:
            query_params.append(('reportTypes', params['report_types']))  # noqa: E501
            collection_formats['reportTypes'] = 'csv'  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/reports/2021-06-30/schedules', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ReportScheduleList',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def get_reports(self, **kwargs):  # noqa: E501
        """get_reports  # noqa: E501

        Returns report details for the reports that match the filters that you specify.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_reports(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param list[str] report_types: A list of report types used to filter reports. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information. When reportTypes is provided, the other filter parameters (processingStatuses, marketplaceIds, createdSince, createdUntil) and pageSize may also be provided. Either reportTypes or nextToken is required.
        :param list[str] processing_statuses: A list of processing statuses used to filter reports.
        :param list[str] marketplace_ids: A list of marketplace identifiers used to filter reports. The reports returned will match at least one of the marketplaces that you specify.
        :param int page_size: The maximum number of reports to return in a single call.
        :param datetime created_since: The earliest report creation date and time for reports to include in the response, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format. The default is 90 days ago. Reports are retained for a maximum of 90 days.
        :param datetime created_until: The latest report creation date and time for reports to include in the response, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format. The default is now.
        :param str next_token: A string token returned in the response to your previous request. `nextToken` is returned when the number of results exceeds the specified `pageSize` value. To get the next page of results, call the `getReports` operation and include this token as the only parameter. Specifying `nextToken` with any other parameters will cause the request to fail.
        :return: GetReportsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.get_reports_with_http_info(**kwargs)  # noqa: E501
        else:
            (data) = self.get_reports_with_http_info(**kwargs)  # noqa: E501
            return data

    def get_reports_with_http_info(self, **kwargs):  # noqa: E501
        """get_reports  # noqa: E501

        Returns report details for the reports that match the filters that you specify.  **Usage Plan:**  | Rate (requests per second) | Burst | | ---- | ---- | | 0.0222 | 10 |  The `x-amzn-RateLimit-Limit` response header returns the usage plan rate limits that were applied to the requested operation, when available. The table above indicates the default rate and burst values for this operation. Selling partners whose business demands require higher throughput may see higher rate and burst values than those shown here. For more information, refer to [Usage Plans and Rate Limits in the Selling Partner API](https://developer-docs.amazon.com/sp-api/docs/usage-plans-and-rate-limits-in-the-sp-api).  # noqa: E501
        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.get_reports_with_http_info(async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param list[str] report_types: A list of report types used to filter reports. Refer to [Report Type Values](https://developer-docs.amazon.com/sp-api/docs/report-type-values) for more information. When reportTypes is provided, the other filter parameters (processingStatuses, marketplaceIds, createdSince, createdUntil) and pageSize may also be provided. Either reportTypes or nextToken is required.
        :param list[str] processing_statuses: A list of processing statuses used to filter reports.
        :param list[str] marketplace_ids: A list of marketplace identifiers used to filter reports. The reports returned will match at least one of the marketplaces that you specify.
        :param int page_size: The maximum number of reports to return in a single call.
        :param datetime created_since: The earliest report creation date and time for reports to include in the response, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format. The default is 90 days ago. Reports are retained for a maximum of 90 days.
        :param datetime created_until: The latest report creation date and time for reports to include in the response, in <a href='https://developer-docs.amazon.com/sp-api/docs/iso-8601'>ISO 8601</a> date time format. The default is now.
        :param str next_token: A string token returned in the response to your previous request. `nextToken` is returned when the number of results exceeds the specified `pageSize` value. To get the next page of results, call the `getReports` operation and include this token as the only parameter. Specifying `nextToken` with any other parameters will cause the request to fail.
        :return: GetReportsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['report_types', 'processing_statuses', 'marketplace_ids', 'page_size', 'created_since', 'created_until', 'next_token']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method get_reports" % key
                )
            params[key] = val
        del params['kwargs']

        if self.api_client.client_side_validation and ('report_types' in params and
                                            len(params['report_types']) > 10):
            raise ValueError("Invalid value for parameter `report_types` when calling `get_reports`, number of items must be less than or equal to `10`")  # noqa: E501
        if self.api_client.client_side_validation and ('report_types' in params and
                                            len(params['report_types']) < 1):
            raise ValueError("Invalid value for parameter `report_types` when calling `get_reports`, number of items must be greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('processing_statuses' in params and
                                            len(params['processing_statuses']) < 1):
            raise ValueError("Invalid value for parameter `processing_statuses` when calling `get_reports`, number of items must be greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('marketplace_ids' in params and
                                            len(params['marketplace_ids']) > 10):
            raise ValueError("Invalid value for parameter `marketplace_ids` when calling `get_reports`, number of items must be less than or equal to `10`")  # noqa: E501
        if self.api_client.client_side_validation and ('marketplace_ids' in params and
                                            len(params['marketplace_ids']) < 1):
            raise ValueError("Invalid value for parameter `marketplace_ids` when calling `get_reports`, number of items must be greater than or equal to `1`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] > 100):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `get_reports`, must be a value less than or equal to `100`")  # noqa: E501
        if self.api_client.client_side_validation and ('page_size' in params and params['page_size'] < 1):  # noqa: E501
            raise ValueError("Invalid value for parameter `page_size` when calling `get_reports`, must be a value greater than or equal to `1`")  # noqa: E501
        collection_formats = {}

        path_params = {}

        query_params = []
        if 'report_types' in params:
            query_params.append(('reportTypes', params['report_types']))  # noqa: E501
            collection_formats['reportTypes'] = 'csv'  # noqa: E501
        if 'processing_statuses' in params:
            query_params.append(('processingStatuses', params['processing_statuses']))  # noqa: E501
            collection_formats['processingStatuses'] = 'csv'  # noqa: E501
        if 'marketplace_ids' in params:
            query_params.append(('marketplaceIds', params['marketplace_ids']))  # noqa: E501
            collection_formats['marketplaceIds'] = 'csv'  # noqa: E501
        if 'page_size' in params:
            query_params.append(('pageSize', params['page_size']))  # noqa: E501
        if 'created_since' in params:
            query_params.append(('createdSince', params['created_since']))  # noqa: E501
        if 'created_until' in params:
            query_params.append(('createdUntil', params['created_until']))  # noqa: E501
        if 'next_token' in params:
            query_params.append(('nextToken', params['next_token']))  # noqa: E501

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['application/json'])  # noqa: E501

        # Authentication setting
        auth_settings = []  # noqa: E501

        return self.api_client.call_api(
            '/reports/2021-06-30/reports', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='GetReportsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)
