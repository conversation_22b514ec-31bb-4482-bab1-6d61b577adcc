# coding: utf-8

"""
    Selling Partner API for Reports

    The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.  # noqa: E501

    OpenAPI spec version: 2021-06-30
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from setuptools import setup, find_packages  # noqa: H301

NAME = "swagger-client-reports"
VERSION = "1.0.0"
# To install the library, run the following
#
# python setup.py install
#
# prerequisite: setuptools
# http://pypi.python.org/pypi/setuptools

REQUIRES = [
    "certifi>=2017.4.17",
    "python-dateutil>=2.1",
    "six>=1.10",
    "urllib3>=1.23"
]
    

setup(
    name=NAME,
    version=VERSION,
    description="Selling Partner API for Reports",
    author_email="",
    url="",
    keywords=["Swagger", "Selling Partner API for Reports"],
    install_requires=REQUIRES,
    packages=find_packages(),
    include_package_data=True,
    long_description="""\
    The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.  # noqa: E501
    """
)
