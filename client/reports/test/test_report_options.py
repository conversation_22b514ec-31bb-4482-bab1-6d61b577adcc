# coding: utf-8

"""
    Selling Partner API for Reports

    The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.  # noqa: E501

    OpenAPI spec version: 2021-06-30
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_reports
from swagger_client_reports.models.report_options import ReportOptions  # noqa: E501
from swagger_client_reports.rest import ApiException


class TestReportOptions(unittest.TestCase):
    """ReportOptions unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testReportOptions(self):
        """Test ReportOptions"""
        # FIXME: construct object with mandatory attributes with example values
        # model = swagger_client_reports.models.report_options.ReportOptions()  # noqa: E501
        pass


if __name__ == '__main__':
    unittest.main()
