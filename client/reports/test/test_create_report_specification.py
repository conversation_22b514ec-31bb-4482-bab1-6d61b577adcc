# coding: utf-8

"""
    Selling Partner API for Reports

    The Selling Partner API for Reports lets you retrieve and manage a variety of reports that can help selling partners manage their businesses.  # noqa: E501

    OpenAPI spec version: 2021-06-30
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import unittest

import swagger_client_reports
from swagger_client_reports.models.create_report_specification import CreateReportSpecification  # noqa: E501
from swagger_client_reports.rest import ApiException


class TestCreateReportSpecification(unittest.TestCase):
    """CreateReportSpecification unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testCreateReportSpecification(self):
        """Test CreateReportSpecification"""
        # FIXME: construct object with mandatory attributes with example values
        # model = swagger_client_reports.models.create_report_specification.CreateReportSpecification()  # noqa: E501
        pass


if __name__ == '__main__':
    unittest.main()
